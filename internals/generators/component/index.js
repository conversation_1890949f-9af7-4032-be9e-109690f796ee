/**
 * Component Generator
 */

/* eslint strict: ["off"] */

'use strict';

const componentExists = require('../utils/componentExists');

module.exports = {
  description: 'Add an unconnected component',
  prompts: [
    {
      type: 'input',
      name: 'name',
      message: 'What should it be called?',
      default: 'Button',
      validate: (value) => {
        if (/.+/.test(value)) {
          return componentExists(value) ? 'A component or container with this name already exists' : true;
        }

        return 'The name is required';
      }
    },
    // {
    //   type: 'confirm',
    //   name: 'memo',
    //   default: false,
    //   message: 'Do you want to wrap your component in React.memo?'
    // },
    {
      type: 'confirm',
      name: 'wantMessages',
      default: true,
      message: 'Do you want i18n messages (i.e. will this component use text)?'
    },
    {
      type: 'confirm',
      name: 'wantLoadable',
      default: true,
      message: 'Do you want to load the component asynchronously?'
    },
    {
      type: 'confirm',
      name: 'wantTests',
      default: true,
      message: 'Do you want tests?'
    }
  ],
  actions: (data) => {
    // Generate index.js and index.test.js
    const actions = [
      {
        type: 'add',
        path: '../../src/components/{{properCase name}}/index.tsx',
        templateFile: './component/index.tsx.hbs',
        abortOnFail: true
      }
    ];

    // If they want tests
    if (data.wantTests) {
      actions.push({
        type: 'add',
        path: '../../src/components/{{properCase name}}/tests/index.test.tsx',
        templateFile: './component/test.tsx.hbs',
        abortOnFail: true
      });
    }

    // If they want a i18n messages file
    if (data.wantMessages) {
      actions.push({
        type: 'add',
        path: '../../src/components/{{properCase name}}/messages.ts',
        templateFile: './component/messages.ts.hbs',
        abortOnFail: true
      });
    }

    // If want Loadable.js to load the component asynchronously
    if (data.wantLoadable) {
      actions.push({
        type: 'add',
        path: '../../src/components/{{properCase name}}/Loadable.ts',
        templateFile: './component/loadable.ts.hbs',
        abortOnFail: true
      });
    }
    // If want Loadable.js to load the component asynchronously

    actions.push({
      type: 'prettify',
      path: '/components/'
    });

    return actions;
  }
};
