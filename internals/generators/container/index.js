/**
 * Container Generator
 */

const componentExists = require('../utils/componentExists');

module.exports = {
  description: 'Add a container component',
  prompts: [
    // {
    //   type: 'input',
    //   name: 'folder',
    //   message: '컴포넌트가 생성되는 폴더를 지정하시겠습니까? 끝에 / 를 꼭넣어주세요!!!!',
    //   default: 'View',
    //   validate: (value) => {
    //     if (/.+/.test(value)) {
    //       return componentExists(value) ? 'A component or container with this name already exists' : true;
    //     }
    //     return 'The name is required';
    //   }
    // },
    {
      type: 'input',
      name: 'name',
      message: 'What should it be called?',
      default: 'Form',
      validate: (value) => {
        if (/.+/.test(value)) {
          return componentExists(value) ? 'A component or container with this name already exists' : true;
        }
        return 'The name is required';
      }
    },
    // {
    //   type: 'confirm',
    //   name: 'memo',
    //   default: false,
    //   message: 'Do you want to wrap your component in React.memo?'
    // },
    {
      type: 'confirm',
      name: 'wantHeaders',
      default: false,
      message: 'Do you want headers?'
    }
    // {
    //   type: 'confirm',
    //   name: 'wantActionsAndReducer',
    //   default: true,
    //   message: 'Do you want an actions/constants/selectors/reducer tuple for this container?'
    // },
    // {
    //   type: 'confirm',
    //   name: 'wantSaga',
    //   default: true,
    //   message: 'Do you want sagas for asynchronous flows? (e.g. fetching data)'
    // },
    // {
    //   type: 'confirm',
    //   name: 'wantMessages',
    //   default: true,
    //   message: 'Do you want i18n messages (i.e. will this component use text)?'
    // }
    // {
    //   type: 'confirm',
    //   name: 'wantTypes',
    //   default: true,
    //   message: 'Do you want to have types.ts file?'
    // },
    // {
    //   type: 'confirm',
    //   name: 'wantTests',
    //   default: true,
    //   message: 'Do you want to have tests'
    // }
  ],
  actions: (data) => {
    // Generate index.ts and index.test.tsx
    const actions = [
      {
        type: 'add',
        path: '../../src/containers/{{properCase name}}/index.tsx',
        templateFile: './container/index.tsx.hbs',
        abortOnFail: true
      },
      {
        type: 'add',
        path: '../../src/containers/{{properCase name}}/styles.tsx',
        templateFile: './container/styles.tsx.hbs',
        abortOnFail: true
      },
      {
        type: 'add',
        path: '../../src/containers/{{properCase name}}/messages.tsx',
        templateFile: './container/messages.ts.hbs',
        abortOnFail: true
      },
      {
        type: 'add',
        path: '../../src/containers/{{properCase name}}/viewMeta.tsx',
        templateFile: './container/viewMeta.tsx.hbs',
        abortOnFail: true
      }
    ];

    // If component wants tests
    if (data.wantTests) {
      actions.push({
        type: 'add',
        path: '../../src/containers/{{properCase name}}/tests/index.test.tsx',
        templateFile: './container/test.tsx.hbs',
        abortOnFail: true
      });
    }

    // If component wants messages
    // if (data.wantMessages) {
    //   actions.push({
    //     type: 'add',
    //     path: '../../src/containers/{{properCase name}}/messages.ts',
    //     templateFile: './container/messages.ts.hbs',
    //     abortOnFail: true
    //   });
    // }

    // If they want actions and a reducer, generate actions.ts, constants.ts,
    // reducer.ts and the corresponding tests for actions and the reducer
    actions.push({
      type: 'add',
      path: '../../src/containers/{{properCase name}}/actions.ts',
      templateFile: './container/actions.ts.hbs',
      abortOnFail: true
    });
    // Constants
    actions.push({
      type: 'add',
      path: '../../src/containers/{{properCase name}}/constants.ts',
      templateFile: './container/constants.ts.hbs',
      abortOnFail: true
    });

    if (data.wantActionsAndReducer) {
      // Actions
      actions.push({
        type: 'add',
        path: '../../src/containers/{{properCase name}}/tests/actions.test.ts',
        templateFile: './container/actions.test.ts.hbs',
        abortOnFail: true
      });
      // Selectors
      actions.push({
        type: 'add',
        path: '../../src/containers/{{properCase name}}/selectors.ts',
        templateFile: './container/selectors.ts.hbs',
        abortOnFail: true
      });
      actions.push({
        type: 'add',
        path: '../../src/containers/{{properCase name}}/tests/selectors.test.ts',
        templateFile: './container/selectors.test.ts.hbs',
        abortOnFail: true
      });

      // Reducer
      actions.push({
        type: 'add',
        path: '../../src/containers/{{properCase name}}/reducer.ts',
        templateFile: './container/reducer.ts.hbs',
        abortOnFail: true
      });
      actions.push({
        type: 'add',
        path: '../../src/containers/{{properCase name}}/tests/reducer.test.ts',
        templateFile: './container/reducer.test.ts.hbs',
        abortOnFail: true
      });
    }

    // Sagas
    if (data.wantSaga) {
      actions.push({
        type: 'add',
        path: '../../src/containers/{{properCase name}}/saga.ts',
        templateFile: './container/saga.ts.hbs',
        abortOnFail: true
      });
      actions.push({
        type: 'add',
        path: '../../src/containers/{{properCase name}}/tests/saga.test.ts',
        templateFile: './container/saga.test.ts.hbs',
        abortOnFail: true
      });
    }

    if (data.wantLoadable) {
      actions.push({
        type: 'add',
        path: '../../src/containers/{{properCase name}}/Loadable.ts',
        templateFile: './component/loadable.ts.hbs',
        abortOnFail: true
      });
    }

    if (data.wantTypes || data.wantActionsAndReducer) {
      actions.push({
        type: 'add',
        path: '../../src/containers/{{properCase name}}/types.ts',
        templateFile: './container/types.ts.hbs',
        abortOnFail: true
      });
      actions.push({
        type: 'modify',
        path: '../../src/types/index.ts',
        pattern: new RegExp(/.*\/\/.*\[IMPORT NEW CONTAINERSTATE ABOVE\].+\n/),
        templateFile: './container/importContainerState.hbs',
        abortOnFail: true
      });
      actions.push({
        type: 'modify',
        path: '../../src/types/index.ts',
        pattern: new RegExp(/.*\/\/.*\[INSERT NEW REDUCER KEY ABOVE\].+\n/),
        templateFile: './container/srcendApplicationRootState.hbs',
        abortOnFail: true
      });
    }

    actions.push({
      type: 'prettify',
      path: '/containers/'
    });

    return actions;
  }
};
