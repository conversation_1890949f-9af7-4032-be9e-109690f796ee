const path = require("path");
const webpack = require("webpack");
const webpackMerge = require("webpack-merge");
const CaseSensitivePathsPlugin = require('case-sensitive-paths-webpack-plugin');

const paths = require("./paths");
// const getClientEnvironment = require('./env');
const commonConfig = require("../webpack.config.js");
const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin');

var env = require('./env');
var publicPath = '/';
module.exports = webpackMerge(commonConfig(), {

	/*
		source mapping 스타일을 선택 한다.
		이 값은 build 및 rebuild 속도에 영향울 준다.

		eval - 개발용으로 이 type 빌드 속도가 가장 빠르며 단점은 행 번호가 표시 되지 않는다.
		cheap-module-source-map - 로드 된 소스 맵을 라인 당 단일 매핑으로 간소
	*/
	devtool: 'inline-source-map',
	entry : [
    '@babel/polyfill',
		require.resolve('webpack-dev-server/client') + '?/',
		require.resolve('webpack/hot/dev-server'),
		// require.resolve('./polyfills'),
		// 'react-hot-loader/patch',
	],
  target: process.env.NODE_ENV === 'development' ? ['web', 'es5'] : 'browserslist',
	output : {
    environment: {
      arrowFunction: false,
      bigIntLiteral: false,
      const: false,
      destructuring: false,
      dynamicImport: false,
      forOf: false,
      module: false,
    },

		path : path.resolve(__dirname, 'build'),
		filename : "js/bundle.js",
		publicPath : "/"

	},

  module : {

    rules : [
      {
        test : /\.(jpg|png|gif|eot|otf|webp|svg|ttf|woff|woff2)(\?.*)?$/,
        use : [{
          loader : "url-loader",
          options : { limit : 10000 }
        }]
      }
    ]
  },

	plugins : [
		//서버를 핫모드(소스 수정 시 자동 reload)로 전환한다.
		new webpack.DefinePlugin(env),
		new webpack.HotModuleReplacementPlugin(),
		new ReactRefreshWebpackPlugin(),
		//누락 된 모듈을 'npm install'하면,
		//webpack이 그것을 감지 할 수 있도록 개발 서버를
		//자동으로 검색을 해 수행하므로 다시 시작 할 필요가 없다.
		new CaseSensitivePathsPlugin()

	]

});
