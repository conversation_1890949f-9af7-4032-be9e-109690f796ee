import logging
from typing import Any, List, Optional

from llama_index.core.base.embeddings.base import (
    DEFAULT_EMBED_BATCH_SIZE,
    BaseEmbedding,
)
from llama_index.core.bridge.pydantic import Field, PrivateAttr
from llama_index.core.callbacks import CallbackManager
from llama_index.core.utils import get_cache_dir, infer_torch_device
from llama_index.embeddings.huggingface.utils import (
    get_query_instruct_for_model_name,
    get_text_instruct_for_model_name,
)
from sentence_transformers import SentenceTransformer

DEFAULT_HUGGINGFACE_LENGTH = 512
logger = logging.getLogger(__name__)


class CustomHugging(BaseEmbedding):

    max_length: int = Field(
        default=DEFAULT_HUGGINGFACE_LENGTH, description="Maximum length of input.", gt=0
    )
    normalize: bool = Field(default=True, description="Normalize embeddings or not.")
    query_instruction: Optional[str] = Field(
        description="Instruction to prepend to query text.", default=None
    )
    text_instruction: Optional[str] = Field(
        description="Instruction to prepend to text.", default=None
    )
    cache_folder: Optional[str] = Field(
        description="Cache folder for Hugging Face files.", default=None
    )

    _model: Any = PrivateAttr()
    _device: str = PrivateAttr()
    _parallel_process: bool = PrivateAttr()
    _target_devices: Optional[List[str]] = PrivateAttr()
    _query_prompt_name: Optional[str] = PrivateAttr()
    _text_prompt_name: Optional[str] = PrivateAttr()

    def __init__(
            self,
            model_name: str = "dunzhang/stella_en_400M_v5",
            tokenizer_name: Optional[str] = "deprecated",
            pooling: str = "deprecated",
            max_length: Optional[int] = None,
            query_instruction: Optional[str] = None,
            text_instruction: Optional[str] = None,
            normalize: bool = True,
            model: Optional[Any] = "deprecated",
            tokenizer: Optional[Any] = "deprecated",
            embed_batch_size: int = 64,
            cache_folder: Optional[str] = None,
            trust_remote_code: bool = False,
            device: Optional[str] = None,
            callback_manager: Optional[CallbackManager] = None,
            parallel_process: bool = False,
            target_devices: Optional[List[str]] = None,
            query_prompt_name: Optional[str] = None,
            text_prompt_name: Optional[str] = None,
            **model_kwargs,
    ):
        device = device or infer_torch_device()
        cache_folder = cache_folder or get_cache_dir()

        for variable, value in [
            ("model", model),
            ("tokenizer", tokenizer),
            ("pooling", pooling),
            ("tokenizer_name", tokenizer_name),
        ]:
            if value != "deprecated":
                raise ValueError(
                    f"{variable} is deprecated. Please remove it from the arguments."
                )
        if model_name is None:
            raise ValueError("The `model_name` argument must be provided.")

        model = SentenceTransformer(
            model_name,
            device=device,
            cache_folder=cache_folder,
            trust_remote_code=trust_remote_code,
            prompts=({
                "query": query_instruction
                         or get_query_instruct_for_model_name(model_name),
                "text": text_instruction
                        or get_text_instruct_for_model_name(model_name),
            } if query_instruction is not None or text_instruction is not None else None),
            **model_kwargs,
        )
        if max_length:
            model.max_seq_length = max_length
        else:
            max_length = model.max_seq_length

        super().__init__(
            embed_batch_size=embed_batch_size,
            callback_manager=callback_manager,
            model_name=model_name,
            max_length=max_length,
            normalize=normalize,
            query_instruction=query_instruction,
            text_instruction=text_instruction,
        )
        self._device = device
        self._model = model
        self._parallel_process = parallel_process
        self._target_devices = target_devices
        self._query_prompt_name = query_prompt_name
        self._text_prompt_name = text_prompt_name

    @classmethod
    def class_name(cls) -> str:
        return "HuggingFaceEmbedding"

    def _embed(
            self,
            sentences: List[str],
            prompt_name: Optional[str] = None,
    ) -> List[List[float]]:
        if self._parallel_process:
            pool = self._model.start_multi_process_pool(
                target_devices=self._target_devices
            )
            emb = self._model.encode_multi_process(
                sentences=sentences,
                pool=pool,
                batch_size=self.embed_batch_size,
                prompt_name=prompt_name,
                normalize_embeddings=self.normalize,
            )
            self._model.stop_multi_process_pool(pool=pool)

        else:
            emb = self._model.encode(
                sentences,
                batch_size=self.embed_batch_size,
                prompt_name=prompt_name,
                normalize_embeddings=self.normalize,
            )

        return emb.tolist()

    def _get_query_embedding(self, query: str) -> List[float]:
        return self._embed(query, prompt_name=self._query_prompt_name)

    async def _aget_query_embedding(self, query: str) -> List[float]:
        return self._get_query_embedding(query)

    async def _aget_text_embedding(self, text: str) -> List[float]:
        return self._get_text_embedding(text)

    def _get_text_embedding(self, text: str) -> List[float]:
        return self._embed(text, prompt_name=self._text_prompt_name)

    def _get_text_embeddings(self, texts: List[str]) -> List[List[float]]:
        return self._embed(texts, prompt_name=self._text_prompt_name)