openapi: 3.0.0
info:
  title: 식권대장 API
  description: 식권대장 클라이언트 대응용 API
  version: 3.0.0

servers:
  - url: https://api.mealc.co.kr
    description: test server

tags:
  - name: Shipping
    description: 상품 배송
  - name: CaptainPay
    description: 간편결제
  - name: Company
    description: 회사 관리
  - name: Payment
    description: 결제
  - name: App
    description: 초기 접속 정보
  - name: Store
    description: 제휴점 정보
  - name: Booking
    description: 예약 관리
  - name: Account
    description: 사용자 개인 관련
  - name: Common
    description: 공통 모듈
  - name: Link
    description: 외부서비스
  - name: Demand
    description: 식권 신청 관리
  - name: Notice
    description: 공지사항
  - name: Event
    description: 이벤트
  - name: Infra
    description: 연동 관리
  - name: Openapi
    description: 외부 연동
  - name: Download
    description: 다운로드 관리

paths:
  # tag: Shipping
  /shipping/v1:
    get:
      tags:
        - Shipping
      summary: 상품 배송
      description: 메인> 주문/배송 내역> 상품 배송
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  contents:
                    type: array
                    items:
                      properties:
                        couponId:
                          type: string
                          description: 쿠폰 ID
                        storeName:
                          type: string
                          description: 예약 식당 이름
                        shippingDate:
                          type: integer
                          description: 주문 일자
                        isCancelable:
                          type: boolean
                          description: 취소 가능 여부</br>true -> 취소 가능</br>false -> 취소 불가
                        logistics:
                          type: object
                          properties:
                            logisticsNumber:
                              type: string
                              description: 송장번호
                            trakingHistoryLink:
                              type: string
                              description: 택배사 배송정보 조회 URL
                            logisticsName:
                              type: string
                              description: 택배사 명
                        shipping:
                          type: object
                          properties:
                            recipient:
                              type: string
                              description: 수령인
                            tel:
                              type: string
                              description: 전화번호
                            userSelectedType:
                              type: string
                              description: 검색 결과에서 사용자가 선택한 주소의 타입 R(도로명), J(지번)
                            roadAddress:
                              type: string
                              description: 도로명 주소
                            jibunAddress:
                              type: string
                              description: 지번 주소
                            addressDetail:
                              type: string
                              description: 상세주소
                            shippingMemo:
                              type: string
                              description: 배송 메모
                          required:
                            - recipient
                            - tel
                            - userSelectedType
                            - roadAddress
                            - jibunAddress
                            - jibunAddress
                            - addressDetail
                        menuList:
                          type: array
                          items:
                            properties:
                              menuId:
                                type: string
                                description: 메뉴 ID
                              menuName:
                                type: string
                                description: 메뉴명
                              quantity:
                                type: integer
                                description: 수량
                              price:
                                type: integer
                                description: 금액
                            required:
                              - menuName
                              - quantity
                              - price
                      required:
                        - couponId
                        - storeName
                        - shippingDate
                        - isCancelable
        500:
          $ref: '#/components/responses/ServerError'
  /shipping/v1/address/{storeId}:
    get:
      tags:
        - Shipping
      summary: 주소 API
      description: 주소 API
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: storeId
          description: 제휴점 고유 아이디 제휴점별 배송가능 지역 체크를 위해 필요
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK <br />
            요청 성공시 아래의 response data를 ios/android에 정의된 브릿지를 통해 데이터를 전달한다.<br />
            1. android<br/>
            1.1 성공 SikdaeBridge.shippingAddress<br />
            2. ios<br />
            2.1 성공 shippingAddress<br />
          content:
            application/json:
              schema:
                type: object
                properties:
                  userSelectedType:
                    type: string
                    description: 검색 결과에서 사용자가 선택한 주소의 타입 R(도로명), J(지번)
                  roadAddress:
                    type: string
                    description: 도로명 주소
                  jibunAddress:
                    type: string
                    description: 지번 주소
                required:
                  - userSelectedType
        500:
          $ref: '#/components/responses/ServerError'
  /shipping/v1/product:
    post:
      tags:
        - Shipping
      summary: 배송 상품 주문 v1
      description: 배송 상품 주문 v1
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                storeId:
                  type: string
                  description: 제휴점 고유 아이디
                shipping:
                  type: object
                  properties:
                    recipient:
                      type: string
                      description: 수령인
                    tel:
                      type: string
                      description: 전화번호
                    userSelectedType:
                      type: string
                      description: 검색 결과에서 사용자가 선택한 주소의 타입 R(도로명), J(지번)
                    roadAddress:
                      type: string
                      description: 도로명 주소
                    jibunAddress:
                      type: string
                      description: 지번 주소
                    addressDetail:
                      type: string
                      description: 상세주소
                    shippingMemo:
                      type: string
                      description: 배송 메모
                menu:
                  type: array
                  items:
                    properties:
                      id:
                        type: string
                        description: 메뉴 고유 아이디
                      count:
                        type: integer
                        description: 메뉴 선택 개수 (default -> 1)
                    required:
                      - id
                      - count
              required:
                - storeId
                - shipping
                - menu
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  content:
                    properties:
                      pay:
                        type: object
                        properties:
                          couponId:
                            type: string
                            description: 고유 ID
                          roomId:
                            type: integer
                            description: 결제번호
                      store:
                        type: object
                        properties:
                          storeId:
                            type: string
                            description: 제휴점 고유 아이디
                          name:
                            type: string
                            description: 제휴점 이름
                          supplyTypes:
                            type: array
                            items:
                              properties:
                                code:
                                  type: string
                                  enum: [LOCAL, DELIVERY, BOOKING, QPCON, SHIPPING]
                                  description: LOCAL -> 방문식사<br/>DELIVERY -> 배달식사<br/>BOOKING -> 예약식사<br/>QPCON -> 모바일상품권<br/>SHIPPING -> 배송 상품
                                name:
                                  type: string
                                  description: 코드에 관한 TEXT(방문식사,배달식사,예약식사,모바일상품권,배송상품)
                              required:
                                - code
                                - name
                      date:
                        type: object
                        properties:
                          pay:
                            type: integer
                            description: 결제시간
                          cancel:
                            type: integer
                            description: 취소시간
                          available:
                            type: integer
                            description: 유효시간
                          process:
                            type: integer
                            description: 처리시간
                          plus:
                            type: integer
                            description: 지급시간
                          minus:
                            type: integer
                            description: 차감시간
                          expired:
                            type: integer
                            description: 소멸시간
                          charge:
                            type: integer
                            description: 충전시간
                          mealTime:
                            type: integer
                            description: 식사시간
                      cause:
                        type: object
                        properties:
                          process:
                            type: string
                            description: 처리사유
                          plus:
                            type: string
                            description: 지급사유
                          minus:
                            type: string
                            description: 차감사유
                          expired:
                            type: string
                            description: 소멸사유
                      worker:
                        type: object
                        properties:
                          pay:
                            type: string
                            description: 결제자
                          plus:
                            type: string
                            description: 지급자
                          minus:
                            type: string
                            description: 차감자
                      user:
                        type: object
                        properties:
                          userId:
                            type: string
                            description: 본인 고유 아이디
                          name:
                            type: string
                            description: 본인 이름
                          totalAmount:
                            type: integer
                            description: 본인 총 금액
                      policys:
                        type: array
                        items:
                          properties:
                            policyIdx:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 이름
                            amount:
                              type: integer
                              description: 정책 사용 금액
                      guests:
                        type: array
                        description: 사용 안함(무조건 혼자결제 예약식사와 화면을 같이 쓰기 때문에 구조체 유지)
                        items:
                          properties:
                            userId:
                              type: string
                              description: 함께 결제자 고유 아이디
                            name:
                              type: string
                              description: 함께 결제자 이름
                            division:
                              type: string
                              description: 함께 결제자 부서
                            amount:
                              type: integer
                              description: 함께 결제자 사용 금액
                      menus:
                        type: array
                        items:
                          properties:
                            menuId:
                              type: string
                              description: 메뉴 고유 아이디
                            name:
                              type: string
                              description: 메뉴 이름
                            price:
                              type: integer
                              description: 메뉴 가격
                            quantity:
                              type: integer
                              description: 메뉴 수량
                            totalPrice:
                              type: integer
                              description: 메뉴 금액
                            images:
                              type: object
                              properties:
                                main:
                                  type: string
                                  description: 메인 이미지 경로
                                thumb:
                                  type: string
                                  description: 썸네일 이미지 경로
                      amount:
                        type: object
                        properties:
                          totalPay:
                            type: integer
                            description: 총 금액
                          plus:
                            type: integer
                            description: 지급식대 금액
                          minus:
                            type: integer
                            description: 차감식대 금액
                          expired:
                            type: integer
                            description: 소멸식대 금액
                          balance:
                            type: integer
                            description: 잔여식대 금액
                          chargePoint:
                            type: integer
                            description: 충전 포인트
                          chargeAmount:
                            type: integer
                            description: 충전 금액
                      addons:
                        type: object
                        properties:
                          warning:
                            type: string
                            description: 유의사항
                          chargeType:
                            type: string
                            description: 충전방식
                          type:
                            type: string
                            description: 상품 배송의 경우 USED/CANCEL 두개 type만 사용<br/>
                                          내역 타입<br/>
                                          USED -> 혼자결제<br/>
                                          MULTI_USED -> 함께결제<br/>
                                          ADMIN_USED -> 관리자 혼자결제<br/>
                                          ADMIN_MULTI_USED -> 관리자 함께결제<br/>
                                          QPCON_USED -> 모바일상품권 결제<br/>
                                          QPCON_EXPIRED -> 모바일상품권 소멸<br/>
                                          CANCEL -> 결제 취소<br/>
                                          POINT_PLUS -> 식대 포인트 지급<br/>
                                          POINT_MINUS -> 식대 포인트 차감<br/>
                                          POINT_EXPIRED -> 식대 포인트 소멸<br/>
                                          MYPOINT_CHARGE -> 마이포인트 충전<br/>
                          isCancelable:
                            type: boolean
                            description: 취소 가능 여부<br/>true -> 취소 가능<br/>false -> 취소 불가
                          pin:
                            type: string
                            description: 모바일 상품권 고유 번호
                      shipping:
                        type: object
                        properties:
                          recipient:
                            type: string
                            description: 수령인
                          tel:
                            type: string
                            description: 전화번호
                          userSelectedType:
                            type: string
                            description: 검색 결과에서 사용자가 선택한 주소의 타입 R(도로명), J(지번)
                          roadAddress:
                            type: string
                            description: 도로명 주소
                          jibunAddress:
                            type: string
                            description: 지번 주소
                          addressDetail:
                            type: string
                            description: 상세주소
                          shippingMemo:
                            type: string
                            description: 배송 메모
                        required:
                          - recipient
                          - tel
                          - userSelectedType
                          - roadAddress
                          - jibunAddress
                          - jibunAddress
                          - addressDetail
        500:
          $ref: '#/components/responses/ServerError'
  /shipping/v1/product/{couponId}:
    get:
      tags:
        - Shipping
      summary: 상품 배송
      description: 메인> 주문/배송 내역> 상품 배송> 자세히
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: couponId
          description: 쿠폰 ID
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  content:
                    properties:
                      pay:
                        type: object
                        properties:
                          couponId:
                            type: string
                            description: 쿠폰 고유 ID
                          roomId:
                            type: integer
                            description: 결제번호
                      store:
                        type: object
                        properties:
                          storeId:
                            type: string
                            description: 제휴점 고유 아이디
                          name:
                            type: string
                            description: 제휴점 이름
                          supplyTypes:
                            type: array
                            items:
                              properties:
                                code:
                                  type: string
                                  enum: [LOCAL, DELIVERY, BOOKING, QPCON,SHIPPING]
                                  description: LOCAL -> 방문식사<br/>DELIVERY -> 배달식사<br/>BOOKING -> 예약식사<br/>QPCON -> 모바일상품권<br/>SHIPPING -> 상품배송
                                name:
                                  type: string
                                  description: 코드에 관한 TEXT(방문식사,배달식사,예약식사,모바일상품권,상품배송)
                              required:
                                - code
                                - name
                      date:
                        type: object
                        properties:
                          pay:
                            type: integer
                            description: 결제시간
                          cancel:
                            type: integer
                            description: 취소시간
                          available:
                            type: integer
                            description: 유효시간
                          process:
                            type: integer
                            description: 처리시간
                          plus:
                            type: integer
                            description: 지급시간
                          minus:
                            type: integer
                            description: 차감시간
                          expired:
                            type: integer
                            description: 소멸시간
                          charge:
                            type: integer
                            description: 충전시간
                          mealTime:
                            type: integer
                            description: 식사시간
                          cancelable:
                            type: integer
                            description: 취소가능시간
                      cause:
                        type: object
                        properties:
                          process:
                            type: string
                            description: 처리사유
                          plus:
                            type: string
                            description: 지급사유
                          minus:
                            type: string
                            description: 차감사유
                          expired:
                            type: string
                            description: 소멸사유
                      worker:
                        type: object
                        properties:
                          pay:
                            type: string
                            description: 결제자
                          plus:
                            type: string
                            description: 지급자
                          minus:
                            type: string
                            description: 차감자
                      user:
                        type: object
                        properties:
                          userId:
                            type: string
                            description: 본인 고유 아이디
                          name:
                            type: string
                            description: 본인 이름
                          totalAmount:
                            type: integer
                            description: 본인 총 금액
                      policys:
                        type: array
                        items:
                          properties:
                            policyIdx:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 이름
                            amount:
                              type: integer
                              description: 정책 사용 금액
                      menus:
                        type: array
                        items:
                          properties:
                            menuId:
                              type: string
                              description: 메뉴 고유 아이디
                            name:
                              type: string
                              description: 메뉴 이름
                            price:
                              type: integer
                              description: 메뉴 가격
                            quantity:
                              type: integer
                              description: 메뉴 수량
                            totalPrice:
                              type: integer
                              description: 메뉴 금액
                            images:
                              type: object
                              properties:
                                main:
                                  type: string
                                  description: 메인 이미지 경로
                                thumb:
                                  type: string
                                  description: 썸네일 이미지 경로
                      amount:
                        type: object
                        properties:
                          totalPay:
                            type: integer
                            description: 총 금액
                          plus:
                            type: integer
                            description: 지급식대 금액
                          minus:
                            type: integer
                            description: 차감식대 금액
                          expired:
                            type: integer
                            description: 소멸식대 금액
                          balance:
                            type: integer
                            description: 잔여식대 금액
                          chargePoint:
                            type: integer
                            description: 충전 포인트
                          chargeAmount:
                            type: integer
                            description: 충전 금액
                      addons:
                        type: object
                        properties:
                          warning:
                            type: string
                            description: 유의사항
                          chargeType:
                            type: string
                            description: 충전방식
                          type:
                            type: string
                            description: 상품 배송의 경우 USED/CANCEL 두개 type만 사용<br/>
                              내역 타입<br/>
                              USED -> 혼자결제<br/>
                              MULTI_USED -> 함께결제<br/>
                              ADMIN_USED -> 관리자 혼자결제<br/>
                              ADMIN_MULTI_USED -> 관리자 함께결제<br/>
                              QPCON_USED -> 모바일상품권 결제<br/>
                              QPCON_EXPIRED -> 모바일상품권 소멸<br/>
                              CANCEL -> 결제 취소<br/>
                              POINT_PLUS -> 식대 포인트 지급<br/>
                              POINT_MINUS -> 식대 포인트 차감<br/>
                              POINT_EXPIRED -> 식대 포인트 소멸<br/>
                              MYPOINT_CHARGE -> 마이포인트 충전<br/>
                          paymentType:
                            type: string
                            description: 결제 내역 타입<br/>PAY -> 결제완료<br/>CANCEL -> 결제 취소
                          isCancelable:
                            type: boolean
                            description: 취소 가능 여부<br/>true -> 취소 가능<br/>false -> 취소 불가
                      shipping:
                        type: object
                        properties:
                          recipient:
                            type: string
                            description: 수령인
                          tel:
                            type: string
                            description: 전화번호
                          userSelectedType:
                            type: string
                            description: 검색 결과에서 사용자가 선택한 주소의 타입 R(도로명), J(지번)
                          roadAddress:
                            type: string
                            description: 도로명 주소
                          jibunAddress:
                            type: string
                            description: 지번 주소
                          addressDetail:
                            type: string
                            description: 상세주소
                          shippingMemo:
                            type: string
                            description: 배송 메모
                        required:
                          - recipient
                          - tel
                          - userSelectedType
                          - roadAddress
                          - jibunAddress
                          - jibunAddress
                          - addressDetail
                      logistics:
                        type: object
                        properties:
                          logisticsNumber:
                            type: string
                            description: 송장번호
                          trakingHistoryLink:
                            type: string
                            description: 택배사 배송정보 조회 URL
                          logisticsName:
                            type: string
                            description: 택배사 명
        500:
          $ref: '#/components/responses/ServerError'
    delete:
      tags:
        - Shipping
      summary: 배송 상품 주문 취소 v1
      description: 배송 상품 주문 취소 v1
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: couponId
          description: 결제 고유 아이디
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  content:
                    properties:
                      pay:
                        type: object
                        properties:
                          couponId:
                            type: string
                            description: 고유 ID
                          roomId:
                            type: integer
                            description: 결제번호
                      store:
                        type: object
                        properties:
                          storeId:
                            type: string
                            description: 제휴점 고유 아이디
                          name:
                            type: string
                            description: 제휴점 이름
                          supplyTypes:
                            type: array
                            items:
                              properties:
                                code:
                                  type: string
                                  enum: [LOCAL, DELIVERY, BOOKING, QPCON, SHIPPING]
                                  description: LOCAL -> 방문식사<br/>DELIVERY -> 배달식사<br/>BOOKING -> 예약식사<br/>QPCON -> 모바일상품권<br/>SHIPPING -> 배송 상품
                                name:
                                  type: string
                                  description: 코드에 관한 TEXT(방문식사,배달식사,예약식사,모바일상품권,배송상품)
                              required:
                                - code
                                - name
                      date:
                        type: object
                        properties:
                          pay:
                            type: integer
                            description: 결제시간
                          cancel:
                            type: integer
                            description: 취소시간
                          available:
                            type: integer
                            description: 유효시간
                          process:
                            type: integer
                            description: 처리시간
                          plus:
                            type: integer
                            description: 지급시간
                          minus:
                            type: integer
                            description: 차감시간
                          expired:
                            type: integer
                            description: 소멸시간
                          charge:
                            type: integer
                            description: 충전시간
                          mealTime:
                            type: integer
                            description: 식사시간
                      cause:
                        type: object
                        properties:
                          process:
                            type: string
                            description: 처리사유
                          plus:
                            type: string
                            description: 지급사유
                          minus:
                            type: string
                            description: 차감사유
                          expired:
                            type: string
                            description: 소멸사유
                      worker:
                        type: object
                        properties:
                          pay:
                            type: string
                            description: 결제자
                          plus:
                            type: string
                            description: 지급자
                          minus:
                            type: string
                            description: 차감자
                      user:
                        type: object
                        properties:
                          userId:
                            type: string
                            description: 본인 고유 아이디
                          name:
                            type: string
                            description: 본인 이름
                          totalAmount:
                            type: integer
                            description: 본인 총 금액
                      policys:
                        type: array
                        items:
                          properties:
                            policyIdx:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 이름
                            amount:
                              type: integer
                              description: 정책 사용 금액
                      guests:
                        type: array
                        description: 사용 안함(무조건 혼자결제 예약식사와 화면을 같이 쓰기 때문에 구조체 유지)
                        items:
                          properties:
                            userId:
                              type: string
                              description: 함께 결제자 고유 아이디
                            name:
                              type: string
                              description: 함께 결제자 이름
                            division:
                              type: string
                              description: 함께 결제자 부서
                            amount:
                              type: integer
                              description: 함께 결제자 사용 금액
                      menus:
                        type: array
                        items:
                          properties:
                            menuId:
                              type: string
                              description: 메뉴 고유 아이디
                            name:
                              type: string
                              description: 메뉴 이름
                            price:
                              type: integer
                              description: 메뉴 가격
                            quantity:
                              type: integer
                              description: 메뉴 수량
                            totalPrice:
                              type: integer
                              description: 메뉴 금액
                            images:
                              type: object
                              properties:
                                main:
                                  type: string
                                  description: 메인 이미지 경로
                                thumb:
                                  type: string
                                  description: 썸네일 이미지 경로
                      amount:
                        type: object
                        properties:
                          totalPay:
                            type: integer
                            description: 총 금액
                          plus:
                            type: integer
                            description: 지급식대 금액
                          minus:
                            type: integer
                            description: 차감식대 금액
                          expired:
                            type: integer
                            description: 소멸식대 금액
                          balance:
                            type: integer
                            description: 잔여식대 금액
                          chargePoint:
                            type: integer
                            description: 충전 포인트
                          chargeAmount:
                            type: integer
                            description: 충전 금액
                      addons:
                        type: object
                        properties:
                          warning:
                            type: string
                            description: 유의사항
                          chargeType:
                            type: string
                            description: 충전방식
                          type:
                            type: string
                            description: 상품 배송의 경우 USED/CANCEL 두개 type만 사용<br/>
                                        내역 타입<br/>
                                        USED -> 혼자결제<br/>
                                        MULTI_USED -> 함께결제<br/>
                                        ADMIN_USED -> 관리자 혼자결제<br/>
                                        ADMIN_MULTI_USED -> 관리자 함께결제<br/>
                                        QPCON_USED -> 모바일상품권 결제<br/>
                                        QPCON_EXPIRED -> 모바일상품권 소멸<br/>
                                        CANCEL -> 결제 취소<br/>
                                        POINT_PLUS -> 식대 포인트 지급<br/>
                                        POINT_MINUS -> 식대 포인트 차감<br/>
                                        POINT_EXPIRED -> 식대 포인트 소멸<br/>
                                        MYPOINT_CHARGE -> 마이포인트 충전<br/>
                          isCancelable:
                            type: boolean
                            description: 취소 가능 여부<br/>true -> 취소 가능<br/>false -> 취소 불가
                          pin:
                            type: string
                            description: 모바일 상품권 고유 번호
                      shipping:
                        type: object
                        properties:
                          recipient:
                            type: string
                            description: 수령인
                          tel:
                            type: string
                            description: 전화번호
                          userSelectedType:
                            type: string
                            description: 검색 결과에서 사용자가 선택한 주소의 타입 R(도로명), J(지번)
                          roadAddress:
                            type: string
                            description: 도로명 주소
                          jibunAddress:
                            type: string
                            description: 지번 주소
                          addressDetail:
                            type: string
                            description: 상세주소
                          shippingMemo:
                            type: string
                            description: 배송 메모
                        required:
                          - recipient
                          - tel
                          - userSelectedType
                          - roadAddress
                          - jibunAddress
                          - jibunAddress
                          - addressDetail
        500:
          $ref: '#/components/responses/ServerError'
  # tag: CaptainPay
  /captainpay/v2/{userId}/fill/etc:
    get:
      tags:
        - CaptainPay
      summary: 대장포인트 충전(신용카드/가상계좌) (황상현)
      description: 대장포인트 충전(신용카드/가상계좌)
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: userId
          description: 사용자 고유 아이디
          required: true
          schema:
            type: string
        - in: parameter
          name: price
          description: 충전금액 min 10000 max 500000
          required: true
          schema:
            type: integer
        - in: parameter
          name: payMethod
          description: 결제 구분 <br />
            credit/virtual (신용카드/가상계좌)
          required: true
          schema:
            type: string
        - in: parameter
          name: deviceType
          description: 호출 타입 <br />
            ANDROID/IOS enum type 반드시 대문자 필수!!!
          required: true
          schema:
            type: string
            enum: [ANDROID, IOS]
      responses:
        200:
          description: OK <br />
            요청 성공시 아래의 response data를 ios/android에 정의된 브릿지를 통해 데이터를 전달한다.<br />
            1. android<br/>
            1.1 충전 성공 SikdaeBridge.captainPayEtc<br />
            1.2 충전 실패/오류 SikdaeBridge.captainPayEtcError<br />
            1.3 충전 취소 SikdaeBridge.forceFinish<br />
            2. ios<br />
            2.1 충전 성공 captainPayEtc<br />
            2.2 충전 실패/오류 captainPayEtcError<br />
            2.3 충전 취소 forceFinish
          content:
            application/json:
              schema:
                type: object
                properties:
                  resultCode:
                    type: string
                    enum: [SUCCESS,FAIL]
                    description: 성공/실패 응답
                  resultMessage:
                    type: string
                    description: 성공/실패 응답 메시지
                  price:
                    type: integer
                    description: 충전 요청 금액
                  totalPoint:
                    type: integer
                    description: 대장 포인트 총 잔액
                  fillDate:
                    type: integer
                    description: 충전 승인 일시
                  payType:
                    type: string
                    enum: [CAPTAINPAY_CREDIT, CAPTAINPAY_ACCOUNT]
                    description: 결제 수단 <br />
                      CAPTAINPAY_CREDIT(간편결제(신용카드),CAPTAINPAY_ACCOUNT(간편결제(가상계좌)), credit(신용카드), virtual(가상계좌)
                  payTypeName:
                    type: string
                    description: 결제 수단 간편결제(신용카드),간편결제(가상계좌),신용카드,가상계좌
                  pointLogId:
                    type: string
                    description: 대장포인트 충전 로그 상세 Id<br />식권 사용내역 조회 필수 파라미터
                  pointBookType:
                    type: string
                    description: MYPOINT_CHARGE 고정 <br />식권 사용내역 조회 필수 파라미터
                  bankName:
                    type: string
                    description: 가상계좌 입금 은행명
                  accountNo:
                    type: string
                    description: 가상게좌 계좌번호
                  expireDate:
                    type: string
                    description: 입금 만료일
                  comment:
                    type: string
                    description: 기타 메시지<br /> 가상계좌 의 경우 추가 메시지 <br /> * 신용카드에서는 현재는 사용안함 *
                  commentUseHtml:
                    type: boolean
                    description: 가상 계좌 충전 대기 기타 메시지 html tag 사용여부 <br /> * 신용카드에서는 현재는 사용안함 *
                  terms:
                    type: array
                    items:
                      properties:
                        title:
                          type: string
                          description: 제목
                        content:
                          type: string
                          description: 내용
                        type:
                          type: string
                          description: 약관 타입 CAPTAIN_PAY_FILL 단일 타입<br/>
                            화면에서 표시할 약관이 추가되면 타입 추가
                        revisioned:
                          type: string
                          description: 갱신일자 [yyyy-mm-dd]
                      required:
                        - type
                        - content
                required:
                  - resultCode
                  - resultMessage
                  - price
                  - totalPoint
                  - terms
        500:
          $ref: '#/components/responses/ServerError'
  /captainpay/v1/{userId}/fill/etc:
    get:
      tags:
        - CaptainPay
      summary: 대장포인트 충전(신용카드/가상계좌) (황상현)
      description: 대장포인트 충전(신용카드/가상계좌)
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: userId
          description: 사용자 고유 아이디
          required: true
          schema:
            type: string
        - in: parameter
          name: price
          description: 충전금액 min 10000 max 500000
          required: true
          schema:
            type: integer
        - in: parameter
          name: payMethod
          description: 결제 구분 <br />
                       credit/virtual (신용카드/가상계좌)
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK <br />
                       요청 성공시 아래의 response data를 ios/android에 정의된 브릿지를 통해 데이터를 전달한다.<br />
                       1. android<br/>
                         1.1 충전 성공 SikdaeBridge.captainPayEtc<br />
                         1.2 충전 실패/오류 SikdaeBridge.captainPayEtcError<br />
                         1.3 충전 취소 SikdaeBridge.forceFinish<br />
                       2. ios<br />
                         2.1 충전 성공 captainPayEtc<br />
                         2.2 충전 실패/오류 captainPayEtcError<br />
                         2.3 충전 취소 forceFinish
          content:
            application/json:
              schema:
                type: object
                properties:
                  resultCode:
                    type: string
                    enum: [SUCCESS,FAIL]
                    description: 성공/실패 응답
                  resultMessage:
                    type: string
                    description: 성공/실패 응답 메시지
                  price:
                    type: integer
                    description: 충전 요청 금액
                  totalPoint:
                    type: integer
                    description: 대장 포인트 총 잔액
                  fillDate:
                    type: integer
                    description: 충전 승인 일시
                  payType:
                    type: string
                    enum: [CAPTAINPAY_CREDIT, CAPTAINPAY_ACCOUNT]
                    description: 결제 수단 <br />
                      CAPTAINPAY_CREDIT(간편결제(신용카드),CAPTAINPAY_ACCOUNT(간편결제(가상계좌)), credit(신용카드), virtual(가상계좌)
                  payTypeName:
                    type: string
                    description: 결제 수단 간편결제(신용카드),간편결제(가상계좌),신용카드,가상계좌
                  pointLogId:
                    type: string
                    description: 대장포인트 충전 로그 상세 Id<br />식권 사용내역 조회 필수 파라미터
                  pointBookType:
                    type: string
                    description: MYPOINT_CHARGE 고정 <br />식권 사용내역 조회 필수 파라미터
                  bankName:
                    type: string
                    description: 가상계좌 입금 은행명
                  accountNo:
                    type: string
                    description: 가상게좌 계좌번호
                  expireDate:
                    type: string
                    description: 입금 만료일
                  comment:
                    type: string
                    description: 기타 메시지<br /> 가상계좌 의 경우 추가 메시지 <br /> * 신용카드에서는 현재는 사용안함 *
                  terms:
                    type: array
                    items:
                      properties:
                        title:
                          type: string
                          description: 제목
                        content:
                          type: string
                          description: 내용
                        type:
                          type: string
                          description: 약관 타입 CAPTAIN_PAY_FILL 단일 타입<br/>
                            화면에서 표시할 약관이 추가되면 타입 추가
                        revisioned:
                          type: string
                          description: 갱신일자 [yyyy-mm-dd]
                      required:
                        - type
                        - content
                required:
                  - resultCode
                  - resultMessage
                  - price
                  - totalPoint
                  - terms
        500:
          $ref: '#/components/responses/ServerError'

  /captainpay/v1/{userId}/cards:
    get:
      tags:
        - CaptainPay
      summary: 간편결제  카드 목록 (박소현)
      description: 사용자 등록 카드 목록 조회
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: userId
          description: 사용자 UID
          required: true
          schema:
            type: string
        - in: query
          name: page
          description: 현재 페이지 <br>(값이 없는 경우 1)
          required: false
          schema:
            type: integer
        - in: query
          name: pageRow
          description: 페이지당 항목 수 <br>(값이 없는 경우 1000)
          required: false
          schema:
            type: integer 
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  pageInfo:
                    type: object
                    properties:
                      page:
                        type: integer
                        description: 현재 페이지
                        default: 1
                      pageRow:
                        type: integer
                        description: 페이지당 항목 수
                        default: 1000
                      totalPage:
                        type: integer
                        description: 전체 페이지 수 (페이지에 대한 갯수)
                        default: 1
                      totalRow:
                        type: integer
                        description: 전체 데이터 수
                        default: 전체 row 갯수
                  cards:  
                    type: array
                    default : 사용자 카드 리스트가 존재하지 않는 경우 null
                    items:
                      properties:
                        userCardId:
                          type: string
                          description: 간편결제 등록 카드 고유번호
                        cardCode:
                          type: string
                          format: char
                          description: 카드사 구분 코드
                        cardName:
                          type: string
                          description: 카드사명
                        cardKind:
                          type: integer
                          description: 카드종류 (0->개인, 1->법인)
                        keyColor:
                          type: string
                          description: 카드색깔
                        logoImage: 
                          type: string
                          description: 카드로고이미지
                        fontColor:
                          type: string
                          description: 카드 폰트 컬러
                        cardNumber:
                          type: string
                          description: 사용자카드번호<br>자리 nnnn-****-****-nnnn<br>15자리 nnnn-****-****-nnn<br>14자리 nnnn-****-****-nn
                      required:
                        - userCardId
                        - cardCode
                        - cardName
                        - cardKind
                        - keyColor
                        - logoImage
                        - fontColor
                        - cardNumber
                  terms:
                    type: array
                    items:
                      properties:
                        title:
                          type: string
                          description: 제목
                        content:
                          type: string
                          description: 내용
                        type:
                          type: string
                          description: 약관 타입 CAPTAIN_PAY_FILL 단일 타입<br/>
                            화면에서 표시할 약관이 추가되면 타입 추가
                        revisioned:
                          type: string
                          description: 갱신일자 [yyyy-mm-dd]
                      required:
                        - title
                        - type
                        - content
                        - revisioned
        500:
          $ref: '#/components/responses/ServerError'
    post:
      tags:
        - CaptainPay
      summary: 간편결제 카드 등록 (황상현)
      description: 간편결제 카드 등록
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in : path
          name: userId
          description: 사용자 고유 아이디
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                cardNumber:
                  type: string
                  description: 카드번호 rsa암호화
                expireYearMonth:
                  type: string
                  description: 유효기간 YYMM rsa암호화
                cardPassword:
                  type: string
                  description: 카드 비밀번호 앞 2자리 rsa암호화
                regNoType:
                  type: string
                  description: Personal(개인), Corporation(법인) 구분
                regNo:
                  type: string
                  description: 인증번호(생년월일 or 사업자번호) rsa암호화
                email:
                  type: string
                  description: 이메일
                keyId:
                  type: string
                  description: RSA 공개키 GUID
              required:
                - cardNumber
                - expireYearMonth
                - cardPassword
                - regNo
                - email
                - keyId
      responses:
        201:
          description: Created
        500:
          $ref: '#/components/responses/ServerError'
  /captainpay/v1/{userId}/cards/{userCardId}:
    get:
      tags:
        - CaptainPay
      summary: 간편결제 카드 상세 조회 (황상현)
      description: 간편결제 카드 상세 조회
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in : path
          name: userId
          description: 사용자 고유 아이디
          required: true
          schema:
            type: string
        - in : path
          name: userCardId
          description: 간편결제 등록 카드 고유 식별자
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  userCardId:
                    type: string
                    description: 카드 고유 값
                  cardNumber:
                    type: string
                    description: 카드번호<br/>6자리 nnnn-****-****-nnnn<br/>15자리 nnnn-****-****-nnn<br/>14자리 nnnn-****-****-nn
                  cardName:
                    type: string
                    description: 회사명
                  logoImage:
                    type: string
                    description: 카드 이미지
                  fontColor:
                    type: string
                    description: 카드 폰트 컬러
                  keyColor:
                    type: string
                    description: 카드 디자인 hex code
                required:
                  - userCardId
                  - cardNumber
                  - cardName
                  - logoImage
                  - fontColor
                  - keyColor
        500:
          $ref: '#/components/responses/ServerError'
    delete:
      tags:
        - CaptainPay
      summary: 간편결제 카드 삭제 (황상현)
      description: 간편결제 카드 삭제
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: userId
          description: 사용자 고유 아이디
          required: true
          schema:
            type: string
        - in : path
          name: userCardId
          description: 간편결제 등록 카드 고유 식별자
          required: true
          schema:
            type: string
      responses:
        202:
          description: Accepted
        500:
          $ref: '#/components/responses/ServerError'

  /captainpay/v1/{userId}/password:
    post:
      tags:
        - CaptainPay
      summary: 간편결제 비밀번호 등록 및 유효성 검사(정철균)
      description: 사용자의 간편결제 비밀번호를 등록하고, 간편결제 비밀번호의 유효성을 검사한다.
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: userId
          description: 사용자 고유 아이디
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              properties:
                password:
                  type: string
                  description: 간편결제 비밀번호 rsa암호화
                  required: true
                keyId:
                  type: string
                  description: RSA 공개키 GUID
                isValid:
                  type: boolean
                  description: 간편결제 비밀번호 유효성 검사 분기 속성 (true 일 경우만 유효성 검사)
              required:
                - password
                - keyId
      responses:
        200:
          description: OK (간편결제 비밀번호 유효성 검사)
        201:
          description: Created (간편결제 비밀번호 등록)
        500:
          $ref: '#/components/responses/ServerError'
    put:
      tags:
        - CaptainPay
      summary: 간편결제 비밀번호 변경 (정철균)
      description: 사용자의 간편결제 비밀번호를 변경한다.
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: userId
          description: 사용자 고유 아이디
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              properties:
                password:
                  type: string
                  description: 간편결제 비밀번호 rsa암호화
                  required: true
                transactionId:
                  type: string
                  description: 본인인증 트랜잭션 ID 본인인증 결과 페이지에서 APP으로 전달
                  required: true
                keyId:
                  type: string
                  description: RSA 공개키 GUID
              required:
                - password
                - transactionId
                - keyId
      responses:
        200:
          description: OK
        500:
          $ref: '#/components/responses/ServerError'
    get:
      tags:
        - CaptainPay
      summary: 간편결제 비밀번호 등록 여부 조회 (정철균)
      description: 간편결제 비밀번호 등록 여부 조회
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: userId
          description: 사용자 고유 아이디
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                properties:
                  hasPassword:
                    type: boolean
                    description: 간편결제 비밀번호 등록 여부
                    required: true
                  failCount:
                    type: integer
                    description: 간편결제 비밀번호 확인 실패 횟 수(-1 등록 안되었을때)
                    required: true
                required:
                  - hasPassword
                  - failCount
        404:
          $ref: '#/components/responses/ServerError'

  /captainpay/v1/{userId}/fill:
    put:
      tags:
        - CaptainPay
      summary: 간편결제 충전 (황상현)
      description: 간편결제 충전
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in : path
          name: userId
          description: 사용자 고유 아이디
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              properties:
                userCardId:
                  type: string
                  description: 간편결제 등록 카드 고유 식별자
                transactionId:
                  type: string
                  description: 간편결제 주문번호
                easyPassword:
                  type: string
                  description: 간편결제 비밀번호 rsa 암호화
                price:
                  type: integer
                  description: 충전 금액
                keyId:
                  type: string
                  description: RSA 공개키 GUID
              required:
                - userCardId
                - transactionId
                - easyPassword
                - price
                - keyId
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  resultCode:
                    type: string
                    enum: [SUCCESS,FAIL]
                    description: 성공/실패 응답
                  resultMessage:
                    type: string
                    description: 성공/실패 응답 메시지
                  price:
                    type: integer
                    description: 충전 요청 금액
                  totalPoint:
                    type: integer
                    description: 대장 포인트 총 잔액
                  fillDate:
                    type: integer
                    description: 충전 승인 일시
                  payType:
                    type: string
                    enum: [CAPTAINPAY_CREDIT, CAPTAINPAY_ACCOUNT]
                    description: 결제 수단 CAPTAINPAY_CREDIT(간편결제(신용카드),CAPTAINPAY_ACCOUNT(간편결제(가상계좌))
                  payTypeName:
                    type: string
                    description: 결제 수단 간편결제(신용카드),간편결제(가상계좌)
                  pointLogId:
                    type: string
                    description: 대장포인트 충전 로그 상세 Id<br />식권 사용내역 조회 필수 파라미터
                  pointBookType:
                    type: string
                    description: MYPOINT_CHARGE 고정 <br />식권 사용내역 조회 필수 파라미터
                  terms:
                    type: array
                    items:
                      properties:
                        title:
                          type: string
                          description: 제목
                        content:
                          type: string
                          description: 내용
                        type:
                          type: string
                          description: 약관 타입 CAPTAIN_PAY_FILL 단일 타입<br/>
                                       화면에서 표시할 약관이 추가되면 타입 추가
                        revisioned:
                          type: string
                          description: 갱신일자 [yyyy-mm-dd]
                      required:
                        - type
                        - content
                required:
                  - resultCode
                  - resultMessage
                  - price
                  - totalPoint
                  - terms
        500:
          $ref: '#/components/responses/ServerError'

  /captainpay/v1/card/venders:
    get:
      tags:
        - CaptainPay
      summary: 등록 가능 카드사 목록 (박소현)
      description: 등록 가능 카드 버튼 클릭시 목록 조회
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  registableCards:  
                    type: array
                    items:
                      properties:
                        cardCode:
                          type: string
                          format: char
                          description: 카드사코드
                        cardName:
                          type: string
                          description: 카드사명
                        keyColor:
                          type: string
                          description: 카드색깔
                        logoImage: 
                          type: string
                          description: 카드로고이미지
                        fontColor:
                          type: string
                          description: 카드 폰트 컬러
                      required:
                        - cardCode
                        - cardName
                        - keyColor
                        - logoImage
                        - fontColor
                required:
                  - registableCards
        500:
          $ref: '#/components/responses/ServerError'

  /captainpay/v1/card/info:
    get:
      tags:
        - CaptainPay
      summary: 카드번호에 따른 카드사 조회 (박소현)
      description: 카드번호에 따른 카드사 조회
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: inputCardNumber
          description: 입력한 카드번호 6자리
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  cardCode:
                    type: string
                    format: char
                    description: 카드사코드 (카드사 코드가 존재하지 않을 경우 -> DE)
                  cardName:
                    type: string
                    description: 카드사명
                  keyColor:
                    type: string
                    description: 카드색깔
                  logoImage: 
                    type: string
                    description: 카드로고이미지
                  fontColor:
                    type: string
                    description: 카드 폰트 컬러
                required:
                  - cardCode
                  - cardName
                  - keyColor
                  - logoImage
                  - fontColor
        500:
          $ref: '#/components/responses/ServerError'

  # tag : Company
  /company/v1/member:
    get:
      tags:
        - Company
      summary: 사용자 전체 조회
      description: 단말 > 위임,그룹,신청 > 사용자조회
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: name
          description: 사용자 이름 검색
          required: false
          schema:
            type: string
        - in: query
          name: cateId
          description: 부서 고유 아이디
          required: false
          schema:
            type: string
        - in: query
          name: idx
          description: 마지막 컨텐츠 고유 번호
          required: false
          schema:
            type: string
        - in: query
          name: size
          description: 페이지당 항목 수
          required: false
          schema:
            type: integer
        - in: query
          name: self
          description: 본인 포함 여부 <br/>true -> 본인 포함 <br/>false -> 본인 미 포함 (default)
          required: false
          schema:
            type: boolean
        - in: query
          name: present
          description: 본선물하기 가능한 사용자 조회 <br/>true -> 선물하기 가능한 사용자 <br/>false -> 상관 없는 사용자 (default)
          required: false
          schema:
            type: boolean
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  properties:
                    idx:
                      type: integer
                      description: 컨텐츠 고유 번호
                    uid:
                      type: string
                      description: 사용자 고유 아이디
                    name:
                      type: string
                      description: 사용자 이름
                    comid:
                      type: string
                      description: 고객사 고유 아이디
                    comname:
                      type: string
                      description: 고객사 이름
                    position:
                      type: string
                      description: 직책
                    rankposition:
                      type: string
                      description: 직위
                    division:
                      type: string
                      description: 부서
                    email:
                      type: string
                      description: 이메일
                    sex:
                      type: string
                      description: 설별 M -> 남자, F -> 여자
                    birthday:
                      type: string
                      format: 'date-time'
                      description: 생일
                    signId:
                      type: string
                      description: 회원가입 아이디
                    isActive:
                      type: boolean
                      description: 사용자 사용 가능 여부
                  required:
                    - idx
                    - uid
                    - name
                    - comid
                    - comname
                    - division
                    - signId
                    - isActive
        500:
          $ref: '#/components/responses/ServerError'
  /company/v2/member:
    get:
      tags:
        - Company
      summary: 단말 > 사용자 조회 팝업 (전체,부서)
      parameters: 
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: name
          description: 사용자 이름 검색
          required: false
          schema:
            type: string
        - in: query
          name: orgCode
          description: 부서 고유 코드 <br/>부서 필요 없음 -> null or ""(공백)<br/>부서 최상위 -> 회사 고유 아이디 <br/>부서 하위 뎁스 -> 상위 orgCode
          required: false
          schema:
            type: string
        - in: query
          name: page
          description: 페이지 번호
          required: false
          schema:
            type: string
        - in: query
          name: pagerow
          description: 페이지당 항목 수
          required: false
          schema:
            type: integer        
        - in: query
          name: self
          description: 본인 포함 여부 <br/>true -> 본인 포함 <br/>false -> 본인 미 포함 (default)
          required: false
          schema:
            type: boolean
        - in: query
          name: present
          description: 본선물하기 가능한 사용자 조회 <br/>true -> 선물하기 가능한 사용자 <br/>false -> 상관 없는 사용자 (default)
          required: false
          schema:
            type: boolean
        - in: query
          name: view
          description: 요청 화면 <br/>CONFIRM -> 식권 신청 결재선
          required: false
          schema:
            type: string
        - in: query
          name: demandIdx
          description: 신청 정책 고유 아이디
          required: false
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  division:
                    type: array
                    items:
                      properties:
                        orgCode:
                          type: string
                          description: 부서 고유 아이디
                        name:
                          type: string
                          description: 부서 이름
                        userCount:
                          type: integer
                          description: 사용자 숫자
                      required:
                        - orgCode
                        - name
                        - userCount
                  user:
                    type: array
                    items:
                      properties:
                        uid:
                          type: string
                          description: 사용자 고유 아이디
                        name:
                          type: string
                          description: 사용자 이름
                        position:
                          type: string
                          description: 직책
                        orgCode:
                          type: string
                          description: 부서 고유 아이디
                        division:
                          type: string
                          description: 부서 이름
                        isActive:
                          type: boolean
                          description: 해당 사용자 사용 가능 여부 <br/>true -> 사용가능 <br/>false -> 사용 불가능
                      required:
                        - uid
                        - name
                        - orgCode
                        - division
                        - isActive
        500:
          $ref: '#/components/responses/ServerError'
      
  /company/v1/division:
    get:
      tags:
        - Company
      summary: 부서 리스트 조회
      description: 단말 > 위임,그룹,신청 > 부서 조회
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  properties:
                    cateId:
                      type: integer
                      description: 부서 고유 번호
                    comId:
                      type: string
                      description: 회사 고유 번호
                    type:
                      type: string
                      description: 유형 D -> 부서, R -> 직위
                    seq:
                      type: integer
                      description: 우선순위
                    name:
                      type: string
                      description: 이름
                  required:
                    - cateId
                    - comId
                    - type
                    - seq
                    - name
        500:
          $ref: '#/components/responses/ServerError'

# tag : Payment
  /payment/v1/captainpay/{userId}/order:
    post:
      tags:
        - Payment
      summary: 간편결제 주문번호 생성
      description: 간편결제 주문번호 생성
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in : path
          name: userId
          description: 사용자 고유 아이디
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  transactionId:
                    type: string
                    description: 주문번호
                required:
                  - transactionId
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v1/policy/assign:
    get:
      tags:
        - Payment
      summary: 정책별 금액 할당
      parameters: 
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: amount
          description: 결제 금액
          required: true
          schema:
            type: integer
        - in: query
          name: sid
          description: 제휴점 고유 아이디
          required: true
          schema:
            type: string
        - in: query
          name: isGroup
          description: 함께결제 유무<br> true - 함께결제<br/> false - 혼자결제(default)
          required: false
          schema:
            type: boolean
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  policy:
                    type: array
                    items:
                      properties:
                        id:
                          type: integer
                          description: 정책 고유 아이디
                        amount:
                          type: integer
                          description: 금액
                  mypoint:
                    type: integer
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v2/barcode:
    get:
      tags:
        - Payment
      summary: 진에어 전용 바코드
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  barCode:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 바코드 고유 아이디
                      color:
                        type: string
                        description: 바코드 색
                    required:
                      - id
                      - color
                  store:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 제휴점 고유 아이디
                      name:
                        type: string
                        description: 제휴점 이름
                    required:
                      - id
                      - name
                  date:
                    type: integer
                    format: date-time
                    description: 날짜
                required:
                  - barCode
                  - store
                  - date
        500:
          $ref: '#/components/responses/ServerError'
    post:
      tags:
        - Payment
      summary: 일회용 바코드 생성
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  type:
                    type: string
                    description: 결제타입<br>MENU->메뉴선택형<br>AMOUNT->금액입력형
                    enum: [MENU, AMOUNT]
                  totalPrice:
                    type: integer
                    format: int32
                    description: 총 금액
                  user:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 사용자 고유 아이디
                      name:
                        type: string
                        description: 사용자 이름
                      point:
                        type: array
                        items:
                          properties:
                            policy:
                              type: integer
                              format: int64
                              description: 정책 고유 아이디
                            amount:
                              type: integer
                              format: int32
                              description: 사용 금액
                          required:
                            - policy
                            - amount
                      mypoint:
                        type: object
                        properties:
                          amount:
                            type: integer
                            format: int32
                            description: 내 포인트 사용 금액
                        required:
                          - amount
                    required:
                      - id
                      - name
                      - point
                      - mypoint
                  store:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 제휴점 고유 아이디
                      menu:
                        type: array
                        items:
                          properties:
                            id:
                              type: string
                              description: 메뉴 고유 아이디
                            count:
                              type: integer
                              format: int32
                              description: 메뉴 선택 갯수
                          required:
                            - id
                            - count
                    required:
                      - id
                      - menu
                required:
                  - type
                  - totalPrice
                  - user
                  - store
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v3/barcode:
    get:
      tags:
        - Payment
      summary: 고정형 바코드 조회
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  barCode:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 바코드 고유 아이디
                      date:
                        type: integer
                        format: date-time
                        description: 바코드 생성 시간
                    required:
                      - id
                      - date
                required:
                  - barCode
        500:
          $ref: '#/components/responses/ServerError'
    post:
      tags:
        - Payment
      summary: 일회용 바코드 생성
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  type:
                    type: string
                    description: 결제타입<br>MENU->메뉴선택형<br>AMOUNT->금액입력형
                    enum: [MENU, AMOUNT]
                  totalPrice:
                    type: integer
                    format: int32
                    description: 총 금액
                  user:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 사용자 고유 아이디
                      name:
                        type: string
                        description: 사용자 이름
                      point:
                        type: array
                        items:
                          properties:
                            policy:
                              type: integer
                              format: int64
                              description: 정책 고유 아이디
                            amount:
                              type: integer
                              format: int32
                              description: 사용 금액
                          required:
                            - policy
                            - amount
                      mypoint:
                        type: object
                        properties:
                          amount:
                            type: integer
                            format: int32
                            description: 내 포인트 사용 금액
                        required:
                          - amount
                    required:
                      - id
                      - name
                      - point
                      - mypoint
                  store:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 제휴점 고유 아이디
                      menu:
                        type: array
                        items:
                          properties:
                            id:
                              type: string
                              description: 메뉴 고유 아이디
                            count:
                              type: integer
                              format: int32
                              description: 메뉴 선택 갯수
                          required:
                            - id
                            - count
                    required:
                      - id
                      - menu
                  coupon:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 식권 고유 아이디
                      date:
                        type: string
                        format: 'date-time'
                        description: 식권 발급 시간
                    required:
                      - id
                      - date
                required:
                  - type
                  - totalPrice
                  - user
                  - store
                  - coupon
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v4/barcode:
    get:
      tags:
        - Payment
      summary: 공통 바코드 조회
      description: 진에어, 고정형 통일
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  barCode:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 바코드 고유 아이디
                      date:
                        type: integer
                        format: date-time
                        description: 바코드 생성 시간
                    required:
                      - id
                      - date
                required:
                  - barCode
        500:
          $ref: '#/components/responses/ServerError'
    post: 
      tags:
        - Payment
      summary: 일회용 바코드 생성
      description: 금액직접입력 대응
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                storeId: 
                  type: string
                  description: 제휴점 아이디
                roomIdx:
                  type: integer
                  format: int32
                  description: 결제방 아이디
                couponId:
                  type: string
                  description: 쿠폰 아이디
              required:
                - storeId
                - roomIdx
                - couponId
      responses:
        201:
          description: Created
          content:
            application/json:
              schema:
                type: object
                properties:
                  barCode:
                    type: string
                    description: 바코드 고유 아이디
                  codeType:
                    type: string
                    description: 코드 유형
                    enum: [BARCODE, QRCODE]
                  term:
                    type: array
                    items:
                      properties:
                        type:
                          type: string
                          description: 약관타입
                          enum: [BARCODE_NOTICE, BARCODE_RESTRICTIONS, BARCODE_CANCELLATION, BARCODE_NOTICE_BEAT, BARCODE_CANCEL_BEAT]
                        title:
                          type: string
                          description: 제목
                        content:
                          type: string
                          description: 내용
                      required:
                        - type
                        - content
                  date:
                    type: integer
                    format: date-time
                    description: 바코드 생성 날짜
                required:
                  - barCode
                  - term
                  - date
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v1/captaincode:
    post:
      tags:
        - Payment
      summary: CaptainCode 생성 요청
      description: CaptainCode 생성 요청
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                storeId:
                  type: string
                  description: 제휴점 아이디
      responses:
        201:
          description: Created
          content:
            application/json:
              schema:
                type: object
                properties:
                  barCode:
                    type: string
                    description: 바코드 고유 아이디
                  codeType:
                    type: string
                    description: 코드 유형
                    enum: [BARCODE, QRCODE]
                  refreshCycle:
                    type: integer
                    description: 갱신 주기 (second)
                  term:
                    type: array
                    items:
                      properties:
                        type:
                          type: string
                          description: 약관타입 (추후 삭제 예정, enum 관리 안함)
                          enum: [BARCODE_NOTICE]
                        title:
                          type: string
                          description: 제목
                        content:
                          type: string
                          description: 내용
                      required:
                        - title
                        - content
                  date:
                    type: integer
                    format: date-time
                    description: 코드 생성 날짜
                required:
                  - barCode
                  - codeType
                  - refreshCycle
                  - term
                  - date
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v1/captaincode/{captainCode}:
    get:
      tags:
        - Payment
      summary: CaptainCode 상태 조회
      description: CaptainCode 상태 조회
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: captainCode
          description: CaptainCode
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  thirdPartyCouponInfo:
                    type: object
                    properties:
                      succeed:
                        type: boolean
                        description: 성공 여부 (GS, BEAT, CaptainCode)
                      resultTitle:
                        type: string
                        description: 응답메세지 타이틀 (BEAT, CaptainCode)
                      resultMsg:
                        type: string
                        description: 응답메세지 (GS, BEAT, CaptainCode)
                      resultCode:
                        type: string
                        description: 응답코드 (GS, BEAT, CaptainCode)
                      buttonName:
                        type: string
                        description: 버튼 이름 (BEAT, CaptainCode)
                      couponId:
                        type: string
                        description: 식권아이디 (GS, BEAT, CaptainCode)
                      thirdPartyCouponId:
                        type: string
                        description: 바코드 (GS, BEAT, CaptainCode)
                      storeId:
                        type: string
                        description: 제휴식당 GUID (GS, BEAT, CaptainCode)
                      storeName:
                        type: string
                        description: 제휴식당 이름 (GS, BEAT, CaptainCode)
                      approvalAmount:
                        type: integer
                        format: int64
                        description: 승인금액 (GS, BEAT, CaptainCode)
                      approvalDate:
                        type: integer
                        format: date-time
                        description: 승인일시 (GS)
                      requestAmount:
                        type: integer
                        format: int64
                        description: 요청금액 (GS)
                      paymentType:
                        type: string
                        enum: [ORDER, USE, CANCEL]
                        description: 결제 작업 유형(GS, BEAT, CaptainCode) <br/> ORDER -> 주문(CaptainCode)<br/> USE -> 사용(GS, BEAT, CaptainCode)<br/> CANCEL -> 사용 취소(GS, BEAT, CaptainCode)
                      type:
                        type: string
                        enum: [USED, MULTI_USED]
                        description: 혼자결제, 함께결제 여부 (GS, BEAT, CaptainCode))
                      status:
                        type: string
                        enum: [ISSUED, ISSUE_CANCELED, PREPARED, USED, USE_CANCELED, NET_CANCELED]
                        description: 상태 (GS, BEAT, CaptainCode)<br> ISSUED 발급(GS, BEAT, CaptainCode)<br>ISSUE_CANCELED 발급취소(GS, BEAT, CaptainCode)<br>PREPARED 사용 준비(CaptainCode)<br>USED 사용(GS, BEAT, CaptainCode)<br>USE_CANCELED 사용취소(GS, BEAT, CaptainCode)<br>NET_CANCELED 사용망취소(GS)
                      refreshCycle:
                        type: integer
                        description: 코드 갱신 주기 (CaptainCode)
                      remainCycle:
                        type: integer
                        description: 코드 갱신 잔여 시간 (CaptainCode)
                    required:
                      - resultMsg
                      - status
                      - paymentType
                      - type
                      - thirdPartyCouponId
                      - couponId
                  user:
                    type: object
                    properties:
                      uid:
                        type: string
                        description: 사용자 uid
                    required:
                      - uid
                  term:
                    type: array
                    items:
                      properties:
                        type:
                          type: string
                          description: 약관타입
                          enum: [BARCODE_CANCELLATION, BARCODE_CANCEL_BEAT]
                        title:
                          type: string
                          description: 제목
                        content:
                          type: string
                          description: 내용
                      required:
                        - type
                        - content
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v4/{couponId}/barcode:
    get:
      tags:
        - Payment
      summary: 바코드 상태 조회 
      description: 바코드 상태 조회
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: couponId
          description: 식권대장 쿠폰ID
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  gsCouponInfo:
                    type: object
                    properties:
                      resultCode: 
                        type: string
                        description: 응답코드
                      resultMsg: 
                        type: string
                        description: 응답메세지
                      status:
                        type: string
                        enum: [ISSUED, ISSUE_CANCELED, USED, USE_CANCELED, NET_CANCELED]
                        description: 상태 <br> ISSUED 발급<br>ISSUE_CANCELED 발급취소<br>USED 사용<br>USE_CANCELED 사용취소<br>NET_CANCELED 사용망취소
                      paymentType: 
                        type: string
                        enum: [USE, CANCEL]
                        description: 사용, 사용취소
                      type: 
                        type: string
                        enum: [USED, MULTI_USED]
                        description: 혼자결제, 함께결제 여부
                      storeId:
                        type: string
                        description: 제휴식당 GUID
                      storeName:
                        type: string
                        description: 제휴식당 이름
                      gsCouponId:
                        type: string
                        description: 바코드
                      couponId:
                        type: string
                        description: 식권아이디
                      requestAmount:
                        type: integer
                        format: int64
                        description: 요청금액
                      responseAmount:
                        type: integer
                        format: int64
                        description: 승인금액
                      approvalDate:
                        type: integer
                        format: date-time
                        description: 승인일시
                    required:
                      - resultMsg
                      - status
                      - paymentType
                      - type
                      - gsCouponId
                      - couponId
                      - requestAmount
                      - approvalAmount
                  user:
                    type: object
                    properties:
                      uid: 
                        type: string
                        description: 사용자 uid
                    required:
                      - uid
                  term:
                    type: array
                    items:
                      properties:
                        type:
                          type: string
                          description: 약관타입
                          enum: [BARCODE_CANCELLATION]
                        title:
                          type: string
                          description: 제목
                        content:
                          type: string
                          description: 내용
                      required:
                        - type
                        - content
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v5/{couponId}/barcode:
    get:
      tags:
        - Payment
      summary: 바코드 상태 조회
      description: 바코드 상태 조회
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: couponId
          description: 식권대장 쿠폰ID
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  thirdPartyCouponInfo:
                    type: object
                    properties:
                      succeed:
                        type: boolean
                        description: 성공 여부 (GS, BEAT)
                      resultTitle:
                        type: string
                        description: 응답메세지 타이틀 (BEAT)
                      resultMsg:
                        type: string
                        description: 응답메세지 (GS, BEAT)
                      resultCode:
                        type: string
                        description: 응답코드 (GS, BEAT)
                      buttonName:
                        type: string
                        description: 버튼 이름 (BEAT)
                      couponId:
                        type: string
                        description: 식권아이디 (GS, BEAT)
                      thirdPartyCouponId:
                        type: string
                        description: 바코드 (GS, BEAT)
                      storeId:
                        type: string
                        description: 제휴식당 GUID (GS, BEAT)
                      storeName:
                        type: string
                        description: 제휴식당 이름 (GS, BEAT)
                      approvalAmount:
                        type: integer
                        format: int64
                        description: 승인금액 (GS, BEAT)
                      approvalDate:
                        type: integer
                        format: date-time
                        description: 승인일시 (GS)
                      requestAmount:
                        type: integer
                        format: int64
                        description: 요청금액 (GS)
                      paymentType:
                        type: string
                        enum: [USE, CANCEL, ORDER]
                        description: 사용, 사용취소 (GS, BEAT)
                      type:
                        type: string
                        enum: [USED, MULTI_USED]
                        description: 혼자결제, 함께결제 여부 (GS, BEAT)
                      status:
                        type: string
                        enum: [ISSUED, ISSUE_CANCELED, USED, USE_CANCELED, NET_CANCELED]
                        description: 상태 (GS, BEAT)<br> ISSUED 발급<br>ISSUE_CANCELED 발급취소<br>USED 사용<br>USE_CANCELED 사용취소<br>NET_CANCELED 사용망취소
                    required:
                      - resultMsg
                      - status
                      - paymentType
                      - type
                      - thirdPartyCouponId
                      - couponId
                      - approvalAmount
                  user:
                    type: object
                    properties:
                      uid:
                        type: string
                        description: 사용자 uid
                    required:
                      - uid
                  term:
                    type: array
                    items:
                      properties:
                        type:
                          type: string
                          description: 약관타입
                          enum: [BARCODE_CANCELLATION, BARCODE_CANCEL_BEAT]
                        title:
                          type: string
                          description: 제목
                        content:
                          type: string
                          description: 내용
                      required:
                        - type
                        - content
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v1/group:
    get:
      tags:
        - Payment
      summary: 함께결제 결제방 유무
      description: 단말 > Polling
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  payroom:
                    type: array
                    items:
                      properties:
                        id:
                          type: string
                          description: 방 고유 아이디
                        status:
                          type: string
                          description: 방 상태 <br/>CREATED -> 방생성 <br/>READY -> 식권발급
                        date:
                          type: string
                          format: 'date-time'
                          description: 방 생성 시간
                        userType:
                          type: string
                          description: 본인 유형 <br/>LEADER -> 결제자 <br/>ENTRUST -> 위임 <br/>GUEST -> 손님
                        leader:
                          type: object
                          properties:
                            id:
                              type: string
                              description: 방장 고유 아이디
                            name:
                              type: string
                              description: 방장 이름
                          required:
                            - id
                            - name
                        guest:
                          type: array
                          items:
                            properties:
                              id:
                                type: string
                                description: 방장 고유 아이디
                              name:
                                type: string
                                description: 방장 이름
                            required:
                              - id
                              - name
                      required:
                        - id
                        - status
                        - date
                        - userType
                        - leader
                        - guest
        500:
          $ref: '#/components/responses/ServerError'
    post:
      tags:
        - Payment
      summary: 함께 결제 방 생성
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 함께 결제하기 > 사용자선택 > 금액확정
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: uid
          description: 사용자 고유 아이디
          schema:
            type: string
        - in: query
          name: sid
          description: 제휴점 고유 아이디
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  description: 결제타입<br>MENU->메뉴선택형<br>AMOUNT->금액입력형
                  enum: [MENU, AMOUNT]
                totalPrice:
                  type: integer
                  format: int32
                  description: 총 금액
                user:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 사용자 고유 아이디
                    name:
                      type: string
                      description: 사용자 이름
                    point:
                      type: array
                      items:
                        properties:
                          policy:
                            type: integer
                            format: int64
                            description: 정책 고유 아이디
                          amount:
                            type: integer
                            format: int32
                            description: 사용 금액
                        required:
                          - policy
                          - amount
                    mypoint:
                      type: object
                      properties:
                        amount:
                          type: integer
                          format: int32
                          description: 내 포인트 사용 금액
                      required:
                        - amount
                  required:
                    - id
                    - name
                    - point
                    - mypoint
                guest:
                  type: array
                  items:
                    properties:
                      id:
                        type: string
                        description: 사용자 고유 아이디
                      name:
                        type: string
                        description: 사용자 이름
                      amount:
                        type: integer
                        format: int32
                        description: 사용 금액
                store:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 제휴점 고유 아이디
                    menu:
                      type: array
                      items:
                        properties:
                          id:
                            type: string
                            description: 메뉴 고유 아이디
                          count:
                            type: integer
                            format: int32
                            description: 메뉴 선택 갯수
                        required:
                          - id
                          - count
                  required:
                    - id
                    - menu
              required:
                - type
                - totalPrice
                - user
                - store
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  room:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 결제방 고유 아이디
                    required:
                      - id
                  date:
                    type: string
                    format: 'date-time'
                    description: 결제일
                required:
                  - room
                  - date
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v2/group:
    post:
      tags:
        - Payment
      summary: 함께 결제 방 생성
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 함께 결제하기 > 사용자선택 > 금액확정
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: uid
          description: 사용자 고유 아이디
          schema:
            type: string
        - in: query
          name: sid
          description: 제휴점 고유 아이디
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  description: 결제타입<br>MENU->메뉴선택형<br>AMOUNT->금액입력형
                  enum: [MENU, AMOUNT]
                totalPrice:
                  type: integer
                  format: int32
                  description: 총 금액
                user:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 사용자 고유 아이디
                    name:
                      type: string
                      description: 사용자 이름
                    point:
                      type: array
                      items:
                        properties:
                          policy:
                            type: integer
                            format: int64
                            description: 정책 고유 아이디
                          amount:
                            type: integer
                            format: int32
                            description: 사용 금액
                        required:
                          - policy
                          - amount
                    mypoint:
                      type: object
                      properties:
                        amount:
                          type: integer
                          format: int32
                          description: 내 포인트 사용 금액
                      required:
                        - amount
                  required:
                    - id
                    - name
                    - point
                    - mypoint
                guest:
                  type: array
                  items:
                    properties:
                      id:
                        type: string
                        description: 사용자 고유 아이디
                      name:
                        type: string
                        description: 사용자 이름
                      amount:
                        type: integer
                        format: int32
                        description: 사용 금액
                store:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 제휴점 고유 아이디
                    menu:
                      type: array
                      items:
                        properties:
                          id:
                            type: string
                            description: 메뉴 고유 아이디
                          count:
                            type: integer
                            format: int32
                            description: 메뉴 선택 갯수
                        required:
                          - id
                          - count
                  required:
                    - id
                    - menu
              required:
                - type
                - totalPrice
                - user
                - store
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  room:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 결제방 고유 아이디
                    required:
                      - id
                  date:
                    type: string
                    format: 'date-time'
                    description: 결제일
                required:
                  - room
                  - date
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v3/group:
    post:
      tags:
        - Payment
      summary: 함께 결제 방 생성
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 함께 결제하기 > 사용자선택 > 금액확정
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  description: 결제타입<br>MENU->메뉴선택형<br>AMOUNT->금액입력형
                  enum: [MENU, AMOUNT]
                totalPrice:
                  type: integer
                  format: int32
                  description: 총 금액
                user:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 사용자 고유 아이디
                    name:
                      type: string
                      description: 사용자 이름
                    point:
                      type: array
                      items:
                        properties:
                          policy:
                            type: integer
                            format: int64
                            description: 정책 고유 아이디
                          amount:
                            type: integer
                            format: int32
                            description: 사용 금액
                        required:
                          - policy
                          - amount
                    mypoint:
                      type: object
                      properties:
                        amount:
                          type: integer
                          format: int32
                          description: 내 포인트 사용 금액
                      required:
                        - amount
                  required:
                    - id
                    - name
                    - point
                    - mypoint
                guest:
                  type: array
                  items:
                    properties:
                      id:
                        type: string
                        description: 사용자 고유 아이디
                      name:
                        type: string
                        description: 사용자 이름
                      amount:
                        type: integer
                        format: int32
                        description: 사용 금액
                store:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 제휴점 고유 아이디
                    menu:
                      type: array
                      items:
                        properties:
                          id:
                            type: string
                            description: 메뉴 고유 아이디
                          count:
                            type: integer
                            format: int32
                            description: 메뉴 선택 갯수
                        required:
                          - id
                          - count
                  required:
                    - id
                    - menu
              required:
                - type
                - totalPrice
                - user
                - store
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  room:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 결제방 고유 아이디
                    required:
                      - id
                  date:
                    type: string
                    format: 'date-time'
                    description: 결제일
                required:
                  - room
                  - date
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v1/group/{roomIdx}:
    get:
      tags:
        - Payment
      summary: 결제방 정보 조회
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: roomIdx
          description: 결제방 고유 아이디
          required: true
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  payroom:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 결제방 고유 아이디
                      status:
                        type: string
                        description: 결제방 상태 <br/>CREATED -> 방생성 <br/>READY -> 식권발급 <br/>USED -> 식권사용완료
                      paymentType:
                        type: string
                        enum: [MENU, AMOUNT]
                        description: 결제유형 <br/>MENU -> 메뉴 <br/>AMOUNT -> 금액입력
                      totalPrice:
                        type: integer
                        description: 총 정가
                      totalAmount:
                        type: integer
                        description: 총 결제금액
                      date:
                        type: string
                        format: 'date-time'
                        description: 결제방 생성 시간
                      used:
                        type: string
                        format: 'date-time'
                        description: 결제 시간
                      store:
                        type: object
                        properties:
                          id:
                            type: string
                            description: 제휴점 고유 아이디
                          name:
                            type: string
                            description: 제휴점 이름
                          supplyType:
                            type: integer
                            description: 제휴점 종류 <br/>0 -> Local(방문 가맹점) <br/>1 -> QPcon(큐피콘 가맹점) <br/>2 -> LocalDelivery(방문,배달 가맹점) <br/>3 -> Delivery(배달 가맹점) <br/>4 -> Cafeteria(구내식당) - 일회용 바코드 <br/>5 -> Cafeteria(구내식당) - 고정형 바코드
                          menu:
                            type: array
                            items:
                              properties:
                                id:
                                  type: string
                                  description: 메뉴 고유 아이디
                                name:
                                  type: string
                                  description: 메뉴 이름
                                price:
                                  type: integer
                                  description: 정가
                                salesPrice:
                                  type: integer
                                  description: 판매가
                                count:
                                  type: integer
                                  description: 메뉴 개수
                              required:
                                - id
                                - name
                                - price
                                - salePrice
                                - count
                        required:
                          - id
                          - name
                          - supplyType
                          - menu
                      leader:
                        type: object
                        properties:
                          id:
                            type: string
                            description: 사용자 고유 아이디
                          name:
                            type: string
                            description: 사용자 이름
                          division:
                            type: string
                            description: 부서
                          type:
                            type: string
                            description: 유형
                          amount:
                            type: integer
                            description: 결제 금액
                          isAgree:
                            type: boolean
                            description: 결제 승인 여부
                          point:
                            type: array
                            items:
                              properties:
                                policy:
                                  type: integer
                                  description: 정책 고유 아이디
                                amount:
                                  type: integer
                                  description: 금액
                              required:
                                - policy
                                - amount
                          mypoint:
                            type: object
                            properties:
                              amount:
                                type: integer
                                description: 내 포인트 금액
                            required:
                              - amount
                        required:
                          - id
                          - name
                          - division
                          - type
                          - amount
                          - isAgree
                          - point
                          - mypoint
                      guest:
                        type: array
                        items:
                          properties:
                            id:
                              type: string
                              description: 사용자 고유 아이디
                            name:
                              type: string
                              description: 사용자 이름
                            division:
                              type: string
                              description: 부서
                            type:
                              type: string
                              description: 유형
                            amount:
                              type: integer
                              description: 결제 금액
                            isAgree:
                              type: boolean
                              description: 결제 승인 여부
                            point:
                              type: array
                              items:
                                properties:
                                  policy:
                                    type: integer
                                    description: 정책 고유 아이디
                                  amount:
                                    type: integer
                                    description: 금액
                                required:
                                  - policy
                                  - amount
                            mypoint:
                              type: object
                              properties:
                                amount:
                                  type: integer
                                  description: 내 포인트 금액
                              required:
                                - amount
                    required:
                      - id
                      - status
                      - paymentType
                      - totalPrice
                      - totalAmount
                      - date
                      - store
                      - leader
        500:
          $ref: '#/components/responses/ServerError'
    delete:
      tags:
        - Payment
      summary: 함께 결제방 삭제
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 함께 결제하기 > 사용자선택 > 금액확정 > 취소하기
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: roomIdx
          description: 결제방 고유 아이디
          required: true
          schema:
            type: integer
      responses:
        204:
          description: NO CONTENT
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v2/group/{roomIdx}:
    get:
      tags:
        - Payment
      summary: 결제방 정보 조회
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: roomIdx
          description: 결제방 고유 아이디
          required: true
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  payroom:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 결제방 고유 아이디
                      status:
                        type: string
                        description: 결제방 상태 <br/>CREATED -> 방생성 <br/>READY -> 식권발급 <br/>USED -> 식권사용완료
                      paymentType:
                        type: string
                        enum: [MENU, AMOUNT]
                        description: 결제유형 <br/>MENU -> 메뉴 <br/>AMOUNT -> 금액입력
                      totalPrice:
                        type: integer
                        description: 총 정가
                      totalAmount:
                        type: integer
                        description: 총 결제금액
                      date:
                        type: string
                        format: 'date-time'
                        description: 결제방 생성 시간
                      used:
                        type: string
                        format: 'date-time'
                        description: 결제 시간
                      store:
                        type: object
                        properties:
                          id:
                            type: string
                            description: 제휴점 고유 아이디
                          name:
                            type: string
                            description: 제휴점 이름
                          supplyType:
                            type: integer
                            description: 제휴점 종류 <br/>0 -> Local(방문 가맹점) <br/>1 -> QPcon(큐피콘 가맹점) <br/>2 -> LocalDelivery(방문,배달 가맹점) <br/>3 -> Delivery(배달 가맹점) <br/>4 -> Cafeteria(구내식당) - 일회용 바코드 <br/>5 -> Cafeteria(구내식당) - 고정형 바코드
                          menu:
                            type: array
                            items:
                              properties:
                                id:
                                  type: string
                                  description: 메뉴 고유 아이디
                                name:
                                  type: string
                                  description: 메뉴 이름
                                price:
                                  type: integer
                                  description: 정가
                                salesPrice:
                                  type: integer
                                  description: 판매가
                                count:
                                  type: integer
                                  description: 메뉴 개수
                              required:
                                - id
                                - name
                                - price
                                - salePrice
                                - count
                        required:
                          - id
                          - name
                          - supplyType
                          - menu
                      leader:
                        type: object
                        properties:
                          id:
                            type: string
                            description: 사용자 고유 아이디
                          name:
                            type: string
                            description: 사용자 이름
                          division:
                            type: string
                            description: 부서
                          type:
                            type: string
                            description: 유형
                          amount:
                            type: integer
                            description: 결제 금액
                          isAgree:
                            type: boolean
                            description: 결제 승인 여부
                          point:
                            type: array
                            items:
                              properties:
                                policy:
                                  type: integer
                                  description: 정책 고유 아이디
                                amount:
                                  type: integer
                                  description: 금액
                              required:
                                - policy
                                - amount
                          mypoint:
                            type: object
                            properties:
                              amount:
                                type: integer
                                description: 내 포인트 금액
                            required:
                              - amount
                        required:
                          - id
                          - name
                          - division
                          - type
                          - amount
                          - isAgree
                          - point
                          - mypoint
                      guest:
                        type: array
                        items:
                          properties:
                            id:
                              type: string
                              description: 사용자 고유 아이디
                            name:
                              type: string
                              description: 사용자 이름
                            division:
                              type: string
                              description: 부서
                            type:
                              type: string
                              description: 유형
                            amount:
                              type: integer
                              description: 결제 금액
                            isAgree:
                              type: boolean
                              description: 결제 승인 여부
                            point:
                              type: array
                              items:
                                properties:
                                  policy:
                                    type: integer
                                    description: 정책 고유 아이디
                                  amount:
                                    type: integer
                                    description: 금액
                                required:
                                  - policy
                                  - amount
                            mypoint:
                              type: object
                              properties:
                                amount:
                                  type: integer
                                  description: 내 포인트 금액
                              required:
                                - amount
                    required:
                      - id
                      - status
                      - paymentType
                      - totalPrice
                      - totalAmount
                      - date
                      - store
                      - leader
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v1/group/{roomIdx}/coupon:
    post:
      tags:
        - Payment
      summary: 함께 결제 식권 발급
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 함께 결제하기 > 사용자선택 > 금액확정
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: roomIdx
          description: 결제방 고유 아이디
          required: true
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  coupon:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 식권 고유 아이디
                      color:
                        type: string
                        description: 식권 색깔
                    required:
                      - id
                      - color
                  store:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 제휴점 고유 아이디
                      name:
                        type: string
                        description: 제휴점 이름
                    required:
                      - id
                      - name
                  date:
                    type: string
                    format: 'date-time'
                    description: 결제일
                required:
                  - coupon
                  - store
                  - date
        500:
          $ref: '#/components/responses/ServerError'
    delete:
      deprecated: true
      tags:
        - Payment
      summary: 식권 발급 취소
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 함께 결제하기 > 사용자선택 > 금액확정 > 다음 > 취소
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: roomIdx
          description: 결제방 고유 아이디
          required: true
          schema:
            type: integer
      responses:
        204:
          description: NO CONTENT
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v2/group/{roomIdx}/coupon:
    post:
      tags:
        - Payment
      summary: 함께 결제 식권 발급
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 함께 결제하기 > 사용자선택 > 금액확정
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: roomIdx
          description: 결제방 고유 아이디
          required: true
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  coupon:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 식권 고유 아이디
                      color:
                        type: string
                        description: 식권 색깔
                    required:
                      - id
                      - color
                  store:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 제휴점 고유 아이디
                      name:
                        type: string
                        description: 제휴점 이름
                    required:
                      - id
                      - name
                  paymentType:
                    properties:
                      useType:
                        type: string
                        enum: [COUPON, BARCODE]
                        description: 식권 or 바코드 사용여부
                      code:
                        type: string
                        enum: [CAFETERIA, GS, BEAT]
                        description: CAFETERIA -> 구내식당<br/>GS -> GS편의점<br/>BEAT -> Beat커피
                      name:
                        type: string
                        description: 코드에 관한 TEXT(일반구내식당, GS편의점, Beat커피)
                      shopCode:
                        type: string
                        description: 연동 제휴점 고유코드
                    required:
                      - useType
                  date:
                    type: string
                    format: 'date-time'
                    description: 결제일
                required:
                  - coupon
                  - store
                  - date
                  - paymentType
        500:
          $ref: '#/components/responses/ServerError'
    delete:
      tags:
        - Payment
      summary: 식권 발급 취소 v2
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 함께 결제하기 > 사용자선택 > 금액확정 > 다음 > 취소
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: roomIdx
          description: 결제방 고유 아이디
          required: true
          schema:
            type: integer
      responses:
        204:
          description: NO CONTENT
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v1/group/{roomIdx}/coupon/{couponId}:
    put:
      tags:
        - Payment
      summary: 함께 결제 식권 사용
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 함께 결제하기 > 사용자선택 > 금액확정 > 다음 > 결제완료
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: roomIdx
          description: 결제방 고유 아이디
          required: true
          schema:
            type: integer
        - in: path
          name: couponId
          description: 식권 고유 아이디
          required: true
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  coupon:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 식권 고유 아이디
                    required:
                      - id
                  gifticon:
                    type: object
                    properties:
                      productId:
                        type: string
                        description: 큐피콘 상품 아이디
                      pin:
                        type: string
                        description: 큐피콘 PIN 코드
                      storeName:
                        type: string
                        description: 큐피콘 제휴점 이름
                      menuName:
                        type: string
                        description: 큐피콘 메뉴 이름
                      price:
                        type: integer
                        description: 큐피콘 가격
                      intro:
                        type: string
                        description: 큐피콘 메모
                      issueDate:
                        type: string
                        format: 'date-time'
                        description: 큐피콘 발급일
                      expireDate:
                        type: string
                        format: 'date-time'
                        description: 큐피콘 만료일
                      images:
                        type: object
                        properties:
                          main:
                            type: string
                            description: 메뉴 이미지 경로
                          thumb:
                            type: string
                            description: 썸네일 이미지 경로
                        required:
                          - productId
                          - pin
                          - storeName
                          - menuName
                          - price
                          - intro
                          - issueDate
                          - expireDate
                          - images
                    required:
                      - id
                      - name
                  date:
                    type: string
                    format: 'date-time'
                    description: 결제일
                required:
                  - coupon
                  - date
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v2/group/{roomIdx}/coupon/{couponId}:
    put:
      tags:
        - Payment
      summary: 함께 결제 식권 사용
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 함께 결제하기 > 사용자선택 > 금액확정 > 다음 > 결제완료
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: roomIdx
          description: 결제방 고유 아이디
          required: true
          schema:
            type: integer
        - in: path
          name: couponId
          description: 식권 고유 아이디
          required: true
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  coupon:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 식권 고유 아이디
                    required:
                      - id
                  gifticon:
                    type: object
                    properties:
                      productId:
                        type: string
                        description: 큐피콘 상품 아이디
                      pin:
                        type: string
                        description: 큐피콘 PIN 코드
                      storeName:
                        type: string
                        description: 큐피콘 제휴점 이름
                      menuName:
                        type: string
                        description: 큐피콘 메뉴 이름
                      price:
                        type: integer
                        description: 큐피콘 가격
                      intro:
                        type: string
                        description: 큐피콘 메모
                      issueDate:
                        type: string
                        format: 'date-time'
                        description: 큐피콘 발급일
                      expireDate:
                        type: string
                        format: 'date-time'
                        description: 큐피콘 만료일
                      images:
                        type: object
                        properties:
                          main:
                            type: string
                            description: 메뉴 이미지 경로
                          thumb:
                            type: string
                            description: 썸네일 이미지 경로
                        required:
                          - productId
                          - pin
                          - storeName
                          - menuName
                          - price
                          - intro
                          - issueDate
                          - expireDate
                          - images
                    required:
                      - id
                      - name
                  date:
                    type: string
                    format: 'date-time'
                    description: 결제일
                required:
                  - coupon
                  - date
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v1/group/{roomIdx}/guest:
    post:
      tags:
        - Payment
      summary: 함께 결제 게스트 승인
      description: 단말 > Polling > 승인하러가기 > 승인
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: roomIdx
          description: 결제방 고유 아이디
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 사용자 고유 아이디
                    name:
                      type: string
                      description: 사용자 이름
                  required:
                    - id
                    - name
                point:
                  type: array
                  items:
                    properties:
                      policy:
                        type: integer
                        format: int64
                        description: 정책 고유 아이디
                      amount:
                        type: integer
                        format: int32
                        description: 사용 금액
                    required:
                      - policy
                      - amount
                mypoint:
                  type: object
                  properties:
                    amount:
                      type: integer
                      format: int32
                      description: 내 포인트 사용 금액
                  required:
                    - amount
              required:
                - user
                - point
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 사용자 고유 아이디
                      name:
                        type: string
                        description: 사용자 이름
                    required:
                      - id
                      - name
                  date:
                    type: string
                    format: 'date-time'
                    description: 결제일
                required:
                  - user
                  - date
        500:
          $ref: '#/components/responses/ServerError'
    delete:
      tags:
        - Payment
      summary: 함께결제 게스트 취소
      description: 단말 > Polling > 승인하러가기 > 취소
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: roomIdx
          description: 결제방 고유 아이디
          required: true
          schema:
            type: integer
      responses:
        204:
          description: NO CONTENT
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v2/group/{roomIdx}/guest:
    post:
      tags:
        - Payment
      summary: 함께 결제 게스트 승인
      description: 단말 > Polling > 승인하러가기 > 승인
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: roomIdx
          description: 결제방 고유 아이디
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                user:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 사용자 고유 아이디
                    name:
                      type: string
                      description: 사용자 이름
                  required:
                    - id
                    - name
                point:
                  type: array
                  items:
                    properties:
                      policy:
                        type: integer
                        format: int64
                        description: 정책 고유 아이디
                      amount:
                        type: integer
                        format: int32
                        description: 사용 금액
                    required:
                      - policy
                      - amount
                mypoint:
                  type: object
                  properties:
                    amount:
                      type: integer
                      format: int32
                      description: 내 포인트 사용 금액
                  required:
                    - amount
              required:
                - user
                - point
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 사용자 고유 아이디
                      name:
                        type: string
                        description: 사용자 이름
                    required:
                      - id
                      - name
                  date:
                    type: string
                    format: 'date-time'
                    description: 결제일
                required:
                  - user
                  - date
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v1/group/policy:
    post:
      tags:
        - Payment
      summary: 함께결제자 잔액 조회
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 함께 결제하기 > 사용자선택
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                leader:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 사용자 고유 아이디
                    name:
                      type: string
                      description: 사용자 이름
                  required:
                    - id
                    - name
                guest:
                  type: array
                  items:
                    properties:
                      id:
                        type: string
                        description: 사용자 고유 아이디
                      name:
                        type: string
                        description: 사용자 이름
                    required:
                      - id
                      - name
                sid:
                  type: string
                  description: 제휴점 고유 아이디
              required:
                - leader
                - guest
                - sid
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  geust:
                    type: array
                    items:
                      properties:
                        id:
                          type: string
                          description: 사용자 고유 아이디
                        name:
                          type: string
                          description: 사용자 이름
                        amount:
                          type: integer
                          description: 잔액
                        division:
                          type: string
                          description: 부서
                        isInfinite:
                          type: boolean
                          description: 무한지급 사용 유무
                      required:
                        - id
                        - name
                        - amount
                        - division
                        - isInfinite
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v2/group/policy:
    post:
      tags:
        - Payment
      summary: 함께결제자 잔액 조회
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 함께 결제하기 > 사용자선택
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                leader:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 사용자 고유 아이디
                    name:
                      type: string
                      description: 사용자 이름
                  required:
                    - id
                    - name
                guest:
                  type: array
                  items:
                    properties:
                      id:
                        type: string
                        description: 사용자 고유 아이디
                      name:
                        type: string
                        description: 사용자 이름
                    required:
                      - id
                      - name
                sid:
                  type: string
                  description: 제휴점 고유 아이디
              required:
                - leader
                - guest
                - sid
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  geust:
                    type: array
                    items:
                      properties:
                        id:
                          type: string
                          description: 사용자 고유 아이디
                        name:
                          type: string
                          description: 사용자 이름
                        amount:
                          type: integer
                          description: 잔액
                        division:
                          type: string
                          description: 부서
                        isInfinite:
                          type: boolean
                          description: 무한지급 사용 유무
                      required:
                        - id
                        - name
                        - amount
                        - division
                        - isInfinite
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v1/coupon:
    delete:
      tags:
        - Payment
      summary: 식권 결제 취소
      description: 결제 취소
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: id
          description: 결제 고유 번호
          required: true
          schema:
            type: string
        - in: query
          name: storePw
          description: 제휴점 취소 비밀번호
          required: true
          schema:
            type: string
      responses:
        204:
          description: NO CONTENT
          content:
            application/json:
              schema:
                type: object
                properties:
                  content:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 결제 고유 ID
                      datetime:
                        type: integer
                        description: 결제 취소 시간
                      cause:
                        type: string
                        description: 사유
                    required:
                      - id
                      - datetime
                      - cause
        500:
          $ref: '#/components/responses/ServerError'
    post:
      tags:
        - Payment
      summary: 혼자결제 식권 발급 - Benefia용
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: uid
          description: 사용자 고유 아이디
          schema:
            type: string
        - in: query
          name: sid
          description: 제휴점 고유 아이디
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  description: 결제타입<br>MENU->메뉴선택형<br>AMOUNT->금액입력형
                  enum: [MENU, AMOUNT]
                totalPrice:
                  type: integer
                  format: int32
                  description: 총 금액
                user:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 사용자 고유 아이디
                    name:
                      type: string
                      description: 사용자 이름
                point:
                  type: array
                  items:
                    properties:
                      policy:
                        type: integer
                        format: int64
                        description: 정책 고유 아이디
                      amount:
                        type: integer
                        format: int32
                        description: 사용 금액
                    required:
                      - policy
                      - amount
                mypoint:
                  type: object
                  properties:
                    amount:
                      type: integer
                      format: int32
                      description: 내 포인트 사용 금액
                  required:
                    - amount
                store:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 제휴점 고유 아이디
                    menu:
                      type: array
                      items:
                        properties:
                          id:
                            type: string
                            description: 메뉴 고유 아이디
                          count:
                            type: integer
                            format: int32
                            description: 메뉴 선택 갯수
                        required:
                          - id
                          - count
                  required:
                    - id
                    - menu
              required:
                - type
                - totalPrice
                - user
                - point
                - store
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  coupon:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 식권 고유 아이디
                      color:
                        type: string
                        description: 식권 고유 색깔
                    required:
                      - id
                      - color
                  store:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 제휴점 고유 아이디
                      name:
                        type: string
                        description: 제휴점 이름
                  date:
                    type: string
                    format: 'date-time'
                    description: 결제일
                required:
                  - coupon
                  - store
                  - date
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v2/coupon:
    put:
      tags:
        - Payment
      summary: 혼자결제 식권 사용
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 혼자 결제하기 > 금액 확정 > 결제 완료
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: couponId
          description: 식권 고유 아이디
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  coupon:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 식권 고유 아이디
                    required:
                      - id
                  gifticon:
                    type: object
                    properties:
                      productId:
                        type: string
                        description: 큐피콘 상품 아이디
                      pin:
                        type: string
                        description: 큐피콘 PIN 코드
                      storeName:
                        type: string
                        description: 큐피콘 제휴점 이름
                      menuName:
                        type: string
                        description: 큐피콘 메뉴 이름
                      price:
                        type: integer
                        description: 큐피콘 가격
                      intro:
                        type: string
                        description: 큐피콘 메모
                      issueDate:
                        type: string
                        format: 'date-time'
                        description: 큐피콘 발급일
                      expireDate:
                        type: string
                        format: 'date-time'
                        description: 큐피콘 만료일
                      images:
                        type: object
                        properties:
                          main:
                            type: string
                            description: 메뉴 이미지 경로
                          thumb:
                            type: string
                            description: 썸네일 이미지 경로
                        required:
                          - productId
                          - pin
                          - storeName
                          - menuName
                          - price
                          - intro
                          - issueDate
                          - expireDate
                          - images
                    required:
                      - id
                      - name
                  date:
                    type: string
                    format: 'date-time'
                    description: 결제일
                required:
                  - coupon
                  - date
        500:
          $ref: '#/components/responses/ServerError'
    post:
      tags:
        - Payment
      summary: 혼자결제 식권 발급
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 혼자 결제하기 > 금액 확정
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: uid
          description: 사용자 고유 아이디
          schema:
            type: string
        - in: query
          name: sid
          description: 제휴점 고유 아이디
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  description: 결제타입<br>MENU->메뉴선택형<br>AMOUNT->금액입력형
                  enum: [MENU, AMOUNT]
                totalPrice:
                  type: integer
                  format: int32
                  description: 총 금액
                user:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 사용자 고유 아이디
                    name:
                      type: string
                      description: 사용자 이름
                    point:
                      type: array
                      items:
                        properties:
                          policy:
                            type: integer
                            format: int64
                            description: 정책 고유 아이디
                          amount:
                            type: integer
                            format: int32
                            description: 사용 금액
                        required:
                          - policy
                          - amount
                    mypoint:
                      type: object
                      properties:
                        amount:
                          type: integer
                          format: int32
                          description: 내 포인트 사용 금액
                      required:
                        - amount
                  required:
                    - id
                    - name
                    - point
                    - mypoint
                store:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 제휴점 고유 아이디
                    menu:
                      type: array
                      items:
                        properties:
                          id:
                            type: string
                            description: 메뉴 고유 아이디
                          count:
                            type: integer
                            format: int32
                            description: 메뉴 선택 갯수
                        required:
                          - id
                          - count
                  required:
                    - id
                    - menu
              required:
                - type
                - totalPrice
                - user
                - store
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  coupon:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 식권 고유 아이디
                      color:
                        type: string
                        description: 식권 고유 색깔
                    required:
                      - id
                      - color
                  store:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 제휴점 고유 아이디
                      name:
                        type: string
                        description: 제휴점 이름
                  date:
                    type: string
                    format: 'date-time'
                    description: 결제일
                required:
                  - coupon
                  - store
                  - date
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v3/coupon:
    post:
      tags:
        - Payment
      summary: 혼자결제 식권 발급
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 혼자 결제하기 > 금액 확정
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: uid
          description: 사용자 고유 아이디
          schema:
            type: string
        - in: query
          name: sid
          description: 제휴점 고유 아이디
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                type:
                  type: string
                  description: 결제타입<br>MENU->메뉴선택형<br>AMOUNT->금액입력형
                  enum: [MENU, AMOUNT]
                totalPrice:
                  type: integer
                  format: int32
                  description: 총 금액
                user:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 사용자 고유 아이디
                    name:
                      type: string
                      description: 사용자 이름
                    point:
                      type: array
                      items:
                        properties:
                          policy:
                            type: integer
                            format: int64
                            description: 정책 고유 아이디
                          amount:
                            type: integer
                            format: int32
                            description: 사용 금액
                        required:
                          - policy
                          - amount
                    mypoint:
                      type: object
                      properties:
                        amount:
                          type: integer
                          format: int32
                          description: 내 포인트 사용 금액
                      required:
                        - amount
                  required:
                    - id
                    - name
                    - point
                    - mypoint
                store:
                  type: object
                  properties:
                    id:
                      type: string
                      description: 제휴점 고유 아이디
                    menu:
                      type: array
                      items:
                        properties:
                          id:
                            type: string
                            description: 메뉴 고유 아이디
                          count:
                            type: integer
                            format: int32
                            description: 메뉴 선택 갯수
                        required:
                          - id
                          - count
                  required:
                    - id
                    - menu
              required:
                - type
                - totalPrice
                - user
                - store
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  roomIdx:
                    type: integer
                    format: int32
                    description: 결제방번호
                  coupon:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 식권 고유 아이디
                      color:
                        type: string
                        description: 식권 고유 색깔
                    required:
                      - id
                      - color
                  store:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 제휴점 고유 아이디
                      name:
                        type: string
                        description: 제휴점 이름
                  paymentType:
                    properties:
                      useType:
                        type: string
                        enum: [COUPON, BARCODE]
                        description: 식권 or 바코드 사용여부
                      code:
                        type: string
                        enum: [CAFETERIA, GS, BEAT]
                        description: CAFETERIA -> 구내식당<br/>GS -> GS편의점<br/>BEAT -> Beat커피
                      name:
                        type: string
                        description: 코드에 관한 TEXT(일반구내식당, GS편의점, Beat커피)
                      shopCode:
                        type: string
                        description: 연동 제휴점 고유코드
                    required: 
                      - useType
                  date:
                    type: string
                    format: 'date-time'
                    description: 결제일
                required:
                  - roomIdx
                  - coupon
                  - store
                  - paymentType
                  - date
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v1/coupon/{couponId}:
    put:
      tags:
        - Payment
      summary: 혼자결제 식권 사용
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 혼자 결제하기 > 금액 확정 > 결제 완료
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: couponId
          description: 식권 고유 아이디
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  coupon:
                    type: object
                    properties:
                      id:
                        type: integer
                        description: 식권 고유 아이디
                    required:
                      - id
                  gifticon:
                    type: object
                    properties:
                      productId:
                        type: string
                        description: 큐피콘 상품 아이디
                      pin:
                        type: string
                        description: 큐피콘 PIN 코드
                      storeName:
                        type: string
                        description: 큐피콘 제휴점 이름
                      menuName:
                        type: string
                        description: 큐피콘 메뉴 이름
                      price:
                        type: integer
                        description: 큐피콘 가격
                      intro:
                        type: string
                        description: 큐피콘 메모
                      issueDate:
                        type: string
                        format: 'date-time'
                        description: 큐피콘 발급일
                      expireDate:
                        type: string
                        format: 'date-time'
                        description: 큐피콘 만료일
                      images:
                        type: object
                        properties:
                          main:
                            type: string
                            description: 메뉴 이미지 경로
                          thumb:
                            type: string
                            description: 썸네일 이미지 경로
                        required:
                          - productId
                          - pin
                          - storeName
                          - menuName
                          - price
                          - intro
                          - issueDate
                          - expireDate
                          - images
                    required:
                      - id
                      - name
                  date:
                    type: string
                    format: 'date-time'
                    description: 결제일
                required:
                  - coupon
                  - date
        500:
          $ref: '#/components/responses/ServerError'
  /payment/v3/coupon/{couponId}:
    delete:
      tags:
        - Payment
      summary: 혼자결제 식권 발급 취소
      description: 단말 > 메인 > 식사하기 > 메뉴선택 > 혼자결제 > 금액확정 > 뒤로가기
      parameters:
        -   $ref: '#/components/parameters/header-auth'
        -   in: path
            name: couponId
            description: 식권 고유 아이디
            required: true
            schema:
              type: string
      responses:
        204:
          description: NO CONTENT
        500:
          $ref: '#/components/responses/ServerError'
# tag : App
  /app/v1/me:
    get:
      tags:
        - App
      summary: 초기 접속 정보
      description: 단말 > 접속
      parameters: 
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  content:
                    properties:
                      comid:
                        type: string
                        description: 고객사 고유 아이디
                      comname:
                        type: string
                        description: 고객사 명
                      compointright:
                        type: integer
                        description: Android 에서 사용중이나 의미없는 정보로 0으로 하드코딩
                      uid:
                        type: string
                        description: 사용자 고유 아이디
                      name:
                        type: string
                        description: 사용자 이름
                      signId:
                        type: string
                        description: 회원가입 아이디
                      level:
                        type: integer
                        description: 사용자 레벨<br/>-5 -> 임시 탈퇴<br/>-1 -> 일시정지<br/>0 -> 완전 탈퇴<br/>1 -> 일반 사용자<br/>30 -> 회사 관리자<br/>99 -> 전체 관리자
                      rankposition:
                        type: string
                        description: 직책
                      position:
                        type: string
                        description: 직책
                      migversion:
                        type: string
                        description: 회원가입 버전<br/>null -> 기존회원<br/>v1 -> 일반 마이그레이션<br/>v2 -> 강제 마이그레이션<br/>v3 -> 아이디 회원가입 완료<br/>v4 -> 관리자 수동 가입
                      division:
                        type: string
                        description: 부서명
                      isAgree:
                        type: boolean
                        description: 약관 동의 여부<br/>true -> 동의<br/>false -> 미동의
                      cellphone:
                        type: string
                        description: 사용자 전화 번호
                      email:
                        type: string
                        description: 이메일
                      passwordreset:
                        type: string
                        description: 비밀번호 재설정 여부<br/>Y -> 변경완료<br/>N -> 변경 미완료
                      mypoint:
                        type: integer
                        description: 마이포인트 금액
                      payText:
                        type: string
                        description: 결재 완료 문자
                      employeeNo:
                        type: string
                        description: 사번 (인터컴 에서 쓰기위함)
                      intercomIdHash:
                        type: string
                        description: 인터컴 해쉬키
                      companyRegDate:
                        type: integer
                        description: 고객사 생성일
                      isBooking:
                        type: boolean
                        description: 예약기능 가능 여부
                      isCoupon:
                        type: boolean
                        description: 방문식사 가능 여부
                      isQrCodeScan:
                        type: boolean
                        description: QR 코드 스캔 사용 여부
                      signupWaitUrl:
                        type: string
                        description: 회원가입 승인 대기 페이지 (없으면 이동 안함)
                      shippingInfo :
                        type: object
                        properties:
                          previousShipping:
                            type: object
                            description: 이전 배송정보
                            properties:
                              recipient:
                                type: string
                                description: 수령인
                              tel:
                                type: string
                                description: 전화번호
                              userSelectedType:
                                type: string
                                description: 검색 결과에서 사용자가 선택한 주소의 타입 R(도로명), J(지번)
                              roadAddress:
                                type: string
                                description: 도로명 주소
                              jibunAddress:
                                type: string
                                description: 지번 주소
                              addressDetail:
                                type: string
                                description: 상세주소
                              shippingMemo:
                                type: string
                                description: 배송 메모
                          isShipping:
                            type: boolean
                            description: 상품 배송 사용 여부
                          shippingStores:
                            type: array
                            items:
                              properties:
                                storeId:
                                  type: string
                                  description: 제휴식당 고유 ID
                                storeName:
                                  type: string
                                  description: 제휴식당 명
                                imgUrl:
                                  type: string
                                  description: 상품 배송 메인 로고 이미지
                              required:
                                - storeId
                                - storeName
                                - imgUrl
                        required:
                          - isShipping
                      comlimit:
                        properties:
                          mealcMultipay:
                            type: boolean
                            description: 함께 결제 사용 유무<br/>true -> 사용<br/>false -> 미 사용
                          isTimeDisplayed:
                            type: boolean
                            description: 시간 노출 여부<br/>true -> 노출<br/>false -> 미 노출
                          isPointPresent:
                            type: boolean
                            description: 포인트 선물하기 가능 여부<br/>true -> 가능<br/>false -> 불 가능
                          mealcMultientrust:
                            type: boolean
                            description: 위임 사용 유무<br/>true -> 사용<br/>false -> 미 사용
                          isDemand:
                            type: boolean
                            description: 식권 신청 정책 사용 유무<br/>true -> 사용<br/>false -> 미 사용
                          companynotice:
                            type: boolean
                            description: 사내 공지 사용 유무<br/>true -> 사용<br/>false -> 미 사용
                          mealmultimenu:
                            type: boolean
                            description: 다중메뉴 결제 허용 여부<br/>true -> 허용<br/>false -> 미 허용
                          isDemandManage:
                            type: boolean
                            description: 식권 승인 가능 여부<br/>true -> 가능<br/>false -> 불 가능
                          mealcUsemypoint:
                            type: boolean
                            description: 마이포인트 사용 유무<br/>true -> 사용<br/>false -> 미 사용
                          isTicketFormat:
                            type: boolean
                            description: 원/장 여부<br/>true -> 장<br/>false -> 원
                          isCashReceipt:
                            type: boolean
                            description: 현금영수증 등록 메뉴 노출여부<br/>true -> 노출<br/>false -> 비 노출
                          cafeteriaType:
                            type: string
                            description: 구내식당 유형<br/>MENU_V1 -> 외부식당<br/>ONLY_V1 -> 구내식당 전용<br/>MULTI_V1 -> 구내식당 + 외부식당
                          priorityDemandPolicy:
                            type: string
                            description: 식권 승인 필터 우선순위<br/>AUTO -> 자동승인<br/>MANUAL -> 수동승인<br/>CONFIRM -> 결재선
                          version:
                            type: string
                            description: 고객사 버전<br/>null -> 기존 고객사<br/>v1 -> 그룹사 기능 고객사
                          comid:
                            type: string
                            description: 고객사 고유 아이디
                          mealcService:
                            type: boolean
                            description: 식권 사용 유무(안드로이드만 사용중인데 의미 없어 true 로 하드코딩)
                          isMealGroup:
                            type: boolean
                            description: 차등지급 사용 유무(안드로이드만 사용중인데 의미 없어 true 로 하드코딩)
                          isMarket:
                            type: boolean
                            default: false
                            description: 대장 마켓 사용 유무(백오피스에서 설정 기준으로 노출, 기본값(비노출)->false)
                        required:
                          - mealcMultipay
                          - isTimeDisplayed
                          - isPointPresent
                          - mealcMultientrust
                          - isDemand
                          - companynotice
                          - mealmultimenu
                          - isDemandManage
                          - mealcUsemypoint
                          - isTicketFormat
                          - isCashReceipt
                          - cafeteriaType
                          - priorityDemandPolicy
                          - comid
                          - mealcService
                          - isMealGroup
                          - isMarket
                      companyLocation:
                        type: object
                        description: 위치정보
                        properties:
                          gpslat:
                            type: number
                            format: double
                            description: GPS
                          gpslon:
                            type: number
                            format: double
                            description: GPS
                        required:
                          - gpslat
                          - gpslosw
                    required:
                      - comid
                      - comname
                      - compointright
                      - uid
                      - name
                      - level
                      - division
                      - isAgree
                      - passwordreset
                      - payText
                      - intercomIdHash
                      - companyRegDate
                      - isBooking
                      - isCoupon
                      - isQrCodeScan
                  status:
                    type: integer
                    description: 1 -> 성공 <br/>0 -> 실패

  /app/api/mypoint:
    get:
      tags:
        - App
      summary: 내 포인트 확인
      description: 단말 > LNB > My 포인트 > 내 포인트 확인
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  content:
                    type: object
                    properties:
                      mypoint:
                        type: integer
                        description: 금액
                      expire:
                        type: object
                        properties:
                          amount:
                            type: integer
                            description: 만료 금액
                          text:
                            type: string
                            description: 만료 표시 문구 (30일 내 소멸 예정 5,000)
                        required:
                          - amount
                          - text
                      captainPay:
                        type: object
                        description: 대장페이 정보
                        properties:
                          cardCount:
                            type: integer
                            description: 등록 카드 수
                          password:
                            type: boolean
                            description: 대장페이 비밀번호 등록 여부 true -> 등록, false -> 미등록
                          isCertification:
                            type: boolean
                            description: 본인인증 여부 true -> 인증, false -> 미인증
                  status:
                    type: integer
                    description: 성공 여부 1 -> 성공, -1 -> 실패
        500:
          $ref: '#/components/responses/ServerError'

# tag : Store
  /store/v1:
    get:
      tags:
        - Store
      summary: 제휴점 리스트
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: keyword
          description: 검색어
          schema:
            type: string
        - in: query
          name: supplytype
          description: 제휴점 종류</br>0 -> Local(방문)</br>1 -> QPcon(큐피콘)</br>2 -> LocalDelivery(방문,배달)</br>3 -> Delivery(배달)</br>4 -> Cafeteria(일회용바코드)</br>5 -> Cafeteria(고정형바코드)
          required: true
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  properties:
                    category:
                      type: string
                      description: 카테고리
                    store:
                      type: array
                      items:
                        properties:
                          id:
                            type: string
                            description: 제휴점 고유 아이디
                          name:
                            type: string
                            description: 제휴점 이름
                          intro:
                            type: string
                            description: 제휴점 설명
                          supplyType:
                            type: integer
                            format: int32
                            description: 제휴점 종류</br>0 -> Local(방문)</br>1 -> QPcon(큐피콘)</br>2 -> LocalDelivery(방문,배달)</br>3 -> Delivery(배달)</br>4 -> Cafeteria(일회용바코드)</br>5 -> Cafeteria(고정형바코드)
                          payment:
                            type: string
                            description: 결제 수단</br>MENU -> 메뉴선택</br>MULTI -> 메뉴선택 + 금액직접입력
                          paymentRate:
                            type: integer
                            description: 결제 레이트
                        required:
                          - id
                          - name
                          - intro
                          - supplyType
                          - payment
                          - paymentRate
                  required:
                    - category
                    - store
        500:
          $ref: '#/components/responses/ServerError'
  /store/v2:
    get:
      tags:
        - Store
      summary: 제휴점 리스트
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: keyword
          description: 검색어
          schema:
            type: string
        - in: query
          name: supplytype
          description: 제휴점 종류</br>0 -> Local(방문)</br>1 -> QPcon(큐피콘)</br>2 -> LocalDelivery(방문,배달)</br>3 -> Delivery(배달)</br>4 -> Cafeteria(일회용바코드)</br>5 -> Cafeteria(고정형바코드)
          required: true
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  properties:
                    favorites:
                      type: object
                      properties:
                        category:
                          type: string
                          description: 카테고리명
                        categoryId:
                          type: integer
                          format: int32
                          description: 카테고리 고유 아이디 (즐겨찾기용 카테고리 ID는 -9999로 하드코딩)
                        list:
                          type: array
                          items:
                            properties:
                              id:
                                type: string
                                description: 제휴점 고유 아이디
                              name:
                                type: string
                                description: 제휴점 이름
                              intro:
                                type: string
                                description: 제휴점 설명
                              supplyType:
                                type: integer
                                format: int32
                                description: 제휴점 종류</br>0 -> Local(방문)</br>1 -> QPcon(큐피콘)</br>2 -> LocalDelivery(방문,배달)</br>3 -> Delivery(배달)</br>4 -> Cafeteria(일회용바코드)</br>5 -> Cafeteria(고정형바코드)
                              payment:
                                type: string
                                description: 결제 수단</br>MENU -> 메뉴선택</br>MULTI -> 메뉴선택 + 금액직접입력
                              paymentRate:
                                type: integer
                                description: 결제 레이트
                            required:
                              - id
                              - name
                              - intro
                              - supplyType
                              - payment
                              - paymentRate
                      required:
                        - list
                    store:
                      type: array
                      items:
                        properties:
                          store:
                            type: array
                            items:
                              properties:
                                category:
                                  type: string
                                  description: 카테고리명
                                categoryId:
                                  type: integer
                                  format: int32
                                  description: 카테고리 고유 아이디 (즐겨찾기용 카테고리 ID는 -9999로 하드코딩)
                                list:
                                  type: array
                                  items:
                                    properties:
                                      id:
                                        type: string
                                        description: 제휴점 고유 아이디
                                      name:
                                        type: string
                                        description: 제휴점 이름
                                      intro:
                                        type: string
                                        description: 제휴점 설명
                                      supplyType:
                                        type: integer
                                        format: int32
                                        description: 제휴점 종류</br>0 -> Local(방문)</br>1 -> QPcon(큐피콘)</br>2 -> LocalDelivery(방문,배달)</br>3 -> Delivery(배달)</br>4 -> Cafeteria(일회용바코드)</br>5 -> Cafeteria(고정형바코드)
                                      payment:
                                        type: string
                                        description: 결제 수단</br>MENU -> 메뉴선택</br>MULTI -> 메뉴선택 + 금액직접입력
                                      paymentRate:
                                        type: integer
                                        description: 결제 레이트
                                    required:
                                      - id
                                      - name
                                      - intro
                                      - supplyType
                                      - payment
                                      - paymentRate
                              required:
                                - list
                        required:
                          - favorites
                          - store
                  required:
                    - favorites
                    - store
        500:
          $ref: '#/components/responses/ServerError'
  /store/v3:
    get:
      tags:
        - Store
      summary: 사업장연결용 제휴점 리스트
      deprecated: true
      parameters: 
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: keyword
          description: 검색어
          schema:
            type: string
        - in: query
          name: supplytype
          description: 제휴점 종류</br>0 -> Local(방문)</br>1 -> QPcon(큐피콘)</br>2 -> LocalDelivery(방문,배달)</br>3 -> Delivery(배달)</br>4 -> Cafeteria(일회용바코드)</br>5 -> Cafeteria(고정형바코드)
          required: true
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  favorites:
                    $ref: '#/components/schemas/common.store.list'
                  store:
                    type: array
                    items:
                      $ref: '#/components/schemas/common.store.list'
                required:
                  - store
        500:
          $ref: '#/components/responses/ServerError'
  /store/v4:
    get:
      tags:
        - Store
      summary: 제휴식당 리스트(제휴점 통합)
      description: (2020-05-15)location gps 정보 추가 <br />
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: keyword
          description: 검색어
          schema:
            type: string
        - in: query
          name: supplyType
          description: LOCAL -> 방문식사<br/>DELIVERY -> 배달식사<br/>BOOKING -> 예약식사<br/>QPCON -> 모바일상품권
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  supplyTypes:
                    type: array
                    items:
                      properties:
                        code:
                          type: string
                          enum: [LOCAL, DELIVERY, BOOKING, QPCON]
                          description: LOCAL -> 방문식사<br/>DELIVERY -> 배달식사<br/>BOOKING -> 예약식사<br/>QPCON -> 모바일상품권
                        name:
                          type: string
                          description: 코드에 관한 TEXT(방문식사,배달식사,예약식사,모바일상품권)
                      required:
                        - code
                        - name
                  categories:
                    type: array
                    items:
                      properties:
                        id:
                          type: integer
                          description: 카테고리 고유 아이디
                        name:
                          type: string
                          description: 카테고리명
                        order:
                          type: integer
                          description: 카테고리 정렬 순서
                      required:
                        - id
                        - name
                        - order
                  stores:
                    type: array
                    items:
                      properties:
                        category:
                          type: object
                          properties:
                            id:
                              type: integer
                              description: 카테고리 고유 아이디
                            name:
                              type: string
                              description: 카테고리명
                            order:
                              type: integer
                              description: 카테고리 정렬 순서
                          required:
                            - id
                            - name
                            - order
                        contents:
                          type: array
                          items:
                            properties:
                              order:
                                type: integer
                                description: 제휴점 정렬 순서
                              id:
                                type: string
                                description: 제휴점 고유 아이디
                              multipay:
                                type: boolean
                                description: 함께결제 사용 가능 여부
                              name:
                                type: string
                                description: 제휴점 이름
                              intro:
                                type: string
                                description: 제휴점 설명
                              supplyTypes:
                                type: array
                                items:
                                  properties:
                                    code:
                                      type: string
                                      enum: [LOCAL, DELIVERY, BOOKING, QPCON]
                                      description: LOCAL -> 방문식사<br/>DELIVERY -> 배달식사<br/>BOOKING -> 예약식사<br/>QPCON -> 모바일상품권
                                    name:
                                      type: string
                                      description: 코드에 관한 TEXT(방문식사,배달식사,예약식사,모바일상품권)
                                  required:
                                    - code
                                    - name
                              isFavorites:
                                type: boolean
                                description: 즐겨찾기 여부
                              isBooking:
                                type: boolean
                                description: 예약 가능 여부
                              barcodeType:
                                type: string
                                enum: [STORE, MENU]
                                description: 구내식당 바코드 사용 형태<br/>STORE -> 제휴점에서 진입<br/>MENU -> 메뉴에서 진입
                              isCaptainCode:
                                type: boolean
                                description: CaptainCode 여부
                              category:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                    description: 카테고리 고유 아이디
                                  name:
                                    type: string
                                    description: 카테고리명
                                required:
                                  - id
                                  - name
                              location:
                                type: object
                                description: 위치정보
                                properties:
                                  gpslat:
                                    type: number
                                    format: double
                                    description: GPS
                                  gpslon:
                                    type: number
                                    format: double
                                    description: GPS
                                  image:
                                    type: string
                                    description: 제휴점 위치 이미지 코드상으론 Jinair 전용... 상세에서 사용해서 그대로 추가해둠
                            required:
                              - order
                              - id
                              - multipay
                              - name
                              - supplyType
                              - isFavorites
                      required:
                        - category
                        - stores
        500:
          $ref: '#/components/responses/ServerError'
  /store/v5:
    get:
      tags:
        - Store
      summary: 제휴식당 리스트 (제휴식당 탐색기능 개선)
      description: (2020-08-12) 응답값에 신규식당 flag 추가, 업종 및 커스텀업종 추가, 추천제휴점 추가
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  supplyTypes:
                    type: array
                    items:
                      properties:
                        code:
                          type: string
                          enum: [LOCAL, DELIVERY, BOOKING, QPCON]
                          description: LOCAL -> 방문식사<br/>DELIVERY -> 배달식사<br/>BOOKING -> 예약식사<br/>QPCON -> 모바일상품권
                        name:
                          type: string
                          description: 코드에 관한 TEXT(방문식사,배달식사,예약식사,모바일상품권)
                      required:
                        - code
                        - name
                  categories:
                    type: array
                    items:
                      properties:
                        id:
                          type: integer
                          description: 커스텀 카테고리 고유 아이디
                        name:
                          type: string
                          description: 커스텀 카테고리명
                        order:
                          type: integer
                          description: 커스텀 카테고리 정렬 순서
                      required:
                        - id
                        - name
                  mainCategories:
                    type: array
                    items:
                      properties:
                        id:
                          type: integer
                          description: 업종 고유 아이디
                        name:
                          type: string
                          description: 업종명
                        logoImage:
                          type: string
                          description: 로고 이미지 경로
                      required:
                        - id
                        - name
                  recommendedStores:
                    type: array
                    items:
                      properties:
                        order:
                          type: integer
                          description: 제휴점 정렬 순서
                        id:
                          type: string
                          description: 제휴점 고유 아이디
                        multipay:
                          type: boolean
                          description: 함께결제 사용 가능 여부
                        name:
                          type: string
                          description: 제휴점 이름
                        intro:
                          type: string
                          description: 제휴점 설명
                        supplyTypes:
                          type: array
                          items:
                            properties:
                              code:
                                type: string
                                enum: [LOCAL, DELIVERY, BOOKING, QPCON]
                                description: LOCAL -> 방문식사<br/>DELIVERY -> 배달식사<br/>BOOKING -> 예약식사<br/>QPCON -> 모바일상품권
                              name:
                                type: string
                                description: 코드에 관한 TEXT(방문식사,배달식사,예약식사,모바일상품권)
                            required:
                              - code
                              - name
                        isFavorites:
                          type: boolean
                          description: 즐겨찾기 여부
                        isNew:
                          type: boolean
                          description: 신규제휴점 여부
                        isBooking:
                          type: boolean
                          description: 예약 가능 여부
                        barcodeType:
                          type: string
                          enum: [STORE, MENU]
                          description: 구내식당 바코드 사용 형태<br/>STORE -> 제휴점에서 진입<br/>MENU -> 메뉴에서 진입
                        isCaptainCode:
                          type: boolean
                          description: CaptainCode 여부
                        isRecommended:
                          type: boolean
                          description: 추천여부 (=true)
                        category:
                          type: object
                          properties:
                            id:
                              type: integer
                              description: 커스텀 카테고리 고유 아이디
                            name:
                              type: string
                              description: 커스텀 카테고리명
                            order:
                              type: integer
                              description: 커스텀 카테고리 정렬 순서
                          required:
                            - id
                            - name
                        mainCategories:
                          type: array
                          items:
                            properties:
                              id:
                                type: integer
                                description: 업종 고유 아이디
                              name:
                                type: string
                                description: 업종 카테고리명
                              logoImage:
                                type: string
                                description: 로고 이미지 경로
                            required:
                              - id
                              - name
                        location:
                          type: object
                          description: 위치정보
                          properties:
                            gpslat:
                              type: number
                              format: double
                              description: GPS
                            gpslon:
                              type: number
                              format: double
                              description: GPS
                            image:
                              type: string
                              description: 제휴점 위치 이미지 코드상으론 Jinair 전용... 상세에서 사용해서 그대로 추가해둠
                      required:
                        - order
                        - id
                        - multipay
                        - name
                        - supplyType
                        - isFavorites
                        - defaultCategory
                        - isNew
                        - isRecommended
                  stores:
                    type: array
                    items:
                      properties:
                        order:
                          type: integer
                          description: 제휴점 정렬 순서
                        id:
                          type: string
                          description: 제휴점 고유 아이디
                        multipay:
                          type: boolean
                          description: 함께결제 사용 가능 여부
                        name:
                          type: string
                          description: 제휴점 이름
                        intro:
                          type: string
                          description: 제휴점 설명
                        supplyTypes:
                          type: array
                          items:
                            properties:
                              code:
                                type: string
                                enum: [LOCAL, DELIVERY, BOOKING, QPCON]
                                description: LOCAL -> 방문식사<br/>DELIVERY -> 배달식사<br/>BOOKING -> 예약식사<br/>QPCON -> 모바일상품권
                              name:
                                type: string
                                description: 코드에 관한 TEXT(방문식사,배달식사,예약식사,모바일상품권)
                            required:
                              - code
                              - name
                        isFavorites:
                          type: boolean
                          description: 즐겨찾기 여부
                        isNew:
                          type: boolean
                          description: 신규제휴점 여부
                        isBooking:
                          type: boolean
                          description: 예약 가능 여부
                        barcodeType:
                          type: string
                          enum: [STORE, MENU]
                          description: 구내식당 바코드 사용 형태<br/>STORE -> 제휴점에서 진입<br/>MENU -> 메뉴에서 진입
                        isCaptainCode:
                          type: boolean
                          description: CaptainCode 여부
                        isRecommended:
                          type: boolean
                          description: 추천여부 (=false)
                        category:
                          type: object
                          properties:
                            id:
                              type: integer
                              description: 커스텀 카테고리 고유 아이디
                            name:
                              type: string
                              description: 커스텀 카테고리명
                            order:
                              type: integer
                              description: 커스텀 카테고리 정렬 순서
                          required:
                            - id
                            - name
                        mainCategories:
                          type: array
                          items:
                            properties:
                              id:
                                type: integer
                                description: 업종 고유 아이디
                              name:
                                type: string
                                description: 업종 카테고리명
                              logoImage:
                                type: string
                                description: 로고 이미지 경로
                            required:
                              - id
                              - name
                        location:
                          type: object
                          description: 위치정보
                          properties:
                            gpslat:
                              type: number
                              format: double
                              description: GPS
                            gpslon:
                              type: number
                              format: double
                              description: GPS
                            image:
                              type: string
                              description: 제휴점 위치 이미지 코드상으론 Jinair 전용... 상세에서 사용해서 그대로 추가해둠
                      required:
                        - order
                        - id
                        - multipay
                        - name
                        - supplyType
                        - isFavorites
                        - defaultCategory
                        - isNew
                        - isRecommended
                required:
                  - supplyTypes
                  - mainCategories
                  - stores
        500:
          $ref: '#/components/responses/ServerError'

  /store/v1/{storeId}:
    get:
      tags:
        - Store
      summary: 단말 > 메인 > 식사하기 > 제휴점 리스트 > 상세
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: storeId
          description: 제휴점 고유 아이디
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  store:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 제휴점 고유 아이디
                      name:
                        type: string
                        description: 제휴점명
                      intro:
                        type: string
                        description: 제휴점 설명
                      phone:
                        type: string
                        description: 제휴점 전화번호
                      address:
                        type: string
                        description: 제휴점 주소
                      supplyType:
                        type: integer
                        description: 제휴점 종류
                      payment:
                        type: string
                        description: 결제수단 <br/>MENU -> 메뉴선택 <br/>MULTI -> 메뉴선택 + 금액입력
                      paymentRate:
                        type: number
                        format: float
                        description: 정가의 판매가 비율 (ex. 0.9 는 정가의 90%)
                      location:
                        type: object
                        description: 위치정보
                        properties:
                          gpslat:
                            type: number
                            format: double
                            description: GPS
                          gpslon:
                            type: number
                            format: double
                            description: GPS
                          image:
                            type: string
                            description: 제휴점 위치 이미지 (이미지 정보가 있으면 GPS 대신 이미지를 노출)
                        required:
                          - gpslat
                          - gpslon
                    required:
                      - id
                      - name
                      - intro
                      - phone
                      - address
                      - supplyType
                      - payment
                      - paymentRate
                      - location
        500:
          $ref: '#/components/responses/ServerError'
  /store/v2/{storeId}:
    get:
      tags:
        - Store
      summary: 단말 > 메인 > 식사하기 > 제휴점 리스트 > 상세
      deprecated: true
      parameters: 
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: storeId
          description: 제휴점 고유 아이디
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  store:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 제휴점 고유 아이디
                      name:
                        type: string
                        description: 제휴점명
                      intro:
                        type: string
                        description: 제휴점 설명
                      phone:
                        type: string
                        description: 제휴점 전화번호
                      address:
                        type: string
                        description: 제휴점 주소
                      supplyType:
                        type: integer
                        description: 제휴점 종류
                      payment:
                        type: string
                        description: 결제수단 <br/>MENU -> 메뉴선택 <br/>MULTI -> 메뉴선택 + 금액입력
                      paymentRate:
                        type: number
                        format: float
                        description: 정가의 판매가 비율 (ex. 0.9 는 정가의 90%)
                      location:
                        type: object
                        description: 위치정보
                        properties:
                          gpslat:
                            type: number
                            format: double
                            description: GPS
                          gpslon:
                            type: number
                            format: double
                            description: GPS
                          image:
                            type: string
                            description: 제휴점 위치 이미지 (이미지 정보가 있으면 GPS 대신 이미지를 노출)
                        required:
                          - gpslat
                          - gpslon
                    required:
                      - id
                      - name
                      - intro
                      - phone
                      - address
                      - supplyType
                      - payment
                      - paymentRate
                      - location
        500:
          $ref: '#/components/responses/ServerError'
  /store/v4/{storeId}:
    get:
      tags:
        - Store
      summary: 제휴식당 상세(제휴점 통합)
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: storeId
          description: 제휴식당 고유 아이디
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  store:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 제휴점 고유 아이디
                      name:
                        type: string
                        description: 제휴점 이름
                      multipay:
                        type: boolean
                        description: 함께결제 사용 가능 여부
                      supplyTypes:
                        type: array
                        items:
                          properties:
                            code:
                              type: string
                              enum: [LOCAL, DELIVERY, BOOKING, QPCON]
                              description: LOCAL -> 방문식사<br/>DELIVERY -> 배달식사<br/>BOOKING -> 예약식사<br/>QPCON -> 모바일상품권
                            name:
                              type: string
                              description: 코드에 관한 TEXT(방문식사,배달식사,예약식사,모바일상품권)
                          required:
                            - code
                            - name
                      limitAmount: 
                        type: integer
                        description: 결제제한금액 <br>(기본값 -1 -> 금액제한없음)
                      isFavorites:
                        type: boolean
                        description: 즐겨찾기 여부
                      isBooking:
                        type: boolean
                        description: 예약 가능 여부
                      barcodeType:
                        type: string
                        enum: [STORE, MENU]
                        description: 구내식당 바코드 사용 형태<br/>STORE -> 제휴점에서 진입<br/>MENU -> 메뉴에서 진입
                      image:
                        type: string
                        description: 이미지 경로
                      addr:
                        type: string
                        description: 주소
                      tel:
                        type: string
                        description: 전화번호
                      paymentType:
                        type: string
                        description: 결제수단 <br/>MENU -> 메뉴선택 <br/>MULTI -> 메뉴선택 + 금액입력
                      paymentRate:
                        type: number
                        format: float
                        description: 정가의 판매가 비율 (ex. 0.9 는 정가의 90%)
                      location:
                        type: object
                        description: 위치정보
                        properties:
                          gpslat:
                            type: number
                            format: double
                            description: GPS
                          gpslon:
                            type: number
                            format: double
                            description: GPS
                        required:
                          - gpslat
                          - gpslon
                    required:
                      - id
                      - name
                      - multipay
                      - supplyType
                      - isFavorites
                      - paymentType
                  booked:
                    type: array
                    description: 예약 날짜 array (오늘 날짜 기준 ASC)
                    items:
                      properties:
                        date:
                          type: integer
                          description: 예약 가능 날짜
                        isAvailable:
                          type: boolean
                          description: 예약 가능 여부
                      required:
                        - date
                        - isAvailable
                required:
                  - store
        500:
          $ref: '#/components/responses/ServerError'
  /store/v5/{storeId}:
    get:
      tags:
        - Store
      summary: 제휴식당 상세(제휴점 통합)
      description: 상품배송 제휴점 타입 추가<br/>
        배송비,배송정보 추가(2020-09-07)
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: storeId
          description: 제휴식당 고유 아이디
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  store:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 제휴점 고유 아이디
                      name:
                        type: string
                        description: 제휴점 이름
                      multipay:
                        type: boolean
                        description: 함께결제 사용 가능 여부
                      supplyTypes:
                        type: array
                        items:
                          properties:
                            code:
                              type: string
                              enum: [LOCAL, DELIVERY, BOOKING, QPCON,SHIPPING]
                              description: LOCAL -> 방문식사<br/>DELIVERY -> 배달식사<br/>BOOKING -> 예약식사<br/>QPCON -> 모바일상품권<br/>SHIPPING -> 상품배송
                            name:
                              type: string
                              description: 코드에 관한 TEXT(방문식사,배달식사,예약식사,모바일상품권,상품배송)
                          required:
                            - code
                            - name
                      limitAmount:
                        type: integer
                        description: 결제제한금액 <br>(기본값 -1 -> 금액제한없음)
                      isFavorites:
                        type: boolean
                        description: 즐겨찾기 여부
                      isBooking:
                        type: boolean
                        description: 예약 가능 여부
                      barcodeType:
                        type: string
                        enum: [STORE, MENU]
                        description: 구내식당 바코드 사용 형태<br/>STORE -> 제휴점에서 진입<br/>MENU -> 메뉴에서 진입
                      isCaptainCode:
                        type: boolean
                        description: CaptainCode 여부
                      image:
                        type: string
                        description: 이미지 경로
                      addr:
                        type: string
                        description: 주소
                      tel:
                        type: string
                        description: 전화번호
                      shippingFee:
                        type: integer
                        description: 배송비
                      shippingInfo:
                        type: string
                        description: 배송 정보
                      paymentType:
                        type: string
                        description: 결제수단 <br/>MENU -> 메뉴선택 <br/>MULTI -> 메뉴선택 + 금액입력
                      paymentRate:
                        type: number
                        format: float
                        description: 정가의 판매가 비율 (ex. 0.9 는 정가의 90%)
                      location:
                        type: object
                        description: 위치정보
                        properties:
                          gpslat:
                            type: number
                            format: double
                            description: GPS
                          gpslon:
                            type: number
                            format: double
                            description: GPS
                        required:
                          - gpslat
                          - gpslon
                    required:
                      - id
                      - name
                      - multipay
                      - supplyType
                      - isCaptainCode
                      - isFavorites
                      - paymentType
                  booked:
                    type: array
                    description: 예약 날짜 array (오늘 날짜 기준 ASC)
                    items:
                      properties:
                        date:
                          type: integer
                          description: 예약 가능 날짜
                        isAvailable:
                          type: boolean
                          description: 예약 가능 여부
                      required:
                        - date
                        - isAvailable
                required:
                  - store
        500:
          $ref: '#/components/responses/ServerError'
  /store/v1/{storeId}/menu:
    get:
      tags:
        - Store
      summary: 단말 > 메인 > 식사하기 > 제휴점 리스트 > 상세 > 메뉴리스트
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: storeId
          description: 제휴점 고유 아이디
          required: true
          schema:
            type: string
        - in: query
          name: keyword
          description: 검색어
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  properties:
                    category:
                      type: string
                      description: 카테고리
                    menu:
                      type: array
                      items:
                        properties:
                          id:
                            type: string
                            description: 메뉴 고유 아이디
                          name:
                            type: string
                            description: 메뉴명
                          intro:
                            type: string
                            description: 메뉴 설명
                          productid:
                            type: string
                            description: 큐피콘 상품 아이디
                          price:
                            type: integer
                            format: int32
                            description: 정가
                          salesPrice:
                            type: integer
                            format: int32
                            description: 판매가
                          images:
                            type: object
                            properties:
                              main:
                                type: string
                                description: 메뉴 이미지 경로
                              thumb:
                                type: string
                                description: 메뉴 썸네일 이미지 경로
                        required:
                          - id
                          - name
                          - intro
                          - price
                          - salesPrice
                  required:
                    - category
                    - menu
        500:
          $ref: '#/components/responses/ServerError'
  /store/v2/{storeId}/menu:
    get:
      tags:
        - Store
      summary: 단말 > 메인 > 식사하기 > 제휴점 리스트 > 상세 > 메뉴리스트
      deprecated: true
      parameters: 
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: storeId
          description: 제휴점 고유 아이디
          required: true
          schema:
            type: string
        - in: query
          name: keyword
          description: 검색어
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  menu:
                    type: array
                    items:
                      properties:
                        category:
                          type: string
                          description: 카테고리명
                        list:
                          type: array
                          description: 메뉴 정보 Array
                          items:
                            properties:
                              id:
                                type: string
                                description: 메뉴 고유 아이디
                              name:
                                type: string
                                description: 메뉴명
                              intro:
                                type: string
                                description: 메뉴 설명
                              productid:
                                type: string
                                description: 큐피콘 상품 아이디
                              price:
                                type: integer
                                format: int32
                                description: 정가
                              salesPrice:
                                type: integer
                                format: int32
                                description: 판매가
                              images:
                                type: object
                                properties:
                                  main:
                                    type: string
                                    description: 메뉴 이미지 경로
                                  thumb:
                                    type: string
                                    description: 메뉴 썸네일 이미지 경로
                            required:
                              - id
                              - name
                              - intro
                              - price
                              - salesPrice
                      required:
                        - list
                        - category
        500:
          $ref: '#/components/responses/ServerError'
  /store/v4/{storeId}/menu:
    get:
      tags:
        - Store
      summary: 제휴식당 메뉴 리스트
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: storeId
          description: 제휴식당 고유 아이디
          required: true
          schema:
            type: string
        - in: query
          name: date
          description: 조회 날짜 (yyyy-MM-dd) - 예약식사 일 경우만
          schema:
            type: string
        - in: query
          name: keyword
          description: 메뉴 검색어
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  menus:
                    type: array
                    items:
                      properties:
                        category:
                          type: object
                          properties:
                            id:
                              type: integer
                              description: 카테고리 고유 아이디
                            name:
                              type: string
                              description: 카테고리명
                            order:
                              type: integer
                              description: 카테고리 정렬 순서
                          required:
                            - id
                            - name
                            - order
                        contents:
                          type: array
                          items:
                            properties:
                              order:
                                type: integer
                                description: 메뉴 정렬 순서
                              id:
                                type: string
                                description: 메뉴 고유 아이디
                              name:
                                type: string
                                description: 메뉴 이름
                              intro:
                                type: string
                                description: 메뉴 설명
                              warning:
                                type: string
                                description: 모바일 상품권일 경우 주의사항
                              price:
                                type: integer
                                format: int32
                                description: 정가
                              salesPrice:
                                type: integer
                                format: int32
                                description: 판매가
                              images:
                                type: object
                                properties:
                                  main:
                                    type: string
                                    description: 메뉴 이미지 경로
                                  thumb:
                                    type: string
                                    description: 메뉴 썸네일 이미지 경로
                              productId:
                                type: string
                                description: 큐피콘 상품 아이디
                              booking:
                                type: object
                                properties:
                                  artifactIdx:
                                    type: integer
                                    description: 예약 항목 고유 아이디
                                  isBooked:
                                    type: boolean
                                    description: 예약 여부</br>true -> 예약 완료</br>false -> 예약 안함
                                  isBooking:
                                    type: boolean
                                    description: 예약 가능 여부</br>true -> 예약 가능</br>false -> 예약 불가
                                  status:
                                    type: string
                                    description: 예약 식사 상태</br>CLOSED -> 예약 종료</br>BOOKING -> 예약 중<br/>DRAFT -> 예약 준비
                            required:
                              - order
                              - id
                              - name
                              - price
                              - salesPrice
                required:
                  - store
        500:
          $ref: '#/components/responses/ServerError'
  /store/v1/favorites:
    post:
      tags:
        - Store
      summary: 제휴점 즐겨찾기 추가
      description: 단말 > 메인 > 식사하기 > 가맹점 리스트
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                sid:
                  type: string
                  description: 제휴점 고유 아이디
              required:
                - sid
      responses:
        201:
          description: CREATED
          content:
            application/json:
              schema:
                type: object
                properties:
                  sid:
                    type: string
                    description: 제휴점 고유 아이디
                required:
                - sid
        500:
          $ref: '#/components/responses/ServerError'
  /store/v1/favorites/{storeId}:
    delete:
      tags:
        - Store
      summary: 제휴점 즐겨찾기 삭제
      description: 단말 > 메인 > 식사하기 > 가맹점 리스트
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: storeId
          description: 제휴점 고유 아이디
          required: true
          schema:
            type: string
      responses:
        204:
          description: No Content
        500:
          $ref: '#/components/responses/ServerError'
  /store/v4/{storeId}/favorites:
    post:
      tags:
        - Store
      summary: 제휴식당 즐겨찾기 추가
      description: 단말 > 메인 > 식사하기 > 제휴식당 리스트 > 제휴식당 상세
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: storeId
          description: 제휴점 고유 아이디
          required: true
          schema:
            type: string
      responses:
        201:
          description: CREATED
        500:
          $ref: '#/components/responses/ServerError'
    delete:
      tags:
        - Store
      summary: 제휴식당 즐겨찾기 삭제
      description: 단말 > 메인 > 식사하기 > 제휴식당 리스트 > 상세
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: storeId
          description: 제휴점 고유 아이디
          required: true
          schema:
            type: string
      responses:
        204:
          description: No Content
        500:
          $ref: '#/components/responses/ServerError'

# tag : Booking
  /booking/v1/book/date:
    get:
      tags:
        - Booking
      summary: 예약 가능 날짜
      description: 예약 결제 > 예약 목록 > 예약 가능 날짜
      parameters: 
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  contents:
                    type: array
                    description: 예약 가능 날짜 array (오늘 날짜 기준 ASC)
                    items:
                      properties:
                        bookingDate:
                          type: integer
                          description: 예약 가능 날짜
        500:
          $ref: '#/components/responses/ServerError'
  /booking/v1/book/check:     
    post:
      tags:
        - Booking
      summary: 예약 결제 체크
      description: 예약 결제 > 예약 목록 > 선택 > 예약 결제 > 예약 가능 유무 체크
      parameters: 
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                contents:
                  type: array
                  items:
                    properties:
                      artifactIdx:
                        type: integer
                        description: 예약 항목 고유 아이디
                      menuId:
                        type: string
                        description: 메뉴 고유 아이디
                      menuCount:
                        type: integer
                        description: 메뉴 선택 개수 (default -> 1)
                    required:
                      - artifactIdx
                      - menuId
      responses:
        201:
          description: CREATED
        500:
          $ref: '#/components/responses/ServerError'     
  /booking/v2/book/check:     
    post:
      tags:
        - Booking
      summary: 예약 결제 체크 V2
      description: 예약 결제 > 예약 목록 > 선택 > 예약 결제 > 예약 가능 유무 체크
      parameters: 
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                artifactIdx:
                  type: string
                  description: 예약항목 고유 아이디
                menu: 
                  type: array
                  items:
                    properties: 
                      id:
                        type: string
                        description: 메뉴 고유 아이디
                      count:
                        type: integer
                        description: 메뉴 선택 개수 (default -> 1)
                    required: 
                      - id
                      - count
              required: 
                - artifactIdx
                - menu
      responses:
        201:
          description: CREATED
        500:
          $ref: '#/components/responses/ServerError'     
  /booking/v1/book:
    get:
      tags:
        - Booking
      summary: 예약 가능한 메뉴 목록
      description: 예약 결제 > 예약 목록
      parameters: 
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: date
          description: 조회 날짜 (yyyy-MM-dd)
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  contents:
                    type: array
                    items:
                      properties:
                        name:
                          type: string
                          description: 예약 항목 카테고리 묶음 이름
                        priority:
                          type: integer
                          description: 노출 순서
                        artifacts:
                          type: array
                          items:
                            properties:
                              artifactIdx:
                                type: integer
                                description: 예약 항목 고유 아이디
                              menu:
                                type: object
                                properties:
                                  id:
                                    type: string
                                    description: 메뉴 고유 아이디
                                  name:
                                    type: string
                                    description: 메뉴 이름
                                  images:
                                    type: object
                                    properties:
                                      main:
                                        type: string
                                        description: 메인 이미지 경로
                                      thumb:
                                        type: string
                                        description: 썸네일 이미지 경로
                                required:
                                  - menuId
                                  - name
                              store:
                                type: object
                                properties:
                                  id:
                                    type: string
                                    description: 제휴점 고유 아이디
                                  name:
                                    type: string
                                    description: 제휴점 이름
                                required:
                                  - storeId
                                  - name
                              price:
                                type: integer
                                description: 금액
                              isBooked:
                                type: boolean
                                description: 예약 여부</br>true -> 예약 완료</br>false -> 예약 안함
                              isBooking:
                                type: boolean
                                description: 예약 가능 여부</br>true -> 예약 가능</br>false -> 예약 불가
                              status:
                                type: string
                                description: 예약 식사 상태</br>CLOSED -> 예약 종료</br>BOOKING -> 예약 중<br/>DRAFT -> 예약 준비
                            required:
                              - idx
                              - menu
                              - store
                              - price
                              - isBooked
                              - isBooking
                      required:
                        - name
                        - priority
        500:
          $ref: '#/components/responses/ServerError'  
    post:
      tags:
        - Booking
      summary: 예약 하기
      description: 식사하기 > 제휴점 선택 > 예약결제 > 예약하기 목록 선택 > 예약 하기
      parameters: 
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                contents:
                  type: array
                  items:
                    properties:
                      artifactIdx:
                        type: integer
                        description: 예약 항목 고유 아이디
                      menuId:
                        type: string
                        description: 메뉴 고유 아이디
                      menuCount:
                        type: integer
                        description: 메뉴 선택 개수 (default -> 1)
                    required:
                      - artifactIdx
                      - menuId
      responses:
        201:
          description: CREATED
          content:
            application/json:
              schema:
                type: object
                properties:
                  content:
                    type: object
                    properties:
                      datetime:
                        type: integer
                        description: 예약 성공 시간
                      bookings:
                        type: array
                        items:
                          properties:
                            artifactIdx:
                              type: integer
                              description: 예약 항목 고유 아이디
                            historyIdx:
                              type: integer
                              description: 예약 고유 아이디
                            couponId:
                              type: string
                              description: 결제 고유 아이디
                          required:
                            - artifactIdx
                            - historyIdx
                    required:
                      - datetime
        500:
          $ref: '#/components/responses/ServerError'
  /booking/v2/book: 
    post:
      tags:
        - Booking
      summary: 예약 하기 v2
      description: 예약메뉴 목록(수량선택) 후, 예약요청처리 API
      parameters: 
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                artifactIdx:
                  type: string
                  description: 예약항목 고유 아이디
                menu: 
                  type: array
                  items:
                    properties: 
                      id:
                        type: string
                        description: 메뉴 고유 아이디
                      count:
                        type: integer
                        description: 메뉴 선택 개수 (default -> 1)
                    required: 
                      - id
                      - count
              required: 
                - artifactIdx
                - menu
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  content:
                    properties:
                      pay:
                        type: object
                        properties:
                          id:
                            type: string
                            description: 고유 ID (취소 용)
                          roomId:
                            type: integer
                            description: 결제번호
                      store:
                        type: object
                        properties:
                          storeId:
                            type: string
                            description: 제휴점 고유 아이디
                          name:
                            type: string
                            description: 제휴점 이름
                          supplyTypes:
                            type: array
                            items:
                              properties:
                                code:
                                  type: string
                                  enum: [LOCAL, DELIVERY, BOOKING, QPCON]
                                  description: LOCAL -> 방문식사<br/>DELIVERY -> 배달식사<br/>BOOKING -> 예약식사<br/>QPCON -> 모바일상품권
                                name:
                                  type: string
                                  description: 코드에 관한 TEXT(방문식사,배달식사,예약식사,모바일상품권)
                              required:
                                - code
                                - name
                      date:
                        type: object
                        properties:
                          pay:
                            type: integer
                            description: 결제시간
                          cancel:
                            type: integer
                            description: 취소시간
                          available:
                            type: integer
                            description: 유효시간
                          process:
                            type: integer
                            description: 처리시간
                          plus:
                            type: integer
                            description: 지급시간
                          minus:
                            type: integer
                            description: 차감시간
                          expired:
                            type: integer
                            description: 소멸시간
                          charge:
                            type: integer
                            description: 충전시간
                          mealTime:
                            type: integer
                            description: 식사시간
                      cause:
                        type: object
                        properties:
                          process:
                            type: string
                            description: 처리사유
                          plus:
                            type: string
                            description: 지급사유
                          minus:
                            type: string
                            description: 차감사유
                          expired:
                            type: string
                            description: 소멸사유
                      worker:
                        type: object
                        properties:
                          pay:
                            type: string
                            description: 결제자
                          plus:
                            type: string
                            description: 지급자
                          minus:
                            type: string
                            description: 차감자
                      user:
                        type: object
                        properties:
                          userId:
                            type: string
                            description: 본인 고유 아이디
                          name:
                            type: string
                            description: 본인 이름
                          totalAmount:
                            type: integer
                            description: 본인 총 금액
                      policys:
                        type: array
                        items:
                          properties:
                            policyIdx:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 이름
                            amount:
                              type: integer
                              description: 정책 사용 금액
                      guests:
                        type: array
                        items:
                          properties:
                            userId:
                              type: string
                              description: 함께 결제자 고유 아이디
                            name:
                              type: string
                              description: 함께 결제자 이름
                            division:
                              type: string
                              description: 함께 결제자 부서
                            amount:
                              type: integer
                              description: 함께 결제자 사용 금액
                      menus:
                        type: array
                        items:
                          properties:
                            menuId:
                              type: string
                              description: 메뉴 고유 아이디
                            name:
                              type: string
                              description: 메뉴 이름
                            price:
                              type: integer
                              description: 메뉴 가격
                            quantity:
                              type: integer
                              description: 메뉴 수량
                            totalPrice:
                              type: integer
                              description: 메뉴 금액
                            images:
                              type: object
                              properties:
                                main:
                                  type: string
                                  description: 메인 이미지 경로
                                thumb:
                                  type: string
                                  description: 썸네일 이미지 경로
                      amount:
                        type: object
                        properties:
                          totalPay:
                            type: integer
                            description: 총 금액
                          plus:
                            type: integer
                            description: 지급식대 금액
                          minus:
                            type: integer
                            description: 차감식대 금액
                          expired:
                            type: integer
                            description: 소멸식대 금액
                          balance:
                            type: integer
                            description: 잔여식대 금액
                          chargePoint:
                            type: integer
                            description: 충전 포인트
                          chargeAmount:
                            type: integer
                            description: 충전 금액
                      addons:
                        type: object
                        properties:
                          warning:
                            type: string
                            description: 유의사항
                          chargeType:
                            type: string
                            description: 충전방식
                          type:
                            type: string
                            description: 내역 타입<br/>USED -> 혼자결제<br/>MULTI_USED -> 함께결제<br/>ADMIN_USED -> 관리자 혼자결제<br/>ADMIN_MULTI_USED -> 관리자 함께결제<br/>QPCON_USED -> 모바일상품권 결제<br/>QPCON_EXPIRED -> 모바일상품권 소멸<br/>CANCEL -> 결제 취소<br/>POINT_PLUS -> 식대 포인트 지급<br/>POINT_MINUS -> 식대 포인트 차감<br/>POINT_EXPIRED -> 식대 포인트 소멸<br/>MYPOINT_CHARGE -> 마이포인트 충전
                          isCancelable:
                            type: boolean
                            description: 취소 가능 여부<br/>true -> 취소 가능<br/>false -> 취소 불가
                          pin:
                            type: string
                            description: 모바일 상품권 고유 번호
        500:
          $ref: '#/components/responses/ServerError'  
  /booking/v1/book/{historyIdx}:
    delete:
      tags:
        - Booking
      summary: 예약 취소
      description: 예약 관리 > 예약 선택 > 취소
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: historyIdx
          description: 예약 고유 아이디
          required: true
          schema:
            type: integer
      responses:
        204:
          description: No Content
        500:
          $ref: '#/components/responses/ServerError'
  /booking/v2/book/{historyIdx}:
    delete:
      tags:
        - Booking
      summary: 예약 취소 v2
      description: 예약 함 > 예약 리스트 or 예약 상세 선택 > 취소
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: historyIdx
          description: 예약 고유 아이디
          required: true
          schema:
            type: integer
        - in: query
          name: couponId
          description: 쿠폰 ID
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  content:
                    properties:
                      pay:
                        type: object
                        properties:
                          id:
                            type: string
                            description: 고유 ID (취소 용)
                          roomId:
                            type: integer
                            description: 결제번호
                      store:
                        type: object
                        properties:
                          storeId:
                            type: string
                            description: 제휴점 고유 아이디
                          name:
                            type: string
                            description: 제휴점 이름
                          supplyTypes:
                            type: array
                            items:
                              properties:
                                code:
                                  type: string
                                  enum: [LOCAL, DELIVERY, BOOKING, QPCON]
                                  description: LOCAL -> 방문식사<br/>DELIVERY -> 배달식사<br/>BOOKING -> 예약식사<br/>QPCON -> 모바일상품권
                                name:
                                  type: string
                                  description: 코드에 관한 TEXT(방문식사,배달식사,예약식사,모바일상품권)
                              required:
                                - code
                                - name
                      date:
                        type: object
                        properties:
                          pay:
                            type: integer
                            description: 결제시간
                          cancel:
                            type: integer
                            description: 취소시간
                          available:
                            type: integer
                            description: 유효시간
                          process:
                            type: integer
                            description: 처리시간
                          plus:
                            type: integer
                            description: 지급시간
                          minus:
                            type: integer
                            description: 차감시간
                          expired:
                            type: integer
                            description: 소멸시간
                          charge:
                            type: integer
                            description: 충전시간
                          mealTime:
                            type: integer
                            description: 식사시간
                      cause:
                        type: object
                        properties:
                          process:
                            type: string
                            description: 처리사유
                          plus:
                            type: string
                            description: 지급사유
                          minus:
                            type: string
                            description: 차감사유
                          expired:
                            type: string
                            description: 소멸사유
                      worker:
                        type: object
                        properties:
                          pay:
                            type: string
                            description: 결제자
                          plus:
                            type: string
                            description: 지급자
                          minus:
                            type: string
                            description: 차감자
                      user:
                        type: object
                        properties:
                          userId:
                            type: string
                            description: 본인 고유 아이디
                          name:
                            type: string
                            description: 본인 이름
                          totalAmount:
                            type: integer
                            description: 본인 총 금액
                      policys:
                        type: array
                        items:
                          properties:
                            policyIdx:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 이름
                            amount:
                              type: integer
                              description: 정책 사용 금액
                      guests:
                        type: array
                        items:
                          properties:
                            userId:
                              type: string
                              description: 함께 결제자 고유 아이디
                            name:
                              type: string
                              description: 함께 결제자 이름
                            division:
                              type: string
                              description: 함께 결제자 부서
                            amount:
                              type: integer
                              description: 함께 결제자 사용 금액
                      menus:
                        type: array
                        items:
                          properties:
                            menuId:
                              type: string
                              description: 메뉴 고유 아이디
                            name:
                              type: string
                              description: 메뉴 이름
                            price:
                              type: integer
                              description: 메뉴 가격
                            quantity:
                              type: integer
                              description: 메뉴 수량
                            totalPrice:
                              type: integer
                              description: 메뉴 금액
                            images:
                              type: object
                              properties:
                                main:
                                  type: string
                                  description: 메인 이미지 경로
                                thumb:
                                  type: string
                                  description: 썸네일 이미지 경로
                      amount:
                        type: object
                        properties:
                          totalPay:
                            type: integer
                            description: 총 금액
                          plus:
                            type: integer
                            description: 지급식대 금액
                          minus:
                            type: integer
                            description: 차감식대 금액
                          expired:
                            type: integer
                            description: 소멸식대 금액
                          balance:
                            type: integer
                            description: 잔여식대 금액
                          chargePoint:
                            type: integer
                            description: 충전 포인트
                          chargeAmount:
                            type: integer
                            description: 충전 금액
                      addons:
                        type: object
                        properties:
                          warning:
                            type: string
                            description: 유의사항
                          chargeType:
                            type: string
                            description: 충전방식
                          type:
                            type: string
                            description: 내역 타입<br/>USED -> 혼자결제<br/>MULTI_USED -> 함께결제<br/>ADMIN_USED -> 관리자 혼자결제<br/>ADMIN_MULTI_USED -> 관리자 함께결제<br/>QPCON_USED -> 모바일상품권 결제<br/>QPCON_EXPIRED -> 모바일상품권 소멸<br/>CANCEL -> 결제 취소<br/>POINT_PLUS -> 식대 포인트 지급<br/>POINT_MINUS -> 식대 포인트 차감<br/>POINT_EXPIRED -> 식대 포인트 소멸<br/>MYPOINT_CHARGE -> 마이포인트 충전
                          isCancelable:
                            type: boolean
                            description: 취소 가능 여부<br/>true -> 취소 가능<br/>false -> 취소 불가
                          pin:
                            type: string
                            description: 모바일 상품권 고유 번호
        500:
          $ref: '#/components/responses/ServerError'
# tag : Account
  /account/v1/otp:
    get:
      tags:
        - Account
      summary: 일정 시간 동안만 유효한 바코드 조회
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: string
                    description: 코드
                  seed:
                    type: integer
                    format: int32
                    description: 코드 재생성 seed
                  created:
                    type: string
                    format: 'date-time'
                    description: 생성시간
                required:
                  - code
                  - seed
                  - created
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/pointbook:
    get:
      tags:
        - Account
      summary: 식권 사용내역
      parameters: 
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: page
          description: 페이지 번호
          required: false
          schema:
            type: string
        - in: query
          name: pageRow
          description: 페이지당 항목 수
          required: false
          schema:
            type: integer
        - in: query
          name: start
          description: 시작일(yyyy-MM-dd)
          required: false
          schema:
            type: string
        - in: query
          name: end
          description: 종료일(yyyy-MM-dd)
          required: false
          schema:
            type: string
        - in: query
          name: filter
          description: 검색 필터<br/>ALL -> 전체 (default value)<br/>COUPON -> 결제<br/>EXPIRED -> 소멸<br/>PLUS -> 지급<br/>MINUS -> 차감<br/>CANCEL -> 취소<br/>ADMIN_COUPON -> 관리자 결제<br/>QPCON -> 모바일상품권<br/>MYPOINT -> 대장포인트
          required: false
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  contents:
                    type: array
                    items:
                      properties:
                        id:
                          type: string
                          description: 고유 ID (상세진입용)
                        title:
                          type: string
                          description: 제목
                        type:
                          type: string
                          description: 내역 타입<br/>USED -> 혼자결제<br/>MULTI_USED -> 함께결제<br/>ADMIN_USED -> 관리자 혼자결제<br/>ADMIN_MULTI_USED -> 관리자 함께결제<br/>QPCON_USED -> 모바일상품권 결제<br/>QPCON_EXPIRED -> 모바일상품권 소멸<br/>CANCEL -> 결제 취소<br/>POINT_PLUS -> 식대 포인트 지급<br/>POINT_MINUS -> 식대 포인트 차감<br/>POINT_EXPIRED -> 식대 포인트 소멸<br/>MYPOINT_CHARGE -> 대장포인트 충전
                        description:
                          type: string
                          description: 내용
                        amount:
                          type: integer
                          description: 금액
                        totalAmount:
                          type: integer
                          description: 총 금액
                        useDate:
                          type: string
                          format: date
                          description: 사용 날짜 (Long)
                      required:
                        - id
                        - title
                        - type
                        - amount
                        - useDate
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/pointbook/{id}:
    get:
      tags:
        - Account
      summary: 식권 사용내역 상세
      parameters: 
        - $ref: '#/components/parameters/header-auth'
        - in : path
          name : id
          description: 상세 고유 번호
          required: true
          schema:
            type: string
        - in: query
          name: type
          description: 내역 타입<br/>USED -> 혼자결제<br/>MULTI_USED -> 함께결제<br/>ADMIN_USED -> 관리자 혼자결제<br/>ADMIN_MULTI_USED -> 관리자 함께결제<br/>QPCON_USED -> 모바일상품권 결제<br/>QPCON_EXPIRED -> 모바일상품권 소멸<br/>CANCEL -> 결제 취소<br/>POINT_PLUS -> 식대 포인트 지급<br/>POINT_MINUS -> 식대 포인트 차감<br/>POINT_EXPIRED -> 식대 포인트 소멸<br/>MYPOINT_CHARGE -> 대장포인트 충전
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  content:
                    properties:
                      pay:
                        type: object
                        properties:
                          id:
                            type: string
                            description: 고유 ID (취소 용)
                          roomId:
                            type: integer
                            description: 결제번호
                      store:
                        type: object
                        properties:
                          storeId:
                            type: string
                            description: 제휴점 고유 아이디
                          name:
                            type: string
                            description: 제휴점 이름
                      date:
                        type: object
                        properties:
                          pay:
                            type: integer
                            description: 결제시간
                          cancel:
                            type: integer
                            description: 취소시간
                          available:
                            type: integer
                            description: 유효시간
                          process:
                            type: integer
                            description: 처리시간
                          plus:
                            type: integer
                            description: 지급시간
                          minus:
                            type: integer
                            description: 차감시간
                          expired:
                            type: integer
                            description: 소멸시간
                          charge:
                            type: integer
                            description: 충전시간
                      cause:
                        type: object
                        properties:
                          process:
                            type: string
                            description: 처리사유
                          plus:
                            type: string
                            description: 지급사유
                          minus:
                            type: string
                            description: 차감사유
                          expired:
                            type: string
                            description: 소멸사유
                      worker:
                        type: object
                        properties:
                          pay:
                            type: string
                            description: 결제자
                          plus:
                            type: string
                            description: 지급자
                          minus:
                            type: string
                            description: 차감자
                      user:
                        type: object
                        properties:
                          userId:
                            type: string
                            description: 본인 고유 아이디
                          name:
                            type: string
                            description: 본인 이름
                          totalAmount:
                            type: integer
                            description: 본인 총 금액
                      policys:
                        type: array
                        items:
                          properties:
                            policyIdx:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 이름
                            amount:
                              type: integer
                              description: 정책 사용 금액
                      guests:
                        type: array
                        items:
                          properties:
                            userId:
                              type: string
                              description: 함께 결제자 고유 아이디
                            name:
                              type: string
                              description: 함께 결제자 이름
                            division:
                              type: string
                              description: 함께 결제자 부서
                            amount:
                              type: integer
                              description: 함께 결제자 사용 금액
                      menus:
                        type: array
                        items:
                          properties:
                            menuId:
                              type: string
                              description: 메뉴 고유 아이디
                            name:
                              type: string
                              description: 메뉴 이름
                            price:
                              type: integer
                              description: 메뉴 가격
                            quantity:
                              type: integer
                              description: 메뉴 수량
                            totalPrice:
                              type: integer
                              description: 메뉴 금액
                            images:
                              type: object
                              properties:
                                main:
                                  type: string
                                  description: 메인 이미지 경로
                                thumb:
                                  type: string
                                  description: 썸네일 이미지 경로
                      amount:
                        type: object
                        properties:
                          totalPay:
                            type: integer
                            description: 총 금액
                          plus:
                            type: integer
                            description: 지급식대 금액
                          minus:
                            type: integer
                            description: 차감식대 금액
                          expired:
                            type: integer
                            description: 소멸식대 금액
                          balance:
                            type: integer
                            description: 잔여식대 금액
                          chargePoint:
                            type: integer
                            description: 충전 포인트
                          chargeAmount:
                            type: integer
                            description: 충전 금액
                      addons:
                        type: object
                        properties:
                          warning:
                            type: string
                            description: 유의사항
                          chargeType:
                            type: string
                            description: 충전방식
                          type:
                            type: string
                            description: 내역 타입<br/>USED -> 혼자결제<br/>MULTI_USED -> 함께결제<br/>ADMIN_USED -> 관리자 혼자결제<br/>ADMIN_MULTI_USED -> 관리자 함께결제<br/>QPCON_USED -> 모바일상품권 결제<br/>QPCON_EXPIRED -> 모바일상품권 소멸<br/>CANCEL -> 결제 취소<br/>POINT_PLUS -> 식대 포인트 지급<br/>POINT_MINUS -> 식대 포인트 차감<br/>POINT_EXPIRED -> 식대 포인트 소멸<br/>MYPOINT_CHARGE -> 대장포인트 충전
                          isCancelable:
                            type: boolean
                            description: 취소 가능 여부<br/>true -> 취소 가능<br/>false -> 취소 불가
                          pin:
                            type: string
                            description: 모바일 상품권 고유 번호
                      barCode:
                        type: string
                        description: 바코드 고유 아이디
                      barCodeInfo:
                        type: object
                        properties:
                          barCode:
                            type: string
                            description: 바코드 고유 아이디
                          codeType:
                            type: string
                            description: 코드 유형
                            enum: [BARCODE, QRCODE]
                          isDisplay:
                            type: boolean
                            description: 바코드 노출 여부 (true -> 노출, false -> 비노출)
                      term:
                        type: array
                        items:
                          properties:
                            type:
                              type: string
                              description: 약관타입
                              enum: [BARCODE_CANCELLATION, BARCODE_CANCEL_BEAT]
                            title:
                              type: string
                              description: 제목
                            content:
                              type: string
                              description: 내용
                          required:
                            - type
                            - content
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/booking:
    get:
      tags:
        - Account
      summary: 예약 내역
      description: 메인 > 예약 내역
      parameters: 
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: page
          description: 페이지 번호
          required: false
          schema:
            type: string
        - in: query
          name: pageRow
          description: 페이지당 항목 수
          required: false
          schema:
            type: integer
        - in: query
          name: start
          description: 시작일(yyyy-MM-dd)
          required: false
          schema:
            type: string
        - in: query
          name: end
          description: 종료일(yyyy-MM-dd)
          required: false
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  contents:
                    type: array
                    items:
                      properties:
                        id:
                          type: integer
                          description: 고유 ID (상세진입용)
                        title:
                          type: string
                          description: 제목
                        type:
                          type: string
                          description: 내역 타입<br/>BOOKED -> 예약완료<br/>BOOKED_PAY -> 예약결제 완료<br/>BOOKED_CANCEL -> 예약 취소</br>BOOKED_PAY_CANCEL -> 예약 결제 취소
                        description:
                          type: string
                          description: 내용
                        useDate:
                          type: integer
                          description: 예약 사용 날짜
                        isCancelable:
                          type: boolean
                          description: 취소 가능 여부</br>true -> 취소 가능</br>false -> 취소 불가
                      required:
                        - id
                        - title
                        - type
                        - description
                        - useDate
                        - isCancelable
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/booking/{historyIdx}:
    get:
      tags:
        - Account
      summary: 예약 내역 상세
      description: 메인 > 예약 내역 > 상세
      parameters: 
        - $ref: '#/components/parameters/header-auth'
        - in : path
          name : id
          description: 상세 고유 번호
          required: true
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  content:
                    properties:
                      pay:
                        type: object
                        properties:
                          payId:
                            type: integer
                            description: 결제번호
                      store:
                        type: object
                        properties:
                          storeId:
                            type: string
                            description: 제휴점 고유 아이디
                          name:
                            type: string
                            description: 제휴점 이름
                      date:
                        type: object
                        properties:
                          booking:
                            type: integer
                            description: 예약 시간
                          mealTime:
                            type: integer
                            description: 식사 시간
                          pay:
                            type: integer
                            description: 결제시간
                          cancel:
                            type: integer
                            description: 취소시간
                      worker:
                        type: object
                        properties:
                          booking:
                            type: string
                            description: 예약자
                      user:
                        type: object
                        properties:
                          userId:
                            type: string
                            description: 본인 고유 아이디
                          name:
                            type: string
                            description: 본인 이름
                          totalAmount:
                            type: integer
                            description: 본인 총 금액
                      policys:
                        type: array
                        items:
                          properties:
                            policyIdx:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 이름
                            amount:
                              type: integer
                              description: 정책 사용 금액
                      menus:
                        type: array
                        items:
                          properties:
                            menuId:
                              type: string
                              description: 메뉴 고유 아이디
                            name:
                              type: string
                              description: 메뉴 이름
                            price:
                              type: integer
                              description: 메뉴 가격
                            quantity:
                              type: integer
                              description: 메뉴 수량
                            amount:
                              type: integer
                              description: 메뉴 금액
                            bookingGroup:
                              type: string
                              description: 예약 식사 시간
                            images:
                              type: object
                              properties:
                                main:
                                  type: string
                                  description: 메인 이미지 경로
                                thumb:
                                  type: string
                                  description: 썸네일 이미지 경로
                      amount:
                        type: object
                        properties:
                          payTotal:
                            type: integer
                            description: 총 금액
                      addons:
                        type: object
                        properties:
                          isCancelable:
                            type: boolean
                            description: 취소 가능 여부</br>true -> 취소 가능</br>false -> 취소 불가
                          type:
                            type: string
                            description: 내역 타입<br/>BOOKED -> 예약완료<br/>BOOKED_PAY -> 예약결제 완료<br/>BOOKED_CANCEL -> 예약 취소</br>BOOKED_PAY_CANCEL -> 예약 결제 취소
        500:
          $ref: '#/components/responses/ServerError'
  /account/v2/booking/{historyIdx}:
    get:
      tags:
        - Account
      summary: 예약 내역 상세
      description: 메인 > 예약 내역 > 상세
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in : path
          name : historyIdx
          description: 상세 고유 번호
          required: true
          schema:
            type: integer
        - in : query
          name : couponId
          description: 쿠폰 ID
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  content:
                    properties:
                      pay:
                        type: object
                        properties:
                          id:
                            type: string
                            description: 고유 ID (취소 용)
                          roomId:
                            type: integer
                            description: 결제번호
                      store:
                        type: object
                        properties:
                          storeId:
                            type: string
                            description: 제휴점 고유 아이디
                          name:
                            type: string
                            description: 제휴점 이름
                          supplyTypes:
                            type: array
                            items:
                              properties:
                                code:
                                  type: string
                                  enum: [LOCAL, DELIVERY, BOOKING, QPCON]
                                  description: LOCAL -> 방문식사<br/>DELIVERY -> 배달식사<br/>BOOKING -> 예약식사<br/>QPCON -> 모바일상품권
                                name:
                                  type: string
                                  description: 코드에 관한 TEXT(방문식사,배달식사,예약식사,모바일상품권)
                              required:
                                - code
                                - name
                      date:
                        type: object
                        properties:
                          pay:
                            type: integer
                            description: 결제시간
                          cancel:
                            type: integer
                            description: 취소시간
                          available:
                            type: integer
                            description: 유효시간
                          process:
                            type: integer
                            description: 처리시간
                          plus:
                            type: integer
                            description: 지급시간
                          minus:
                            type: integer
                            description: 차감시간
                          expired:
                            type: integer
                            description: 소멸시간
                          charge:
                            type: integer
                            description: 충전시간
                          mealTime:
                            type: integer
                            description: 식사시간
                          cancelable:
                            type: integer
                            description: 취소가능시간
                      cause:
                        type: object
                        properties:
                          process:
                            type: string
                            description: 처리사유
                          plus:
                            type: string
                            description: 지급사유
                          minus:
                            type: string
                            description: 차감사유
                          expired:
                            type: string
                            description: 소멸사유
                      worker:
                        type: object
                        properties:
                          pay:
                            type: string
                            description: 결제자
                          plus:
                            type: string
                            description: 지급자
                          minus:
                            type: string
                            description: 차감자
                      user:
                        type: object
                        properties:
                          userId:
                            type: string
                            description: 본인 고유 아이디
                          name:
                            type: string
                            description: 본인 이름
                          totalAmount:
                            type: integer
                            description: 본인 총 금액
                      policys:
                        type: array
                        items:
                          properties:
                            policyIdx:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 이름
                            amount:
                              type: integer
                              description: 정책 사용 금액
                      guests:
                        type: array
                        items:
                          properties:
                            userId:
                              type: string
                              description: 함께 결제자 고유 아이디
                            name:
                              type: string
                              description: 함께 결제자 이름
                            division:
                              type: string
                              description: 함께 결제자 부서
                            amount:
                              type: integer
                              description: 함께 결제자 사용 금액
                      menus:
                        type: array
                        items:
                          properties:
                            menuId:
                              type: string
                              description: 메뉴 고유 아이디
                            name:
                              type: string
                              description: 메뉴 이름
                            price:
                              type: integer
                              description: 메뉴 가격
                            quantity:
                              type: integer
                              description: 메뉴 수량
                            totalPrice:
                              type: integer
                              description: 메뉴 금액
                            images:
                              type: object
                              properties:
                                main:
                                  type: string
                                  description: 메인 이미지 경로
                                thumb:
                                  type: string
                                  description: 썸네일 이미지 경로
                      amount:
                        type: object
                        properties:
                          totalPay:
                            type: integer
                            description: 총 금액
                          plus:
                            type: integer
                            description: 지급식대 금액
                          minus:
                            type: integer
                            description: 차감식대 금액
                          expired:
                            type: integer
                            description: 소멸식대 금액
                          balance:
                            type: integer
                            description: 잔여식대 금액
                          chargePoint:
                            type: integer
                            description: 충전 포인트
                          chargeAmount:
                            type: integer
                            description: 충전 금액
                      addons:
                        type: object
                        properties:
                          warning:
                            type: string
                            description: 유의사항
                          chargeType:
                            type: string
                            description: 충전방식
                          type:
                            type: string
                            description: 결제 내역 타입<br/>USED -> 혼자결제<br/>MULTI_USED -> 함께결제<br/>ADMIN_USED -> 관리자 혼자결제<br/>ADMIN_MULTI_USED -> 관리자 함께결제<br/>QPCON_USED -> 모바일상품권 결제<br/>QPCON_EXPIRED -> 모바일상품권 소멸<br/>CANCEL -> 결제 취소<br/>POINT_PLUS -> 식대 포인트 지급<br/>POINT_MINUS -> 식대 포인트 차감<br/>POINT_EXPIRED -> 식대 포인트 소멸<br/>MYPOINT_CHARGE -> 마이포인트 충전
                          bookedType:
                            type: string
                            description: 예약 내역 타입<br/>BOOKED -> 예약완료<br/>BOOKED_PAY -> 예약결제 완료<br/>BOOKED_CANCEL -> 예약 취소</br>BOOKED_PAY_CANCEL -> 예약 결제 취소
                          isCancelable:
                            type: boolean
                            description: 취소 가능 여부<br/>true -> 취소 가능<br/>false -> 취소 불가
                          pin:
                            type: string
                            description: 모바일 상품권 고유 번호
        500:
          $ref: '#/components/responses/ServerError'
  /account/v2/booking:
    get:
      tags:
        - Account
      summary: 예약 함
      description: 메인 > 예약함
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  contents:
                    type: array
                    items:
                      properties:
                        historyId:
                          type: integer
                          description: 고유 ID (상세진입용)
                        couponId:
                          type: string
                          description: 쿠폰 ID
                        storeName:
                          type: string
                          description: 예약 식당 이름
                        mealTime:
                          type: integer
                          description: 식사 시간
                        bookingDate:
                          type: integer
                          description: 예약 일자
                        isCancelable:
                          type: boolean
                          description: 취소 가능 여부</br>true -> 취소 가능</br>false -> 취소 불가
                        menuList:
                          type: array
                          items:
                            properties:
                              menuId:
                                type: string
                                description: 메뉴 ID
                              menuName:
                                type: string
                                description: 메뉴명
                              quantity:
                                type: integer
                                description: 수량
                              price:
                                type: integer
                                description: 금액
                            required:
                              - menuName
                              - quantity
                              - price
                      required:
                        - historyId
                        - couponId
                        - storeName
                        - mealTime
                        - bookingDate
                        - isCancelable
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/push:
    get:
      tags:
        - Account
      summary: 푸쉬 알림함
      description: 메인 > 알림함
      parameters: 
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: page
          description: 페이지 번호
          required: false
          schema:
            type: string
        - in: query
          name: pageRow
          description: 페이지당 항목 수
          required: false
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  contents:
                    type: array
                    items:
                      properties:
                        idx:
                          type: integer
                          description: 고유 idx
                        type:
                          type: string
                          description: 푸쉬 타입<br/>PAYMENT -> 결제<br/>POINT -> 식대<br/>NOTICE -> 공지<br/>BOOKING -> 예약
                        title:
                          type: string
                          description: 제목
                        message:
                          type: string
                          description: 내용
                        action:
                          type: string
                          description: Action Code 값
                        param:
                          type: string
                          description: 이동용 파라미터 값
                        created:
                          type: string
                          format: date
                          description: 전송 날짜 (Long)
                      required:
                        - idx
                        - type
                        - title
                        - message
                        - action
                        - created
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/entrust:
    get:
      tags:
        - Account
      summary: 위임 조회
      description: 단말 > LNB > 결제위임
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/common.entrust.list'
        500:
          $ref: '#/components/responses/ServerError'
    post:
      tags:
        - Account
      summary: 위임 등록
      description: 단말 > LNB > 결제 위임
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                receiveruids:
                  type: array
                  description: 위임자 아이디 목록
                  items:
                    type: string
      responses:
        201:
          description: CREATED
        500:
          $ref: '#/components/responses/ServerError'
    put:
      tags:
        - Account
      summary: 위임 수정
      description: 단말 > LNB > 결제 위임
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                receiveruid:
                  type: string
                  description: 수정할 사용자 아이디
                trust:
                  type: boolean
                  description: 위임여부
              required:
                - receiveruid
                - trust
      responses:
        200:
          description: OK
        500:
          $ref: '#/components/responses/ServerError'
    delete:
      tags:
        - Account
      summary: 위임 삭제
      description: 단말 > LNB > 결제 위임
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: receiveruid
          description: 삭제할 아이디
          required: true
          schema:
            type: string
      responses:
        204:
          description: NO CONTENT
        500:
          $ref: '#/components/responses/ServerError'
  /account/v2/entrust:
    get:
      tags:
        - Account
      summary: 위임 조회
      description: 단말 > LNB > 결제위임
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/common.entrust.list'
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/entrust/me:
    get:
      tags:
        - Account
      summary: 나에게 위임 조회
      description: 단말 > LNB > 결제위임
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/common.entrust.list'
        500:
          $ref: '#/components/responses/ServerError'
  /account/v2/entrust/me:
    get:
      tags:
        - Account
      summary: 나에게 위임 조회
      description: 단말 > LNB > 결제위임
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/common.entrust.list'
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/group:
    get:
      tags:
        - Account
      summary: 그룹 조회
      description: 단말 > LNB > 그룹관리
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  properties:
                    groupid:
                      type: string
                      description: 그룹 번호
                    ownerid:
                      type: string
                      description: 소유자 사용자 아이디
                    name:
                      type: string
                      description: 그룹 이름
                    member:
                      type: integer
                      description: 소속 인원수
                    showorder:
                      type: integer
                      description: 노출 순위
                    description:
                      type: string
                      description: 그룹 설명
                    regdate:
                      type: string
                      format: 'date-time'
                      description: 등록일
                  required:
                    - groupid
                    - ownerid
                    - name
                    - member
                    - showorder
                    - description
                    - regdate
        500:
          $ref: '#/components/responses/ServerError'
    post:
      tags:
        - Account
      summary: 그룹 등록
      description: 단말 > LNB > 그룹 관리
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 그룹 명
                description:
                  type: string
                  description: 그룹 설명
                addusers:
                  type: array
                  description: 추가할 사용자 uid 목록
                  items:
                    type: string
                removeusers:
                  type: array
                  description: 삭제할 사용자 uid 목록
                  items:
                    type: string
              required:
                - name
                - description
      responses:
        201:
          description: CREATED
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/group/{id}:
    get:
      tags:
        - Account
      summary: 그룹 상세 조회
      description: 단말 > LNB > 그룹 관리
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: id
          description: 그룹 아이디
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  properties:
                    division:
                      type: string
                      description: 부서 명
                    uid:
                      type: string
                      description: 사용자 고유 번호
                    name:
                      type: string
                      description: 사용자 이름
                    groupId:
                      type: string
                      description: 그룹 고유 아이디
                    regDate:
                      type: string
                      format: 'date-time'
                      description: 생성일
                    rankposition:
                      type: string
                      description: 직위
                    position:
                      type: string
                      description: 직책
                    signId:
                      type: string
                      description: 회원가입 아이디
                  required:
                    - division
                    - uid
                    - name
                    - groupId
                    - regDate
                    - signId
        500:
          $ref: '#/components/responses/ServerError'
    put:
      tags:
        - Account
      summary: 그룹 수정
      description: 단말 > LNB > 그룹 관리
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: id
          description: 그룹 고유 번호
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: 그룹 명
                description:
                  type: string
                  description: 그룹 설명
                addusers:
                  type: array
                  description: 추가할 사용자 uid 목록
                  items:
                    type: string
                removeusers:
                  type: array
                  description: 삭제할 사용자 uid 목록
                  items:
                    type: string
              required:
                - name
                - description
      responses:
        200:
          description: OK
        500:
          $ref: '#/components/responses/ServerError'
    delete:
      tags:
        - Account
      summary: 그룹 삭제
      description: 단말 > LNB > 그룹 관리
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: receiveruid
          description: 삭제할 아이디
          required: true
          schema:
            type: string
      responses:
        204:
          description: NO CONTENT
        500:
          $ref: '#/components/responses/ServerError'
  /account/v2/group/{id}:
    get:
      tags:
        - Account
      summary: 그룹 상세 조회
      description: 단말 > LNB > 그룹 관리
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: id
          description: 그룹 아이디
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  properties:
                    division:
                      type: string
                      description: 부서 명
                    uid:
                      type: string
                      description: 사용자 고유 번호
                    name:
                      type: string
                      description: 사용자 이름
                    groupId:
                      type: string
                      description: 그룹 고유 아이디
                    regDate:
                      type: string
                      format: 'date-time'
                      description: 생성일
                    rankposition:
                      type: string
                      description: 직위
                    position:
                      type: string
                      description: 직책
                    signId:
                      type: string
                      description: 회원가입 아이디
                  required:
                    - division
                    - uid
                    - name
                    - groupId
                    - regDate
                    - signId
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/info:
    get:
      tags:
        - Account
      summary: 사용자 기본 정보
      description: 단말 > LNB > 설정 > 사용자 정보
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  signId:
                    type: string
                    description: 회원가입 아이디
                  name:
                    type: string
                    description: 사용자 이름
                  email:
                    type: string
                    description: 이메일
                  emailAuth:
                    type: boolean
                    description: 이메일 인증여부 true -> 완료, false -> 필요
                  phone:
                    type: string
                    description: 전화번호
                  smsAuth:
                    type: boolean
                    description: 전화번호 인증여부 true -> 완료, false -> 필요
                  rankposition:
                    type: string
                    description: 직위
                  position:
                    type: string
                    description: 직책
                  divisions:
                    type: array
                    description: 부서 (하위 -> 상위)
                    items:
                      type: string
                required:
                  - signId
                  - name
                  - divisions
        500:
          $ref: '#/components/responses/ServerError'
  /account/v2/info:
    get:
      tags:
        - Account
      summary: 사용자 기본 정보
      description: 단말 > LNB > 설정 > 사용자 정보
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  signId:
                    type: string
                    description: 회원가입 아이디
                  name:
                    type: string
                    description: 사용자 이름
                  email:
                    type: string
                    description: 이메일
                  emailAuth:
                    type: boolean
                    description: 이메일 인증여부 true -> 완료, false -> 필요
                  phone:
                    type: string
                    description: 전화번호
                  smsAuth:
                    type: boolean
                    description: 전화번호 인증여부 true -> 완료, false -> 필요
                  rankposition:
                    type: string
                    description: 직위
                  position:
                    type: string
                    description: 직책
                  divisions:
                    type: array
                    description: 부서 (하위 -> 상위)
                    items:
                      type: string
                  captainPay:
                    type: object
                    description: 대장페이 정보
                    properties:
                      cardCount:
                        type: integer
                        description: 등록 카드 수
                      password:
                        type: boolean
                        description: 대장페이 비밀번호 등록 여부 true -> 등록, false -> 미등록
                      isCertification:
                        type: boolean
                        description: 본인인증 여부 true -> 인증, false -> 미인증
                required:
                  - signId
                  - name
                  - divisions
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/demand:
    get:
      tags:
        - Account
      summary: 식권신청 조회
      description: 단말 > LNB > 식권신청
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    type: object
                    properties:
                      name:
                        type: string
                        description: 이름
                      division:
                        type: string
                        description: 부서명
                    required:
                      - name
                      - diision
                  demand:
                    type: object
                    properties:
                      accept:
                        type: boolean
                        description: 승인 여부
                      charge:
                        type: boolean
                        description: 충전 여부
                      reason:
                        type: string
                        description: 신청 사유
                      date:
                        type: string
                        format: 'date-time'
                        description: 신청 시간
                    required:
                      - accept
                      - charge
                      - reason
                      - date
        500:
          $ref: '#/components/responses/ServerError'
    post:
      tags:
        - Account
      summary: 식권신청 등록
      description: 단말 > LNB > 식권신청
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: 신청 사유
      responses:
        201:
          description: CREATED
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    type: object
                    properties:
                      name:
                        type: string
                        description: 이름
                      division:
                        type: string
                        description: 부서명
                    required:
                      - name
                      - diision
                  demand:
                    type: object
                    properties:
                      accept:
                        type: boolean
                        description: 승인 여부
                      charge:
                        type: boolean
                        description: 충전 여부
                      reason:
                        type: string
                        description: 신청 사유
                      date:
                        type: string
                        format: 'date-time'
                        description: 신청 시간
                    required:
                      - accept
                      - charge
                      - reason
                      - date
        500:
          $ref: '#/components/responses/ServerError'
    delete:
      tags:
        - Account
      summary: 식권신청 취소
      description: 단말 > LNB > 식권신청
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        204:
          description: NOT CONTENT
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/mypoint/nice:
    get:
      tags:
        - Account
      summary: NICE 결제 페이지 호출
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/password:
    put:
      tags:
        - Account
      summary: 최초 비밀번호 설정
      description: 로그인 > 앱내 최초 비밀번호 변경
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                password:
                  type: string
                  description: 변경할 비밀번호
              required:
                - password
      responses:
        200:
          description: OK
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/point:
    get:
      tags:
        - Account
      summary: 식대 정책 사용 포인트
      description: 단말 > 메인 > 식사하기 > 사용포인트
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 사용자 고유 아이디
                      name:
                        type: string
                        description: 이름
                    required:
                      - id
                      - name
                  policy:
                    type: object
                    properties:
                      day:
                        type: array
                        items:
                          $ref: '#/components/schemas/common.point.list'
                      longterm:
                        type: array
                        items:
                          $ref: '#/components/schemas/common.point.list'
                      infinite:
                        type: array
                        items:
                          $ref: '#/components/schemas/common.point.list'
                      demand:
                        type: array
                        items:
                          $ref: '#/components/schemas/common.point.list'
                      etc:
                        type: object
                        properties:
                          name:
                            type: string
                            description: 마이 포인트 이름
                          amount:
                            type: integer
                            description: 포인트 금액
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/point/{policyIdx}:
    get:
      tags:
        - Account
      summary: 식대 정책 사용 포인트 내역
      description: 단말 > 메인 > 식사하기 > 사용포인트
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in : path
          name: policyIdx
          description: 정책 고유 아이디
          required: true
          schema:
            type: integer
        - in : query
          name: idx
          description: 마지막 컨텐츠 고유 번호
          schema:
            type: integer
        - in : query
          name: size
          description: 페이지 사이즈
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  history:
                    type: array
                    items:
                      properties:
                        idx:
                          type: integer
                          description: 정책 고유 번호
                        cause:
                          type: string
                          description: 사유
                        amount:
                          type: integer
                          description: 금액
                        balance:
                          type: integer
                          description: 잔액
                        regDate:
                          type: string
                          format: 'date-time'
                          description: 등록 시간
                        expireDate:
                          type: string
                          format: 'date-time'
                          description: 만료 시간
                        active:
                          type: boolean
                          description: 내역 설정
                      required:
                        - idx
                        - cause
                        - amount
                        - balance
                        - regDate
                        - expireDate
                        - active
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/policy:
    get:
      tags:
        - Account
      summary: 식대 정책 지급 포인트
      description: 단말 > 메인 > 식사하기 > 지급포인트
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in : query
          name: sid
          description: 제휴점 고유 아이디
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 사용자 고유 아이디
                      name:
                        type: string
                        description: 이름
                    required:
                      - id
                      - name
                  policy:
                    type: object
                    properties:
                      day:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 명
                            amount:
                              type: integer
                              description: 포인트 금액
                            startTime:
                              type: string
                              format: 'date-time'
                              description: 시작 시간
                            endTime:
                              type: string
                              format: 'date-time'
                              description: 마지막 시간
                            isAvailable:
                              type: boolean
                              description: 사용 가능 여부 true -> 가능, false -> 불가능
                            isGroupPay:
                              type: boolean
                              description: 함계 결제 제한 여부 true -> 가능, false -> 불가능
                            isPresent:
                              type: boolean
                              description: 선물하기 사용 가능 여부 true -> 가능, false -> 불가능
                            isStore:
                              type: boolean
                              description: 제휴점 제한 여부 true -> 가능, false -> 불가능
                          required:
                            - id
                            - name
                            - amount
                            - startTime
                            - endTime
                            - isAvailable
                            - isGroupPay
                            - isPresent
                            - isStore
                      longterm:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 명
                            amount:
                              type: integer
                              description: 포인트 금액
                            startDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 시작 시간
                            endDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 마지막 시간
                            startTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 시작 시간
                            endTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 마지막 시간
                            isAvailable:
                              type: boolean
                              description: 사용 가능 여부 true -> 가능, false -> 불가능
                            isGroupPay:
                              type: boolean
                              description: 함계 결제 제한 여부 true -> 가능, false -> 불가능
                            isPresent:
                              type: boolean
                              description: 선물하기 사용 가능 여부 true -> 가능, false -> 불가능
                            isStore:
                              type: boolean
                              description: 제휴점 제한 여부 true -> 가능, false -> 불가능
                            expire:
                              type: object
                              properties:
                                date:
                                  type: string
                                  format: 'date-time'
                                  description: 정책 만료 일자
                                amount:
                                  type: integer
                                  description: 만료 금액
                              required:
                                - date
                                - amount
                          required:
                            - id
                            - name
                            - amount
                            - startDate
                            - endDate
                            - startTime
                            - endTime
                            - isAvailable
                            - isGroupPay
                            - isPresent
                            - isStore
                      infinite:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 명
                            amount:
                              type: integer
                              description: 포인트 금액
                            startDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 시작 시간
                            endDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 마지막 시간
                            startTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 시작 시간
                            endTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 마지막 시간
                            isAvailable:
                              type: boolean
                              description: 사용 가능 여부 true -> 가능, false -> 불가능
                            isGroupPay:
                              type: boolean
                              description: 함계 결제 제한 여부 true -> 가능, false -> 불가능
                            isPresent:
                              type: boolean
                              description: 선물하기 사용 가능 여부 true -> 가능, false -> 불가능
                            isStore:
                              type: boolean
                              description: 제휴점 제한 여부 true -> 가능, false -> 불가능
                          required:
                            - id
                            - name
                            - amount
                            - startDate
                            - endDate
                            - startTime
                            - endTime
                            - isAvailable
                            - isGroupPay
                            - isPresent
                            - isStore
                      demand:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 명
                            amount:
                              type: integer
                              description: 포인트 금액
                            startDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 시작 시간
                            endDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 마지막 시간
                            startTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 시작 시간
                            endTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 마지막 시간
                            isAvailable:
                              type: boolean
                              description: 사용 가능 여부 true -> 가능, false -> 불가능
                            isGroupPay:
                              type: boolean
                              description: 함계 결제 제한 여부 true -> 가능, false -> 불가능
                            isPresent:
                              type: boolean
                              description: 선물하기 사용 가능 여부 true -> 가능, false -> 불가능
                            isStore:
                              type: boolean
                              description: 제휴점 제한 여부 true -> 가능, false -> 불가능
                          required:
                            - id
                            - name
                            - amount
                            - startDate
                            - endDate
                            - startTime
                            - endTime
                            - isAvailable
                            - isGroupPay
                            - isPresent
                            - isStore
                      mypoint:
                        type: object
                        properties:
                          name:
                            type: string
                            description: 마이 포인트 이름
                          amount:
                            type: integer
                            description: 포인트 금액
        500:
          $ref: '#/components/responses/ServerError'
  /account/v2/policy:
    get:
      tags:
        - Account
      summary: 식대 정책 지급 포인트
      deprecated: true
      description: 단말 > 메인 > 식사하기 > 지급포인트 <br />
        공휴일 식대 사용 제한 추가로 deprecated 처리<br />
        단 해당 API 에서 공휴일 제한은 invalidStatus-> WEEK 상태와 같이 사용<br />
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in : query
          name: sid
          description: 제휴점 고유 아이디
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 사용자 고유 아이디
                      name:
                        type: string
                        description: 이름
                    required:
                      - id
                      - name
                  policy:
                    type: object
                    properties:
                      day:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 명
                            amount:
                              type: integer
                              description: 포인트 금액
                            startTime:
                              type: string
                              format: 'date-time'
                              description: 시작 시간
                            endTime:
                              type: string
                              format: 'date-time'
                              description: 마지막 시간
                            isAvailable:
                              type: boolean
                              description: 사용 가능 여부 true -> 가능, false -> 불가능
                            isGroupPay:
                              type: boolean
                              description: 함계 결제 제한 여부 true -> 가능, false -> 불가능
                            isPresent:
                              type: boolean
                              description: 선물하기 사용 가능 여부 true -> 가능, false -> 불가능
                            isStore:
                              type: boolean
                              description: 제휴점 제한 여부 true -> 가능, false -> 불가능
                            type:
                              type: string
                              enum: [DAY, DEMAND, LONGTERM, INFINITE, MYPOINT]
                              description: DAY -> 일일, DEMAND -> 신청, LONGTERM -> 장기, INFINITE -> 무제한, MYPOINT -> 대장포인트
                            usedAmount:
                              type: integer
                              description: 사용 금액
                            availableWeek:
                              type: string
                              description: 사용 요일
                            invalidStatus:
                              type: string
                              enum: [WEEK, TIME]
                              description: WEEK -> 요일 안됨, TIME -> 시간 안됨
                            invalidText:
                              type: string
                              description: 사용 불가 사유
                            limitPayCount:
                              type: integer
                              description: 횟수 제한 개수
                            limitAmount:
                              type: integer
                              description: 결제 금액 제한
                          required:
                            - id
                            - name
                            - amount
                            - startTime
                            - endTime
                            - isAvailable
                            - isGroupPay
                            - isPresent
                            - isStore
                            - type
                            - usedAmount
                            - availableWeek
                      longterm:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 명 메모
                            amount:
                              type: integer
                              description: 포인트 금액
                            startDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 시작 시간
                            endDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 마지막 시간
                            startTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 시작 시간
                            endTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 마지막 시간
                            isAvailable:
                              type: boolean
                              description: 사용 가능 여부 true -> 가능, false -> 불가능
                            isGroupPay:
                              type: boolean
                              description: 함계 결제 제한 여부 true -> 가능, false -> 불가능
                            isPresent:
                              type: boolean
                              description: 선물하기 사용 가능 여부 true -> 가능, false -> 불가능
                            isStore:
                              type: boolean
                              description: 제휴점 제한 여부 true -> 가능, false -> 불가능
                            expire:
                              type: object
                              properties:
                                date:
                                  type: string
                                  format: 'date-time'
                                  description: 정책 만료 일자
                                amount:
                                  type: integer
                                  description: 만료 금액
                                range:
                                  type: integer
                                  description: 정책 만료 일자 0 -> 오늘 소멸 예정 ,1 -> 내일 소멸 예정 ,2 ~ -> XX일 내 소멸 예정
                                text:
                                  type: string
                                  description: 정책 만료 표시 문구
                                order:
                                  type: string
                                  description: 정책 만료 표시 기준 (대문자) ,SHORT -> 짧은것 노출 ,LONG -> 긴것 노출
                              required:
                                - date
                                - amount
                                - range
                                - text
                                - order
                            type:
                              type: string
                              enum: [DAY, DEMAND, LONGTERM, INFINITE, MYPOINT]
                              description: DAY -> 일일, DEMAND -> 신청, LONGTERM -> 장기, INFINITE -> 무제한, MYPOINT -> 대장포인트
                            usedAmount:
                              type: integer
                              description: 사용 금액
                            availableWeek:
                              type: string
                              description: 사용 요일
                            invalidStatus:
                              type: string
                              enum: [WEEK, TIME]
                              description: WEEK -> 요일 안됨, TIME -> 시간 안됨
                            invalidText:
                              type: string
                              description: 사용 불가 사유
                            limitPayCount:
                              type: integer
                              description: 횟수 제한 개수
                            limitAmount:
                              type: integer
                              description: 결제 금액 제한
                            depositOffset:
                              type: string
                              description: 지급일
                            depositRange:
                              type: string
                              description: 유효기간
                          required:
                            - id
                            - name
                            - amount
                            - startDate
                            - endDate
                            - startTime
                            - endTime
                            - isAvailable
                            - isGroupPay
                            - isPresent
                            - isStore
                            - type
                            - usedAmount
                            - availableWeek
                            - depositOffset
                            - depositRange
                      infinite:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 명
                            amount:
                              type: integer
                              description: 포인트 금액
                            startDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 시작 시간
                            endDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 마지막 시간
                            startTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 시작 시간
                            endTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 마지막 시간
                            isAvailable:
                              type: boolean
                              description: 사용 가능 여부 true -> 가능, false -> 불가능
                            isGroupPay:
                              type: boolean
                              description: 함계 결제 제한 여부 true -> 가능, false -> 불가능
                            isPresent:
                              type: boolean
                              description: 선물하기 사용 가능 여부 true -> 가능, false -> 불가능
                            isStore:
                              type: boolean
                              description: 제휴점 제한 여부 true -> 가능, false -> 불가능
                            type:
                              type: string
                              enum: [DAY, DEMAND, LONGTERM, INFINITE, MYPOINT]
                              description: DAY -> 일일, DEMAND -> 신청, LONGTERM -> 장기, INFINITE -> 무제한, MYPOINT -> 대장포인트
                            usedAmount:
                              type: integer
                              description: 사용 금액
                            availableWeek:
                              type: string
                              description: 사용 요일
                            invalidStatus:
                              type: string
                              enum: [WEEK, TIME]
                              description: WEEK -> 요일 안됨, TIME -> 시간 안됨
                            invalidText:
                              type: string
                              description: 사용 불가 사유
                            limitPayCount:
                              type: integer
                              description: 횟수 제한 개수
                            limitAmount:
                              type: integer
                              description: 결제 금액 제한
                          required:
                            - id
                            - name
                            - amount
                            - startDate
                            - endDate
                            - startTime
                            - endTime
                            - isAvailable
                            - isGroupPay
                            - isPresent
                            - isStore
                            - type
                            - usedAmount
                            - availableWeek
                      demand:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 명
                            amount:
                              type: integer
                              description: 포인트 금액
                            startDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 시작 시간
                            endDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 마지막 시간
                            startTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 시작 시간
                            endTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 마지막 시간
                            isAvailable:
                              type: boolean
                              description: 사용 가능 여부 true -> 가능, false -> 불가능
                            isGroupPay:
                              type: boolean
                              description: 함계 결제 제한 여부 true -> 가능, false -> 불가능
                            isPresent:
                              type: boolean
                              description: 선물하기 사용 가능 여부 true -> 가능, false -> 불가능
                            isStore:
                              type: boolean
                              description: 제휴점 제한 여부 true -> 가능, false -> 불가능
                            type:
                              type: string
                              enum: [DAY, DEMAND, LONGTERM, INFINITE, MYPOINT]
                              description: DAY -> 일일, DEMAND -> 신청, LONGTERM -> 장기, INFINITE -> 무제한, MYPOINT -> 대장포인트
                            usedAmount:
                              type: integer
                              description: 사용 금액
                            availableWeek:
                              type: string
                              description: 사용 요일
                            invalidStatus:
                              type: string
                              enum: [WEEK, TIME]
                              description: WEEK -> 요일 안됨, TIME -> 시간 안됨
                            invalidText:
                              type: string
                              description: 사용 불가 사유
                            limitPayCount:
                              type: integer
                              description: 횟수 제한 개수
                            limitAmount:
                              type: integer
                              description: 결제 금액 제한
                            demandStartTime:
                              type: string
                              format: 'date-time'
                              description: 신청 가능 시작 시간
                            demandEndTime:
                              type: string
                              format: 'date-time'
                              description: 신청 가능 마지막 시간
                            otpType:
                              type: string
                              enum:
                                - OTP
                                - DEMAND
                                - NONE
                              description: 식권 발급기 관련 내용(OTP -> 바코드, DEMAND -> 신청 화면, NONE -> 사용안함)
                            demandIdx:
                              type: integer
                              format: int64
                              description: 신청 고유 아이디
                            isDemandAvailable:
                              type: boolean
                              description: 신청 가능 여부 true -> 가능, false -> 불가능
                            depositAmount:
                              type: integer
                              description: 지급 받을 설정된 금액
                          required:
                            - id
                            - name
                            - amount
                            - startDate
                            - endDate
                            - startTime
                            - endTime
                            - isAvailable
                            - isGroupPay
                            - isPresent
                            - isStore
                            - type
                            - usedAmount
                            - availableWeek
                            - demandStartTime
                            - demandEndTime
                            - otpType
                            - demandIdx
                            - isDemandAvailable
                            - depositAmount
                      mypoint:
                        type: object
                        properties:
                          name:
                            type: string
                            description: 대장 포인트 이름
                          amount:
                            type: integer
                            description: 포인트 금액
                          type:
                            type: string
                            enum: [DAY, DEMAND, LONGTERM, INFINITE, MYPOINT]
                            description: DAY -> 일일, DEMAND -> 신청, LONGTERM -> 장기, INFINITE -> 무제한, MYPOINT -> 대장포인트
                          usedAmount:
                            type: integer
                            description: 사용 금액
                        required:
                          - name
                          - amount
                          - type
                          - usedAmount
                  budget:
                    type: integer
                    description: 예산 정보 (-1 -> 사용안함)
                required:
                  - user
                  - policy
                  - budget
        500:
          $ref: '#/components/responses/ServerError'
  /account/v3/policy:
    get:
      tags:
        - Account
      summary: 식대 정책 지급 포인트 (황상현)
      description: 단말 > 메인 > 식사하기 > 지급포인트 <br />
        공휴일 정책 제한 추가
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: sid
          description: 제휴점 고유 아이디
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    type: object
                    properties:
                      id:
                        type: string
                        description: 사용자 고유 아이디
                      name:
                        type: string
                        description: 이름
                    required:
                      - id
                      - name
                  policy:
                    type: object
                    properties:
                      day:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 명
                            amount:
                              type: integer
                              description: 포인트 금액
                            startTime:
                              type: string
                              format: 'date-time'
                              description: 시작 시간
                            endTime:
                              type: string
                              format: 'date-time'
                              description: 마지막 시간
                            isAvailable:
                              type: boolean
                              description: 사용 가능 여부 true -> 가능, false -> 불가능
                            isGroupPay:
                              type: boolean
                              description: 함계 결제 제한 여부 true -> 가능, false -> 불가능
                            isPresent:
                              type: boolean
                              description: 선물하기 사용 가능 여부 true -> 가능, false -> 불가능
                            isStore:
                              type: boolean
                              description: 제휴점 제한 여부 true -> 가능, false -> 불가능
                            type:
                              type: string
                              enum: [DAY, DEMAND, LONGTERM, INFINITE, MYPOINT]
                              description: DAY -> 일일, DEMAND -> 신청, LONGTERM -> 장기, INFINITE -> 무제한, MYPOINT -> 대장포인트
                            usedAmount:
                              type: integer
                              description: 사용 금액
                            availableWeek:
                              type: string
                              description: 사용 요일
                            invalidStatus:
                              type: string
                              enum: [WEEK, TIME, HOLIDAY]
                              description: WEEK -> 요일 안됨, TIME -> 시간 안됨, HOLIDAY -> 공휴일
                            invalidText:
                              type: string
                              description: 사용 불가 사유
                            limitPayCount:
                              type: integer
                              description: 횟수 제한 개수
                            limitAmount:
                              type: integer
                              description: 결제 금액 제한
                          required:
                            - id
                            - name
                            - amount
                            - startTime
                            - endTime
                            - isAvailable
                            - isGroupPay
                            - isPresent
                            - isStore
                            - type
                            - usedAmount
                            - availableWeek
                      longterm:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 명 메모
                            amount:
                              type: integer
                              description: 포인트 금액
                            startDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 시작 시간
                            endDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 마지막 시간
                            startTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 시작 시간
                            endTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 마지막 시간
                            isAvailable:
                              type: boolean
                              description: 사용 가능 여부 true -> 가능, false -> 불가능
                            isGroupPay:
                              type: boolean
                              description: 함계 결제 제한 여부 true -> 가능, false -> 불가능
                            isPresent:
                              type: boolean
                              description: 선물하기 사용 가능 여부 true -> 가능, false -> 불가능
                            isStore:
                              type: boolean
                              description: 제휴점 제한 여부 true -> 가능, false -> 불가능
                            expire:
                              type: object
                              properties:
                                date:
                                  type: string
                                  format: 'date-time'
                                  description: 정책 만료 일자
                                amount:
                                  type: integer
                                  description: 만료 금액
                                range:
                                  type: integer
                                  description: 정책 만료 일자 0 -> 오늘 소멸 예정 ,1 -> 내일 소멸 예정 ,2 ~ -> XX일 내 소멸 예정
                                text:
                                  type: string
                                  description: 정책 만료 표시 문구
                                order:
                                  type: string
                                  description: 정책 만료 표시 기준 (대문자) ,SHORT -> 짧은것 노출 ,LONG -> 긴것 노출
                              required:
                                - date
                                - amount
                                - range
                                - text
                                - order
                            type:
                              type: string
                              enum: [DAY, DEMAND, LONGTERM, INFINITE, MYPOINT]
                              description: DAY -> 일일, DEMAND -> 신청, LONGTERM -> 장기, INFINITE -> 무제한, MYPOINT -> 대장포인트
                            usedAmount:
                              type: integer
                              description: 사용 금액
                            availableWeek:
                              type: string
                              description: 사용 요일
                            invalidStatus:
                              type: string
                              enum: [WEEK, TIME, HOLIDAY]
                              description: WEEK -> 요일 안됨, TIME -> 시간 안됨, HOLIDAY -> 공휴일
                            invalidText:
                              type: string
                              description: 사용 불가 사유
                            limitPayCount:
                              type: integer
                              description: 횟수 제한 개수
                            limitAmount:
                              type: integer
                              description: 결제 금액 제한
                            depositOffset:
                              type: string
                              description: 지급일
                            depositRange:
                              type: string
                              description: 유효기간
                          required:
                            - id
                            - name
                            - amount
                            - startDate
                            - endDate
                            - startTime
                            - endTime
                            - isAvailable
                            - isGroupPay
                            - isPresent
                            - isStore
                            - type
                            - usedAmount
                            - availableWeek
                            - depositOffset
                            - depositRange
                      infinite:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 명
                            amount:
                              type: integer
                              description: 포인트 금액
                            startDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 시작 시간
                            endDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 마지막 시간
                            startTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 시작 시간
                            endTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 마지막 시간
                            isAvailable:
                              type: boolean
                              description: 사용 가능 여부 true -> 가능, false -> 불가능
                            isGroupPay:
                              type: boolean
                              description: 함계 결제 제한 여부 true -> 가능, false -> 불가능
                            isPresent:
                              type: boolean
                              description: 선물하기 사용 가능 여부 true -> 가능, false -> 불가능
                            isStore:
                              type: boolean
                              description: 제휴점 제한 여부 true -> 가능, false -> 불가능
                            type:
                              type: string
                              enum: [DAY, DEMAND, LONGTERM, INFINITE, MYPOINT]
                              description: DAY -> 일일, DEMAND -> 신청, LONGTERM -> 장기, INFINITE -> 무제한, MYPOINT -> 대장포인트
                            usedAmount:
                              type: integer
                              description: 사용 금액
                            availableWeek:
                              type: string
                              description: 사용 요일
                            invalidStatus:
                              type: string
                              enum: [WEEK, TIME, HOLIDAY]
                              description: WEEK -> 요일 안됨, TIME -> 시간 안됨, HOLIDAY -> 공휴일
                            invalidText:
                              type: string
                              description: 사용 불가 사유
                            limitPayCount:
                              type: integer
                              description: 횟수 제한 개수
                            limitAmount:
                              type: integer
                              description: 결제 금액 제한
                          required:
                            - id
                            - name
                            - amount
                            - startDate
                            - endDate
                            - startTime
                            - endTime
                            - isAvailable
                            - isGroupPay
                            - isPresent
                            - isStore
                            - type
                            - usedAmount
                            - availableWeek
                      demand:
                        type: array
                        items:
                          properties:
                            id:
                              type: integer
                              description: 정책 고유 아이디
                            name:
                              type: string
                              description: 정책 명
                            amount:
                              type: integer
                              description: 포인트 금액
                            startDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 시작 시간
                            endDate:
                              type: string
                              format: 'date-time'
                              description: 유효 기간 마지막 시간
                            startTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 시작 시간
                            endTime:
                              type: string
                              format: 'date-time'
                              description: 사용 가능 마지막 시간
                            isAvailable:
                              type: boolean
                              description: 사용 가능 여부 true -> 가능, false -> 불가능
                            isGroupPay:
                              type: boolean
                              description: 함계 결제 제한 여부 true -> 가능, false -> 불가능
                            isPresent:
                              type: boolean
                              description: 선물하기 사용 가능 여부 true -> 가능, false -> 불가능
                            isStore:
                              type: boolean
                              description: 제휴점 제한 여부 true -> 가능, false -> 불가능
                            type:
                              type: string
                              enum: [DAY, DEMAND, LONGTERM, INFINITE, MYPOINT]
                              description: DAY -> 일일, DEMAND -> 신청, LONGTERM -> 장기, INFINITE -> 무제한, MYPOINT -> 대장포인트
                            usedAmount:
                              type: integer
                              description: 사용 금액
                            availableWeek:
                              type: string
                              description: 사용 요일
                            invalidStatus:
                              type: string
                              enum: [WEEK, TIME, HOLIDAY]
                              description: WEEK -> 요일 안됨, TIME -> 시간 안됨, HOLIDAY -> 공휴일
                            invalidText:
                              type: string
                              description: 사용 불가 사유
                            limitPayCount:
                              type: integer
                              description: 횟수 제한 개수
                            limitAmount:
                              type: integer
                              description: 결제 금액 제한
                            demandStartTime:
                              type: string
                              format: 'date-time'
                              description: 신청 가능 시작 시간
                            demandEndTime:
                              type: string
                              format: 'date-time'
                              description: 신청 가능 마지막 시간
                            otpType:
                              type: string
                              enum:
                                - OTP
                                - DEMAND
                                - NONE
                              description: 식권 발급기 관련 내용(OTP -> 바코드, DEMAND -> 신청 화면, NONE -> 사용안함)
                            demandIdx:
                              type: integer
                              format: int64
                              description: 신청 고유 아이디
                            isDemandAvailable:
                              type: boolean
                              description: 신청 가능 여부 true -> 가능, false -> 불가능
                            depositAmount:
                              type: integer
                              description: 지급 받을 설정된 금액
                          required:
                            - id
                            - name
                            - amount
                            - startDate
                            - endDate
                            - startTime
                            - endTime
                            - isAvailable
                            - isGroupPay
                            - isPresent
                            - isStore
                            - type
                            - usedAmount
                            - availableWeek
                            - demandStartTime
                            - demandEndTime
                            - otpType
                            - demandIdx
                            - isDemandAvailable
                            - depositAmount
                      mypoint:
                        type: object
                        properties:
                          name:
                            type: string
                            description: 대장 포인트 이름
                          amount:
                            type: integer
                            description: 포인트 금액
                          type:
                            type: string
                            enum: [DAY, DEMAND, LONGTERM, INFINITE, MYPOINT]
                            description: DAY -> 일일, DEMAND -> 신청, LONGTERM -> 장기, INFINITE -> 무제한, MYPOINT -> 대장포인트
                          usedAmount:
                            type: integer
                            description: 사용 금액
                          expire:
                            type: object
                            properties:
                              amount:
                                type: integer
                                description: 만료 금액
                              text:
                                type: string
                                description: 만료 표시 문구 (30일 내 소멸 예정 5,000)
                            required:
                              - amount
                              - text
                        required:
                          - name
                          - amount
                          - type
                          - usedAmount
                  budget:
                    type: integer
                    description: 예산 정보 (-1 -> 사용안함)
                required:
                  - user
                  - policy
                  - budget
        500:
          $ref: '#/components/responses/ServerError'

  /account/v1/policy/{policyIdx}:
    get:
      tags:
        - Account
      summary: 개인 식대정책 지급/사용 내역
      description: 단말 > 메인 > 식사하기 > 지급/사용 내역
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in : path
          name: policyIdx
          description: 정책 고유 아이디
          required: true
          schema:
            type: integer
        - in : query
          name: idx
          description: 마지막 컨텐츠 고유 번호
          schema:
            type: integer
        - in : query
          name: size
          description: 페이지 사이즈
          schema:
            type: integer
        - in: query
          name: type
          description: 요청에 대한 타입 withdraw -> 사용내역 ,deposit -> 지급내역
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  history:
                    type: array
                    items:
                      properties:
                        idx:
                          type: integer
                          description: 정책 고유 번호
                        cause:
                          type: string
                          description: 사유
                        amount:
                          type: integer
                          description: 금액
                        balance:
                          type: integer
                          description: 잔액
                        regDate:
                          type: string
                          format: 'date-time'
                          description: 등록 시간
                        expireDate:
                          type: string
                          format: 'date-time'
                          description: 만료 시간
                        active:
                          type: boolean
                          description: 내역 설정
                      required:
                        - idx
                        - cause
                        - amount
                        - balance
                        - regDate
                        - expireDate
                        - active
        500:
          $ref: '#/components/responses/ServerError'
  /account/v2/policy/{policyIdx}:
    get:
      tags:
        - Account
      summary: 개인 식대정책 지급/사용 내역
      description: 단말 > 메인 > 식사하기 > 지급/사용 내역
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in : path
          name: policyIdx
          description: 정책 고유 아이디
          required: true
          schema:
            type: integer
        - in : query
          name: idx
          description: 마지막 컨텐츠 고유 번호
          schema:
            type: integer
        - in : query
          name: size
          description: 페이지 사이즈
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  history:
                    type: array
                    items:
                      properties:
                        idx:
                          type: integer
                          description: 정책 고유 번호
                        cause:
                          type: string
                          description: 사유
                        amount:
                          type: integer
                          description: 금액
                        balance:
                          type: integer
                          description: 잔액
                        regDate:
                          type: string
                          format: 'date-time'
                          description: 등록 시간
                        expireDate:
                          type: string
                          format: 'date-time'
                          description: 만료 시간
                        active:
                          type: boolean
                          description: 내역 설정
                      required:
                        - idx
                        - cause
                        - amount
                        - balance
                        - regDate
                        - expireDate
                        - active
        500:
          $ref: '#/components/responses/ServerError'
  /account/v3/policy/{policyIdx}:
    get:
      tags:
        - Account
      summary: 개인 식대정책 지급/사용 내역
      description: 단말 > 메인 > 식사하기 > 지급/사용 내역
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in : path
          name: policyIdx
          description: 정책 고유 아이디
          required: true
          schema:
            type: integer
        - in : query
          name: idx
          description: 마지막 컨텐츠 고유 번호
          schema:
            type: integer
        - in : query
          name: size
          description: 페이지 사이즈
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  history:
                    type: array
                    items:
                      properties:
                        idx:
                          type: integer
                          description: 정책 고유 번호
                        cause:
                          type: string
                          description: 사유
                        amount:
                          type: integer
                          description: 금액
                        balance:
                          type: integer
                          description: 잔액
                        regDate:
                          type: string
                          format: 'date-time'
                          description: 등록 시간
                        expireDate:
                          type: string
                          format: 'date-time'
                          description: 만료 시간
                        active:
                          type: boolean
                          description: 내역 설정
                      required:
                        - idx
                        - cause
                        - amount
                        - balance
                        - regDate
                        - expireDate
                        - active
        500:
          $ref: '#/components/responses/ServerError'
  /account/v4/policy/{policyIdx}:
    get:
      tags:
        - Account
      summary: 개인 식대정책 지급/사용 내역
      description: 단말 > 메인 > 식사하기 > 지급/사용 내역
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in : path
          name: policyIdx
          description: 정책 고유 아이디
          required: true
          schema:
            type: integer
        - in : query
          name: idx
          description: 마지막 컨텐츠 고유 번호
          schema:
            type: integer
        - in : query
          name: size
          description: 페이지 사이즈
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  history:
                    type: array
                    items:
                      properties:
                        idx:
                          type: integer
                          description: 정책 고유 번호
                        cause:
                          type: string
                          description: 사유
                        amount:
                          type: integer
                          description: 금액
                        balance:
                          type: integer
                          description: 잔액
                        regDate:
                          type: string
                          format: 'date-time'
                          description: 등록 시간
                        expireDate:
                          type: string
                          format: 'date-time'
                          description: 만료 시간
                        active:
                          type: boolean
                          description: 내역 설정
                        type:
                          type: string
                          description: I -> 지급, O -> 차감
                      required:
                        - idx
                        - cause
                        - amount
                        - balance
                        - regDate
                        - expireDate
                        - active
                        - type
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/policy/history:
    get:
      tags:
        - Account
      summary: 정책 모아보기 히스토리
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: policyIdx
          description: 정책 고유 IDX
          required: false
          schema:
            type: integer
        - in: query
          name: type
          description: 정책 TYPE (DAY->일별, DEMAND->요청, LONGTERM->장기, INFINITE->무제한, MYPOINT->대장포인트)
          required: true
          schema:
            type: string
        - in: query
          name: page
          description: 페이지 번호
          required: false
          schema:
            type: string
        - in: query
          name: pageRow
          description: 페이지당 항목 수
          required: false
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  contents:
                    type: array
                    items:
                      properties:
                        id:
                          type: string
                          description: 고유 ID (상세진입용)
                        title:
                          type: string
                          description: 제목
                        type:
                          type: string
                          description: 내역 타입<br/>USED -> 혼자결제<br/>MULTI_USED -> 함께결제<br/>ADMIN_USED -> 관리자 혼자결제<br/>ADMIN_MULTI_USED -> 관리자 함께결제<br/>QPCON_USED -> 모바일상품권 결제<br/>QPCON_EXPIRED -> 모바일상품권 소멸<br/>CANCEL -> 결제 취소<br/>POINT_PLUS -> 식대 포인트 지급<br/>POINT_MINUS -> 식대 포인트 차감<br/>POINT_EXPIRED -> 식대 포인트 소멸<br/>MYPOINT_CHARGE -> 대장포인트 충전
                        description:
                          type: string
                          description: 내용
                        amount:
                          type: integer
                          description: 금액
                        totalAmount:
                          type: integer
                          description: 총 금액
                        useDate:
                          type: string
                          format: date
                          description: 사용 날짜 (Long)
                      required:
                        - id
                        - title
                        - type
                        - amount
                        - useDate
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/present:
    post:
      tags:
        - Account
      summary: 최초 비밀번호 설정
      description: 로그인 > 앱내 최초 비밀번호 변경
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                present:
                  type: object
                  properties:
                    user:
                      type: object
                      properties:
                        id:
                          type: string
                          description: 받은 사람 고유 아이디
                        name:
                          type: string
                          description: 받는 사람 이름
                      required:
                        - id
                        - name
                    policy:
                      type: array
                      items:
                        properties:
                          idx:
                            type: integer
                            description: 정책 고유 아이디
                          amount:
                            type: integer
                            description: 선물할 포인트 금액
                        required:
                          - idx
                          - amount
              required:
                - user
                - policy
      responses:
        201:
          description: CREATED
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/setting/phone:
    put:
      tags:
        - Account
      summary: 핸드폰 번호 변경
      description: 단말 > LNB > 설정 > 수정
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                phone:
                  type: string
                  description: 전화번호
              required:
                - phone
      responses:
        200:
          description: OK
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/setting/email:
    put:
      tags:
        - Account
      summary: 이메일 변경
      description: 단말 > LNB > 설정 > 수정
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  description: 이메일
              required:
                - email
      responses:
        200:
          description: OK
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/setting/position:
    put:
      tags:
        - Account
      summary: 직책 변경
      description: 단말 > LNB > 설정 > 수정
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                position:
                  type: string
                  description: 직책 명
              required:
                - position
      responses:
        200:
          description: OK
        500:
          $ref: '#/components/responses/ServerError'
  /account/v1/setting/cashreceipt:
    get:
      tags:
        - Account
      summary: 현금영수증 발급 정보 조회
      description: 단말 > LNB > 설정 > 현금영수증
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  email:
                    type: string
                    description: 이메일
                  phone:
                    type: string
                    description: 전화번호
                  card:
                    type: string
                    description: 카드번호
                  type:
                    type: string
                    description: 선택유형 phone, card
        500:
          $ref: '#/components/responses/ServerError'
    put:
      tags:
        - Account
      summary: 현금영수증 발급 정보 등록
      description: 단말 > LNB > 설정 > 현금영수증
      parameters:
        - $ref: '#/components/parameters/header-auth'
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  description: 이메일
                phone:
                  type: string
                  description: 전화번호
                card:
                  type: string
                  description: 카드번호
                type:
                  type: string
                  description: 선택유형 phone, card
              required:
                - type
      responses:
        200:
          description: OK
        500:
          $ref: '#/components/responses/ServerError'

# tag : Common
  /common/v1/pointbook/filter:
    get:
      tags:
        - Common
      summary: 식권 사용내역 - 필터
      parameters: 
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  contents:
                    type: array
                    items:
                      properties:
                        id:
                          type: string
                          description: 필터 고유 코드
                        name:
                          type: string
                          description: 필터 고유 이름
        500:
          $ref: '#/components/responses/ServerError'

# tag : Link
  /link/hrefs/{service}:
    get:
      tags:
        - Link
      summary: 외부 서비스 링크 (세틀뱅크:"settlebank")
      parameters: 
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: type
          description: 결제타입 (계좌이체:"account")
          required: true
          schema:
            type: string
        - in: query
          name: amount
          description: 결제금액
          required: true
          schema:
            type: integer
      responses:
        200:
          description: success and returns text/html
        500:
          $ref: '#/components/responses/ServerError'

# tag: Demand
  /demand/v1/approval:
    get:
      tags:
        - Demand
      summary: 식권 승인 내역
      description: 단말 > LNB > 식권승인
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: filter
          description: 검색조건 <br/>ALL -> 전체(default)<br/>INPUT -> 진행중<br/>READY -> 대기<br/>CHARGE -> 지급완료<br/>REJECT -> 반려
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  total:
                    type: integer
                    description: 총 개수
                  filter:
                    type: string
                    description: 검색조건 <br/>ALL -> 전체(default)<br/>INPUT -> 진행중<br/>READY -> 대기<br/>CHARGE -> 지급완료<br/>REJECT -> 반려
                  approval:
                    type: array
                    items:
                      properties:
                        user:
                          type: object
                          description: 신청자 정보
                          properties:
                            idx:
                              type: integer
                              description: 신청자 신청 고유 아이디
                            id:
                              type: string
                              description: 신청자 고유 아이디
                            name:
                              type: string
                              description: 신청자 이름
                            division:
                              type: string
                              description: 신청자 최상위 부서
                            position:
                              type: string
                              description: 신청자 직책
                            status:
                              type: string
                              description: 신청 상태 <br/>REJECT -> 반려('AUTO' 는 해당안됨) <br/>CHARGE -> 지급완료 <br/>INPUT -> 신청 완료 (승인대기)
                            demandName:
                              type: string
                              description: 신청 이름
                            demandType:
                              type: string
                              description: 식권 신청 TYPE <br/>AUTO -> 자동 승인 <br/>MANUAL -> 수동 승인 <br/>CONFIRM -> 결재선 승인
                            amount:
                              type: integer
                              description: 식권 신청 금액
                            reason:
                              type: string
                              description: 식권 신청 사유
                            regDate:
                              type: string
                              format: 'date-time'
                              description: 식권 신청 날짜
                          required:
                            - idx
                            - id
                            - signId
                            - name
                            - division
                            - status
                            - demandName
                            - demandType
                            - amount
                            - reason
                            - regDate
                        confirm:
                          type: array
                          description: 결재자 정보
                          items:
                            properties:
                              idx:
                                type: integer
                                description: 식권 결재자 고유 번호
                              id:
                                type: string
                                description: 식권 결재자 고유 아이디
                              name:
                                type: string
                                description: 식권 결재자 이름
                              status:
                                type: string
                                description: 결재자 상태 <br/>REJECT -> 반려 <br/>INPUT -> 결재선 대기 <br/>READY -> 승인중 (승인 대기) <br/>ACCEPT -> 승인완료
                            required:
                              - idx
                              - id
                              - name
                              - status
        500:
          $ref: '#/components/responses/ServerError'
    post:
      tags:
        - Demand
      summary: 식권 승인
      description: 단말 > LNB > 식권승인
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: type
          description: 승인 타입
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                userIdx:
                  type: array
                  items:
                    type: integer
              required:
                - userIdx
      responses:
        201:
          description: CREATED
        500:
          $ref: '#/components/responses/ServerError'
    put:
      tags:
        - Demand
      summary: 식권 반려
      description: 단말 > LNB > 식권승인
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: type
          description: 승인 타입
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                userIdx:
                  type: array
                  items:
                    type: integer
                reason:
                  type: string
                  description: 사유
              required:
                - userIdx
                - reason
      responses:
        200:
          description: OK
        500:
          $ref: '#/components/responses/ServerError'
  /demand/v2/approval:
    get:
      tags:
        - Demand
      summary: 식권 승인 내역
      description: 단말 > LNB > 식권승인
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: filter
          description: 검색조건 <br/>ALL -> 전체(default)<br/>INPUT -> 진행중<br/>READY -> 대기<br/>CHARGE -> 지급완료<br/>REJECT -> 반려
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  total:
                    type: integer
                    description: 총 개수
                  filter:
                    type: string
                    description: 검색조건 <br/>ALL -> 전체(default)<br/>INPUT -> 진행중<br/>READY -> 대기<br/>CHARGE -> 지급완료<br/>REJECT -> 반려
                  approval:
                    type: array
                    items:
                      properties:
                        user:
                          type: object
                          description: 신청자 정보
                          properties:
                            idx:
                              type: integer
                              description: 신청자 신청 고유 아이디
                            id:
                              type: string
                              description: 신청자 고유 아이디
                            signId:
                              type: string
                              description: 신청자 회원가입 아이디
                            name:
                              type: string
                              description: 신청자 이름
                            division:
                              type: string
                              description: 신청자 최상위 부서
                            position:
                              type: string
                              description: 신청자 직책
                            status:
                              type: string
                              description: 신청 상태 <br/>REJECT -> 반려('AUTO' 는 해당안됨) <br/>CHARGE -> 지급완료 <br/>INPUT -> 신청 완료 (승인대기)
                            demandName:
                              type: string
                              description: 신청 이름
                            demandType:
                              type: string
                              description: 식권 신청 TYPE <br/>AUTO -> 자동 승인 <br/>MANUAL -> 수동 승인 <br/>CONFIRM -> 결재선 승인
                            amount:
                              type: integer
                              description: 식권 신청 금액
                            reason:
                              type: string
                              description: 식권 신청 사유
                            regDate:
                              type: string
                              format: 'date-time'
                              description: 식권 신청 날짜
                          required:
                            - idx
                            - id
                            - signId
                            - name
                            - division
                            - status
                            - demandName
                            - demandType
                            - amount
                            - reason
                            - regDate
                        confirm:
                          type: array
                          description: 결재자 정보
                          items:
                            properties:
                              idx:
                                type: integer
                                description: 식권 결재자 고유 번호
                              id:
                                type: string
                                description: 식권 결재자 고유 아이디
                              name:
                                type: string
                                description: 식권 결재자 이름
                              status:
                                type: string
                                description: 결재자 상태 <br/>REJECT -> 반려 <br/>INPUT -> 결재선 대기 <br/>READY -> 승인중 (승인 대기) <br/>ACCEPT -> 승인완료
                            required:
                              - idx
                              - id
                              - name
                              - status
        500:
          $ref: '#/components/responses/ServerError'
  /demand/v1/approval/user:
    get:
      tags:
        - Demand
      summary: 식권 이전 결재자
      description: 단말 > LNB > 식권신청
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: demandIdx
          description: 식권 신청 고유 아이디
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  confirm:
                    type: array
                    description: 결재자 정보
                    items:
                      properties:
                        idx:
                          type: integer
                          description: 결재자 고유 번호
                        id:
                          type: string
                          description: 결재자 고유 아이디
                        name:
                          type: string
                          description: 결재자 이름
                        division:
                          type: string
                          description: 결재자 최상위 부서
                        position:
                          type: string
                          description: 결재자 직책
                      required:
                        - idx
                        - id
                        - name
                        - division
        500:
          $ref: '#/components/responses/ServerError'
  /demand/v2/approval/user:
    get:
      tags:
        - Demand
      summary: 식권 이전 결재자
      description: 단말 > LNB > 식권신청
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: demandIdx
          description: 식권 신청 고유 아이디
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  confirm:
                    type: array
                    description: 결재자 정보
                    items:
                      properties:
                        idx:
                          type: integer
                          description: 결재자 고유 번호
                        id:
                          type: string
                          description: 결재자 고유 아이디
                        name:
                          type: string
                          description: 결재자 이름
                        division:
                          type: string
                          description: 결재자 최상위 부서
                        position:
                          type: string
                          description: 결재자 직책
                      required:
                        - idx
                        - id
                        - name
                        - division
        500:
          $ref: '#/components/responses/ServerError'
  /demand/v1/accept:
    get:
      tags:
        - Demand
      summary: 식권 신청 상세
      description: 단말 > LNB > 식권신청
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: accept
          description: 승인 여부
          schema:
            type: boolean
        - in: query
          name: offset
          description: 마지막 컨텐츠 고유 번호
          schema:
            type: integer
        - in: query
          name: size
          description: 페이지 사이즈
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  info:
                    type: object
                    properties:
                      total:
                        type: integer
                        description: 총 신청 인원
                      await:
                        type: integer
                        description: 대기자 인원
                      accept:
                        type: integer
                        description: 승인된 인원
                    required:
                      - total
                      - await
                      - accept
                  user:
                    type: array
                    items:
                      properties:
                        idx:
                          type: integer
                          description: 신청 고유 번호
                        name:
                          type: string
                          description: 신청자 이름
                        division:
                          type: string
                          description: 신청자 부서
                        date:
                          type: string
                          format: 'date-time'
                          description: 신청 시간
                      required:
                        - idx
                        - name
                        - division
                        - date
        500:
          $ref: '#/components/responses/ServerError'
    post:
      tags:
        - Demand
      summary: 식권 신청자 승인
      description: 단말 > LNB > 식권신청
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: type
          description: 승인 타입
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                demand:
                  type: array
                  items:
                    properties:
                      idx:
                        type: integer
                        description: 신청 고유 아이디
                    required:
                      - idx
      responses:
        201:
          description: CREATED
          content:
            application/json:
              schema:
                type: object
                properties:
                  demand:
                    type: object
                    properties:
                      count:
                        type: integer
                        description: 승인 된 신청자 수
        500:
          $ref: '#/components/responses/ServerError'
  /demand/v1/coupon:
    get:
      tags:
        - Demand
      summary: 식권 신청 내역
      description: 단말 > LNB > 식권신청
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: idx
          description: 마지막 컨텐츠 번호
          schema:
            type: integer
        - in: query
          name: size
          description: 페이지 개수
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  properties:
                    user:
                      type: object
                      description: 신청자 정보
                      properties:
                        idx:
                          type: integer
                          description: 신청자 신청 고유 아이디
                        status:
                          type: string
                          description: 신청 상태 <br/>REJECT -> 반려('AUTO' 는 해당안됨) <br/>CHARGE -> 지급완료 <br/>INPUT -> 신청 완료 (승인대기)
                        demandName:
                          type: string
                          description: 신청 이름
                        demandType:
                          type: string
                          description: 식권 신청 TYPE <br/>AUTO -> 자동 승인 <br/>MANUAL -> 수동 승인 <br/>CONFIRM -> 결재선 승인
                        amount:
                          type: integer
                          description: 식권 신청 금액
                        reason:
                          type: string
                          description: 식권 신청 사유
                        regDate:
                          type: string
                          format: 'date-time'
                          description: 식권 신청 날짜
                        rejectReason:
                          type: string
                          description: 반려 사유
                      required:
                        - idx
                        - status
                        - demandName
                        - demandType
                        - amount
                        - reason
                        - regDate
                    confirm:
                      type: array
                      description: 결재자 정보
                      items:
                        properties:
                          idx:
                            type: integer
                            description: 식권 결재자 고유 번호
                          name:
                            type: string
                            description: 식권 결재자 이름
                          status:
                            type: string
                            description: 결재자 상태 <br/>REJECT -> 반려 <br/>INPUT -> 결재선 대기 <br/>READY -> 승인중 (승인 대기) <br/>ACCEPT -> 승인완료
                        required:
                          - idx
                          - name
                          - status
        500:
          $ref: '#/components/responses/ServerError'
    post:
      tags:
        - Demand
      summary: 식권 신청
      description: 단말 > LNB > 식권신청
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: type
          description: 승인 타입
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                demandIdx:
                  type: integer
                  description: 신청 고유 아이디
                reason:
                  type: string
                  description: 사유
                confirm:
                  type: array
                  items:
                    properties:
                      idx:
                        type: integer
                        description: 결재 순서 (0부터 순서대로)
                      userId:
                        type: string
                        description: 결재자 고유 아이디
                      userName:
                        type: string
                        description: 결재자 이름
                    required:
                      - idx
                      - userId
                      - userName
              required:
                - demandIdx
                - reason
                - confirm
      responses:
        201:
          description: CREATED
          content:
            application/json:
              schema:
                type: object
                properties:
                  userIdx:
                    type: integer
                    description: 승인자 고유 번호
                required:
                  - userIdx
        500:
          $ref: '#/components/responses/ServerError'
  /demand/v1/coupon/{idx}:
    get:
      tags:
        - Demand
      summary: 식권 신청 상세
      description: 단말 > LNB > 식권신청
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: idx
          description: 식권 신청자 고유 번호
          required: true
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    type: object
                    description: 신청자 정보
                    properties:
                      idx:
                        type: integer
                        description: 신청자 신청 고유 아이디
                      id:
                        type: string
                        description: 신청자 고유 아이디
                      name:
                        type: string
                        description: 신청자 이름
                      division:
                        type: string
                        description: 신청자 최상위 부서
                      position:
                        type: string
                        description: 신청자 직책
                      status:
                        type: string
                        description: 신청 상태 <br/>REJECT -> 반려('AUTO' 는 해당안됨) <br/>CHARGE -> 지급완료 <br/>INPUT -> 신청 완료 (승인대기)
                      demandName:
                        type: string
                        description: 신청 이름
                      demandType:
                        type: string
                        description: 식권 신청 TYPE <br/>AUTO -> 자동 승인 <br/>MANUAL -> 수동 승인 <br/>CONFIRM -> 결재선 승인
                      amount:
                        type: integer
                        description: 식권 신청 금액
                      reason:
                        type: string
                        description: 식권 신청 사유
                      regDate:
                        type: string
                        format: 'date-time'
                        description: 식권 신청 날짜
                      rejectReason:
                        type: string
                        description: 반려 사유
                    required:
                      - idx
                      - id
                      - signId
                      - name
                      - division
                      - status
                      - demandName
                      - demandType
                      - amount
                      - reason
                      - regDate
                  confirm:
                    type: array
                    description: 결재자 정보
                    items:
                      properties:
                        idx:
                          type: integer
                          description: 식권 결재자 고유 번호
                        id:
                          type: string
                          description: 결재자 고유 아이디
                        name:
                          type: string
                          description: 식권 결재자 이름
                        division:
                          type: string
                          description: 식권 결재자 부서
                        position:
                          type: string
                          description: 식권 결재자 직책
                        status:
                          type: string
                          description: 결재자 상태 <br/>REJECT -> 반려 <br/>INPUT -> 결재선 대기 <br/>READY -> 승인중 (승인 대기) <br/>ACCEPT -> 승인완료
                        updateDate:
                          type: string
                          format: 'date-time'
                          description: 식권 신청 수정 날짜
                      required:
                        - idx
                        - id
                        - name
                        - division
                        - status
                        - updateDate
        500:
          $ref: '#/components/responses/ServerError'
    delete:
      tags:
        - Demand
      summary: 식권 신청 취소
      description: 단말 > LNB > 식권신청
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: idx
          description: 식권 신청자 고유 번호
          required: true
          schema:
            type: integer
        - in: query
          name: type
          description: 취소구분 USER -> 사용자 취소, REJECT -> 재신청 취소
          schema:
            type: string
      responses:
        204:
          description: NO CONTENT
        500:
          $ref: '#/components/responses/ServerError'
  /demand/v1/policy:
    get:
      tags:
        - Demand
      summary: 식권 신청 정책 조회
      description: 단말 > LNB > 식권신청
      parameters:
        - $ref: '#/components/parameters/header-auth'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  demand:
                    type: array
                    description: 결재자 정보
                    items:
                      properties:
                        idx:
                          type: integer
                          description: 식권 결재자 고유 번호
                        name:
                          type: string
                          description: 정책 이름
                        type:
                          type: string
                          description: 식권 신청 TYPE <br/>AUTO -> 자동 승인, MANUAL -> 수동 승인, CONFIRM -> 결재선 승인
                        amount:
                          type: integer
                          description: 신청 가능 금액
                        status:
                          type: string
                          description: 신청 상태 <br/>REJECT -> 반려 ('AUTO' 는 해당안됨) <br/>CHARGE -> 지급완료 <br/>INPUT -> 신청 완료 (승인대기) <br/>OK -> 신청 가능 <br/>NOK -> 신청 불가능
                        userIdx:
                          type: integer
                          description: 신청자의 고유 IDX <br/>OK, NOK 가 아닌 경우 노출됨 <br/>상세 진입시 key 값으로 사용됨
                        demandStartTime:
                          type: string
                          format: 'date-time'
                          description: 신청 가능 시작 시간
                        demandEndTime:
                          type: string
                          format: 'date-time'
                          description: 신청 가능 종료 시간
                        policyStartTime:
                          type: string
                          format: 'date-time'
                          description: 포인트 사용 시작 시간
                        policyEndTime:
                          type: string
                          format: 'date-time'
                          description: 포인트 사용 종료 시간
                        policyWeek:
                          type: string
                          description: 포인트 사용 가능 요일 (TEXT 형태)
                      required:
                        - idx
                        - name
                        - type
                        - amount
                        - status
                        - demandStartTime
                        - demandEndTime
                        - policyStartTime
                        - policyEndTime
                        - policyWeek
        500:
          $ref: '#/components/responses/ServerError'

# tag: Notice
  /notice/company/v1/{channel}/{comid}:
    get:
      tags:
        - Notice
      summary: 고객사 공지 사항 목록
      description: 고객사 공지 사항 목록
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: channel
          description: 유입경로 app, admin, com
          required: true
          schema:
            type: string
        - in: path
          name: comid
          description: 회사 고유 아이디
          required: true
          schema:
            type: string
        - in: query
          name: page
          description: 페이지 번호
          schema:
            type: integer
        - in: query
          name: pageRow
          description: 페이지 사이즈
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  info:
                    type: object
                    properties:
                      page:
                        type: integer
                        description: 페이지 번호
                      pageRow:
                        type: integer
                        description: 페이지 개수
                      totalCount:
                        type: integer
                        description: 총 개수
                    required:
                      - page
                      - pageRow
                      - totalCount
                  content:
                    type: array
                    items:
                      properties:
                        postid:
                          type: integer
                          description: 공지 고유 번호
                        title:
                          type: string
                          description: 제목
                        content:
                          type: string
                          description: 내용
                        writer:
                          type: string
                          description: 작성자
                        status:
                          type: boolean
                          description: 노출 여부
                        regdate:
                          type: string
                          format: 'date-time'
                          description: 작성일
                      required:
                        - postid
                        - title
                        - content
                        - writer
                        - status
                        - regdate
                  status:
                    type: integer
                    description: 성공 여부 1 -> 성공, -1 -> 실패
        500:
          $ref: '#/components/responses/ServerError'
  /notice/company/v1/{channel}/{comid}/{noticeid}:
    get:
      tags:
        - Notice
      summary: 고객사 공지 사항 상세
      description: 고객사 공지 사항
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: channel
          description: 유입경로 app, admin, com
          required: true
          schema:
            type: string
        - in: path
          name: comid
          description: 회사 고유 아이디
          required: true
          schema:
            type: string
        - in: path
          name: noticeid
          description: 공지 고유 아이디
          required: true
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  content:
                    type: object
                    properties:
                      postid:
                        type: integer
                        description: 공지 고유 번호
                      title:
                        type: string
                        description: 제목
                      content:
                        type: string
                        description: 내용
                      writer:
                        type: string
                        description: 작성자
                      status:
                        type: boolean
                        description: 노출 여부
                      regdate:
                        type: string
                        format: 'date-time'
                        description: 작성일
                      cid:
                        type: string
                        description: 회사 고유 번호
                      uid:
                        type: string
                        description: 등록자
                      lasteditdate:
                        type: string
                        format: 'date-time'
                        description: 마지막 업데이트 날짜
                  status:
                    type: integer
                    description: 성공 여부 1 -> 성공, -1 -> 실패
        500:
          $ref: '#/components/responses/ServerError'
  /notice/vendys/v1/{channel}:
    get:
      tags:
        - Notice
      summary: 벤디스 공지 사항 목록
      description: 벤디스 공지 사항 목록
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: channel
          description: 유입경로 app, admin
          required: true
          schema:
            type: string
        - in: path
          name: comid
          description: 회사 고유 아이디
          required: true
          schema:
            type: string
        - in: query
          name: page
          description: 페이지 번호
          schema:
            type: integer
        - in: query
          name: pageRow
          description: 페이지 사이즈
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  info:
                    type: object
                    properties:
                      page:
                        type: integer
                        description: 페이지 번호
                      pageRow:
                        type: integer
                        description: 페이지 개수
                      totalCount:
                        type: integer
                        description: 총 개수
                    required:
                      - page
                      - pageRow
                      - totalCount
                  content:
                    type: array
                    items:
                      properties:
                        status:
                          type: boolean
                          description: 노출 여부
                        opentype:
                          type: string
                          description: 오픈 타입
                        postid:
                          type: integer
                          description: 공지 고유 번호
                        highlight:
                          type: string
                          description: 하이라이트
                        title:
                          type: string
                          description: 제목
                        regdate:
                          type: string
                          format: 'date-time'
                          description: 작성일
                        image:
                          type: string
                          description: 이미지
                  status:
                    type: integer
                    description: 성공 여부 1 -> 성공, -1 -> 실패
        500:
          $ref: '#/components/responses/ServerError'
  /notice/vendys/v1/{channel}/{noticeid}:
    get:
      tags:
        - Notice
      summary: 벤디스 공지 사항 상세
      description: 벤디스 공지 사항 상세
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: channel
          description: 유입경로 app, admin
          required: true
          schema:
            type: string
        - in: path
          name: comid
          description: 회사 고유 아이디
          required: true
          schema:
            type: string
        - in: path
          name: noticeid
          description: 공지 고유 아이디
          required: true
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  content:
                    type: object
                    properties:
                      status:
                        type: boolean
                        description: 노출 여부
                      opentype:
                        type: string
                        description: 오픈 타입
                      lasteditdate:
                        type: string
                        format: 'date-time'
                        description: 마지막 업데이트 날짜
                      postid:
                        type: integer
                        description: 공지 고유 번호
                      highlight:
                        type: string
                        description: 하이라이트
                      title:
                        type: string
                        description: 제목
                      contentshtml:
                        type: string
                        description: html
                      regdate:
                        type: string
                        format: 'date-time'
                        description: 작성일
                      image:
                        type: string
                        description: 이미지
                      userId:
                        type: string
                        description: 로그인한 사용자 GUID
                  status:
                    type: integer
                    description: 성공 여부 1 -> 성공, -1 -> 실패
        500:
          $ref: '#/components/responses/ServerError'
  /notice/main/v1/{channel}:
    get:
      tags:
        - Notice
      summary: 앱 메인 공지사항 목록
      description: 앱 메인 공지사항 목록
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: channel
          description: 유입경로 app
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  content:
                    type: object
                    properties:
                      vendysNotice:
                        type: array
                        items:
                          properties:
                            postid:
                              type: integer
                              description: 공지 고유 번호
                            highlight:
                              type: string
                              description: 하이라이트
                            title:
                              type: string
                              description: 제목
                            regdate:
                              type: string
                              format: 'date-time'
                              description: 작성일
                            image:
                              type: string
                              description: 이미지
                  status:
                    type: integer
                    description: 성공 여부 1 -> 성공, -1 -> 실패
        500:
          $ref: '#/components/responses/ServerError'
  /notice/board:
    get:
      tags:
        - Notice
      summary: 고객사 공지사항 목록 웹뷰
      description: 고객사 공지사항 목록 웹뷰
      parameters:
        - in: query
          name: accesstoken
          description: 사용자 로그인 AccessToken
          required: true
          schema:
            type: string
        - in: query
          name: comid
          description: 고객사 GUID
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
        500:
          description: Server Error
  /notice/viewer:
    get:
      tags:
        - Notice
      summary: 벤디스 공지사항 상세 웹뷰
      description: 벤디스 공지사항 상세 웹뷰
      parameters:
        - in: query
          name: accesstoken
          description: 사용자 로그인 AccessToken
          required: true
          schema:
            type: string
        - in: query
          name: postid
          description: 공지사항 일련번호
          required: true
          schema:
            type: integer
      responses:
        200:
          description: OK
        500:
          description: Server Error

# tag: Event
  /event/v1:
    get:
      tags:
        - Event
      summary: 이벤트 리스트 목록
      description: 이벤트 리스트 목록
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: comId
          description: 회사 아이디
          schema:
            type: integer
          required: true
        - in: query
          name: page
          description: 페이지 번호 
          schema:
            type: integer
        - in: query
          name: pageRow
          description: 페이지 사이즈
          schema:
            type: integer
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  pageInfo:
                    type: object
                    properties:
                      page:
                        type: integer
                        description: 페이지 번호
                      pageRow:
                        type: integer
                        description: 페이지 개수
                      totalCount:
                        type: integer
                        description: 총 개수
                    required:
                      - page
                      - pageRow
                      - totalCount
                  events:
                    type: array
                    items:
                      properties:
                        eventId:
                          type: integer
                          description: 이벤트 고유 번호
                        title:
                          type: string
                          description: 제목
                        partner:
                          type: string
                          description: 제휴사명
                        createDate:
                          type: string
                          format: 'date-time'
                          description: 작성일
                        updateDate:
                          type: string
                          format: 'date-time'
                          description: 수정일
                        startDate:
                          type: string
                          format: 'date-time'
                          description: 노출 시작일
                        endDate:
                          type: string
                          format: 'date-time'
                          description: 노출 종료일
                        image:
                          type: string
                          description: 이미지
                        order:
                          type: integer
                          description: 노출순서
                      required:
                        - eventId
                        - title
                        - partner
                        - updateDate
                        - startDate
                        - endDate
                        - image
                        - order
        500:
          $ref: '#/components/responses/ServerError'

  /event/v1/{eventId}:
    get:
      tags:
        - Event
      summary: 이벤트 상세
      description: 이벤트 상세
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: comId
          description: 회사 아이디
          schema:
            type: string
          required: true
        - in: query
          name: eventId
          description: 이벤트 고유 번호
          schema:
            type: integer
          required: true
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  eventId:
                    type: integer
                    description: 이벤트 고유 번호
                  title:
                    type: string
                    description: 제목
                  partner:
                    type: string
                    description: 제휴사명
                  createDate:
                    type: string
                    format: 'date-time'
                    description: 작성일
                  updateDate:
                    type: string
                    format: 'date-time'
                    description: 수정일
                  startDate:
                    type: string
                    format: 'date-time'
                    description: 노출 시작일
                  endDate:
                    type: string
                    format: 'date-time'
                    description: 노출 종료일
                  image:
                    type: string
                    description: 이미지
                required:
                  - eventId
                  - title
                  - partner
                  - createDate
                  - updateDate
                  - startDate
                  - endDate
                  - image
        500:
          $ref: '#/components/responses/ServerError'

  /event/viewer:
    get:
      tags:
        - Event
      summary: 이벤트 상세 웹뷰 
      description: 이벤트 상세 웹뷰 
      parameters:
        - in: query
          name: accesstoken
          description: 사용자 로그인 AccessToken
          required: true
          schema:
            type: string
        - in: query
          name: eventId
          description: 이벤트 고유 번호
          required: true
          schema:
            type: integer
      responses:
        200:
          description: OK
        500:
          description: Server Error 

# tag: Infra
  /infra/sms/v1/{channel}/send:
    post:
      tags:
        - Infra
      summary: SMS 전송
      description: 단말 > LNB > 식권신청
      deprecated: true
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: channel
          description: com, admin
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                receiver:
                  type: string
                  description: 받는이 전화번호
                message:
                  type: string
                  description: 내용
              required:
                - receiver
                - message
      responses:
        201:
          description: CREATED
          content:
            application/json:
              schema:
                type: object
                properties:
                  contents:
                    type: string
                    description: 성공
                  status:
                    type: integer
                    description: 성공 여부 1 -> 성공, -1 -> 실패
        500:
          $ref: '#/components/responses/ServerError'
  /infra/v1/sms/send:
    post:
      tags:
        - Infra
      summary: SMS 인증 번호 발송
      description: 단말 > LNB > 설정 > 수정 > 인증번호 발송
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: channel
          description: com, admin
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                phone:
                  type: string
                  description: 받는이 전화번호
                code:
                  type: string
                  description: 코드
              required:
                - phone
                - code
      responses:
        201:
          description: CREATED
        500:
          $ref: '#/components/responses/ServerError'
  /infra/v1/sms/check:
    get:
      tags:
        - Infra
      summary: SMS 인증 번호 확인
      description: 단말 > LNB > 설정 > 수정 > 인증번호 확인
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: query
          name: phone
          description: 전화번호
          required: true
          schema:
            type: string
        - in: query
          name: code
          description: 인증 코드
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
        500:
          $ref: '#/components/responses/ServerError'

# tag: openapi
  /openapi/payment/v1/{storeId}:
    post:
      tags:
        - Openapi
      summary: 어양 제휴점 예약 시스템 연동
      description: 어양 제휴점 예약 시스템 연동
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: storeId
          description: 제휴점 고유 아이디
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                key:
                  type: string
                  description: 고유 키
                id:
                  type: string
                  description: 사용자 고유 아이디
                amount:
                  type: integer
                  description: 금액
              required:
                - key
                - id
                - amount
      responses:
        201:
          description: CREATED
          content:
            application/json:
              schema:
                type: object
                properties:
                  couponid:
                    type: string
                    description: 결제 고유 아이디
                  id:
                    type: string
                    description: 사용자 고유 아이디
                  name:
                    type: string
                    description: 사용자 이름
                  amount:
                    type: integer
                    description: 금액
                  date:
                    type: string
                    format: 'date-time'
                    description: 날짜
                required:
                  - couponid
                  - id
                  - name
                  - amount
                  - date
        500:
          $ref: '#/components/responses/ServerError'

# tag: Download
  /noticeboard/{filename}:
    get:
      tags:
        - Download
      summary: 공지사항 이미지
      description: 이미지 다운로드
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: filename
          description: 파일 명
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
        500:
          $ref: '#/components/responses/ServerError'
  /qpcon/{filename}:
    get:
      tags:
        - Download
      summary: 큐피콘 이미지
      description: 이미지 다운로드
      parameters:
        - $ref: '#/components/parameters/header-auth'
        - in: path
          name: filename
          description: 파일 명
          required: true
          schema:
            type: string
      responses:
        200:
          description: OK
        500:
          $ref: '#/components/responses/ServerError'

components:
  parameters:
    header-auth:
      in: header
      name: Authorization
      description: 사용자 로그인 AccessToken
      required: true
      schema:
        type: string
  responses:
    ServerError:
      description: server error.
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
  schemas:
    Error:
      type: object
      properties:
        status:
          type: integer
        title:
          type: string
        message:
          type: string
      required:
        - status
        - title
        - message
    common.store.list:
      properties:
        category:
          type: string
          description: 카테고리명
        categoryId:
          type: integer
          format: int32
          description: 카테고리 고유 아이디 (즐겨찾기용 카테고리 ID는 -9999로 하드코딩)
        list:
          type: array
          items:
            properties:
              id:
                type: string
                description: 제휴점 고유 아이디
              name:
                type: string
                description: 제휴점 이름
              intro:
                type: string
                description: 제휴점 설명
              supplyType:
                type: integer
                format: int32
                description: 제휴점 종류</br>0 -> Local(방문)</br>1 -> QPcon(큐피콘)</br>2 -> LocalDelivery(방문,배달)</br>3 -> Delivery(배달)</br>4 -> Cafeteria(일회용바코드)</br>5 -> Cafeteria(고정형바코드)
              payment:
                type: string
                description: 결제 수단</br>MENU -> 메뉴선택</br>MULTI -> 메뉴선택 + 금액직접입력
            required:
              - id
              - name
              - intro
              - supplyType
              - payment
      required:
        - list
    common.entrust.list:
      properties:
        division:
          type: string
          description: 부서 명
        trust:
          type: boolean
          description: 위임 여부 true -> 위임<br/>false -> 위임안함
        uid:
          type: string
          description: 사용자 고유 번호
        name:
          type: string
          description: 사용자 이름
        rankposition:
          type: string
          description: 직위
        position:
          type: string
          description: 직책
        signId:
          type: string
          description: 회원가입 아이디
      required:
        - division
        - trust
        - uid
        - name
        - signId
    common.point.list:
      properties:
        id:
          type: integer
          description: 정책 고유 아이디
        name:
          type: string
          description: 정책 명
        amount:
          type: integer
          description: 포인트 금액
        startTime:
          type: string
          format: 'date-time'
          description: 시작 시간
        endTime:
          type: string
          format: 'date-time'
          description: 마지막 시간
