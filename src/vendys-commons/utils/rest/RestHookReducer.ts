// --------------------------- Action constants --------------------------
export const CHANGE_DATA = 'RESTHOOK_CHANGE_DATA';

import set from 'set-value';
import get from 'get-value';

const initialState = {
    gridData: {},
    total: 0
}

export default function restHookReducer(state = initialState, action = {}) {
    switch(action.type){
        case CHANGE_DATA:
            var newState = JSON.parse(JSON.stringify(state))
            var { payload } = action;
            var { type, stateKey, reducer = (oldData, newData) => newData, data } = payload;
            var value = get(state, stateKey);
            var newValue =  reducer(value, data);

            set(newState, stateKey, newValue);

            return newState;
        break;
    }
    return state;
}
