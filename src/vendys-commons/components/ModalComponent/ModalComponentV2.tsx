import PropTypes from 'prop-types';
import React, {Component} from 'react';
import {Modal, Button} from 'antd';
// import Button from "vcomponents/WButton"
import MultiReducer, {bindActionCreators} from 'multireducer';
import {connect} from 'react-redux';
import {FormattedMessage} from 'react-intl';
import {FormattedMessageDynamic} from 'utils';

import reducer, * as actions from './ModalComponentActions'
import immutableReducer, * as immutableActions from './ModalComponentImmutableActions'

import './ModalComponent.scss'

class ModalComponent extends Component {
  static propTypes = {
    id: PropTypes.string,
    comp: PropTypes.element,
    title: PropTypes.string,
    isHideFooter: PropTypes.bool,
    heightFull: PropTypes.bool,

    onShow: PropTypes.func,
    onHide: PropTypes.func,

    immutable: PropTypes.bool,
    isForceRender: PropTypes.bool,
    superModal: PropTypes.bool,
    submitType: PropTypes.string
  }

  static defaultProps = {
    title: undefined,
    isHideFooter: false,
    heightFull: false,
    immutable: false,
    visible: false,
    isForceRender: false,
    superModal: false
  }

  constructor() {
    super();

    this.state = {
      visible: false,
      loading: false,
    }
  }

  handleOk = (e) => {
    var {hideModal, triggerSubmit, id, onSubmit} = this.props;
    triggerSubmit(id);
    if (onSubmit) {
      onSubmit(id)
    }
    // hideModal(id);
  }

  handleCancel = (e) => {
    var {hideModal, handleCancel, id} = this.props;
    if(handleCancel) handleCancel(id);
    hideModal(id);
  }

  render() {
    var {id, comp, as, visible, title, isHideFooter, isHideCancel, isHideSubmit, heightFull, width, inputTitle, isForceRender, okTextId, superModal, theme, closable = true, submitType = 'primary'} = this.props;
    var {loading} = this.state;
    var customBodyStyle = {};
    var style = {};

    if (heightFull == true) {
      customBodyStyle.height = '90%';
      style.height = '90%';
    }

    let footers = []
    if (isHideFooter !== true) {
      if (isHideCancel !== true) {
        footers.push(
          <Button key="back" onClick={this.handleCancel.bind(this)}><FormattedMessage id="cancel"
                                                                                      defaultMessage="Cancel"/></Button>,
        )
      }
      if (isHideSubmit !== true) {
        footers.push(
          <Button icon="s-check" key={'submit'} type={submitType} loading={loading} onClick={this.handleOk.bind(this)}>
            <FormattedMessageDynamic id={okTextId || "confirm"} defaultMessage="Confirm"/>
          </Button>
        )
      }
    } else {
      customBodyStyle.borderRadius = '0px 0px 4px 4px';
    }

    return <Modal {...this.props}
                  maskClosable={false}
                  title={inputTitle ? inputTitle :
                    <FormattedMessageDynamic id={title || 'title'} defaultMessage={title}/>}
                  visible={visible}
                  width={width}
                  destroyOnClose={true}
                  closable={closable}
                  bodyStyle={{...this.props.bodyStyle, ...customBodyStyle}}
                  style={{maxWidth: '1500px', ...this.props.style, ...style}}
                  wrapClassName={superModal ? ' super-modal' : '' + " " + (heightFull === true ? 'modal-height-full' : '') + ' ' + id + (isHideFooter ? " hide-footer" : "")}
                  onOk={this.handleOk.bind(this)}
                  onCancel={this.handleCancel.bind(this)}
                  footer={footers}
    >
      {isForceRender ? comp: this.newComp}
    </Modal>
  }

  componentDidMount() {

  }

  componentWillReceiveProps(nextProps) {
    var beforeTransaction = this.props.transaction;
    var {transaction} = nextProps;
    var modalData = nextProps.modalData ? nextProps.modalData : {}

    if (beforeTransaction != true && transaction == true) {
      this.setState({loading: true})
    } else if (beforeTransaction == true && transaction != true) {
      this.setState({loading: false})
    }

    if (this.props.visible != true && nextProps.visible == true) {
      this.newComp = React.cloneElement(this.props.comp, modalData);
      if (this.props.onShow) this.props.onShow();
      this.props.initModalView(this.props.id)
    }

    if (this.props.visible !== false && nextProps.visible == false) {
      if (this.props.onHide) this.props.onHide();
      this.props.destroyModalView(this.props.id)
    }
  }
}

ModalComponent.reducer = reducer;
ModalComponent.actions = actions;
ModalComponent.immutableActions = immutableActions;
ModalComponent.immutableReducer = immutableReducer;

const mapStateToProps = (state, {as, id}) => {

  return {
    visible: state.view[as] && state.view[as][id] && state.view[as][id].visible,
    title: state.view[as] && state.view[as][id] && state.view[as][id].modalData && state.view[as][id].modalData.title,
    transaction: state.view[as] && state.view[as].transactions && state.view[as].transactions[id],
    modalData: state.view[as][id] && state.view[as][id].modalData ? state.view[as][id].modalData : {},
    theme: state.global.theme || 'wh'
  }
}

const mapDispatchToProps = (dispatch, {as, immutable}) => bindActionCreators(immutable ? immutableActions : actions, dispatch, as)
export default connect(mapStateToProps, mapDispatchToProps)(ModalComponent);
