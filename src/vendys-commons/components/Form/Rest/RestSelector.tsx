import React, { ReactElement, useMemo } from 'react';
import { useQuery } from 'react-query';

export interface RestSelectorProps {
  key?;
  id;
  children;
  queryFunc;
  translate?;
  mapping?;
}

const RestSelector: React.FC<RestSelectorProps> = (props) => {
  const { id, children, queryFunc, mapping, translate } = props;
  const { isLoading, isSuccess, isError, isLoadingError, data } = useQuery(id, () => queryFunc(), {});
  const childrenProps = useMemo(() => {
    const result = {
      data: translate ? translate(data) : data,
      loading: isLoading,
      isSuccess,
      isError,
      isLoadingError
    };
    // console.log(`useQuery key = ${id}: `, result);
    return mapping ? mapping(result) : result;
  }, [isLoading, isSuccess, isError, isLoadingError, data]);
  //
  // const content = useMemo(() => children && React.cloneElement(children, childrenProps), [childrenProps, children])
  // // const content = children && React.cloneElement(children, childrenProps);
  // console.log(content && content.props);
  return <>{children && React.cloneElement(children, childrenProps)}</>;
};

export default RestSelector;
