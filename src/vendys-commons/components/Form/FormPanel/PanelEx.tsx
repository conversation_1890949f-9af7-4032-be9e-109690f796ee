import React, { ReactElement, useState, useMemo, useEffect} from 'react'
import {
    <PERSON>u,
    Item,
    Separator,
    Submenu,
    useContextMenu
  } from "react-contexify";

import Panel from './Panel';

interface Props {
    View: ReactElement
    query: (o:object)=>{}
    bindingProps: []
    manipulator: (o:object)=>{}
    onClick: (e:object)=>{}
    contextMenu: {id:string, view: object}
}

export default function PanelEx(props: Props): ReactElement {
    var { View, bindingProps = [], query, manipulator, viewProps = {}, children, contextMenu = {}} = props;
    var [_data, setData] = useState({});

    const { show } = useContextMenu({
        id: contextMenu.id
    });

    function handleContextMenu(event){
        if(!contextMenu.id) return;
        
        event.preventDefault();
        show(event, {
          props: {
              key: 'value'
          }
        })
    }

    const onClick = (event)=>{
        handleContextMenu(event);
        props.onClick(event);
    }

    var _bindingProps:[] = useMemo(()=>{
        return bindingProps.map(b=>props[b]);
    },[bindingProps])

    useEffect(()=>{
        (async ()=>{
            var result =  await query(props);
            if(!result) return;
            if(manipulator){
                setData( manipulator(result) );
            }else{
                if(result.constructor == Promise){
                    result.then(setData);
                }else{
                    setData(result);
                }
            }
        })();
    },_bindingProps)

    return (
        <Panel {...props} title={props.title} style={{width: '100%', height: '100%'}}
        onClick={onClick}>
            {View && <View {...viewProps} {..._data} style={{height: '100%'}} />}
            {children}
        </Panel>
    )
}
