import React, { ReactElement, useMemo, useState } from 'react'

import { Popover, Button, Menu, Radio } from 'antd'
const { SubMenu } = Menu;
import { LeftOutlined, UnorderedListOutlined } from '@ant-design/icons';

interface Props {
    actions: [],
    actioType: 'menu' | 'buttonbar'
}

const menus = [
    {title: 'menu1', icon: <LeftOutlined />, onClick: ()=>{console.log('menu1')}}
]

export default function PanelMenu({actions, actionType = 'menu'}: Props): ReactElement {

    const [visible, setVisible] = useState(false);

    const hide = ()=>{
        setVisible(false);
    }

    const  handleVisibleChange = (v)=>{
        setVisible(v);
    }

    const menus = useMemo(()=>{
        if(!actions) return;
        return <Menu onClick={hide}>
            {actions.map((row, idx)=>{
                return <Menu.Item key={`menu-${idx}`} onClick={row.onClick}>{row.title}</Menu.Item>
            })}
        </Menu>
    }, [actions])

    const buttonbars = useMemo(()=>{
        if(!actions) return;
        return <Radio.Group onChange={(idx)=>{actions[idx].onClick}}>
            {actions.map((row,idx)=>{
                return <Radio.Button value={idx}>{row.title}</Radio.Button>
            })}
      </Radio.Group>
    }, [actions]);

    return (
        <Popover
        content={actionType == 'menu' ? menus : buttonbars}
        trigger="click"
        visible={visible}
        onVisibleChange={handleVisibleChange}
      >
        <Button type="primary" size="small" icon={<UnorderedListOutlined />}></Button>
      </Popover>
    )
}
