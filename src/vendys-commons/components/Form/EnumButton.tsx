 import * as React from 'react';
import { Select, Button, Radio } from 'antd';
import { useState, useEffect } from 'react';
  const Option = Select.Option;
const ButtonGroup = Button.Group;

export interface IEnumButtonProps {
  buttonGroup: boolean;
  data: object;
  value: string;
  onChange?: (String)=> void;
}

const EnumButton:React.FC<IEnumButtonProps> = (props)=>{
  var { data, value, buttonGroup = true, defaultValue, onChange = ()=>{}, style } = props;

  var [ _value, setValue ] = useState(undefined)

  useEffect(()=>{
    var isExist = false;
    Object.keys(data).map((key,idx)=>{
      if(key == _value){
        isExist = true
      }
    })

    if(isExist) return;

    Object.keys(data).map((key,idx)=>{
      if(idx == 0){
        setValue(key)
      }
    })
  }, [data])

  useEffect(()=>{
    setValue(value);
  }, [value])

  const _onChange = (v)=>{
    setValue(v);
    onChange(v);
  }

  if(!data){
      return <div></div>
  }

  if(buttonGroup){
    return (<div style={{...style}}>
      <Radio.Group defaultValue={defaultValue} value={_value} onChange={e=>_onChange(e.target.value)}>
      {data && Object.keys(data).map(key=>{
        var v = data[key];
        return <Radio.Button key={key} value={key} >{v}</Radio.Button>
      }
      ) }
      </Radio.Group>
  </div>);
  }else{
    return (<div style={{...style}}>
      {data && Object.keys(data).map(key=>{
        var v = data[key];
        return <Button key={key} value={key} {...props}>{v}</Button>
      }
      ) }
  </div>);
  }
}

export default EnumButton;
/*
class EnumButton extends React.Component<IEnumButtonProps> {

  static defaultProps = {
    buttonGroup: false,
  }

  public render() {

    var { data, value, buttonGroup, defaultValue} = this.props;

    if(!data){
        return <div></div>
    }else{

      if(buttonGroup){
        return (<div style={{...this.props.style}}>
          <Radio.Group defaultValue={defaultValue} value={value} onChange={e=>this.props.onChange(e.target.value)}>
          {data && Object.keys(data).map(key=>{
            var v = data[key];
            return <Radio.Button key={key} value={v} >{v}</Radio.Button>
          }
          ) }
          </Radio.Group>
      </div>);
      }else{
        return (<div style={{...this.props.style}}>
          {data && Object.keys(data).map(key=>{
            var v = data[key];
            return <Button key={key} value={v} {...this.props}>{value}</Button>
          }
          ) }
      </div>);
      }
    }
  }
}
*/