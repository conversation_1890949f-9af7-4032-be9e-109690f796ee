import * as React from 'react';
import { Popover, Button, InputNumber, Input } from 'antd';
import { SketchPicker } from 'react-color';
import {createSliderWithTooltip, Range as baseRange} from 'rc-slider';
import Faker from 'faker';

import {EnumSelector} from 'vcomponents/Form';
import { ColorPicker } from './index'
const Range = createSliderWithTooltip(baseRange);
import 'rc-slider/assets/index.css';

export interface LevelSelectorProps {
}

enum LevelType{
    numbers = 'numbers',
    strings = 'strings',
    boolean = 'boolean',
}

var fn = function(options){
    return function(value){
        // var { levels, } = options;
        var { type, data, colors } = options;

        var targetIndex = 0;
        switch(type){
            case 'numbers':
                for(var i = 0 ; i < data.length - 1; i++){
                    if(data[i] < value && data[i+1] >= value){
                        targetIndex = i;
                    }
                }
            break;

            default:
                for(var i = 0 ; i < data.length - 1; i++){
                    if(data[i] == value){
                        targetIndex = i;
                    }
                }
            break;
        }

        if(colors.length-1 < targetIndex){
            return colors[colors.length - 1]
        }else{
            return colors[targetIndex]
        }
    }
}

export default class LevelSelector extends React.Component<LevelSelectorProps> {

    constructor(props){
        super(props);
        var colors = [Faker.internet.color(),Faker.internet.color()];

        this.state = {
            slider: {
                min: 1, max: 100,
                trackStyle: colors.map(color=>{return {backgroundColor: color}}),
            },
            data:{
                type: LevelType.numbers,
                data: [50],
                colors,
                ...props.defaultValue
            }
        }
    }

    onTypeChange(type){
        this.setState({data: { ...this.state.data, type} });
        this.triggerChange();
    }

    onTextChange(idx, v){
        var { data } = this.state.data;

        data[idx] = v;
        this.setState({data: {...this.state.data, data: data} })
        this.triggerChange();
    }

    triggerChange(){
        var { onChange } = this.props;
        if(onChange) {
            setTimeout(()=>{
                onChange({...this.state, fn: fn(this.state.data)});
            })
        }
    }

    onCountChange(v){
        var { data, colors, type} = this.state.data;
        var { slider : {min, max}} = this.state;
        var newData = [];

        if(type == 'numbers'){
            var count = Math.floor( (max-min)/v );
            var idx = 0 ;
            for(var i = 0 ; i < v;i++){
                idx += count;
                newData.push(idx);
            }
        }else if(type == 'strings'){
            for(var i = 0 ; i < v;i++){
                newData.push(Faker.name.lastName());
            }
        }else{
            for(var i = 0 ; i < v;i++){
                newData.push();
            }
        }

        var newV = v;
        if(type === 'numbers'){ newV++ }

        if(colors.length < newV){
            colors.push(Faker.internet.color());
        }else{
            colors = colors.slice(0, newV);
        }

        this.setState({data: {...this.state.data, data: newData, colors: colors}})

        this.triggerChange();
    }

    onMaxChange(v){
        this.setState({slider: {...this.state.slider, max: v}})
        this.triggerChange();
    }

    onColorChange(idx, v){
        var { colors } = this.state.data;
        colors[idx] = v;
        this.setState({data: {...this.state.data, colors: [...colors]}})

        this.triggerChange();
    }

    drawSelector(type){
        var { data, slider, colors } = this.state.data;
        var { slider: {min , max}, trackStyle } = this.state;
        switch(type){
            case 'numbers':
                return <div>
                    <div>count: <InputNumber min={min} max={10} value={data.length} onChange={this.onCountChange.bind(this)} /></div>
                    <div>maxValue : <InputNumber min={0} value={max} onChange={this.onMaxChange.bind(this)} /></div>
                    <Range key={data.length}
                        style={{ marginBottom: 15, marginTop: 20, }}
                        min={min}
                        max={max}
                        defaultValue={data}
                        trackStyle={trackStyle}
                    />
                    {colors.map((color,idx)=>{
                        return <ColorPicker key={'color'+idx} value={color} onChange={this.onColorChange.bind(this,idx)} />
                    })}
                </div>
            break;

            case 'strings':
                return <div>
                    count: <InputNumber min={min} max={10} value={data.length} onChange={this.onCountChange.bind(this)} />
                    {data.map((row,idx)=>{
                        return <div>
                            <Input value={row} onChange={this.onTextChange.bind(this,idx)} />
                            <ColorPicker key={'color'+idx} value={colors[idx]} onChange={this.onColorChange.bind(this,idx)} />
                        </div>
                    })}
                </div>
            break;

            case 'boolean':

            break;

            default: return <div></div>
        }
    }

    public render() {
        var { type } = this.state.data;

        return (<div>
            <EnumSelector value={type} data={LevelType} onChange={this.onTypeChange.bind(this)} />
            {this.drawSelector(type)}
        </div>);
    }

    componentDidMount(){

    }
}
