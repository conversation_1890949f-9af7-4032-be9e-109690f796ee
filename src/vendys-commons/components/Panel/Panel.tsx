import React, { ReactElement, useMemo, FC } from 'react'
import PropTypes from 'prop-types'
import { Popover, Button, Space, Skeleton} from 'antd'

import { Container, Header, LeftHeader, Title, SubTitle, Body, ActionButtons} from './PanelStyle';
import { DatabaseOutlined } from '@ant-design/icons'
import { FormattedMessage, useIntl } from 'react-intl';

import PanelMenu from './PanelMenu'
import { FormattedMessageFixed } from '../../utils';

export interface PanelProps {
  title: string,
  subtitle?: string,
  onClick: (id:string)=>{},
  content?: [],
  actions?: [],
  controls?: [],
  flat?: boolean,
  queryId?: string,
  style?: object,
  loading: boolean,
  children?: any
}

const PanelEx:FC<PanelProps> = function ({title, subtitle, actions, children, controls, style, queryId, loading=false, onClick, flat=false}) {
    const intl = useIntl()
    const _title = useMemo(()=>{
        // var t = intl.formatMessage({id:title});
        var t = FormattedMessageFixed({id: title});
        return t;
    },[title])

    const redashMove = (queryId) => {
        window.open(`https://redash.mealc.co.kr/queries/${queryId}`)
    }

    return (
        <Container style={{...style}} >
            <Header>
                <LeftHeader>
                    <Title>{_title || title}</Title>
                    {subtitle && <SubTitle>{subtitle}</SubTitle>}
                </LeftHeader>
                <Space>
                  {controls && controls.map(control=>{
                    var { id, icon , component, title, _onClick } = control
                    if(icon){
                        return <Button size="small" icon={icon} onClick={()=> { (_onClick && _onClick()); onClick(id) } }>{title}</Button>
                    }else if(component){
                        return component;
                    }
                  })}
                  {actions && <ActionButtons>
                      <PanelMenu actions={actions} />
                  </ActionButtons>}
                  {queryId ? <Button size="small" icon={<DatabaseOutlined/>} onClick={() => { redashMove(queryId) }} /> : null}
                </Space>
            </Header>
            <Body>
                <Skeleton loading={loading} active avatar>
                {children}
                </Skeleton>
            </Body>
        </Container>
    )
}

PanelEx.propTypes = {
    // title: PropTypes.string,
}

PanelEx.defaultProps = {
    title: '제목',
    onClick: (id)=>{},
}

export default PanelEx
