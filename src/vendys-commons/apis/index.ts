// make service
import axios from 'axios';
import { registServer } from 'rest/sikdae'

export const sum = (a,b)=>{
    return a+b;
}


const timeout = 30000;

export const ADMIN_SERVER = axios.create({
    baseURL: `${process.env['ADMIN_API']}`,
    timeout,
    // withCredentials: true,
    // responseType: 'json',
    headers: {
        'Content-Type': 'application/json'
    }
});
registServer('ADMIN_SERVER', ADMIN_SERVER);

export const AUTH_SERVER = axios.create({
    baseURL: `${process.env['AUTH_URL']}`,
    timeout,
    // withCredentials: true,
    // responseType: 'json',
    headers: {
        'Content-Type': 'application/json'
    }
});
registServer('AUTH_SERVER', AUTH_SERVER);

export const DOMAIN_SERVER = axios.create({
    baseURL: `${process.env['DOMAIN_URL']}`,
    timeout,
    // withCredentials: true,
    // responseType: 'json',
    headers: {
        'Content-Type': 'application/json'
    }
});
registServer('DOMAIN_SERVER', DOMAIN_SERVER);

export const GR_SERVER = axios.create({
    baseURL: `${process.env['GR_URL']}`,
    timeout,
    // withCredentials: true,
    // responseType: 'json',
    headers: {
        'Content-Type': 'application/json'
    }
});
registServer('GR_SERVER', GR_SERVER);

export const AWS_S3_SERVER = axios.create({
    baseURL: `${process.env['AWS_S3_URL']}`,
    timeout,
    // withCredentials: true,
    // responseType: 'json',
    headers: {
        'Content-Type': 'application/json'
    }
    // credentials: 'include',
});
registServer('AWS_S3_SERVER', AWS_S3_SERVER);

export const REDASH_SERVER = axios.create({
  baseURL: `/redash`,
  timeout,
  headers: {
      'Content-Type': 'application/json'
  },
  withCredentials: true,
  // credentials: 'same-origin',
  // crossdomain: true,
});
registServer('REDASH', REDASH_SERVER);

export const GRAYLOG_SERVER = axios.create({
  baseURL: `/graylog`,
  timeout,
  headers: {
      'Content-Type': 'application/json'
  },
  withCredentials: true,
  // credentials: 'same-origin',
  // crossdomain: true,
});
registServer('GRAYLOG_SERVER', GRAYLOG_SERVER);

export const CRM_SERVER = axios.create({
    baseURL: `/crm`,
    timeout,
    headers: {
        'Content-Type': 'application/json',
    },
    withCredentials: false,
});

export const changeHeader = (token?:string)=>{
    if(!token){
        delete ADMIN_SERVER.defaults.headers.Authorization;
        delete AUTH_SERVER.defaults.headers.Authorization;
        delete DOMAIN_SERVER.defaults.headers.Authorization;
        delete GR_SERVER.defaults.headers.Authorization;
        delete AWS_S3_SERVER.defaults.headers.Authorization;
        return;
    }
    ADMIN_SERVER.defaults.headers.Authorization = `Bearer ${token}`;
    AUTH_SERVER.defaults.headers.Authorization = `Bearer ${token}`;
    DOMAIN_SERVER.defaults.headers.Authorization = `Bearer ${token}`;
    GR_SERVER.defaults.headers.Authorization = `Bearer ${token}`;
    AWS_S3_SERVER.defaults.headers.Authorization = `Bearer ${token}`;
};
