import React, { ReactElement, useState, useEffect } from 'react'
import { Popover, Button } from 'antd';
import {
    Menu,
    Item,
    Separator,
    Submenu,
    useContextMenu
  } from "react-contexify";
interface Props {
}

export const ContextMenu = React.createContext({
    setContextMenu: ()=>{}, 
    showContextMenu: ()=>{},
    hideContextMenu: ()=>{}
});


const drawMenu = (id, menus) =>{
    return <Menu id={id} key={`menu_${id}`}>
        {menus.map( ({label, onClick = ()=>{}, children}, index) =>{
            if(children && children.length > 0){
                return <Submenu label={label} key={'submenu_'+id+'_'+index}>
                    {drawMenuItem({label, onClick, children, index})}
                </Submenu>
            }else{
                return drawMenuItem({label, id, index, onClick});
            }
        })
        }
    </Menu>
}
const drawMenuItem = ({onClick= ()=>{}, label, id, index })=>{
    return <Item onClick={onClick} key={'menu_'+id+'_'+index}>{label}</Item>
}

function InnerContextMenu({id, menus, event, visible = false, onReceive=()=>{}}){
    var {show, hideAll} = useContextMenu({id});

    useEffect(()=>{
        onReceive(id, show);
    },[])

    return <div>
        {drawMenu(id, menus)}
    </div>
}


export default function ContextMenuView({children}: Props): ReactElement {
    const [_contents, setContents] = useState({});
    // const [_content, setContent] = useState(<div>empty</div>);
    // const [_viisble, setVisible] = useState(false);

    const [ _contexts, setContexts ] = useState({});

    const [ _callbacks, setCallback ] = useState({});

    const setContextMenu = (id, menus)=>{
        setContents(c=>{ return {...c, [id]: {id, menus}} });
        // var context = useContextMenu({id});
        // setContexts(oldContext=> {return {...oldContext, [id]:  context}} );
    }

    const showContextMenu = (id, event, meta = {})=>{
        // if(_content[id]){
        //     setContent(_content[id]);
        // }
        // setContents(c=>{return {...c, [id]: { ...c[id], visible: true, event}} });
        // setVisible(true);
        _callbacks[id](event);
    }

    const onReceive = (id, func) => {
        setCallback(c=>{ return {...c, [id]: func } });
    }

    const hideContextMenu = ()=>{
        // setVisible(false);
    }
    const api = { setContextMenu, showContextMenu, hideContextMenu };
    
    return (
        <ContextMenu.Provider value={api}>
            {children}
            {Object.keys(_contents).map(id=>{
                return <InnerContextMenu {..._contents[id]} key={id} onReceive={onReceive}/> 
            })}
        </ContextMenu.Provider>
    )
}
