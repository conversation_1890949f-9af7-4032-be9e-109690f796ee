import React, { ReactElement, useState} from 'react'

 const ModalContext = React.createContext({
  showModal: (id)=>{},
  hideModal: (id)=>{},
  params: {/*[id]: 'a'*/},
  visible: {}
});

interface IModalContextProps {

}

export const ModalContextProvider:React.FC<IModalContextProps> = ({children})=> {

    const showModal = (id, params)=>{
      setData({...data, visible: { ...data.visible, [id]: true }, params: { ...data.params, [id]: params } });
    }

    const hideModal = (id)=>{
        setData({...data, visible: { ...data.visible, [id]: false }, params: { ...data.params, [id]: undefined } });
    }
    const [data, setData] = useState({showModal, hideModal, visible: {}, params: {}});

    return (
        <ModalContext.Provider value={data}>
            {children}
        </ModalContext.Provider>
    )
}

export function withModalContext(Component) {
	return function contextComponent(props) {
		return (
			<ModalContext.Consumer>
				{context => <Component {...props} context={context} />}
			</ModalContext.Consumer>
		)
	}
}

export default ModalContext
// export default ModalContextProvider;
