import React from 'react'
import styled from 'styled-components'

import { Button, Space, Popconfirm } from 'antd'
import { DeleteOutlined, EditOutlined, CopyOutlined  } from '@ant-design/icons'
import { FormattedMessage } from 'react-intl'
export { FormattedMessage } from 'react-intl'
export { EnumSelector } from 'vcomponents/Form'

export { InputNumber } from 'antd'

export const crud = (props)=>{
    const { vivisbleDelete = true, title='msg.delete.confirm', visibleEdit = true, row:{original}, visibleCopy = true, actions: {confirm=()=>{}, cancel=()=>{}} } = props;
    return <Space align="center" style={{width: '100%', justifyContent: 'center'}}>
        {vivisbleDelete ?
        <Popconfirm
            title={<FormattedMessage id={title} />}
            onConfirm={()=>confirm('delete', original)}
            onCancel={()=>cancel('delete', original)}
            okText={<FormattedMessage id="yes" />}
            cancelText={<FormattedMessage id="no" />}
        >
        <Button type="primary" icon={<DeleteOutlined />} /></Popconfirm> : undefined }
        {visibleEdit ? <Button type="primary" icon={<EditOutlined />} onClick={()=>confirm('modify', original)}/> : undefined }
        {visibleCopy ? <Button type="primary" icon={
        <Popconfirm
            title={<FormattedMessage id="msg.copy.confirm" />}
            onConfirm={()=>confirm('copy', original)}
            onCancel={()=>cancel('copy', original)}
            okText={<FormattedMessage id="yes" />}
            cancelText={<FormattedMessage id="no" />}
        >
        <CopyOutlined /></Popconfirm> } /> : undefined }
    </Space>
}

const Crud = crud;

export const del = (props)=>{
    return <Crud {...props} visibleEdit={false} visibleCopy={false} title="delete" />
}

export const update = (props)=>{
    return <Crud {...props} vivisbleDelete={false} visibleCopy={false} title="update" />
}

export const updateDelete = (props)=>{
    return <Crud {...props} visibleCopy={false} />
}

export { default as AddressPost } from 'vcomponents/AddressPost/index2'
