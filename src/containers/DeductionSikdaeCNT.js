import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import moment from 'moment-timezone';
import { Commons, UseStats } from 'components';
import * as cm_action from 'actions/commons';
import * as usg_action from 'actions/usage';
import cm from 'helpers/commons';
import toastr from 'helpers/toastr';

moment.tz.setDefault('Asia/Seoul');

class DeductionSikdaeCNT extends Component {
  constructor(props) {
    super(props);
    this.state = {
      page: 1,
      pagerow: 15,
      totalcount: null,
      staffModal: {
        open: false,
        size: 'large'
      },
      staff: {
        name: '',
        id: ''
      }
    };
  }

  componentWillMount() {
    cm.propsInit(this.props);
    // 식대 사용 통계 리스트
    this.deductionUsageStatList();
  }

  componentDidMount() {
    cm.mainTitle('삼성물산 급여공제');
  }

  shouldComponentUpdate(nextProps, nextState) {
    let result = true;

    const nCommonsProps = {
      dateType: nextProps.dateType,
      date: nextProps.date,
      depth: nextProps.depth
    };

    const bCommonsProps = {
      dateType: this.props.dateType,
      date: this.props.date,
      depth: this.props.depth
    };

    if (JSON.stringify(nCommonsProps) !== JSON.stringify(bCommonsProps)) {
      result = false;
    }

    return result;
  }

  modalAction = (type, dimmer) => {
    if (type === 'open') {
      this.staffSel(1, null);
    }

    this.setState({
      dimmer,
      staffModal: { open: dimmer }
    });
  };

  staffSel = async (page, keyword) => {
    const { cmAPI } = this.props;

    const params = {
      page,
      pagerow: 10,
      keyword
    };

    try {
      await cmAPI.staffSel(params);
    } catch (e) {
      console.log('###### staffSel error : ', e);
    }
  };

  choiceStaff = (data) => {
    this.setState({
      staff: {
        name: data ? data.staff.name : '',
        id: data ? data.staff.id : ''
      },
      staffModal: { open: false }
    });
  };

  submitForm = async (e) => {
    e.preventDefault();

    const { dateCheck } = this.props;

    if (!dateCheck.isValue.isState) {
      toastr('top-right', 'warning', dateCheck.isValue.message);
      return;
    }

    await this.setState({ page: 1 });
    this.deductionUsageStatList();
  };

  deductionUsageStatList = async () => {
    const { date, usageAPI } = this.props;
    const { page, pagerow, totalcount, staff } = this.state;

    const params = {
      startDate: date
        ? date.sDate
        : moment()
            .startOf('month')
            .format('LLL'),
      endDate: date ? date.eDate : moment().format('LLL'),
      page,
      pagerow,
      userId: staff.id === '' ? null : staff.id,
      totalcount: page !== 1 ? totalcount : null
    };

    await usageAPI.usageStatList(params);

    const { usageStat } = this.props;

    if (usageStat && usageStat.data) {
      const { paging } = usageStat.data;
      this.setState({ totalcount: paging.totalcount });
    }

    const { cmAPI } = this.props;

    cmAPI.changeExcel({
      excel: params
    });
  };

  pageClick = async (page) => {
    await this.setState({ page });
    this.deductionUsageStatList();
  };

  render() {
    const { usageStat } = this.props;
    const { page, pagerow, staffModal, dimmer, staff } = this.state;

    const modal = {
      open: staffModal.open,
      size: staffModal.size,
      dimmer,
      close: this.modalAction
    };

    const sb = {
      fn: {
        choiceStaff: this.choiceStaff
      }
    };

    const listFn = {
      page,
      pagerow,
      pageClick: this.pageClick
    };

    let loading = '';
    if (!usageStat) {
      loading = <Commons.LoadingBar />;
    }

    return (
      <main className="sus">
        {loading}

        <UseStats.DeductionSearchBox
          modalAction={this.modalAction}
          staff={staff}
          fn={sb.fn}
          dateType="DD"
          submitForm={this.submitForm}
        />

        <UseStats.StatsList listFn={listFn} />
        {staffModal.open ? (
          <Commons.StaffSearchModal modal={modal} staffSel={this.staffSel} choiceEvent={this.choiceStaff} />
        ) : null}
      </main>
    );
  }
}

DeductionSikdaeCNT = connect(
  (state) => ({
    // common function call
    dateType: state.commons.dateType,
    date: state.commons.date,
    dateCheck: state.commons.dateCheck,
    division: state.commons.division,
    depth: state.commons.depth,
    orgIdx: state.commons.orgIdx,

    staffList: state.commons.staff,
    storeList: state.commons.companyStore,

    groupIdx: state.commons.group,
    policyIdx: state.commons.policy,
    // usage function
    usageStat: state.usage.usageStats
  }),
  (dispatch) => ({
    cmAPI: bindActionCreators(
      {
        staffSel: cm_action.StaffSel,
        changeExcel: cm_action.ChangeExcel
      },
      dispatch
    ),

    usageAPI: bindActionCreators(
      {
        usageStatList: usg_action.DeductionSamsungCNTUsageStatsList
      },
      dispatch
    )
  })
)(DeductionSikdaeCNT);

export default DeductionSikdaeCNT;
