/* eslint-disable react/jsx-props-no-spreading */
import React, { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Switch, Route } from 'react-router-dom';
import moment from 'moment-timezone';
import moments from 'moment';
import { HolidayHeader, HolidayResult, HolidayModify } from 'components/HolidayManagement';
import { LoadingBar } from 'components/Commons';
import * as holidayAction from 'actions/holiday';

import cm from 'helpers/commons';
import PropTypes from 'prop-types';
import toastr from 'helpers/toastr';
import { Segment } from 'semantic-ui-react';

moment.tz.setDefault('Asia/Seoul');

const propTypes = {
  holidayList: PropTypes.shape({
    data: PropTypes.shape({
      content: PropTypes.array
    })
  }),
  loading: PropTypes.bool,
  holidayAPI: PropTypes.objectOf(PropTypes.any).isRequired
};

const defaultProps = {
  holidayList: {
    data: {
      content: []
    }
  },
  loading: false
};

class HolidayManagement extends Component {
  constructor(props) {
    super(props);
    const baseYear = 2020;
    const currentYear = Number(moments().format('YYYY'));
    const yearOptions = [{ value: baseYear.toString(), text: `${baseYear}년` }];

    for (let i = 0; i < currentYear - baseYear + 2; i++) {
      const year = baseYear + i + 1;
      yearOptions.push({ value: year.toString(), text: `${year}년` });
    }

    this.state = {
      options: yearOptions,
      selected: currentYear.toString()
    };
  }

  async componentDidMount() {
    cm.mainTitle('공휴일 관리');
    await this.loadHolidayList(moments().format('YYYY'));
  }

  loadHolidayList = async (year) => {
    const { holidayAPI } = this.props;
    try {
      await holidayAPI.holidayCompanyList(year);
    } catch (e) {
      const {
        response: { data }
      } = e;
      if (data) {
        toastr('top-right', 'error', data.message);
      }
    }
  };

  handleChange = async (year) => {
    this.setState({ selected: year });
    await this.loadHolidayList(year);
  };

  moveModify = () => {
    const {
      router: { history }
    } = this.context;

    const {
      location: { pathname }
    } = history;

    const {
      holidayList: {
        data: { content }
      }
    } = this.props;
    const { selected } = this.state;

    const currentYear = Number(moments().format('YYYY'));
    if (selected < currentYear) {
      alert('과거 연도의 공휴일은 수정할 수 없습니다.');
      return;
    }
    if (content.length === 0) {
      alert('공휴일이 설정되어 있지 않습니다. 필요한 경우, 식권대장에 문의해주세요.');
      return;
    }
    const url = `${pathname}/${selected}`;
    history.push(url);
  };

  render() {
    const {
      loading,
      match,
      holidayList: {
        data: { content }
      }
    } = this.props;

    const { selected, options } = this.state;
    return (
      <main>
        {loading ? <LoadingBar /> : null}
        <Switch>
          <Route
            exact
            path={match.path}
            component={() => (
              <Fragment>
                <Segment vertical>
                  <HolidayHeader
                    header="history"
                    options={options}
                    value={selected}
                    onSearch={this.handleChange}
                    onMovePage={this.moveModify}
                  />
                </Segment>
                <HolidayResult holidays={content} />
              </Fragment>
            )}
          />
          <Route
            path={`${match.path}/:id`}
            component={(props) => <HolidayModify holidayList={content} onSearch={this.handleChange} {...props} />}
          />
        </Switch>
      </main>
    );
  }
}

HolidayManagement.propTypes = propTypes;
HolidayManagement.defaultProps = defaultProps;

HolidayManagement.contextTypes = {
  router: PropTypes.objectOf(PropTypes.any).isRequired
};

export default connect(
  (state) => ({
    holidayList: state.holiday.holidayCompanyList,
    loading: state.holiday.isPending
  }),
  (dispatch) => ({
    holidayAPI: bindActionCreators(
      {
        holidayCompanyList: holidayAction.HolidayCompanyInfoList
      },
      dispatch
    )
  })
)(HolidayManagement);
