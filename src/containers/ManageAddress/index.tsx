import React, { ReactElement, useState, useEffect, useMemo } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import useFetch from 'use-http';

import api from 'config';
import { FormattedMessage, useIntl } from 'react-intl';
import { withModalContext } from 'context/ModalContext';
import { useQuery, useMutation } from 'react-query';

import messages from './messages';
import { columns } from './viewMeta';
import { Container } from './styles';
import { defaultAction } from './actions';
import * as Title from 'vcomponents/Forms/Title';
import ActionButtonBar from 'components/ActionButtonBar';
import { Button, message, Skeleton, Modal, Spin } from 'antd';
import { PageContent } from 'vendys-commons/styles/global';
import Table from 'vcomponents/Table';

import { getAddressBook, delAddressBook } from './state';
import AddressPost from 'vcomponents/AddressPost';
import AddressPostPopup from 'vcomponents/AddressPost/AddressPostPopup';
import { FormattedMessageDynamic } from 'vendys-commons/utils';

import { DeleteSuccess } from 'components/Message';

import { ExclamationCircleOutlined } from '@ant-design/icons';
import { COMPANY_API } from 'apis';
import { useMount } from 'react-use';
import ViewPanel from 'vcomponents/Form/ViewPanel';

interface ManageAddressProps {}

function ManageAddress(props: ManageAddressProps): ReactElement {
  const { history } = props;
  const intl = useIntl();
  const [paging, setPaging] = useState({ page: 1, pageRow: 15 });
  const { isLoading, isError, data: addressBook = {}, error, refetch } = useQuery(
    ['getAddressBook', paging],
    () => getAddressBook(paging),
    {
    retry: false
  });
  const { isLoading: isDelLoading, isError: isDelError, mutate, error: delError } = useMutation(
    ({ addressBookIdx }) => delAddressBook(addressBookIdx),
    {
      onSuccess: async () => {
        await DeleteSuccess();
        refetch();
      }
    }
  );

  const showModalAddAddress = () => {
    props.context.showModal('addressPost');
  };

  const gotoReservationList = () => {
    history.push({
      pathname: '/service/quick/reservation/list'
    });
  };

  const onAddComplete = () => {
    message.success(<FormattedMessage id="quick.address.add.complete" />);
    refetch();
  };

  const onConfirm = (t, row) => {
    if (t == 'delete') {
      mutate(row);
    }
  };

  return (
    <Container>
      <ViewPanel
        title="address.manage"
        buttons={[
          <Button onClick={gotoReservationList} key="btn1">
            <FormattedMessage id="quick.reservation.list" />
          </Button>,
          <Button type="primary" onClick={showModalAddAddress} key="btn2">
            <FormattedMessage id="quick.address.add" />
          </Button>
        ]}
      >
        <Spin spinning={isLoading || isDelLoading}>
          <ActionButtonBar></ActionButtonBar>
          <PageContent>
            <Table
              data={addressBook.data || []}
              columns={columns}
              pageInfo={addressBook.pageInfo || {}}
              confirm={onConfirm}
              onChange={(d) => setPaging({ ...paging, ...d })}
            ></Table>
          </PageContent>
        </Spin>
      </ViewPanel>
      <AddressPostPopup id="addressPost" titleId="quick.address.add" onSuccess={onAddComplete} />
    </Container>
  );
}
export default withModalContext(ManageAddress);
