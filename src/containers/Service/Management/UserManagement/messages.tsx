/*
 * UserManagement Messages
 *
 * This contains all the text for the UserManagement container.
 */

import { defineMessages } from 'react-intl';

export const scope = 'app.containers.UserManagement';

export default defineMessages({
  header: {
    id: `${scope}.header`,
    defaultMessage: '사용자 관리'
  },
  checkbox: {
    id: `${scope}.checkbox`,
    defaultMessage: '휴면 예정 사용자만 노출'
  },
  dormant: {
    id: `${scope}.dormant`,
    defaultMessage: '휴면 사용자 관리'
  },
  addUser: {
    id: `${scope}.add.user`,
    defaultMessage: '사용자 추가'
  },
  position: {
    id: `${scope}.position`,
    defaultMessage: '직위 관리'
  },
  division: {
    id: `${scope}.division`,
    defaultMessage: '부서 이동'
  },
  active: {
    id: `${scope}.active`,
    defaultMessage: '일시정지 해제'
  },
  inactive: {
    id: `${scope}.inactive`,
    defaultMessage: '계정 일시정지'
  },
  withdraw: {
    id: `${scope}.withdraw`,
    defaultMessage: '계정 탈퇴'
  },
  changed: {
    id: `${scope}.changed`,
    defaultMessage: '정보 변경내역'
  },
  mustBeSelectedUser: {
    id: `${scope}.mustBeSelectedUser`,
    defaultMessage: '사용자를 선택 하세요.'
  },
  minLengthTen: {
    id: `${scope}.minLengthTen`,
    defaultMessage: '10자 이하로 입력해주세요..'
  },
  managementByExcel: {
    id: `${scope}.managementByExcel`,
    defaultMessage: '사용자 엑셀파일로 관리'
  }
});
