import styled from 'styled-components';
import { Spin, Button } from 'antd';

export const Container = styled(Spin)``;

export const ContentWrapper = styled.div`
  display: flex;
  gap: 8px;
  & .left-area {
    flex: 0 0 356px;
    max-width: 356px;
    & .ui.attached.tabular.menu {
      justify-content: flex-start;
      width: 100% !important;
    }
    & .ui.bottom.attached.segment {
      padding: 0;
    }
    & i.plus.icon,
    i.minus.icon {
      margin-top: -16px !important;
    }
    & i.sidebar.icon {
      top: 0 !important;
    }
  }
  & .right-area {
    flex: 1;
    max-width: calc(100% - 356px);
  }
`;

export const ButtonArea = styled.div`
  display: flex;
  justify-content: space-between;
  padding-top: 4px;
  padding-bottom: 4px;
`;

export const ExcelBtn = styled(Button)`
  float: right;
  background: #fff;
  border: solid 1px #30b843;
  color: #30b843;
`;

export const LeftAreaContainer = styled.div`
  & .ui.tabular.menu.vendysFullTab .active.item {
    border-top: 3px solid #0bb656;
  }
  & .ui.tabular.menu.vendysFullTab .item {
    border-top: 3px solid #eeeeee;
  }
  & .ui.tabular.menu.vendysFullTab .item:hover {
    border-top: 3px solid #0bb656;
  }
`;
