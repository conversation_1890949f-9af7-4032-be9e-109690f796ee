import React, { useState } from 'react';
import ModalComponent from 'vcomponents/ModalComponent';
import { FormattedMessageDynamic } from 'utils';
import { FormattedMessage } from 'react-intl';
import { withModalContext } from 'context/ModalContext';
import Popup from './Popup';
import { useMutation } from 'react-query';
import { welfareChange } from './state';
import messages from './messages';
import { message } from 'antd';

const WelfareChangeModal = ({ checked, context, callback }) => {
  const [welfare, setWelfare] = useState(null);
  const onSuccess = () => {
    context.hideModal('welfare');
    if (typeof callback === 'function') {
      callback('welfare');
    }
  };
  const { mutate } = useMutation('welfareChange', async ({ userIds, value }) => await welfareChange(userIds, value), {
    onSuccess
  });
  const save = async () => {
    const userIds = checked.map(({ id }) => id);
    if (!welfare) {
      await message.warn('복지그룹을 선택하세요');
      return;
    }
    mutate({
      userIds: userIds,
      value: welfare
    });
  };
  const onChange = (value) => {
    setWelfare(value);
  };
  return (
    <FormattedMessage {...messages.welfare}>
      {(msg) => {
        return (
          <ModalComponent
            title={msg}
            component={<Popup type="welfare" checked={checked} onChange={onChange} />}
            id="welfare"
            width="50%"
            handleOk={save}
            handleCancel={() => context.hideModal('welfare')}
            okText={<FormattedMessageDynamic id="complete" />}
            cancelText={<FormattedMessageDynamic id="cancel" />}
          />
        );
      }}
    </FormattedMessage>
  );
};

export default withModalContext(WelfareChangeModal);
