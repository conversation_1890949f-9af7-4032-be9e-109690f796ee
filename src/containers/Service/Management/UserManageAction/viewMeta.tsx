import { Popup } from 'semantic-ui-react';
import React from 'react';
import {Checkbox, Radio, Typography} from 'antd';
import cm from '../../../../helpers/commons';

import * as TextDecorators from 'decorators/text';

const { datetime: DateTime } = TextDecorators;

export const utilSum = () => {};

export const columns = [
  { Header: 'ID', accessor: 'signId' },
  { Header: '이름', accessor: 'name' },
  { Header: '부서1', accessor: 'groupName' },
  { Header: '이메일', accessor: 'email' },
  { Header: '휴대전화번호', accessor: 'phone' },
  { Header: '직위', accessor: 'rankPosition' },
  { Header: '권한', accessor: 'grade' },
  { Header: '사용자 상태', accessor: 'status' },
  { Header: '가입일', accessor: 'regdate' }
];

export const optionBarTemplate = [
  { id: 'empty', type: 'component', span: 3, data: <div>&nbsp;</div> },
  {
    id: 'userStatus',
    type: 'enum',
    props: { enumId: 'UserStatus', existTotal: true, totalText: '사용자 상태 전체', totalValue: '', defaultValue: '' }
    // span: 0.5
  },
  {
    id: 'userPermission',
    type: 'enum',
    props: { enumId: 'UserPermission', existTotal: true, totalText: '권한 전체', totalValue: '', defaultValue: '' }
    // span: 0.5
  },
  { id: 'inquiry', type: 'string', props: { placeholder: '검색어 입력' } }
];

export const popupChangedColumns = [
  { Header: '#', accessor: 'infoHistoryIdx' },
  { Header: '날짜', accessor: 'created' },
  { Header: '변경된 사용자', accessor: 'reservedSignId' },
  { Header: '변경 항목', accessor: 'typeName' },
  { Header: '상세내역', accessor: 'phone' },
  { Header: '상태', accessor: 'statusName' },
  { Header: '변경한 사람', accessor: 'createdUserName' }
];

export const popupChangedTemplate = [
  {
    id: 'type',
    type: 'enum',
    props: {
      enumId: 'UserStatusChanged',
      existTotal: true,
      totalText: '변경 항목 전체',
      totalValue: 'ALL',
      defaultValue: 'ALL'
    }
    // span: 0.5
  },
  { id: 'keyword', type: 'string', props: { placeholder: '검색어 입력' } }
];

export const passwordResetColumns = [
  {
    label: '초기화 비밀번호',
    id: 'password',
    name: 'password',
    type: 'string',
    viewProps: { type: 'password', placeholder: 'user.password' }
  },
  {
    label: '초기화 비밀번호 재입력',
    id: 'passwordConfirm',
    name: 'passwordConfirm',
    type: 'string',
    viewProps: { type: 'password', placeholder: 'user.password.confirm' }
  }
];

export const myPasswordChangeColumns = [
  {
    label: '현재 비밀번호',
    id: 'source',
    name: 'source',
    type: 'string',
    viewProps: { type: 'password', placeholder: '현재 비밀번호' }
  },
  {
    label: '변경할 비밀번호',
    id: 'target',
    name: 'target',
    type: 'string',
    viewProps: { type: 'password', placeholder: '변경할 비밀번호' }
  },
  {
    label: '변경할 비밀번호 재입력',
    id: 'targetConfirm',
    name: 'targetConfirm',
    type: 'string',
    viewProps: { type: 'password', placeholder: '변경할 비밀번호 재입력' }
  }
];

export const loginHistoriesColumns = [
  { Header: '로그인 일시', accessor: 'created', viewId: 'datetime_str' },
  { Header: 'IP', accessor: 'ip' },
  { Header: 'User Agent', accessor: 'userAgent' }
];
