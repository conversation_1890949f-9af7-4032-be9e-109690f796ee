import React, { useEffect, useMemo, useState } from 'react';
import ModalComponent from 'vcomponents/ModalComponent';
import { FormattedMessageDynamic } from 'utils';
import { FormattedMessage } from 'react-intl';
import { withModalContext } from 'context/ModalContext';
import OptionBar from 'vcomponents/OptionBar';
import Table from 'vcomponents/Table';
import { Button } from 'antd';
import { useMutation, useQueryClient, useQuery } from 'react-query';
import { PageContent } from 'styles/global';
import moment from 'moment';
import { popupChangedColumns, popupChangedTemplate } from './viewMeta';
import messages from './messages';
import { getChangedHistories } from './state';

const ChangedModal = ({ context }) => {
  return (
    <FormattedMessage {...messages.changed}>
      {(msg) => {
        return (
          <ModalComponent
            id="changed"
            title={msg}
            component={<PopupChanged />}
            width="60%"
            footer={[
              <Button key="close" onClick={() => context.hideModal('changed')}>
                <FormattedMessageDynamic id="close" />
              </Button>
            ]}
          />
        );
      }}
    </FormattedMessage>
  );
};

const PopupChanged = (props) => {
  const [paging, setPaging] = useState({ page: 1, pageRow: 10 });
  const [optionData, setOptionData] = useState({ type: 'ALL' });
  const queryClient = useQueryClient();
  const { isLoading, isSuccess, data = {} } = useQuery('getChangedHistories', (data) =>
    getChangedHistories({ ...paging, ...optionData })
  );
  useEffect(() => {
    (async () => {
      await search(undefined);
    })();
  }, [paging]);
  const search = async (param = optionData) => {
    if (isLoading) return;
    setOptionData({ ...optionData, ...param });
    const params = {
      ...param,
      ...paging
    };
    await queryClient.prefetchQuery('getChangedHistories', () => getChangedHistories(params));
  };

  const { content, pageInfo } = useMemo(() => {
    const array = data.content || [];
    const result = array.map(({ created, ...other }) => ({
      created: moment(created).format('YYYY-MM-DD HH:mm'),
      ...other
    }));
    const pInfo = {
      page: data ? data.page - 1 : 0,
      pageRow: data ? data.pageRow : 10,
      totalCount: data ? data.total : 0
    };
    return { content: result, pageInfo: pInfo };
  }, [data.content]);

  return (
    <div>
      <OptionBar template={popupChangedTemplate} colInRow={5} onClick={search} />
      <PageContent>
        <Table
          columns={popupChangedColumns}
          pageInfo={pageInfo}
          data={content}
          onChange={(d) => setPaging({ ...paging, ...d })}
        />
      </PageContent>
    </div>
  );
};

export default withModalContext(ChangedModal);
