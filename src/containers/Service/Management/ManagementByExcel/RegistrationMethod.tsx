import React, { ReactElement, useState } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import download from 'downloadjs';
import * as commons from 'actions/commons';
import storage from 'helpers/storage';
import toastr from 'helpers/toastr';
import cm from 'helpers/commons';
import { Radio, Button } from 'antd';
import { MethodContainer } from './styles';

interface RegistrationMethodProps {
  regMethodFn;
  commonsFn;
}

function RegistrationMethod(props: RegistrationMethodProps): ReactElement {
  const { regMethodFn } = props;
  const { activeStep, method, methodSel } = regMethodFn;
  const [isLoading, setIsLoading] = useState(false);
  const employeeAuth = cm.authGroupSet('employee');

  let btnTitle = '',
    disabledReg = false,
    disabledUpd = false,
    disabledDel = false,
    showReg = true,
    showUpd = true,
    showDel = true;

  if (activeStep === 1) {
    if (method === 'reg') {
      btnTitle = '신규 등록 ';
    } else if (method === 'upd') {
      btnTitle = '사용자 목록 ';
    } else if (method === 'del') {
      btnTitle = '퇴사처리 ';
    }
  } else if (activeStep === 2) {
    if (method === 'reg') {
      disabledUpd = true;
      disabledDel = true;
    } else if (method === 'upd') {
      disabledReg = true;
      disabledDel = true;
    } else if (method === 'del') {
      disabledReg = true;
      disabledUpd = true;
    }
  } else if (activeStep === 3) {
    if (method === 'reg') {
      showUpd = false;
      showDel = false;
    } else if (method === 'upd') {
      showReg = false;
      showDel = false;
    } else if (method === 'del') {
      showReg = false;
      showUpd = false;
    }
  }

  const downloadExcelForm = async () => {
    const { commonsFn } = props;
    const com = storage.get('company');
    let header = null;
    let downloadExcelFile = method === 'reg' ? '신규 등록' : method === 'upd' ? '사용자 목록' : '퇴사처리';
    setIsLoading(true);

    const param = {
      com,
      excel: {
        method: 'get',
        url: '/captain-payment/member/v1/employee/upload/download'
      },
      excelResult: {
        excel: {
          functype: method,
          viewType: 'all'
        }
      }
    };

    try {
      await commonsFn.excelDownload(param).then((result) => {
        const { value } = result;
        if (value) {
          header = value.headers['content-disposition'];
          header = header.split('=');
        }
        download(value.data, decodeURIComponent(header[1]));
        toastr('top-right', 'success', `${downloadExcelFile} 엑셀파일 생성 완료 되었습니다.`);
      });
    } catch (e) {
      const result = e.response;
      if (result && result.data) {
        toastr('top-right', 'error', `${downloadExcelFile} 엑셀파일 생성에 실패 하였습니다.`);
      }
      console.log('###### ExcelDownLoad : ', e);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <MethodContainer>
      <div>
        <div className="sub-title">등록 방식 선택하기</div>
        {activeStep <= 3 && (
          <Radio.Group onChange={methodSel} value={method} className="marginTop">
            {showReg && (employeeAuth.all || employeeAuth.create) && (
              <Radio value={'reg'} disabled={disabledReg}>
                신규 사용자 추가
              </Radio>
            )}
            {showUpd && (employeeAuth.all || employeeAuth.create) && (
              <Radio value={'upd'} disabled={disabledUpd}>
                기존 사용자 정보수정
              </Radio>
            )}
            {showDel && (employeeAuth.all || employeeAuth.create) && (
              <Radio value={'del'} disabled={disabledDel}>
                사용자 퇴사처리
              </Radio>
            )}
          </Radio.Group>
        )}
        {activeStep === 3 && showDel && (
          <div className="brief-text">
            <p>
              * 퇴사처리 후 식대 <span className="txt-red">지급 및 사용이 즉시 중지 </span>됩니다.
            </p>
            <p>
              * My포인트 환불에 대해선 <span className="txt-red">식권대장에 문의</span>해주셔야 합니다.
            </p>
            <p>
              * 계정 탈퇴 후 <span className="txt-red">복구가 불가능</span>하오니, 신중히 처리해주시길 바랍니다.
            </p>
          </div>
        )}

        {activeStep === 1 && (
          <div className="download-excel">
            <Button className="excelBtn" loading={isLoading} onClick={downloadExcelForm}>
              {btnTitle}엑셀서식 다운로드
            </Button>

            {method === 'reg' && (
              <div className="brief-text">
                <p>* 엑셀서식을 다운로드하여 신규 입사자 정보를 입력해주세요.</p>
                <p>* 식권대장 사용 고객사인 경우, 사용자별 식대그룹을 필수로 입력해주세요.</p>
                <p>* 복지대장 사용 고객사인 경우, 사용자별 복지그룹을 필수로 입력해주세요.</p>
                <p>* 엑셀로 등록한 사용자는 일반 사용자 권한으로 등록됩니다.</p>
                <p>* 신규 사용자의 초기 비밀번호는 숫자 0000으로 설정됩니다.</p>
              </div>
            )}

            {method === 'upd' && (
              <div className="brief-text">
                <p>* 사용자목록 엑셀서식을 다운로드 후 사용자 정보를 변경해주세요.</p>
                <p>* 부서관리자의 부서변경 시 관리자 권한을 해제 후 변경해주세요.</p>
                <p>
                  * 기존 사용자의 <b>부서, 이메일, 휴대전화번호, 직위, 직책, 사원번호 정보, 활성상태</b>를 변경할 수
                  있습니다.
                </p>
                <p>
                  * <b>휴대전화번호</b> 변경 시 본인인증된 전화번호 변경 및 삭제한다면 본인인증값도 비활성화 됩니다.
                </p>
                <p>
                  * <b>활성상태</b>는 활성, 일시정지, 일시정지예약으로 구성됩니다.
                </p>
                <p>
                  * <b>일시정지예약</b>은 엑셀 G열 내에 YYYY-MM-DD HH:00의 형식으로 입력해야합니다. (예 : 2023-12-31
                  00:00)
                </p>
                <p>
                  * 목록에 추가된 부서로만 부서 이동이 가능합니다. 신규 부서로 이동을 원하시면, 사용자 관리에서 부서
                  추가 후 엑셀파일을 작성해주세요.
                </p>
                <p>* 식대그룹과 복지그룹 변경 시 기존 포인트는 모두 초기화됩니다.</p>
              </div>
            )}

            {method === 'del' && (
              <div className={method === 'del' ? 'brief-text active' : 'brief-text'}>
                <p>* 엑셀서식을 다운로드 후 퇴사처리할 사용자의 정보를 입력해주세요.</p>
                <p>* 사용자 정보를 세가지 이상 입력하시면, 동명이인 혹은 퇴사처리 오류가 줄어듭니다.</p>
              </div>
            )}
          </div>
        )}
      </div>
    </MethodContainer>
  );
}

const mapStateToProps = (state) => ({});
const mapDispatchToProps = (dispatch, ownProps) => ({
  commonsFn: bindActionCreators(
    {
      excelDownload: commons.ExcelDownload
    },
    dispatch
  )
});

export default connect(mapStateToProps, mapDispatchToProps)(RegistrationMethod);
