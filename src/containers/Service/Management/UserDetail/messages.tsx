/*
 * UserDetail Messages
 *
 * This contains all the text for the UserDetail container.
 */

import { defineMessages } from 'react-intl';
import {captainPaymentGradeMeta, userIndividual} from "containers/Service/Management/UserDetail/viewMeta";

export const scope = 'app.containers.UserDetail';

export default defineMessages({
  header: {
    id: `${scope}.header`,
    defaultMessage: 'This is the UserDetail container!'
  },
  individual: {
    id: `${scope}.individual`,
    defaultMessage: '개인정보'
  },
  employee: {
    id: `${scope}.employee`,
    defaultMessage: '임직원정보'
  },
  meal: {
    id: `${scope}.meal`,
    defaultMessage: '식권대장 정보'
  },
  captainPaymentGrade: {
    id: `${scope}.captainPaymentGrade`,
    defaultMessage: '대장마켓플레이스 권한'
  },
  quickAuthDesc: {
    id: `${scope}.quickAuthDesc`,
    defaultMessage: '퀵대장 서비스 내에서 서비스의 이용 권한'
  }
});
