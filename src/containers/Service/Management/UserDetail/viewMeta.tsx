import React from 'react';
import { Link } from 'react-router-dom';
import moment from 'moment';
import { Input, Radio, Typography, Table, Button, Space, Form } from 'antd';
import { FormattedMessage, useIntl } from 'react-intl';
import EnumRadio from 'vcomponents/Form/EnumRadio';
import { withModalContext } from 'context/ModalContext';
import ActiveModal from 'containers/Service/Management/UserManageAction/ActiveModal';
import { FormattedMessageFixed, Paragraph } from 'utils';
import { cell as Cell, date_str as DateString } from 'decorators/text';
import { DormantUserStatus } from 'constants/enum';
import commons from 'helpers/commons';
import { CertificationComplete, CertificationNone, CertificationContainer } from './styles';
import message from './messages';

export const utilSum = () => {};

const Radios = ({ value, onChange, meta }) => <Radio.Group onChange={onChange} value={value} />;

const CaptainPaymentsPermission = ({ name, onChange, value, disables, disabledAll = false }) => {
  const dataSource = [
    {
      key: '1',
      title: '서비스 이용 신청',
      user: '불가능',
      admin: '불가능',
      super: '가능'
    },
    {
      key: '2',
      title: '사용자 관리',
      user: '불가능',
      admin: '가능',
      super: '가능'
    },
    {
      key: '3',
      title: '모든 대장서비스',
      user: '설정된 권한에 따라 서비스 이용 가능',
      admin: '모든 서비스 이용 가능',
      super: '모든 서비스 이용 가능'
    }
  ];
  const columns = [
    {
      title: '',
      dataIndex: 'title',
      key: 'title'
    },
    {
      title: '사용자',
      dataIndex: 'user',
      key: 'user'
    },
    {
      title: '관리자',
      dataIndex: 'admin',
      key: 'admin'
    },
    {
      title: '최고관리자',
      dataIndex: 'super',
      key: 'super'
    }
  ];
  const handleChange = (v) => {
    onChange({ target: { name, value: v } });
  };

  return (
    <>
      <EnumRadio
        enumId="UserPermission"
        value={value}
        onChange={handleChange}
        disables={disables}
        disabledAll={disabledAll}
        defaultValue="UESR"
      />
      <Table pagination={false} dataSource={dataSource} columns={columns} />
    </>
  );
};

const CertificationInput = ({ type, value, id, onChange, placeholder, onBlur }) => {
  const { value: text, auth } = value || {};
  const handleChange = (e) => {
    if (typeof e === 'string') {
      onChange({ target: { id, value: { value: e, auth } } });
    } else {
      const { name, value: v } = e.target;
      onChange({ target: { name, value: { value: v, auth } } });
    }
  };
  let content;
  switch (type) {
    case 'phone':
      content = (
        <Cell
          onChange={handleChange}
          format="###-####-####"
          mask="_"
          value={text === null ? undefined : text}
          placeholder={placeholder}
          onBlur={onBlur}
        />
      );
      break;
    default:
      content = <Input type={type} value={text} onChange={handleChange} placeholder={placeholder} onBlur={onBlur} />;
      break;
  }
  return (
    <CertificationContainer>
      {content}
      {auth ? <CertificationComplete>인증완료</CertificationComplete> : <CertificationNone>미인증</CertificationNone>}
    </CertificationContainer>
  );
};
export const MealPermissionDescription = ({ superSkip, active }) => {
  if (!active) {
    return <Typography.Text>회사에서 서비스를 이용하고 있지 않습니다.</Typography.Text>;
  }
  return superSkip ? (
    <Typography.Text>대장마켓플레이스 관리자로써, 대장서비스내 모든 기능의 권한을 부여합니다.</Typography.Text>
  ) : (
    <Paragraph key="MealPermissionDesc" id="MealPermissionDesc" />
  );
};

export const WelfarePermissionDescription = ({ superSkip, active }) => {
  if (!active) {
    return <Typography.Text>회사에서 서비스를 이용하고 있지 않습니다.</Typography.Text>;
  }
  return superSkip ? (
    <Typography.Text>복지대장 서비스 모든 권한 접근 가능</Typography.Text>
  ) : (
    <Typography.Text type="danger">복지대장 서비스 모든 권한 접근 불가</Typography.Text>
  );
};

export const individualValidationMessage = {
  required: '필수 값입니다.',
  types: {
    email: '${label} is not a valid email!'
  }
};

export const individualColumn = [
  {
    HeaderId: 'id',
    viewId: 'string',
    accessor: 'signId',
    viewProps: { type: 'text', placeholder: 'user.signId.placeholder' },
    rules: [
      { required: true, message: '아이디는 필수 값입니다.' },
      { min: 6, max: 40, message: 'user.signId.placeholder' },
      () => ({
        validator(_, value) {
          if (/^([0-9a-zA-Z-_@.+])*$/.test(value) === false) {
            return Promise.reject(new Error('아이디는 영문, 숫자, +-_@. 만 가능합니다.'));
          }
          return Promise.resolve();
        }
      })
    ]
  },
  {
    HeaderId: 'user.password',
    viewId: 'string',
    viewProps: { type: 'password', placeholder: 'user.password.placeholder' },
    accessor: 'password',
    rules: [
      { required: true, message: '비밀번호는 필수 값입니다.' },
      { min: 4, message: 'user.password.placeholder' }
    ]
  },
  {
    HeaderId: 'user.password.confirm',
    viewId: 'string',
    viewProps: { type: 'password', placeholder: 'user.password.confirm.placeholder' },
    accessor: 'password2',
    extra: (
      <Typography.Text>
        <FormattedMessage id="user.password.confirm.help" />
      </Typography.Text>
    ),
    rules: [
      { required: true, message: '필수 값입니다.' },
      ({ getFieldValue }) => ({
        validator(_, value) {
          if (!value || getFieldValue('password') === value) {
            return Promise.resolve();
          }
          return Promise.reject(new Error('패스워드가 일치하지 않습니다.'));
        }
      })
    ]
  },
  {
    HeaderId: 'user.name',
    viewId: 'string',
    accessor: 'name',
    viewProps: { type: 'text', placeholder: 'user.name' },
    rules: [
      { required: true, message: '이름은 필수 값입니다.' },
      { max: 30, message: '이름은 30자까지 입력 가능합니다.' }
    ]
  },
  {
    HeaderId: 'user.email',
    viewId: 'component',
    accessor: 'email',
    viewProps: { type: 'email', placeholder: 'user.email.placeholder' },
    rules: [
      {
        type: 'email',
        message: '형식에 맞지 않습니다.',
        validateTrigger: 'onBlur',
        validator(rule, v) {
          if (
            v &&
            ((typeof v === 'string' && !commons.validateEmail(v)) || (v.value && !commons.validateEmail(v.value)))
          ) {
            return Promise.reject(new Error(rule.message));
          }
          return Promise.resolve(v && (v.value || v));
        }
      },
      {
        max: 50,
        message: 'user.email.placeholder',
        validator(rule, v) {
          const msg = `${rule.message}`;
          if (v && ((typeof v === 'string' && v.length > 50) || (v.value && v.value.length > 50))) {
            return Promise.reject(new Error(msg));
          }
          return Promise.resolve(v && (v.value || v));
        }
      }
    ],
    validateTrigger: ['onChange', 'onBlur'],
    data: <CertificationInput />
  },
  {
    HeaderId: 'user.cell',
    viewId: 'component',
    accessor: 'cellphone',
    viewProps: { type: 'phone', placeholder: 'user.cell.placeholder' },
    data: <CertificationInput />
  },
  {
    HeaderId: 'user.birthday',
    type: 'datetime',
    accessor: 'birthday',
    viewProps: { showTime: false }
  }
];

export const employeeColumn = [
  {
    HeaderId: 'user.employee.division',
    viewId: 'select',
    accessor: 'division',
    viewProps: { placeholder: 'user.employee.division' },
    rules: [
      {
        required: true,
        message: '부서는 필수 입니다.',
        validator(rule, v) {
          if (!v) {
            return Promise.reject(new Error(rule.messag));
          }
          return Promise.resolve(v);
        }
      }
    ]
  },
  {
    HeaderId: 'user.employee.position',
    viewId: 'select',
    viewProps: { placeholder: 'user.employee.position' },
    accessor: 'rankPosition'
  },
  {
    HeaderId: 'user.employee.com.id.number',
    viewId: 'string',
    viewProps: { placeholder: 'user.employee.com.id.number.placeholder' },
    accessor: 'comIdNum',
    rules: [{ max: 15, message: 'user.employee.com.id.number.placeholder' }]
  }
];

export const mealColumn = [
  {
    HeaderId: 'user.meal.group',
    viewId: 'select',
    list: [{ value: null, label: '식대그룹 전체' }],
    accessor: 'groupIdx',
    rules: [{ required: true, message: '식대그룹은 필수 입니다.' }],
    extra: (
      <Typography.Text type="danger">
        <FormattedMessage id="user.meal.group.help" />
      </Typography.Text>
    )
  }
];

export const welfareColumn = [
  {
    id: 'welfareGroupIdx',
    name: 'welfareGroupIdx',
    label: 'user.welfare.group',
    type: 'select',
    list: [{ value: null, label: '복지그룹 전체' }],
    rules: [{ required: true, message: '복지그룹은 필수 입니다.' }],
    extra: (
      <Typography.Text type="danger">
        <FormattedMessage id="user.welfare.group.help" />
      </Typography.Text>
    )
  }
];

export const captainPaymentsColumn = [
  {
    HeaderId: 'user.captain.payments.permission',
    viewId: 'component',
    data: <CaptainPaymentsPermission />,
    accessor: 'captainPaymentGrade'
  },
  {
    HeaderId: 'user.meal.permission',
    viewId: 'component',
    accessor: 'mealPermission'
  },
  {
    HeaderId: 'user.welfare.permission',
    viewId: 'component',
    accessor: 'welfarePermission'
  },
  {
    HeaderId: 'user.quick.permission',
    viewId: 'EnumRadio',
    viewProps: { enumId: 'UserPermission' },
    accessor: 'menuAuthIdxList'
  }
];

export const serviceColumn = [
  {
    HeaderId: 'user.captain.payments.permission',
    viewId: 'text',
    viewProps: { enumId: 'UserPermission' },
    accessor: 'paymentsAuth'
  },
  {
    HeaderId: 'user.meal.permission',
    viewId: 'text',
    accessor: 'meal'
  },
  {
    HeaderId: 'user.quick.permission',
    viewId: 'text',
    accessor: 'quick'
  }
];

export const viewControls = [
  {
    component: (
      <Button key="moveDormant">
        <Link to="/service/management/company/users/dormant">
          <FormattedMessage id="management.user.dormant" />
        </Link>
      </Button>
    )
  }
];

export const dormantIndividualColumn = [
  {
    label: 'id',
    type: 'string',
    id: 'signId',
    name: 'signId',
    viewProps: { type: 'text', placeholder: 'user.signId.placeholder', disabled: true },
    rules: [
      { required: true, message: '아이디는 필수 값입니다.' },
      { min: 6, max: 40, message: 'user.signId.placeholder' }
    ]
  },
  {
    label: 'user.name',
    type: 'string',
    id: 'name',
    name: 'name',
    viewProps: { type: 'text', placeholder: 'user.name', disabled: true },
    rules: [{ required: true, message: '아이디는 필수 값입니다.' }]
  }
];

export const Status = withModalContext(({ value, context, user, changeLoading, callback }) => {
  let status = value;
  if (value && value.constructor === Object) {
    status = value.code;
  }
  const isInactive = status === 'INACTIVE';
  return (
    <div>
      <Space>
        <span>{DormantUserStatus[status]}</span>
        {isInactive ? <Button onClick={() => context.showModal('active')}>일시정지 해제</Button> : null}
        {user.length ? <ActiveModal checked={user} callback={callback} changeLoading={changeLoading} /> : null}
      </Space>
    </div>
  );
});

const DormantDate = ({ value }) => {
  return (
    <>
      {value ? (
        <Typography.Text>
          휴면 처리(
          <DateString style={{ display: 'inline' }} value={value} />)
        </Typography.Text>
      ) : (
        '-'
      )}
    </>
  );
};

export const dormantServiceInfoColumn = [
  {
    label: 'user.status',
    type: 'component',
    id: 'status',
    name: 'status'
    // data: <Status />
  }
];

export const myInfoColumn = individualColumn.reduce((a, c) => {
  const { accessor } = c;
  if (accessor === 'password' || accessor === 'password2' || accessor === 'sex') {
    return a;
  }
  return [...a, c];
}, []);
