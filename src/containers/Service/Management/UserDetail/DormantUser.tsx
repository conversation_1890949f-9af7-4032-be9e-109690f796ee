import React, { ReactElement, useEffect } from 'react';
import { Button, Col, Form, Row, Space } from 'antd';

import FormPanel from 'vcomponents/Form/FormPanel';
import FormBuilder from 'vcomponents/FormBuilder/index2';
import ViewPanel from 'vcomponents/Form/ViewPanel';
import TopButtonAction from 'containers/Service/Management/UserManageAction/TopButtonAction';
import { useMutation } from 'react-query';

import { dormantIndividualColumn, dormantServiceInfoColumn, Status } from './viewMeta';
import { Container } from './styles';
import { dormantUserInfoAPI } from './state';

interface UserDetailProps {}

function UserDetail({ history, match }: UserDetailProps): ReactElement {
  const { isLoading, data, mutate } = useMutation('dormantUserInfoAPI', (userId) =>
    dormantUserInfoAPI(userId)
  );

  useEffect(() => {
    getDormantUser();
  }, [match.params.userId]);

  const getDormantUser = () => {
    const { userId } = match.params;
    if (userId) {
      mutate(userId);
    }
  };

  return (
    <Container spinning={isLoading}>
      <ViewPanel title="management.user.dormant">
        {!isLoading && data && <TopButtonAction selectedData={[data]} dormant callback={getDormantUser} />}
        <Form.Provider>
          <Space direction="vertical" style={{ width: '100%' }}>
            <FormPanel title="management.user.individual" loading={isLoading}>
              <FormBuilder name="individual" data={data} columns={dormantIndividualColumn} />
            </FormPanel>
            <FormPanel title="management.user.service" loading={isLoading}>
              <FormBuilder name="service" data={data} columns={dormantServiceInfoColumn}>
                <Status id="status" user={data && Object.keys(data).length ? [data] : []} callback={getDormantUser} />
              </FormBuilder>
            </FormPanel>
            <Form>
              <Row>
                <Col span={8} offset={16}>
                  <Space style={{ float: 'right' }}>
                    <Button htmlType="button" onClick={() => history.goBack()}>
                      취소
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Form>
          </Space>
        </Form.Provider>
      </ViewPanel>
    </Container>
  );
}

export default UserDetail;
