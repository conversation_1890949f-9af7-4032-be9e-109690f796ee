import React from 'react';
import { Div } from './styles';

const BizSubSerial = ({ value }) => <Div>{value ? value : '-'}</Div>;

export const compnayInfoColumns = [
  {
    HeaderId: '법인명',
    accessor: 'companyName',
    viewId: 'string',
    viewProps: { disabled: true }
  },
  {
    HeaderId: '사업자 번호',
    accessor: 'bizSerial',
    viewId: 'string',
    viewProps: { disabled: true }
  },
  {
    HeaderId: '대표자명',
    accessor: 'chargeName',
    viewId: 'string',
    viewProps: { disabled: true }
  },
  {
    HeaderId: '주소',
    accessor: 'address',
    viewId: 'string',
    viewProps: { disabled: true }
  },
  {
    HeaderId: '업태',
    accessor: 'bizCondition',
    viewId: 'string',
    viewProps: { disabled: true }
  },
  {
    HeaderId: '종목',
    accessor: 'bizType',
    viewId: 'string',
    viewProps: { disabled: true }
  },
  {
    HeaderId: '연락처',
    accessor: 'phone',
    viewId: 'string',
    viewProps: { disabled: true }
  },
  {
    HeaderId: '종사업자 번호',
    accessor: 'bizSubSerial',
    viewId: 'component',
    viewProps: { disabled: true },
    data: <BizSubSerial />
  }
];

const Component = ({ value: data }) => <Div>{data ? data.value : '-'}</Div>;

const FormatDate = ({ value }) => <Div>{value ? `${value}일` : '-'}</Div>;

export const balanceConditionColumns = [
  {
    HeaderId: '정산 방식',
    accessor: 'settleType',
    viewId: 'component',
    viewProps: { disabled: true },
    data: <Component />
  },
  {
    HeaderId: '정산 주기',
    accessor: 'settlePeriodType',
    viewId: 'component',
    viewProps: { disabled: true },
    data: <Component />
  },
  {
    HeaderId: '첫번째 시작일',
    accessor: 'settleStartDay',
    viewId: 'component',
    viewProps: { disabled: true },
    data: <FormatDate />
  },
  {
    HeaderId: '첫번째 종료일',
    accessor: 'settleEndDay',
    viewId: 'component',
    viewProps: { disabled: true },
    data: <FormatDate />
  },
  {
    HeaderId: '세금계산서 포맷',
    accessor: 'taxFormatType',
    viewId: 'component',
    viewProps: { disabled: true },
    data: <Component />
  },
  {
    HeaderId: '세금신고일자 유형',
    accessor: 'taxInvoiceDate',
    viewId: 'component',
    viewProps: { disabled: true },
    data: <Component />
  }
];

const TaxInvoiceEmail = ({ value }) => <Div>{value ? value : '-'}</Div>;

export const contractInfoColumns = [
  {
    HeaderId: '은행 정보',
    accessor: 'bankName',
    viewId: 'string',
    viewProps: { disabled: true }
  },
  {
    HeaderId: '계좌번호',
    accessor: 'accountNo',
    viewId: 'string',
    viewProps: { disabled: true }
  },
  {
    HeaderId: '입금자',
    accessor: 'depositor',
    viewId: 'string',
    viewProps: { disabled: true }
  },
  {
    HeaderId: '입금일',
    accessor: 'depositDay',
    viewId: 'component',
    viewProps: { disabled: true },
    data: <FormatDate />
  },
  {
    HeaderId: '세금계산서 전송 메일1',
    accessor: 'taxInvoiceEmail1',
    viewId: 'component',
    viewProps: { disabled: true },
    data: <TaxInvoiceEmail />
  },
  {
    HeaderId: '세금계산서 전송 메일2',
    accessor: 'taxInvoiceEmail2',
    viewId: 'component',
    viewProps: { disabled: true },
    data: <TaxInvoiceEmail />
  },
  {
    HeaderId: '입금 유형',
    accessor: 'depositType',
    viewId: 'component',
    viewProps: { disabled: true },
    data: <Component />
  },
  {
    HeaderId: '휴일시',
    accessor: 'depositHolidayType',
    viewId: 'component',
    viewProps: { disabled: true },
    data: <Component />
  },
  {
    HeaderId: '식당 식대 지급일',
    accessor: 'storePayDay',
    viewId: 'component',
    viewProps: { disabled: true },
    data: <FormatDate />
  }
];

export const ManagerForm = ({ value }) =>
  value ? (
    <ul>
      <li>{value.name}</li>
      <li>{value.email}</li>
      <li>{value.phoneNumber}</li>
      <li>{value.mobileNumber}</li>
    </ul>
  ) : null;
