// import { atom, selectorFamily } from 'recoil';
import { COMPANY_API } from 'apis';

// export const optionData = atom({
//     key: "optionData",
//     default: {}
// })

// export const gridState = selectorFamily({
//     key: 'gridState',
//     get: args => async ({get})=>{
//         var state = get(optionData);
//         const { date, ...rest } = state;
//         const { startDate, endDate } = date;

//         var result = await COMPANY_API.get('/captain-payment/quick/v1/booking', {
//           params: {startDate: startDate.format('YYYY-MM-DD'), endDate : endDate.format('YYYY-MM-DD'), ...rest}
//         });

//         return result.data;
//     }
// })

export const gridStateAPI = async (optionData)=>{
  const { date, ...rest } = optionData;
  const { startDate, endDate } = date;

  var result = await COMPANY_API.get('/captain-payment/quick/v1/booking', {
    params: {startDate: startDate.format('YYYY-MM-DD'), endDate : endDate.format('YYYY-MM-DD'), ...rest}
  });

  return result.data;

}


export const getQuickList = async ({ date, ...rest }) => {
  const { startDate, endDate } = date;

  var result = await COMPANY_API.get('/captain-payment/quick/v1/booking', {
    params: {startDate: startDate.format('YYYY-MM-DD'), endDate : endDate.format('YYYY-MM-DD'), ...rest}
  });

  return result.data;

}
