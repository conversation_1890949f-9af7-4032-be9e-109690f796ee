{"0": {"workSheet": {"conditionalFormattings": [], "headerFooter": null, "id": 1, "name": "퀵 예약 내역", "pageSetup": {"fitToPage": false, "margins": {"left": 0.7, "right": 0.7, "top": 0.75, "bottom": 0.75, "header": 0.3, "footer": 0.3}, "orientation": "portrait", "horizontalDpi": 4294967295, "verticalDpi": 4294967295, "pageOrder": "downThenOver", "blackAndWhite": false, "draft": false, "cellComments": "None", "errors": "displayed", "scale": 100, "fitToWidth": 1, "fitToHeight": 1, "firstPageNumber": 1, "useFirstPageNumber": true, "usePrinterDefaults": false, "copies": 1}, "properties": {"defaultRowHeight": 18, "dyDescent": 0, "outlineLevelRow": 0, "outlineLevelCol": 0, "defaultColWidth": 8.83203125}, "rowBreaks": [], "state": "visible", "tables": {}, "views": [{"workbookViewId": 0, "rightToLeft": false, "state": "normal", "showRuler": true, "showRowColHeaders": true, "showGridLines": true, "zoomScale": 100, "zoomScaleNormal": 100, "activeCell": "D9"}]}, "colValidations": {}, "rowLayouts": [{"number": 1, "style": {}}, {"number": 2, "style": {}}, {"number": 3, "style": {}}, {"number": 4, "style": {}}, {"number": 5, "style": {}}, {"number": 6, "style": {}}, {"number": 7, "style": {}}, {"number": 8, "style": {}}, {"number": 9, "style": {}}, {"number": 10, "style": {}}, {"number": 11, "style": {}}, {"number": 12, "style": {}}, {"number": 13, "style": {}}, {"number": 14, "style": {}}, {"number": 15, "style": {}}, {"number": 16, "style": {}}, {"number": 17, "style": {}}, {"number": 18, "style": {}}, {"number": 19, "style": {}}, {"number": 20, "style": {}}, {"number": 21, "style": {}}, {"number": 22, "style": {}}, {"number": 23, "style": {}}, {"number": 24, "style": {}}], "colLayouts": [{"width": 9.33203125, "collapsed": false, "defn": {"width": 9.33203125, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "A", "number": 1, "outlineLevel": 0}, {"width": 7, "collapsed": false, "defn": {"width": 7, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "B", "number": 2, "outlineLevel": 0}, {"width": 5.33203125, "collapsed": false, "defn": {"width": 5.33203125, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "C", "number": 3, "outlineLevel": 0}, {"width": 13.1640625, "collapsed": false, "defn": {"width": 13.1640625, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "D", "number": 4, "outlineLevel": 0}, {"width": 11.5, "collapsed": false, "defn": {"width": 11.5, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "E", "number": 5, "outlineLevel": 0}, {"width": 9.33203125, "collapsed": false, "defn": {"width": 9.33203125, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "F", "number": 6, "outlineLevel": 0}, {"width": 9.33203125, "collapsed": false, "defn": {"width": 9.33203125, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "G", "number": 7, "outlineLevel": 0}, {"width": 9.33203125, "collapsed": false, "defn": {"width": 9.33203125, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "H", "number": 8, "outlineLevel": 0}, {"width": 9.33203125, "collapsed": false, "defn": {"width": 9.33203125, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "I", "number": 9, "outlineLevel": 0}, {"width": 9.33203125, "collapsed": false, "defn": {"width": 9.33203125, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "J", "number": 10, "outlineLevel": 0}, {"width": 10.5, "collapsed": false, "defn": {"width": 10.5, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "K", "number": 11, "outlineLevel": 0}, {"width": 8.1640625, "collapsed": false, "defn": {"width": 8.1640625, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "L", "number": 12, "outlineLevel": 0}, {"width": 6.5, "collapsed": false, "defn": {"width": 6.5, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "M", "number": 13, "outlineLevel": 0}, {"width": 5.33203125, "collapsed": false, "defn": {"width": 5.33203125, "style": {}, "hidden": false, "outlineLevel": 0}, "headerCount": 1, "headers": [null], "hidden": false, "isCustomWidth": true, "isDefault": false, "letter": "N", "number": 14, "outlineLevel": 0}], "cellDatas": [{"address": "A1", "style": {"font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"bottom": {"style": "thick"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": "예약 번호", "col": 1, "row": 1}, {"address": "B1", "style": {"font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"bottom": {"style": "thick"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": "예약자", "col": 2, "row": 1}, {"address": "C1", "style": {"font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"bottom": {"style": "thick"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": "상품", "col": 3, "row": 1}, {"address": "D1", "style": {"font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"bottom": {"style": "thick"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": "보내는 분 이름", "col": 4, "row": 1}, {"address": "E1", "style": {"font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"bottom": {"style": "thick"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": "받는 분 이름", "col": 5, "row": 1}, {"address": "F1", "style": {"font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"bottom": {"style": "thick"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": "물품 크기", "col": 6, "row": 1}, {"address": "G1", "style": {"font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"bottom": {"style": "thick"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": "물품 개수", "col": 7, "row": 1}, {"address": "H1", "style": {"font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"bottom": {"style": "thick"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": "물품 정보", "col": 8, "row": 1}, {"address": "I1", "style": {"font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"bottom": {"style": "thick"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": "예약 일시", "col": 9, "row": 1}, {"address": "J1", "style": {"font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"bottom": {"style": "thick"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": "픽업 일시", "col": 10, "row": 1}, {"address": "K1", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"bottom": {"style": "thick"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": "확정 운임", "col": 11, "row": 1}, {"address": "L1", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"bottom": {"style": "thick"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": "부가세", "col": 12, "row": 1}, {"address": "M1", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"bottom": {"style": "thick"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": "합계", "col": 13, "row": 1}, {"address": "N1", "style": {"font": {"bold": true, "size": 10, "color": {"indexed": 8}, "name": "맑은 고딕", "family": 2, "charset": 129}, "border": {"bottom": {"style": "thick"}}, "fill": {"type": "pattern", "pattern": "none"}, "alignment": {"horizontal": "left"}}, "value": "상태", "col": 14, "row": 1}, {"address": "K2", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 2}, {"address": "L2", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 2}, {"address": "K3", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 3}, {"address": "L3", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 3}, {"address": "K4", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 4}, {"address": "L4", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 4}, {"address": "K5", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 5}, {"address": "L5", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 5}, {"address": "K6", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 6}, {"address": "L6", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 6}, {"address": "K7", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 7}, {"address": "L7", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 7}, {"address": "K8", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 8}, {"address": "L8", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 8}, {"address": "K9", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 9}, {"address": "L9", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 9}, {"address": "K10", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 10}, {"address": "L10", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 10}, {"address": "K11", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 11}, {"address": "L11", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 11}, {"address": "K12", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 12}, {"address": "L12", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 12}, {"address": "K13", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 13}, {"address": "L13", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 13}, {"address": "K14", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 14}, {"address": "L14", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 14}, {"address": "K15", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 15}, {"address": "L15", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 15}, {"address": "K16", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 16}, {"address": "L16", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 16}, {"address": "K17", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 17}, {"address": "L17", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 17}, {"address": "K18", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 18}, {"address": "L18", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 18}, {"address": "K19", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 19}, {"address": "L19", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 19}, {"address": "K20", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 20}, {"address": "L20", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 20}, {"address": "K21", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 21}, {"address": "L21", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 21}, {"address": "K22", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 22}, {"address": "L22", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 22}, {"address": "K23", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 23}, {"address": "L23", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 23}, {"address": "K24", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 11, "row": 24}, {"address": "L24", "style": {"numFmt": "_(* #,##0_);_(* (#,##0);_(* \"-\"_);_(@_)", "font": {"size": 11, "color": {"theme": 1}, "name": "맑은 고딕", "family": 2, "scheme": "minor"}, "border": {}, "fill": {"type": "pattern", "pattern": "none"}}, "value": null, "col": 12, "row": 24}], "images": []}, "workbook": {"created": "2025-03-04T08:16:44.000Z", "creator": "", "lastModifiedBy": "", "modified": "2025-05-20T00:43:15.000Z", "views": [{"x": 40960, "y": 22900, "width": 40960, "height": 23820, "visibility": "visible"}], "properties": {"date1904": false}}}