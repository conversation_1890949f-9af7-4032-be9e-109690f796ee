import styled from 'styled-components';

// eslint-disable-next-line import/prefer-default-export
export const GoodsWrapper = styled.div`
  width: 50%;
  .helperText {
    color: rgba(0, 0, 0, 0.45);
  }
  .ant-input-number {
    width: 100%;
    border: 1px solid #e4e6ef;
    border-radius: 0.42rem;
  }
`;

export const AddressFormWrapper = styled.div`
  width: 50%;
  .ant-input-wrapper.ant-input-group {
    input {
      border-right: none !important;
      border-top-right-radius: 0 !important;
      border-bottom-right-radius: 0 !important;
    }
    .ant-input-group-addon {
      border: 1px solid #e4e6ef !important;
      border-left: none !important;
      border-top-right-radius: 0.42rem;
      border-bottom-right-radius: 0.42rem;

      .ant-input-search-button {
        border: none;
        box-shadow: none;
      }
    }
  }
`;
