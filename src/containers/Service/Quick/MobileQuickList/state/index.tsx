import { MOBILE_API } from 'apis';
import moment from 'moment';

export const getQuickList = async ({ date, ...rest }) => {
  const params = {
    startDate: moment(date.startDate).format('YYYY-MM-DD'),
    endDate: moment(date.endDate).format('YYYY-MM-DD'),
    ...rest
  };

  const result = await MOBILE_API.get('/captain-payment/quick/v1/booking', { params });
  return result.data;
};

const start = moment().add(30, 'minute');
const remainder = 5 - (start.minute() % 5);

export let data = {
  goodsType: 'DOCUMENTS',
  goodsCnt: 1,
  generalFreightVehicle: undefined,
  goodsInfo: undefined,
  remark: undefined,
  isExpress: false,
  isNeedCarry: false,
  isRidePassenger: false,
  weather: 'NONE',
  appointmentDate: moment(start)
    .add(remainder, 'minute')
    .format('YYYY-MM-DD HH:mm:00'),
  wayPointList: []
};

export let addressStart = {
  type: 'STARTPOINT',
  addressBookIdx: 0,
  name: undefined,
  cellphone: undefined,
  userSelectedType: undefined,
  zonecode: undefined,
  roadAddress: undefined,
  jibunAddress: undefined,
  addressDetail: undefined,
  isSaveAddress: false,
  addressBookName: undefined,
  description: undefined
};

export let addressWay = {
  type: 'WAYPOINT',
  addressBookIdx: 0,
  name: undefined,
  cellphone: undefined,
  userSelectedType: undefined,
  zonecode: undefined,
  roadAddress: undefined,
  jibunAddress: undefined,
  addressDetail: undefined,
  isSaveAddress: false,
  addressBookName: undefined,
  description: undefined
};

export let addressEnd = {
  type: 'ENDPOINT',
  addressBookIdx: 0,
  name: undefined,
  cellphone: undefined,
  userSelectedType: undefined,
  zonecode: undefined,
  roadAddress: undefined,
  jibunAddress: undefined,
  addressDetail: undefined,
  isSaveAddress: false,
  addressBookName: undefined,
  description: undefined
};
