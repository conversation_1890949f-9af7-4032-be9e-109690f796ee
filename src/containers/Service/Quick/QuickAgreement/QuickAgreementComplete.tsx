import React, { ReactElement, useState, useMemo, useEffect } from 'react';
import { Row, Button, message, Space, Modal } from 'antd';
import { FormattedMessage } from 'react-intl';
import useFetch from 'use-http';

import {ExclamationCircleOutlined} from '@ant-design/icons'

import messages from './messages';
import { Container } from './styles';
import * as Title from 'vcomponents/Forms/Title';
import HorizontalTable from 'vcomponents/Form/HorizontalTable';
import Terms from 'components/Terms';
import ViewPanel from 'vcomponents/Form/ViewPanel';
import FormPanel from 'vcomponents/Form/FormPanel';
import FormBuilder from 'vcomponents/FormBuilder/index2';

import { startUsingQuick, stopUsingQuick } from './state'
import {useMutation, useQuery} from 'react-query'

interface Props {
  data: object;
}

const columns = [
  { id: 'status', label: 'quick.service.use.status', type: 'string', meta: { disabled: true }, name: 'status' },
  { id: 'agreeUserName', label: 'quick.agreenment.user', type: 'string', viewProps: { disabled: true }, name: 'agreeUserName' },
  { id: 'agreeDate', label: 'quick.agreement.date', type: 'datetime_str', name: 'agreeDate' },
  {
    id: 'serviceTermsPdf',
    label: 'quick.agreement.detail',
    type: 'string',
    name: 'serviceTermsPdf',
    viewProps: { disabled: true },
  }
];

export default function QuickAgreementComplete({ data = { quickService: {}} }: Props): ReactElement {
  const { quickService } = data;
  const [_data, setData] = useState({});
  const _tableData = useMemo(() => {});

  const onSuccess = ()=>{
    Modal.confirm({
      title:  quickService.status == "ACTIVE" ? '일시 중지' : '사용 재개',
      icon: <ExclamationCircleOutlined />,
      content: "변경되었습니다.",
      okText: "확인",
      cancelButtonProps: {style:{display: 'none'}},
      onCancel: ()=>{ location.reload() },
      onOk: ()=>{ location.reload() }
    });
  }

  const {isLoading, error, isError, mutate:stopQuick} = useMutation('stopUsingQuick', ()=>stopUsingQuick(), {onSuccess})
  const {isLoading:isLoading2,  mutate:startUseQuick} = useMutation('startUsingQuick', ()=>startUsingQuick(), {onSuccess})

  return (
    <div>
      <ViewPanel title={quickService.status == 'ACTIVE' ? "service.agreement": "service.agreement"} 
      loading={isLoading || isLoading2}
       desc={quickService.status == 'ACTIVE' ? "service.agreement.complete" : 'service.agreement.pause'}>
        <FormPanel title="captain.quick">
          <FormBuilder columns={columns} data={data.quickService}>
            <Status id="status" onClick={()=>{quickService.status == 'ACTIVE' ? stopQuick() : startUseQuick() }} ></Status>
          </FormBuilder>
        </FormPanel>
      </ViewPanel>
    </div>
  );
}

const Status = ({ value, onClick = () => {} }) => {
  return (
    <Space>
        <Space>
          <FormattedMessage id={value == 'ACTIVE'?"inuse": "notinuse"} />
          <Button onClick={onClick}>
            <FormattedMessage id={value == 'ACTIVE'?"stop.using": "start.using"} />
          </Button>
        </Space>
    </Space>
  );
};
