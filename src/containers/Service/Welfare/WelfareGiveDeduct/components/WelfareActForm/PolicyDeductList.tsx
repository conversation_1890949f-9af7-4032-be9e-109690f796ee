import React, { Component } from 'react';
import { connect } from 'react-redux';
import DatePicker from 'react-datepicker';
import update from 'react-addons-update';
import moment from 'moment-timezone';

import { Header, Checkbox, Form, Label, Icon, Input, Button, Confirm } from 'semantic-ui-react';

import { Commons } from 'components';

import cm from 'helpers/commons';
import toastr from 'helpers/toastr';

import StepThreeStaff from './StepThreeStaff';

moment.tz.setDefault('Asia/Seoul');

const propTypes = {};

const defaultProps = {};

class PolicyDeductList extends Component {
  constructor(props) {
    super(props);

    this.state = {
      startDate: moment(),
      minDate: moment(),
      oneYearDate: moment().add(1, 'y'),
      endDate: null,
      timeDisable: props.auth.schedule ? false : true,
      endDisable: false,
      isMoneySetting: false,
      isDateSetting: false,
      inputCheck: false,
      money: null,
      count: null,
      searchData: null,
      confirmShow: false,
      confirmResult: false,
      keyword: null,

      confirmOpen: false
    };
  }

  componentWillMount() {
    const { scheduleStartDate } = this.props;

    if (scheduleStartDate && scheduleStartDate.data) {
      this.setState({
        startDate: moment(new Date(scheduleStartDate.data.start)),
        minDate: moment(new Date(scheduleStartDate.data.start))
      });
    }
  }

  shouldComponentUpdate(nextProps, nextState) {
    const nText = nextProps.text;
    const bText = this.props.text;
    let result = true;
    if (JSON.stringify(nText) != JSON.stringify(bText)) {
      result = false;
    }

    if (this.state.oneYearDate !== nextState.oneYearDate) return true;

    return result;
  }

  timeIsShow = async (e, type, params) => {
    if (type === 'sDateOrTime') {
      await this.setState({
        timeDisable: update(this.state.timeDisable, { $set: !e.target.checked })
      });
    } else if (type === 'eEndDateDel') {
      const { endDisable } = this.state;

      await this.setState({
        endDisable: update(this.state.endDisable, { $set: !endDisable }),
        endDate: update(this.state.endDate, { $set: null })
      });
    }
  };

  timeChange = (e, type, params) => {
    const { fn } = this.props;
    let value = e;

    if (type === 'sDate') {
      this.setState({
        startDate: update(this.state.startDate, { $set: e })
      });
      this.setState({
        oneYearDate: moment(e).add(1, 'y')
      });
    } else if (type === 'sDateOrTime') {
    } else if (type === 'eDate') {
      this.setState({
        endDate: update(this.state.endDate, { $set: e })
      });
    } else if (type === 'eEndDate') {
      let now = new Date();
      let lastDate = null;

      // lastDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      lastDate = moment().endOf('year');
      this.setState({
        endDate: update(this.state.endDate, { $set: lastDate })
      });

      value = lastDate;
    }

    // fn.stepThreeAllBtn(value, type, params)
  };

  getRezTime = () => {
    const { startDate, endDate, timeDisable, endDisable, isDateSetting } = this.state;
    const { time, fn } = this.props;
    let sDate = null;
    let ttt = moment(time.sTime, 'HH:mm A');
    sDate = moment(startDate);
    sDate = sDate.set({
      hour: ttt.get('hour'),
      minute: ttt.get('minute')
    });
  };

  dateSetting = async (e, params, result) => {
    const { startDate, endDate, timeDisable, endDisable, isDateSetting } = this.state;
    const { time, fn } = this.props;

    if (endDate || endDisable) {
      let confirmResult = result ? result : false;

      if (confirmResult) {
        let sDate = null;
        let eDate = null;
        if (!timeDisable) {
          let ttt = moment(time.sTime, 'HH:mm A');
          sDate = moment(startDate);
          sDate = sDate.set({
            hour: ttt.get('hour'),
            minute: ttt.get('minute')
          });
        }
        if (!endDisable && endDate) {
          eDate = moment(endDate.format('YYYY-MM-DD'));
          eDate = eDate.set({
            hour: 23,
            minute: 59,
            second: 59
          });
        }

        let result = true;

        if (eDate) {
          result = cm.dateCheck(sDate, eDate, false); //날짜 체크
          if (!result.isState) {
            toastr('top-right', 'warning', result.message);
            result = false;
          }
        }

        if (result) {
          params['startDate'] = moment(sDate).format('YYYY-MM-DDTHH:mm');
          params['time'] = !timeDisable ? time : null;
          params['endDate'] = eDate;
          params['isDateSetting'] = confirmResult;
          await this.setState({
            isDateSetting: update(this.state.isDateSetting, { $set: confirmResult })
          });
        } else {
          return;
        }
      }

      //유효기간 설정완료 버튼 일 때
      if (!this.state.isDateSetting) {
        //복지포인트 시작시각 예약하기를 체크 하지 않았을 때
        if (timeDisable) {
          this.confirmOpen(true);
        } else {
          this.confirmOpen(true);
          // this.dateSetting(e, params, true);
        }
      } else {
        await this.setState({
          isDateSetting: update(this.state.isDateSetting, { $set: confirmResult })
        });
      }

      fn.stepThreeAllBtn(e, 'dateSetting', params);
    } else {
      toastr('top-right', 'warning', '복지포인트 유효기간을 선택하세요.');
      return;
    }
  };

  moneySettingSuccess = async (params) => {
    const { policyStaffList, tabTotalData, fn, data, handleClick, welfarePolicy } = this.props;
    const { isMoneySetting } = this.state;
    let totalMoney = 0;
    let totalCount = 0;
    let result = true;
    let message = '';
    let type = data.topMenu.tabType;

    this.setState({ inputCheck: true });
    policyStaffList.some((data) => {
      let policyMoney = 0;
      let giveMoney = Number(data.money);

      data.policy.some((p) => {
        if (p.idx === data.sPolicy) {
          policyMoney = p.amount;
          return true;
        }
      });

      if (type === 'deduct' && policyMoney < giveMoney) {
        message = '차감금액이 잔여 복지포인트보다 큽니다. 잔여 복지인트포다보다 적게 설정해주세요.';
        result = false;
        return true;
      }

      if (data.sPolicy === params.pIdx) {
        if (data.money != 0 && !data.money) {
          $(`#money-${data.id}`).addClass('value-null');
          message = '금액 설정을 모두 해주세요.';
          result = false;
        } else {
          $(`#money-${data.id}`).removeClass('value-null');
          if (!data.reason) {
            $(`#reason-${data.id}`).addClass('value-null');
            message = '사유를 모두 입력하세요.';
            result = false;
          } else {
            $(`#reason-${data.id}`).removeClass('value-null');
          }
        }
        totalMoney += giveMoney;
        totalCount += 1;
      }
    });

    if (result) {
      await this.setState({
        isMoneySetting: update(this.state.isMoneySetting, { $set: !isMoneySetting }),
        money: update(this.state.money, { $set: totalMoney }),
        count: update(this.state.count, { $set: totalCount })
      });

      params['isMoneySetting'] = !isMoneySetting;
      params['totalMoney'] = totalMoney;
      params['totalCount'] = totalCount;

      fn.tabTotalData('moneySetting', params);
      handleClick(null, { index: params.pIdx }, 'open');
    } else {
      toastr('top-right', 'warning', message);
      return;
    }
  };

  handleEntSubmit = (e) => {
    if (e.key === 'Enter') {
      const { text, policyStaffList } = this.props;

      if (text && text.value) {
        let keyword = text ? text.value : null;
        let temp = [];

        policyStaffList.forEach((data, idx) => {
          if ((data.signid && data.signid.indexOf(keyword) > -1) || (data.name && data.name.indexOf(keyword) > -1)) {
            temp.push(data);
          }
        });

        if (temp.length === 0) {
          toastr('top-right', 'warning', '검색결과가 없습니다.');
          return;
        }

        this.setState({
          searchData: temp.length > 0 ? temp : null,
          keyword: keyword
        });
      } else {
        this.setState({
          searchData: null
        });
      }
    }
  };

  confirmOpen = (isShow, result) => {
    const { policyStaffList, handleClick } = this.props;
    const { timeDisable } = this.state;

    const params = {
      gIdx: policyStaffList ? policyStaffList[0].group.idx : null,
      pIdx: policyStaffList ? policyStaffList[0].sPolicy : null
    };

    if (result) {
      this.dateSetting(null, params, result);
    }

    if (timeDisable) {
      this.setState({
        confirmShow: isShow
      });
    } else {
      this.setState({
        confirmOpen: isShow
      });
    }

    if (!isShow) {
      handleClick(null, { index: params.pIdx }, 'open');
    }
  };

  render() {
    const { policyStaffList, fn, data, auth, welfarePolicy } = this.props;
    const {
      startDate,
      endDate,
      timeDisable,
      endDisable,
      money,
      count,
      isDateSetting,
      isMoneySetting,
      searchData,
      oneYearDate,
      minDate,
      confirmShow,
      keyword,
      inputCheck,
      confirmOpen
    } = this.state;

    const params = {
      gIdx: policyStaffList.length ? policyStaffList[0].group.idx : null,
      pIdx: policyStaffList.length ? policyStaffList[0].sPolicy : null
    };

    const date = {
      sDate: policyStaffList.length ? policyStaffList[0].sDate : null,
      eDate: policyStaffList.length ? policyStaffList[0].eDate : null,
      time: policyStaffList.length ? policyStaffList[0].time : null
    };

    const stepThreeStaffProps = {
      data: {
        topMenu: data.topMenu,
        inputCheck
      }
    };

    const mm = cm.dataNextMonth(1);
    const mm2 = cm.dataNextMonth(2);
    let isMinus = data.topMenu.tabType === 'give' ? '' : '(-)';

    const confirmStyle = {
      width: '660px',
      fontSize: '16px',
      fontWeight: 'bold'
    };
    return (
      <div className="step-three-staff">
        <Commons.ConfirmContent
          openFunc={this.confirmOpen}
          confirmShow={confirmShow}
          style={confirmStyle}
          text={'시작시각을 설정하지 않고, 유효기간 설정을 완료하시겠습니까?'}
        />

        <Commons.ConfirmContent
          openFunc={this.confirmOpen}
          confirmShow={confirmOpen}
          style={confirmStyle}
          text={
            <div style={{ padding: 10 }}>
              <div>지급 예약으로 설정하셨습니다. 지급 시점에 사용자들에게 지급 완료 app push가 발송됩니다.</div>
              <div style={{ paddingTop: 5 }}>{`(00시 지급 예약 시, 지급 시점인 00시에 발송됩니다.)`}</div>
            </div>
          }
        />

        <div>
          <Header as="h3" dividing>
            <div className="title1">
              <span>복지포인트 유효기간 설정</span>
            </div>
            <div className="title2">
              {data.topMenu.tabType === 'give' ? (
                <span>*예약을 설정하지 않으면, 지금 바로 복지포인트가 지급됩니다.</span>
              ) : null}
            </div>
          </Header>
        </div>
        <div>
          {data.topMenu.tabType === 'give' ? (
            <Form>
              <div className={isDateSetting ? 'date-setting dim' : 'date-setting'}>
                <Form.Group inline>
                  <Checkbox
                    label="지급 예약"
                    onChange={(e, checkProps) => {
                      const target = {
                        checked: checkProps.checked
                      };
                      this.timeIsShow({ target }, 'sDateOrTime', params);
                    }}
                  />
                </Form.Group>
                <Form.Group inline>
                  <Input labelPosition="right" type="text" placeholder="Amount">
                    <Label basic>
                      <Icon disabled name="calendar" />
                    </Label>
                    <DatePicker
                      className="startDate"
                      customInput={<input style={{ width: 170 }} />}
                      selected={startDate}
                      minDate={minDate}
                      dateFormat="YYYY-MM-DD"
                      disabled={auth.schedule ? false : timeDisable}
                      onChange={(e) => this.timeChange(e, 'sDate', params)}
                    />
                  </Input>
                  <Commons.Time
                    timeType="sTime"
                    placeholder={'HH:mm'}
                    isShowSecond={false}
                    isDim={auth.schedule ? false : timeDisable}
                  />
                  <span className="test-stick"> - </span>
                  <Input labelPosition="right" type="text" placeholder="Amount">
                    <Label basic>
                      <Icon disabled name="calendar" />
                    </Label>
                    <DatePicker
                      className="endDate"
                      customInput={<input style={{ width: 170 }} />}
                      selected={endDate}
                      minDate={minDate}
                      dateFormat="YYYY-MM-DD"
                      disabled={endDisable}
                      onChange={(e) => this.timeChange(e, 'eDate', params)}
                    />
                  </Input>
                  <Button
                    inverted
                    className="date-btn1"
                    color="black"
                    style={{ marginLeft: 0 }}
                    disabled={endDisable}
                    onClick={(e) => this.timeChange(e, 'eEndDate', params)}
                  >
                    {`${new Date().getFullYear()}년 연말까지`}
                  </Button>
                </Form.Group>
              </div>
              <Form.Group className="time-setting">
                <Button inverted color="green" onClick={(e) => this.dateSetting(e, params)}>
                  {isDateSetting ? '재설정' : '유효기간 설정완료'}
                </Button>
                <span id={'time-setting' + params.pIdx}>
                  {isDateSetting && timeDisable
                    ? '지급시간부터'
                    : isDateSetting
                    ? moment(date.sDate).format('YYYY-MM-DD') + '  '
                    : null}
                  {isDateSetting && date.time ? moment(date.sDate).format('HH:mm') + ' 부터 ' : null}
                  {isDateSetting && endDisable
                    ? '  복지포인트 유효기간 없음'
                    : isDateSetting && date.eDate
                    ? moment(endDate).format('YYYY-MM-DD') + ' 24:00까지 '
                    : null}
                  {isDateSetting ? null : <span>유효기간을 설정해주세요.</span>}
                </span>
              </Form.Group>
            </Form>
          ) : (
            <span className="deduct-date-text">차감은 신청 즉시 차감되며, 유효시간을 설정할수 없습니다.</span>
          )}
          <div className={isMoneySetting ? 'dim' : ''}>
            <div>
              <Header as="h4" dividing className="count-line">
                <div className=""></div>
              </Header>
              <div>
                <span className="user-cnt">{'금액 및 사유 입력 (총' + policyStaffList.length + ' 명)'}</span>
              </div>
              <div className="search-box">
                <Commons.SearchInputBox handleEntSubmit={this.handleEntSubmit} placeHolder={'ID, 이름'} />
              </div>
            </div>
            <StepThreeStaff
              fn={fn}
              data={stepThreeStaffProps.data}
              policyStaffList={searchData ? searchData : policyStaffList}
              keyword={keyword}
              welfarePolicy={welfarePolicy}
            />
          </div>
          <Form>
            <Form.Group className="money-setting">
              <Button
                inverted
                color={data.topMenu.tabType === 'give' ? 'green' : 'red'}
                onClick={(e) => this.moneySettingSuccess(params)}
              >
                {isMoneySetting ? '재설정' : '금액 설정완료'}
              </Button>
              {count ? <span>{count + '명 '}</span> : null}
              {money ? <span>{'총 ' + isMinus + cm.numberComma(money) + '원(장)'}</span> : null}
              {!count && !money ? <span>금액과 사유를 입력해주세요.</span> : null}
            </Form.Group>
          </Form>
        </div>
      </div>
    );
  }
}

PolicyDeductList.propTypes = propTypes;
PolicyDeductList.defaultProps = defaultProps;

PolicyDeductList = connect((state) => ({
  time: state.commons.time,
  text: state.commons.text,
  scheduleStartDate: state.sikdae.scheduleStartDate
}))(PolicyDeductList);

export default PolicyDeductList;
