import React, { useEffect, useMemo, useState } from 'react';
import moment from 'moment-timezone';
import download from 'downloadjs';
import { useQuery, useQueryClient } from 'react-query';
import {
  getWelfareTaskList,
  getWelfareTask,
  cancelWelfareTask,
  getWelfareTaskDownload
} from 'apis/captainpayment/welfare';
import ViewPanel from 'vcomponents/Form/ViewPanel';
import Button from 'vcomponents/Button/Basic';
import Table from 'vcomponents/Table';
import OptionBar from 'vcomponents/OptionBar';
import { PageContent } from 'styles/global';
import storage from 'helpers/storage';
import toastr from 'helpers/toastr';

import WelfareGivenDtlModal from './modal/WelfareGivenDtlModal';
import { intiDefaultData, columns, filterColumns } from './viewMeta';
import { Container, ButtonArea, Gap } from './styles';

moment.tz.setDefault('Asia/Seoul');

const WelfareGivenList = ({ history, match, welfareGroup }) => {
  const queryClient = useQueryClient();
  const [params, setParams] = useState({
    page: 1,
    pagerow: 15,
    totalCount: 0,
    startdate: moment()
      .startOf('month')
      .format('LLL'),
    enddate: moment().format('LLL'),
    ...intiDefaultData
  });

  const [modal, setModal] = useState({
    isOpen: false,
    modalIdx: 0,
    modalPage: 1,
    modalPageRow: 10
  });

  // list query
  const { data, isLoading, isSuccess, isFetched, isFetching } = useQuery(
    ['getWelfareTaskList', params],
    () => getWelfareTaskList(params),
    { keepPreviousData: true }
  );

  // list fetch
  useEffect(() => {
    queryClient.prefetchQuery(['getWelfareTaskList', params], () => getWelfareTaskList(params));
  }, [params, queryClient]);

  // detail dependent query
  const { data: detailTask, isLoading: isDetailLoading, isFetching: isDetailFetching } = useQuery(
    ['getWelfareTask', modal],
    () => getGivenDetail(),
    { enabled: !!modal.modalIdx && modal.isOpen }
  );

  // detail fetch
  useEffect(() => {
    if (modal.modalIdx && modal.isOpen) {
      queryClient.prefetchQuery(['getWelfareTask', modal], () => getGivenDetail());
    }
  }, [modal, queryClient]);

  const { pageInfo, content } = useMemo(() => {
    if (!data) {
      return {
        pageInfo: {
          page: 0,
          pageRow: 10,
          totalCount: 0
        },
        content: []
      };
    }
    const { paging: { page, pagerow, totalcount } = {}, task = [] } = data;
    const platTask = task?.map((e) => {
      const { user, date, count, ...other } = e;
      return {
        ...other,
        user: user?.name,
        register: date?.regist,
        execute: date?.excute,
        total: count?.total,
        success: count?.success,
        fail: count?.fail
      };
    });

    return {
      pageInfo: {
        page: page - 1,
        pageRow: pagerow,
        totalCount: totalcount
      },
      content: platTask || []
    };
  }, [data]);

  // 검색
  const submitForm = async (filterData) => {
    // @ts-ignore
    const { date: { startDate, endDate } = {}, ...o } = filterData;
    const newParams = {
      ...params,
      ...o,
      page: 1,
      startdate: startDate && startDate.locale('en').format('LLL'),
      enddate:
        endDate &&
        endDate
          .locale('en')
          .endOf('day')
          .format('LLL')
    };
    setParams(newParams);
  };

  // 페이지 이동
  const pageClick = (paging) => {
    const { page, pageRow } = paging;
    const newPrams = { ...params, page, pagerow: pageRow };
    setParams(newPrams);
  };

  // 상세 모달 열기
  const openDetailModal = async ({ id }) => {
    setModal({ ...modal, isOpen: true, modalIdx: id, modalPage: 1 });
  };

  // 상세 모달 닫기
  const closeModal = () => setModal({ ...modal, isOpen: false });

  // 상세 내역
  const getGivenDetail = async () => {
    const { modalPage, modalPageRow, modalIdx } = modal;
    const dtlParams = {
      page: modalPage,
      pagerow: modalPageRow
    };
    const result = await getWelfareTask(modalIdx, dtlParams);
    return result;
  };

  // 상세내역 페이지 이동
  const detailPageClick = async (paging) => {
    setModal({ ...modal, modalPage: paging });
  };

  // 모달(식대내역 상세) 엑셀 다운로드
  const downloadExcel = async () => {
    const { modalIdx } = modal;
    const com = storage.get('company');

    let header = null;

    try {
      const excelParams = {
        comid: com.id,
        requestid: com.user.id,
        filetype: 'excel'
      };

      const excelDownloadResponse = await getWelfareTaskDownload(modalIdx, excelParams);

      if (excelDownloadResponse) {
        header = excelDownloadResponse.headers['content-disposition'];
        header = header.split('=');
        download(excelDownloadResponse.data, decodeURIComponent(header[1]));
        toastr('top-right', 'success', ' 엑셀파일 생성 완료 되었습니다.');
      }
    } catch (e) {
      const result = e.response;

      if (result && result.data) {
        const { status } = result;

        toastr('top-right', 'error', ` 엑셀파일 생성에 실패 하였습니다.(${String(status)})`);
      }
      console.log('###### ExcelDownLoad : ', e);
    }
  };

  // 지급예약 취소
  const cancelReservation = async () => {
    const { modalIdx } = modal;

    try {
      const canceled = await cancelWelfareTask(modalIdx);

      if (canceled) {
        if (canceled.status === 204) {
          await closeModal();
          setParams({ ...params, page: 0 });
        } else {
          alert('예약 취소 오류입니다.');
        }
      } else {
        toastr('top-right', 'warning', '예약 취소 오류입니다.');
      }
    } catch (e) {
      const { response } = e;

      if (response) {
        toastr('top-right', 'warning', response.data.message);
      }

      console.log('###### cancelSikdae error : ', e);
    }
  };

  const gotoGiveDeduct = () => {
    history.push(`${match.url}/task`);
  };
  const gotoGiveDeductByExcel = () => {
    history.push(`${match.url}/excel`);
  };

  const { isOpen, modalPage, modalPageRow } = modal;

  const modalFn = {
    modal: isOpen,
    modalPage,
    modalPagerow: modalPageRow,

    openModal: openDetailModal,
    closeModal,
    detailPageClick,

    cancelSikdae: cancelReservation,
    downloadExcel
  };

  return (
    <Container spinning={isFetching || isDetailLoading}>
      <Gap>
        {/* {loading} */}
        <ViewPanel title="welfare.point.give.deduct.task">
          <OptionBar template={filterColumns} colInRow={4} onClick={submitForm} />
          {/* <SikdaeGivenForm formFn={formFn} /> */}
          <ButtonArea>
            <Button onClick={gotoGiveDeduct}>지급/차감하기</Button>
            <Button onClick={gotoGiveDeductByExcel}>엑셀 파일로 지급/차감하기</Button>
          </ButtonArea>
          {/* <SikdaeGivenResult resultFn={resultFn} modalFn={modalFn} /> */}

          <PageContent>
            <Table
              columns={columns}
              data={content}
              pageInfo={pageInfo}
              onChange={pageClick}
              onRowClick={openDetailModal}
            />
          </PageContent>
          <WelfareGivenDtlModal isLoading={isDetailLoading} modalFn={modalFn} data={detailTask} />
        </ViewPanel>
      </Gap>
    </Container>
  );
};

export default WelfareGivenList;
