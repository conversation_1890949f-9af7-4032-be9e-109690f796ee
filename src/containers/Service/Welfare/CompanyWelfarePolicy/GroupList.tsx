import React, { ReactElement } from 'react';
import { Menu } from 'semantic-ui-react';

interface GroupListProps {
  group;
  onClick;
}

function GroupList(props: GroupListProps): ReactElement {
  const { group, onClick } = props;
  const buttonList = group
    ? group.map((data) => {
        return (
          <Menu.Item name={data.name} key={data.idx} onClick={() => onClick(data.idx, data.name)}>
            {data.name}
          </Menu.Item>
        );
      })
    : '';
  return (
    <div className="btnGroup">
      <Menu vertical className="vendysLeftMenu group" fluid>
        <Menu.Item className="top" name="title">
          복지그룹
        </Menu.Item>
        {group ? buttonList : ''}
      </Menu>
    </div>
  );
}

export default GroupList;
