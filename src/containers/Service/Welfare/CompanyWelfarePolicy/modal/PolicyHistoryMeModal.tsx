import React, { ReactElement, useMemo } from 'react';
import { Modal, Button } from 'antd';
import Table from 'vcomponents/Table';
import { welfareHistoryColumns } from '../viewMeta';
import { Container } from '../styles';

interface PolicyHistoryMeModalProps {
  modalFn;
}

function PolicyHistoryMeModal(props: PolicyHistoryMeModalProps): ReactElement {
  const { modalFn } = props;
  const { visible, closeModal, welfareHistory, historyPolicyModal } = modalFn;

  const onChange = (v) => {
    historyPolicyModal(v);
  };

  const pageInfo = useMemo(() => {
    const { paging } = welfareHistory;
    return { ...paging, page: paging.page - 1 };
  }, [welfareHistory]);

  return (
    <Modal
      title="복지정책 변경내역"
      visible={visible}
      onCancel={closeModal}
      width={1000}
      closable={false}
      footer={[
        <Button key="back" onClick={closeModal}>
          닫기
        </Button>
      ]}
    >
      <Container>
        <Table
          columns={welfareHistoryColumns}
          data={welfareHistory.history || []}
          pageInfo={pageInfo}
          onChange={onChange}
          style={{ padding: 0 }}
        />
      </Container>
    </Modal>
  );
}

export default PolicyHistoryMeModal;
