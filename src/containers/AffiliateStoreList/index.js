import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Button, Input, Checkbox } from 'semantic-ui-react';
import download from 'downloadjs';

import toastr from 'helpers/toastr';
import storage from 'helpers/storage';
import { Commons } from 'components';
import cm from 'helpers/commons';
import * as cm_action from 'actions/commons';
import AffiliateStoreTable from './AffiliateStoreTable';
import { FlexRowContainer, MapToggleContainer, StoreListContainer } from './Styled';
import StoreListMap from './StoreListMap';

class AffiliateStoreList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      keyword: null,
      togglestatus: false,
      office: {
        gpsLat: 0,
        gpsLon: 0
      },
      storeGps: {
        gpsLat: 0,
        gpsLon: 0
      },
      listClickIdx: null,
      pagingInit: {
        totalcount: 0,
        pagerow: 0
      }
    };
  }

  componentWillMount() {
    const { togglestatus } = this.state;
    this.storeSel(togglestatus, 1, null);
    if (this.state.timeoutId) {
      clearTimeout(this.state.timeoutId);
    }
  }

  componentDidMount() {
    cm.mainTitle('제휴식당 목록');
  }

  storeSel = async (togglestatus, page, keyword) => {
    const { cmAPI } = this.props;

    let params = {
      keyword,
      page,
      pagerow: 15,
      isactive: true
    };
    if (togglestatus) {
      const { pagingInit } = this.state;
      const { totalcount } = pagingInit || {};
      params = { page: 1, pagerow: totalcount, isactive: true };
      if (keyword) {
        params = { keyword, page: 1, pagerow: totalcount, isactive: true };
      }
    }
    const result = await cmAPI.storeSel(params);
    const { value } = result || {};
    const { data } = value || {};
    const { paging } = data || {};
    const { totalcount, pagerow } = paging || 0;
    if (!keyword) {
      this.setState({ pagingInit: { totalcount, pagerow } });
    }
  };

  pageClick = async (page) => {
    const { keyword, togglestatus } = this.state;
    this.storeSel(togglestatus, page, keyword);
  };

  handleEvent = async () => {
    const { keyword, togglestatus } = this.state;

    if (togglestatus) {
      this.setState({ listClickIdx: null });
    }

    this.storeSel(togglestatus, 1, keyword);
  };

  handleChange = (e) => {
    this.setState({ keyword: e.target.value });
  };

  handleEntEvent = (e) => {
    if (e.key === 'Enter') {
      this.handleEvent();
    }
  };

  handleToggleChange = async () => {
    const { togglestatus, keyword } = this.state;
    const newTogglestatus = !togglestatus;
    const newListClickIdx = null;
    this.setState({ listClickIdx: newListClickIdx });

    const { storeList } = this.props;
    const { data } = storeList || {};
    const { office } = data || {};
    const { gpsLat, gpsLon } = office || {};
    this.setState({ office: { gpsLat, gpsLon } });

    this.storeSel(newTogglestatus, 1, keyword);
    this.setState({ togglestatus: newTogglestatus });
  };

  excelDownload = async () => {
    const { cmAPI } = this.props;
    const com = storage.get('company');
    let header = null;

    const params = {
      com,
      excel: {
        url: '/company/v1/store/download',
        method: 'get'
      },
      excelResult: {
        excel: {
          requestid: com.user.id
        }
      }
    };

    await cmAPI.excelDownload(params).then((result) => {
      const { value } = result;
      if (value) {
        header = value.headers['content-disposition'];
        header = header.split('=');
      }
      download(value.data, decodeURIComponent(header[1]));
      toastr('top-right', 'success', `엑셀파일 생성 완료 되었습니다.`);
    });
  };

  listClick = (e, i) => {
    e.stopPropagation();
    e.preventDefault();
    const { storeList } = this.props;
    const { data } = storeList || {};
    const { store } = data || {};
    if (i === undefined) {
      return;
    }
    const { gpsLat, gpsLon } = store[i] || 0;
    this.setState({ storeGps: { gpsLat, gpsLon }, listClickIdx: i });
  };

  render() {
    const { keyword, togglestatus, office, markers, storeGps, listClickIdx } = this.state;
    const { storeList } = this.props || {};
    const { data } = storeList || {};
    const { store } = data || {};
    // const columnWidth = [100, 150, 100, 250, 200];

    let loading = '';
    loading = <Commons.LoadingBar />;

    return (
      <main>
        <FlexRowContainer className="form-input">
          <FlexRowContainer>
            <Input
              value={keyword || ''}
              action={{
                icon: 'search',
                onClick: this.handleEvent
              }}
              onChange={this.handleChange}
              onKeyPress={this.handleEntEvent}
              placeholder="제휴식당명"
            />
            <MapToggleContainer>
              <span>지도로 보기</span>
              <span>
                <Checkbox toggle onClick={this.handleToggleChange} checked={togglestatus} />
              </span>
            </MapToggleContainer>
          </FlexRowContainer>
          <Button className="excelDownBtn" color="green" inverted onClick={this.excelDownload}>
            엑셀 다운로드
          </Button>
        </FlexRowContainer>

        <div>
          {/* <div className='table-box'> */}
          <FlexRowContainer>
            {togglestatus ? (
              <StoreListMap
                markers={markers}
                office={office}
                store={store}
                storeGps={storeGps}
                listClickIdx={listClickIdx}
                keyword={keyword}
              />
            ) : null}
            <StoreListContainer>
              <AffiliateStoreTable
                togglestatus={togglestatus}
                onClick={this.listClick}
                pageclick={this.pageClick}
                storelist={storeList}
              />
            </StoreListContainer>
          </FlexRowContainer>
        </div>
      </main>
    );
  }
}

AffiliateStoreList = connect(
  (state) => ({
    storeList: state.commons.companyStore
  }),
  (dispatch) => ({
    cmAPI: bindActionCreators(
      {
        storeSel: cm_action.StoreList,
        excelDownload: cm_action.ExcelDownload,
        totalStoreSel: cm_action.TotalStoreList
      },
      dispatch
    )
  })
)(AffiliateStoreList);

export default AffiliateStoreList;
