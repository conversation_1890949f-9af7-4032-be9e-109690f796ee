import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Button } from 'semantic-ui-react';
import update from 'react-addons-update';
import moment from 'moment-timezone';
import download from 'downloadjs';

import { SettingForm, ExcelDeductList } from 'components/SikdaeGiveDeductByExcel';
import { ResultPage, LoadingBar, ConfirmContent } from 'components/Commons';

import * as commons from 'actions/commons';
import * as sik_action from 'actions/sikdae';

import storage from 'helpers/storage';
import toastr from 'helpers/toastr';
import cm from 'helpers/commons';

import exportToExcel from 'components/Excel';
import sheets from 'components/Excel/SikdaeGiveDeductByExcel';

moment.tz.setDefault('Asia/Seoul');

/**
 <div className="rt-tr -odd" key={idx}>
 <div className="rt-td" style={styles[0]}>
 {item.status.text}
 </div>
 <div className="rt-td" style={styles[1]}>
 {item.signid}
 </div>
 <div className="rt-td" style={styles[2]}>
 {item.name}
 </div>
 {division.map((divi, i) => (
            <div
              key={`divi${i}`}
              className="rt-td"
              style={{ flex: '128 0 auto', width: 128, maxWidth: 128, padding: '10px 0' }}
            >
              <Popup className="td-tooltip" inverted trigger={<span>{divi}</span>} content={divi} />
            </div>
          ))}
 <div className="rt-td" style={styles[3]}>
 {item.comidnum}
 </div>
 <div className="rt-td" style={styles[4]}>
 {cm.typeName(item.level)}
 </div>
 <div className="rt-td" style={styles[5]}>
 {item.group.name}
 </div>
 <div className="rt-td" style={styles[6]}>
 {item.policy.name}
 </div>
 <div className="rt-td" style={styles[7]}>
 {amountText}
 </div>
 <div className="rt-td" style={styles[8]}>
 {item.task.cause}
 </div>
 <div className="rt-td" style={styles[9]}>
 {item.status.cause}
 </div>
 </div>
 * */
const ResultDataMapper = (data) => {
  const multiDataSet = [];

  if (data.user.length > 0) {
    data.user.map((item) => {
      const division =
        item.status.value === 'fail_auth'
          ? cm.notErrorTrTdDivision(item.fulldivision[0])
          : cm.trTdDivision(item.orgcode);

      const findIndex = String(item.status.cause).match(/.*\(.*:\s+([0-9]+)\s+/);
      const checkData = (findIndex && findIndex.length > 0 && findIndex[1]) || '';

      multiDataSet.push({
        상태: item.status.text,
        ID: item.signid,
        이름: item.name,
        부서1: division[0] || '',
        부서2: division[1] || '',
        부서3: division[2] || '',
        부서4: division[3] || '',
        사원번호: item.comidnum,
        권한: item.grade && item.grade.text,
        식대그룹: item.group.name,
        식대정책: item.policy.name,
        '식대(원/장)': item.task.amount,
        '사용(원/장)': checkData,
        사유: item.status.cause
      });
    });

    console.log('multiDataSet:', multiDataSet);
  }
  return multiDataSet;
};

class SikdaeGiveDeductByExcel extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      isDowloadLoading: false,
      isUploadLoading: false,
      //식대 시작시각 예약하기
      isStartTime: false,
      startDate: moment(),
      endDate: null,
      minDate: moment(),
      timeDisable: false,
      endDisable: false,
      file: null,
      fileValue: null,
      params: null,
      isSuccess: null,
      dateSetting: false,
      list: null,
      isNextStep: false,
      confirmShow: false,
      confirmResult: false,
      auth: {
        give: false,
        deduct: false,
        schedule: false
      },
      mealVoucher: cm.authGroupSet('meal-voucher')
    };
  }

  componentWillMount() {
    const { mealVoucher } = this.state;

    if (mealVoucher.schedule) {
      this.scheduleStartDate();
      this.setState({ timeDisable: true });
    }
  }

  componentDidMount() {
    cm.mainTitle('식대 지급/차감 > 엑셀파일로 지급/차감하기');
  }

  fileChange = async (value) => {
    await this.setState({
      fileValue: update(this.state.fileValue, { $set: value })
    });
  };

  timeChange = (e, type, params) => {
    let value = e;

    if (type === 'sDate') {
      this.setState({
        startDate: update(this.state.startDate, { $set: e })
      });
    } else if (type === 'eDate') {
      this.setState({
        endDate: update(this.state.endDate, { $set: e })
      });
    } else if (type === 'eEndDate') {
      let now = new Date();
      let lastDate = null;

      lastDate = moment().endOf('month');

      this.setState({
        endDate: update(this.state.endDate, { $set: lastDate })
      });

      value = lastDate;
    } else if (type === 'eEndDate2') {
      let now = new Date();
      let lastDate = null;

      lastDate = moment()
        .add(1, 'months')
        .endOf('month');

      this.setState({
        endDate: update(this.state.endDate, { $set: lastDate })
      });

      value = lastDate;
    }
  };

  timeIsShow = async (e, type, data) => {
    if (type === 'sDateOrTime') {
      await this.setState({
        timeDisable: update(this.state.timeDisable, { $set: data.checked })
      });
    } else if (type === 'eEndDateDel') {
      const { endDisable } = this.state;

      await this.setState({
        endDisable: update(this.state.endDisable, { $set: !endDisable })
      });
    }
  };

  downloadExcelForm = async () => {
    const com = storage.get('company'),
      { isDowloadLoading } = this.state;

    const params = {
      com: com,
      excel: {
        url: '/coupon/v1/task/upload/download',
        method: 'get'
      }
    };

    let header = null,
      downloadExcelFile = '식대지급';

    const { commonsFn } = this.props;

    if (isDowloadLoading) {
      toastr('top-right', 'warning', downloadExcelFile + ' 엑셀파일을 생성 중입니다.');
      return;
    }

    try {
      await this.setState({
        isDowloadLoading: true
      });
      await commonsFn.excelDownload(params);

      const { excelDownloadReault } = this.props;
      if (excelDownloadReault) {
        header = excelDownloadReault.headers['content-disposition'];
        header = header.split('=');
      }

      download(excelDownloadReault.data, decodeURIComponent(header[1]));
      toastr('top-right', 'success', downloadExcelFile + ' 엑셀파일 생성 완료 되었습니다.');
    } catch (e) {
      const result = e.response;

      if (result && result.data) {
        let status = result.status;

        toastr('top-right', 'error', downloadExcelFile + ' 엑셀파일 생성에 실패 하였습니다.(' + String(status) + ')');
      }
      console.log('###### ExcelDownLoad : ', e);
    } finally {
      await this.setState({ isDowloadLoading: false });
    }
  };

  enableStartTime = () => {
    const { isStartTime } = this.state;
    this.setState({ isStartTime: !isStartTime });
  };

  goDeductPage = () => {
    this.context.router.history.push('/main/sikdaeGiveDeduct');
  };
  goSikdaePage = () => {
    this.context.router.history.push('/main/sikdaeGivenList');
  };

  excelChecked = async (value, formData) => {
    const { sikAPI } = this.props;
    try {
      await this.setState({
        fileValue: update(this.state.fileValue, { $set: value })
      });

      this.setState({ isUploadLoading: true });
      await sikAPI.sikExcelCheck(formData);

      const { excelCheck } = this.props;

      if (excelCheck && excelCheck.data) {
        const { count, user } = excelCheck.data;

        let failCount = Number(count.fail);
        let isNextStep = false;

        if (failCount === 0) {
          isNextStep = true;
        }

        this.setState({
          isUploadLoading: false,
          file: formData,
          list: excelCheck,
          isNextStep: isNextStep
        });
      }
    } catch (e) {
      this.setState({ isUploadLoading: false });
      let response = e.response;
      if (response) {
        toastr('top-right', 'error', response.data.message);
      }

      console.log('###### excelUpload error : ', e);
    } finally {
    }
  };

  setFileData = (formData, excelCheck) => {
    this.setState({
      file: formData,
      list: excelCheck
    });
  };

  complete = async () => {
    const { file, fileValue, params, dateSetting, isNextStep, list } = this.state,
      { sikAPI } = this.props;

    if (!fileValue) {
      toastr('top-right', 'warning', '엑셀파일을 업로드해주세요.');
      return;
    }

    if (!isNextStep) {
      toastr('top-right', 'warning', '실패항목이 있어 완료할 수 없습니다.');
      return;
    }
    // if(endDisable)

    if (!dateSetting) {
      toastr('top-right', 'warning', '기간을 설정하세요.');
      return;
    }

    const pre_fail = list?.data?.count?.pre_fail ?? 0;
    const isCheckedPreFail =
      !pre_fail ||
      confirm(
        `최대보유한도금액제한 설정에 따른 실패 예정 : ${pre_fail}건\n(실제 지급 시점의 사용자별 잔여 식대에 따라 성공/실패될 수 있습니다.)`
      );
    if (!isCheckedPreFail) return;

    let formData = new FormData();
    formData.append('file', fileValue);

    if (params['reserve']) {
      formData.append('reserve', params['reserve']);
    }
    if (params['expire']) {
      formData.append('expire', params['expire']);
    }

    if (file) {
      try {
        this.setState({
          isLoading: update(this.state.isLoading, { $set: true })
        });

        await sikAPI.sikExcelUpload(formData);

        this.setState({
          isSuccess: update(this.state.isSuccess, { $set: true }),
          isLoading: update(this.state.isLoading, { $set: false })
        });
      } catch (e) {
        console.log('#####: ', e);
        this.setState({
          isSuccess: update(this.state.isSuccess, { $set: false }),
          isLoading: update(this.state.isLoading, { $set: false })
        });
        let response = e.response;
        if (response) {
          toastr('top-right', 'error', response.data.message);
        }
      }
    } else {
      toastr('top-right', 'warning', '엑셀파일을 업로드 해주세요.');
    }
  };

  dateSetting = async (e, result) => {
    const { startDate, endDate, timeDisable, endDisable, isSuccess, dateSetting, list } = this.state;

    const { time } = this.props;

    const params = {};

    // let sDate = moment(startDate).format("YYYY-MM-DD") + " " + time.sTime
    // let eDate = endDate ? endDate.format("YYYY-MM-DD") + " 11:59:59 PM" : null
    let confirmResult = timeDisable ? timeDisable : result;

    //dateSetting -> false : 기간설정이 완료 되지 않았다는 것
    //timeDisable -> false : 식대 시작시각 예약하기가 체크가 되있지 않다는것.

    let sDate = null;
    let eDate = null;

    let sTime = moment(time.sTime, 'HH:mm A');
    sDate = moment(startDate);
    sDate = sDate.set({
      hour: sTime.get('hour'),
      minute: sTime.get('minute')
    });

    if (endDate) {
      eDate = moment(endDate.format('YYYY-MM-DD'));
      eDate = eDate.set({
        hour: 23,
        minute: 59,
        second: 59
      });
    }

    let timeCheck = cm.dateCheck(sDate, eDate, false);

    if (timeDisable && eDate && !timeCheck.isState) {
      toastr('top-right', 'warning', timeCheck.message);
      return;
    }

    if (!endDisable && !endDate) {
      toastr('top-right', 'warning', '식대유효기간을 선택하세요.');
      return;
    }

    if (!dateSetting) {
      //시작시간 설정이 false고 식대시각 체크 박스가 체크 되지 않다면.
      if (!confirmResult && !timeDisable) {
        this.confirmOpen(true, false);
        return;
      }
    }

    if (!dateSetting && confirmResult) {
      params['startDate'] = timeDisable ? `${moment(sDate).format('YYYY-MM-DD HH:mm')} 부터 ` : '지급시간 부터';
      params['time'] = timeDisable ? time : null;
      params['endDate'] = !endDisable ? (endDate ? endDate.format('YYYY-MM-DD') : null) : null;

      let reserve = null;
      let expire = null;

      if (timeDisable) {
        reserve = moment(sDate).toString();
      } else {
        reserve = null;
      }

      if (!endDisable) {
        expire = eDate ? moment(eDate).toString() : null;
      }

      params['reserve'] = reserve;
      params['expire'] = expire;

      await this.setState({
        params: update(this.state.params, { $set: params }),
        dateSetting: update(this.state.dateSetting, { $set: !dateSetting })
      });
    } else {
      await this.setState({
        params: update(this.state.params, { $set: null }),
        dateSetting: update(this.state.dateSetting, { $set: !dateSetting })
      });
    }
  };

  /**
   * [ 권한 중에 schedule 권한이 있을 때 식대지급 start 시간 조회 ]
   * @return {[type]} [description]
   */
  scheduleStartDate = async () => {
    try {
      const { sikAPI } = this.props;
      await sikAPI.scheduleStartDate(null);

      const { scheduleStartDate } = this.props;

      if (scheduleStartDate && scheduleStartDate.data) {
        this.setState({
          startDate: moment(new Date(scheduleStartDate.data.start)),
          minDate: moment(new Date(scheduleStartDate.data.start))
        });
      }
    } catch (e) {
    } finally {
    }
  };

  confirmOpen = async (isOpen, result) => {
    await this.setState({
      confirmShow: isOpen,
      confirmResult: result
    });

    if (result) {
      this.dateSetting(null, result);
    }
  };

  resultExportToExcel = () => {
    const {
      list: { data: ResultList }
    } = this.state;

    exportToExcel({
      fileName: 'tests',
      sheets,
      items: ResultList.user
    });
  };

  render() {
    const {
        isLoading,
        isStartTime,
        startDate,
        endDate,
        timeDisable,
        minDate,
        endDisable,
        isSuccess,
        params,
        dateSetting,
        list,
        isNextStep,
        mealVoucher,
        isDowloadLoading,
        isUploadLoading,
        confirmShow
      } = this.state,
      FormFn = {
        isStartTime: isStartTime,
        enableStartTime: this.enableStartTime,
        setFileData: this.setFileData,
        timeChange: this.timeChange,
        timeIsShow: this.timeIsShow,
        fileChange: this.fileChange,
        dateSetting: this.dateSetting,
        excelChecked: this.excelChecked
      },
      data = {
        startDate: startDate,
        endDate: endDate,
        minDate: minDate,
        timeDisable: timeDisable,
        endDisable: endDisable,
        params: params,
        dateSetting: dateSetting,
        isNextStep: isNextStep
      },
      confirmStyle = {
        width: '660px',
        fontSize: '16px',
        fontWeight: 'bold'
      };

    return (
      <main className="deduct-excel">
        {!isSuccess ? (
          isLoading ? (
            <LoadingBar />
          ) : (
            <div id="deduct-excel-dom">
              <ConfirmContent
                openFunc={this.confirmOpen}
                confirmShow={confirmShow}
                style={confirmStyle}
                text={'시작시각을 설정하지 않고, 유효기간 설정을 완료하시겠습니까?'}
              />
              <div className="download-excel">
                <Button inverted color="green" loading={isDowloadLoading} onClick={this.downloadExcelForm}>
                  식대지급 엑셀서식 다운로드
                </Button>
                <div>
                  <p>* 다운로드 받은 엑셀서식에 내용을 작성해주세요.</p>
                  <p>* 엑셀파일로는 ‘장기’ 식대정책만 지급/차감할 수 있습니다.</p>
                </div>
              </div>

              <div className="body-content">
                <SettingForm FormFn={FormFn} data={data} list={list} auth={mealVoucher} />
                <ExcelDeductList FormFn={FormFn} list={list} isUploadLoading={isUploadLoading} />
              </div>

              <div className="bottom-btns">
                <Button inverted color="red" className="cancel" onClick={this.goDeductPage}>
                  취소
                </Button>
                <Button onClick={this.resultExportToExcel}>Excel</Button>
                <Button color="green" className="complete" onClick={this.complete}>
                  완료
                </Button>
              </div>
            </div>
          )
        ) : null}
        {/* 결과화면 */}
        {isSuccess ? (
          <ResultPage success={isSuccess} continueBtn={this.goDeductPage} checkBtn={this.goSikdaePage} />
        ) : null}
      </main>
    );
  }
}

SikdaeGiveDeductByExcel.contextTypes = {
  router: PropTypes.object.isRequired
};

SikdaeGiveDeductByExcel = connect(
  (state) => ({
    excelDownloadReault: state.commons.excelDownload,
    excelUpload: state.sikdae.sikdae_excel_upload,
    excelCheck: state.sikdae.sikdae_excel_check,
    time: state.commons.time,
    list: state.sikdae.sikdae_excel_check,
    scheduleStartDate: state.sikdae.scheduleStartDate
  }),
  (dispatch) => ({
    commonsFn: bindActionCreators(
      {
        excelDownload: commons.ExcelDownload
      },
      dispatch
    ),

    sikAPI: bindActionCreators(
      {
        sikExcelUpload: sik_action.SikdaeExcelUpload,
        sikExcelCheck: sik_action.SikdaeExcelCheck,
        scheduleStartDate: sik_action.ScheduleStartDate
      },
      dispatch
    )
  })
)(SikdaeGiveDeductByExcel);

export default SikdaeGiveDeductByExcel;
