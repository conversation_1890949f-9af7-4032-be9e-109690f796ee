import React, { ReactElement } from 'react';
import { Row, Button } from 'antd';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withModalContext } from 'context/ModalContext';
import * as captainPayment from 'actions/captainPayment';

import openIcon from 'images/captainMarketPlace/icon_open.svg';
import { newIcon, useIcon } from './img/icon';
import { CardThumbnail, ServiceCardContent, ServiceCardWrapper, Icon, StyledButton } from './styles';

type ServiceType =
  | 'SIKDAE'
  | 'WELFARE'
  | 'BIZGIFT'
  | 'MESSAGE'
  | 'QUICK'
  | 'COMPULSORY'
  | 'VENDING'
  | 'COFFEE'
  | 'MRO'
  | 'FUNERAL';

export interface CardViewProps {
  serviceType: ServiceType;
  status?: 'ACTIVE' | 'INACTIVE' | 'DISABLED';
  url?: string;
  landingUrl?: string;
  image: string;
  logoImage: string;
  desc: string;
  open?: boolean;
  // props 전역으로 사용되는 값, 별도의 interface 구현이 안되있음
  history?: any;
  context?: any;
  captainPaymentApi?: any;
}

function CardView({
  url,
  landingUrl,
  image,
  logoImage,
  desc,
  serviceType,
  status,
  open = true,
  ...props
}: CardViewProps): ReactElement {
  const handleSikdaeCheck = async () => {
    const { context, captainPaymentApi } = props;
    const { value } = await captainPaymentApi.checkSikdaePermission({ serviceType: 'SIKDAE' });
    // eslint-disable-next-line no-shadow
    const { status } = value;
    if (status === 'COMPLETE') return true;
    if (status === 'NONE' || status === 'APPLYING') {
      const message = status === 'NONE' ? 'service.use.apply' : 'service.use.applying';
      context.showModal(message, value);
      return false;
    }
    return false;
  };

  const handleClick = async () => {
    const { history } = props;
    const activeUrl = status === 'ACTIVE' ? url : landingUrl;
    const isOutUrl = activeUrl.indexOf('https') >= 0;

    if (serviceType === 'SIKDAE') {
      const isAgreed = await handleSikdaeCheck();
      return isAgreed && history.push(activeUrl);
    }
    return isOutUrl ? window.open(url) : history.push(activeUrl);
  };

  let disabledText = '자세히 보기';
  let disabledIcon = newIcon;

  if (serviceType === 'SIKDAE') disabledText = '서비스 신청';
  if (!status) disabledIcon = openIcon;
  return (
    <ServiceCardWrapper>
      <CardThumbnail src={image} alt="logos" />
      <ServiceCardContent>
        <Row className="service-logo">
          <img className="logo" src={logoImage} alt="logo" />
          {open ? <Icon src={status === 'ACTIVE' ? useIcon : disabledIcon} /> : null}
        </Row>
        <Row className="desc">{desc}</Row>
        <Row>
          {open ? (
            <StyledButton onClick={handleClick} $status={status}>
              {status === 'ACTIVE' ? '바로가기' : disabledText}
            </StyledButton>
          ) : (
            <Button className="disabled" disabled>
              오픈예정
            </Button>
          )}
        </Row>
      </ServiceCardContent>
    </ServiceCardWrapper>
  );
}

const mapDispatchToProps = (dispatch) => ({
  captainPaymentApi: bindActionCreators(
    {
      checkSikdaePermission: captainPayment.captainPaymentSikdaePermission
    },
    dispatch
  )
});

export default withModalContext(connect(null, mapDispatchToProps)(CardView));
