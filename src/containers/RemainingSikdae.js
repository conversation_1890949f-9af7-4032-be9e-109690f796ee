import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { Commons } from 'components';
import { RemnantsList, RemnantsForm } from 'components/RemainingSikdae';
import cm from 'helpers/commons';
import toastr from 'helpers/toastr';
import * as cp_action from 'actions/company';
import * as cm_action from 'actions/commons';

const propTypes = {};

const defaultProps = {};

class RemainingSikdae extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoadingBar: false,
      sikdaeGroupVal: 0,
      mealVoucher: cm.authGroupSet('meal-voucher')
    };
  }

  isLoadingBarChange = async (isShow) => {
    await this.setState({ isLoadingBar: isShow });
  };

  componentWillMount() {
    cm.propsInit(this.props);
    //식대그룹 조회
    this.sikdaeGroup();
    //부서 조회
    this.divisionSearch();
    this.staffSel(null);
  }

  componentDidMount() {
    cm.mainTitle('사용자별 잔여 식대');
  }

  //식대 그룹 조회
  sikdaeGroup = async () => {
    const { cmAPI } = this.props;
    try {
      await cmAPI.sikdaeGroupList();
    } catch (e) {
      console.log('###### sikdaeGroup : ', e);
    } finally {
    }
  };

  //식대그룹 search
  sikdaeGroupSearch = async () => {
    const { groupIdx } = this.props;

    if (groupIdx.value && groupIdx.value !== 0) {
      const { cmAPI } = this.props;
      try {
        //식대정책 조회
        await cmAPI.sikdaePolicyList(groupIdx.value);
      } catch (e) {
        console.log('##### sikdaePolicyList error : ', e);
      }
    }
    this.setState({ sikdaeGroupVal: groupIdx.value });
  };

  //부서 search
  divisionSearch = async () => {
    try {
      const { cmAPI } = this.props;
      await cmAPI.divisionList();
    } catch (e) {
      console.log('###### divisionSearch : ', e);
    }
  };
  handleEntSubmit = (e) => {
    if (e.key === 'Enter') {
      this.submitForm(e);
    }
  };

  submitForm = (e) => {
    e.preventDefault();
    const { groupIdx, policyIdx, orgIdx, depth, text } = this.props;

    let division = null;
    if (depth) {
      division = depth.value;
    }
    const params = {
      groupid: groupIdx ? groupIdx.value : null,
      policyidx: policyIdx ? policyIdx.value : null,
      orgCode: division,
      keyword: text ? text.value : null
    };
    const { cmAPI } = this.props;
    cmAPI.changeExcel({
      excel: params
    });
    this.staffSel(params);
  };

  staffSel = async (params) => {
    this.isLoadingBarChange(true);
    const { cmAPI } = this.props;

    try {
      await cmAPI.staffSel(params);
    } catch (e) {
      const response = e.response;

      if (response) {
        toastr('top-right', 'error', response.data.message);
      }
      console.log('###### staffSel error : ', e);
    } finally {
      this.isLoadingBarChange(false);
    }
  };

  render() {
    const { isLoadingBar, sikdaeGroupVal, mealVoucher } = this.state;
    const { sikdaeGroup, sikdaePolicy, division } = this.props;
    const formFn = {
      sikdaeGroupVal: sikdaeGroupVal,
      sikdaeGroup: sikdaeGroup,
      sikdaeGroupSearch: this.sikdaeGroupSearch,
      handleEntSubmit: this.handleEntSubmit,
      sikdaePolicy: sikdaePolicy,
      submitForm: this.submitForm,
      division: division
    };
    return (
      <main className="rs">
        {isLoadingBar ? <Commons.LoadingBar /> : null}
        <RemnantsForm formFn={formFn} />
        <RemnantsList />
      </main>
    );
  }
}

RemainingSikdae.propTypes = propTypes;
RemainingSikdae.defaultProps = defaultProps;

RemainingSikdae = connect(
  (state) => ({
    sikdaeGroup: state.commons.sikdaeGroup,
    sikdaePolicy: state.commons.sikdaePolicy,
    groupIdx: state.commons.group,
    policyIdx: state.commons.policy,
    division: state.commons.division,
    depth: state.commons.depth,
    orgIdx: state.commons.orgIdx,
    text: state.commons.text
  }),
  (dispatch) => ({
    cmAPI: bindActionCreators(
      {
        staffSel: cm_action.StaffSel,
        sikdaeGroupList: cm_action.SikdaeGroupList,
        sikdaePolicyList: cm_action.SikdaePolicyList,
        divisionList: cm_action.DivisionList,
        changeExcel: cm_action.ChangeExcel
      },
      dispatch
    )
  })
)(RemainingSikdae);

export default RemainingSikdae;
