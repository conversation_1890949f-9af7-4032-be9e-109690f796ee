import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>u, Container, Segment } from 'semantic-ui-react';
import moment from 'moment-timezone';
moment.tz.setDefault('Asia/Seoul');
import * as cm_action from 'actions/commons';
import * as sik_action from 'actions/sikdae';
import cm from 'helpers/commons';
import { SikdaePolicy, Commons } from 'components';

import toastr from 'helpers/toastr';

class CompanySikdaePolicy extends Component {
  constructor(props) {
    super(props);
    this.state = {
      groupNum: 0,
      activeItem: '식대정책 상세정보',
      changeListPagerow: 10
    };
  }

  componentWillMount() {
    //식대그룹 조회
    this.sikdaeGroup();
  }

  componentDidMount() {
    cm.mainTitle('우리회사 식대정책');
  }

  //식대 그룹 조회
  sikdaeGroup = async () => {
    const { defGroup } = this.state;
    const { cmAPI, sikdaeGroup } = this.props;

    try {
      await cmAPI.sikdaeGroupList();

      const { sikdaeGroup } = this.props;

      //식대그룹이 있을 시 default 정책 조회
      if (sikdaeGroup) {
        let idx = sikdaeGroup.data.group[0].idx;
        let name = sikdaeGroup.data.group[0].name;

        //첫 화면 진입 시 식대정책 조회
        if (defGroup) {
          idx = this.state.groupNum;
          name = this.state.groupName;
          this.setState({
            defGroup: {
              idx: idx,
              name: name
            }
          });
          await this.handleItemClick(null, { name: '식대정책 상세정보' });
        } else {
          this.setState({
            defGroup: {
              idx: idx,
              name: name
            },
            groupNum: idx,
            groupName: name
          });
        }

        this.sikdaeGroupClick(idx, name);
      }
    } catch (e) {
      console.log('###### sikdaeGroup : ', e);
    } finally {
    }
  };

  //식대 그룹 클릭
  sikdaeGroupClick = async (group, name) => {
    const { activeItem } = this.state;
    const { cmAPI, sikdaeGroup, limitStoreResult } = this.props;

    try {
      //식대 정책 조회
      let groupNum = group;

      await cmAPI.sikdaePolicyList(groupNum);
      await this.setState({
        groupNum: groupNum,
        groupName: name ? name : this.state.groupName
      });

      const { sikdaePolicy } = this.props;
      if (sikdaePolicy && sikdaePolicy.data) {
        await this.limitStoreSel(sikdaePolicy.data.policy[0].idx);
      }
    } catch (e) {
      console.log('##### sikdaeGroupClick  : ', e);
    }
  };

  //식대 정책 수정
  sikdaePolicyModFn = async (param) => {
    const { cmAPI, time, week } = this.props,
      idx = this.state.groupNum;
    const params = {
      policy: {
        idx: param.pIdx,
        startTime: param.startTime ? new Date(moment(param.startTime, ['h:mm A'])).getTime() : null,
        endTime: param.endTime ? new Date(moment(param.endTime, ['h:mm A'])).getTime() : null,
        startTime2: param.startTime2 ? new Date(moment(param.startTime2, ['h:mm A'])).getTime() : null,
        endTime2: param.endTime2 ? new Date(moment(param.endTime2, ['h:mm A'])).getTime() : null,
        startTime3: param.startTime3 ? new Date(moment(param.startTime3, ['h:mm A'])).getTime() : null,
        endTime3: param.endTime3 ? new Date(moment(param.endTime3, ['h:mm A'])).getTime() : null,
        day: week ? week.value : null,
        holidayUseOption: param.holidayUseOption,
        isactive: param.isActive
      },
      demand: null
    };

    console.log('=========================');
    console.log(params);
    console.log(week);
    console.log('=========================');
    if (param.pdIdx) {
      params.demand = {
        idx: param.pdIdx,
        starttime: time ? new Date(moment(time.dsDate, ['h:mm A'])).getTime() : null,
        expiretime: time ? new Date(moment(time.deDate, ['h:mm A'])).getTime() : null
      };
    }

    try {
      await cmAPI.sikdaePolicyMod(idx, params);
    } catch (e) {
      const response = e.response;
      if (response) {
        toastr('top-right', 'error', response.data.message);
      }
    } finally {
      const { policyModResult } = this.props;
      if (policyModResult && policyModResult.status === 200) {
        toastr('top-right', 'success', '식대정책 변경이 완료되었습니다.');
      }
      this.sikdaeGroup();
    }
  };

  //제한된 가맹점 조회
  limitStoreSel = async (policyidx) => {
    try {
      const { sikAPI } = this.props;
      const { groupNum } = this.state;

      const params = {
        groupidx: groupNum,
        policyidx: policyidx
      };
      await sikAPI.limitStoreSel(params);
    } catch (e) {
      toastr('top-right', 'warning', '처리 중 오류가 발생했습니다. 다시 시도해주세요.');
    } finally {
    }
  };

  shouldComponentUpdate(nextProps, nextState) {
    const { sikdaeGroup } = this.props;
    let result = JSON.stringify(sikdaeGroup) !== JSON.stringify(nextProps.sikdaeGroup);

    return true;
  }

  handleItemClick = async (e, { name }) => {
    const { defGroup, groupNum } = this.state;
    const { sikdaeGroup } = this.props;

    let idx = e ? sikdaeGroup.data.group[0].idx : defGroup.idx;

    await this.setState({
      activeItem: name,
      groupNum: idx
    });
    await this.sikdaeGroupClick(idx, defGroup.name);
  };

  changePolicyListModal = (type, dimmer) => {
    this.setState({
      dimmer,
      open: dimmer
    });
    if (dimmer) {
      let page = '1';
      this.changePolicyHistorySel(page);
    }
  };

  changePolicyHistorySel = async (page) => {
    const { sikAPI } = this.props;
    const { changeListPagerow } = this.state;

    const params = 'page=' + page + '&pageRow=' + changeListPagerow;

    try {
      await sikAPI.changePolicyHistorySel(params);
    } catch (e) {
      toastr('top-right', 'warning', '처리 중 오류가 발생했습니다. 다시 시도해주세요.');
    } finally {
    }
  };

  render() {
    const { groupNum, groupName, activeItem, defGroup, dimmer, open, changeListPagerow } = this.state,
      { sikdaeGroup, sikdaePolicy, history } = this.props,
      GroupListFn = {
        sikdaeGroup: sikdaeGroup,
        sikdaeGroupClick: this.sikdaeGroupClick,
        groupNum: groupNum
      },
      PolicyListFn = {
        sikdaePolicy: sikdaePolicy,
        groupName: groupName,
        sikdaePolicyModFn: this.sikdaePolicyModFn
      };
    const modal = {
      open: open,
      dimmer: dimmer,
      close: this.changePolicyListModal,
      history: history,
      pagerow: changeListPagerow,
      fn: {
        changePolicyHistorySel: this.changePolicyHistorySel
      }
    };

    return (
      <main className="tab">
        <div className="top-btn button">
          <Button.Group>
            <Button
              inverted={activeItem == '식대정책 상세정보'}
              color="green"
              disabled={activeItem !== '식대정책 상세정보'}
              onClick={(e) => this.changePolicyListModal('open', true)}
            >
              식대정책 변경 내역
            </Button>
          </Button.Group>
        </div>
        <div className="search-box csp">
          <Menu fluid widths={2} className="vendysFullTab" tabular>
            <Menu.Item
              active={activeItem === '식대정책 상세정보'}
              name="식대정책 상세정보"
              onClick={this.handleItemClick}
            >
              <span>식대정책 상세정보</span>
            </Menu.Item>
            <Menu.Item
              name="식대정책 제휴식당 제한"
              active={activeItem === '식대정책 제휴식당 제한'}
              onClick={this.handleItemClick}
            >
              <span>식대정책 제휴식당 제한</span>
            </Menu.Item>
          </Menu>
          {/* <Segment attached='bottom'> */}

          <Container fluid>
            {activeItem === '식대정책 상세정보' ? (
              sikdaeGroup ? (
                <Grid className="search-box">
                  <Grid.Column width={3} className="group">
                    <SikdaePolicy.GroupList GroupListFn={GroupListFn} />
                  </Grid.Column>
                  <Grid.Column width={13} className="policy">
                    <SikdaePolicy.PolicyList PolicyListFn={PolicyListFn} />
                  </Grid.Column>
                </Grid>
              ) : (
                <Commons.LoadingBar />
              )
            ) : (
              <SikdaePolicy.LimitStore
                sikdaeGroupClick={this.sikdaeGroupClick}
                limitStoreSel={this.limitStoreSel}
                groupNum={groupNum}
              />
            )}
          </Container>
          {/* </Segment> */}
          <div className="bottom-text">
            <Segment compact>
              <span>
                신규 그룹 생성, 정책 생성, 지원 금액 변경 등 문의사항은 <EMAIL> 메일로 접수해주시기
                바랍니다.
              </span>
            </Segment>
          </div>

          <SikdaePolicy.ChangePolicyList modal={modal} />
        </div>
      </main>
    );
  }
}

CompanySikdaePolicy = connect(
  (state) => ({
    sikdaeGroup: state.commons.sikdaeGroup,
    sikdaePolicy: state.commons.sikdaePolicy,
    time: state.commons.time,
    week: state.commons.week,
    policyModResult: state.commons.policyModResult,
    limitStoreResult: state.sikdae.limitStoreResult,
    history: state.sikdae.history
  }),
  (dispatch) => ({
    cmAPI: bindActionCreators(
      {
        sikdaeGroupList: cm_action.SikdaeGroupList,
        sikdaePolicyList: cm_action.SikdaePolicyList,
        sikdaePolicyMod: cm_action.SikdaePolicyMod
      },
      dispatch
    ),

    sikAPI: bindActionCreators(
      {
        limitStoreSel: sik_action.LimitStoreSel,
        changePolicyHistorySel: sik_action.ChangePolicyHistorySel
      },
      dispatch
    )
  })
)(CompanySikdaePolicy);

export default CompanySikdaePolicy;
