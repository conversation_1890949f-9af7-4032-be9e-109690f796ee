import React, { Component } from 'react';
import PropTypes from 'prop-types';
import update from 'react-addons-update';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Grid } from 'semantic-ui-react';

import { Dormant, Commons, Management } from 'components';
import * as cpAction from 'actions/company';
import * as cmAction from 'actions/commons';

import cm from 'helpers/commons';
import toastr from 'helpers/toastr';

const propTypes = {};

const defaultProps = {};

class DormantStaffManagement extends Component {
  constructor(props) {
    super(props);
    this.state = {
      pagerow: null,
      param: {
        keyword: null,
        orgCode: null,
        groupid: null,
        viewType: 'all',
        dormantTargetFilter: false
      },
      staffCheckedData: [],
      isLoadingBar: false,
      tabType: 'division',
      userTotal: 0,
      userCnt: 0,
      changeInfoParams: null
    };
  }

  async componentDidMount() {
    cm.mainTitle('휴면 사용자 관리');
    const { param, userTotal } = this.state;
    this.dormantStaffs(param);

    const { divisionUserCnt } = this.props;
    if (divisionUserCnt && userTotal === 0) {
      let cnt = 0;
      divisionUserCnt.forEach((item) => {
        cnt += item.userCnt;
      });
      if (cnt > userTotal) {
        await this.setState({
          userTotal: cnt
        });
      }
    }
  }

  shouldComponentUpdate(nextProps) {
    const { text } = this.props;
    const { text: nText } = nextProps;

    return !(text !== nText);
  }

  isLoadingBarChange = async (isShow) => {
    await this.setState({ isLoadingBar: isShow });
  };

  // 사용자 조회
  dormantStaffs = async (params) => {
    this.isLoadingBarChange(true);
    const { cmAPI } = this.props;

    try {
      params.type = 'all';
      const result = await cmAPI.dormantStaff(params);
      const { staffList } = this.props;

      if (staffList && staffList.data) {
        const { paging } = staffList.data;

        await this.setState({
          userCnt: paging.totalCount
        });
      }
      // this.isLoadingBarChange(false);
    } catch (e) {
      const { response } = e;

      if (response) {
        toastr('top-right', 'error', response.data.message);
      }
      console.log('###### staffSel error : ', e);
    } finally {
      this.isLoadingBarChange(false);
    }
  };

  handleSubmit = (dormantCheck) => {
    const { auth, text } = this.props;
    const { param } = this.state;
    const params = {
      orgCode: param.orgCode,
      groupid: param.groupid,
      grade: auth ? auth.value : null,
      keyword: text ? text.value : null,
      viewType: param.viewType,
      dormantTargetFilter: typeof dormantCheck === 'boolean' ? dormantCheck : param.dormantTargetFilter
    };
    this.setState({
      param: update(this.state.param, {
        keyword: { $set: params.keyword },
        grade: { $set: params.grade }
      })
    });

    this.dormantStaffs(params);
  };

  // 검색
  handleEntSubmit = async (e) => {
    const { auth } = this.props;
    const { param } = this.state;
    if (e.key === 'Enter') {
      const keyword = e.target.value;
      const params = {
        keyword,
        orgCode: param.orgCode,
        groupid: param.groupid,
        grade: auth ? auth.value : null,
        viewType: param.viewType,
        dormantTargetFilter: param.dormantTargetFilter
      };

      this.setState({
        param: update(this.state.param, {
          keyword: { $set: keyword },
          grade: { $set: params.grade }
        })
      });

      this.dormantStaffs(params);
    }
  };

  // 사용자 체크박스 선택 on/off
  staffChecked = async (e, data, type) => {
    const { staffList } = this.props;
    const events = e.target;
    const targetChecked = e.target.checked;

    // 전체 선택 on/off
    if (type === 'all') {
      this.isLoadingBarChange(true);
      const staffData = staffList ? staffList.data.dormantUsers : null;
      // 전체 on/off 된 데이터 반환
      const staffCheckedData = cm.staffCheckedData(e, staffData, targetChecked, 'all');

      await this.setState({
        staffCheckedData
      });

      this.isLoadingBarChange(false);

      $('.allCheckbox').prop('checked', targetChecked);
      $('.staff-checkbox').prop('checked', targetChecked);
    }

    if (type !== 'all') {
      const staffData = this.state.staffCheckedData ? this.state.staffCheckedData : null;
      const staffCheckedData = cm.staffCheckedData(events, staffData, targetChecked, 'each');

      this.setState({
        staffCheckedData
      });
    }
  };

  // 퇴사 bulk 완료
  bulkCompletionBtn = (type, reserveDate) => {
    const { staffCheckedData } = this.state;
    const id = [];
    let value = null;

    // 사용자 id
    staffCheckedData.forEach((data) => {
      id.push(data.id);
    });
    // 계정 탈퇴
    if (type === 'withdraw') {
      value = -5;
      // 계정 일시정지
    } else if (type === 'pause') {
      value = -1;
      // 계정 일시정지해제
    } else if (type === 'pauseCancel') {
      value = 1;
    }

    const params = {
      type: {
        key: type,
        value
      },
      user: {
        id
      },
      reserveDate
    };
    this.staffBulkMod(params);
  };

  staffBulkMod = async (params) => {
    try {
      await this.setState({
        isLoadingBar: true
      });

      const { cpAPI } = this.props;
      await cpAPI.staffBulkMod(params);
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (e) {
      const { response } = e;

      if (response) {
        toastr('top-right', 'error', response.data.message);
      }
      console.log('###### staffBulkMod error : ', e);
    } finally {
    }
  };

  movePage = (url) => {
    this.context.router.history.push(url);
  };

  // 탭 이동
  tabMove = async (type) => {
    await this.setState({
      tabType: type
    });

    this.groupStaffList(null);
  };

  // 정보 변경 내역 조회
  changeInfoSel = async (params) => {
    const { cpAPI } = this.props;
    this.setState({ changeInfoParams: params });
    try {
      await cpAPI.changeInfoSel(params);
    } catch (e) {
      const { response } = e;
      if (response) {
        toastr('top-right', 'error', response.data.message);
      }
      console.log('###### changeInfoSel error : ', e);
    }
  };

  // 정보 변경 내역 예약 취소
  cancelReserve = async (idx) => {
    const { cpAPI } = this.props;
    const { changeInfoParams } = this.state;
    try {
      await cpAPI.cancelReserveDel(idx);
      this.changeInfoSel(changeInfoParams);
    } catch (e) {
      const { response } = e;
      if (response) {
        toastr('top-right', 'error', response.data.message);
      }
      console.log('###### changeInfoSel error : ', e);
    }
  };

  // 휴면사용자 조회 조건 체크
  handleDomantCheck = () => {
    const {
      param: { dormantTargetFilter }
    } = this.state;
    const toggle = !dormantTargetFilter;

    this.handleSubmit(toggle);
    this.setState({
      param: update(this.state.param, {
        dormantTargetFilter: { $set: toggle }
      })
    });
  };

  render() {
    // const { staffList, company } = this.props;
    const { param, staffCheckedData, isLoadingBar, tabType, userCnt } = this.state;

    const rightArea = {
      fn: {
        staffSel: this.dormantStaffs,
        handleEntSubmit: this.handleEntSubmit,
        staffChecked: this.staffChecked,
        isLoadingBarChange: this.isLoadingBarChange,
        handleSubmit: this.handleSubmit,
        handleDomantCheck: this.handleDomantCheck
      },
      data: {
        param,
        tabType,
        staffCheckedData,
        userCnt
        // , isLoadingBar: isLoadingBar
      }
    };

    const bottomBtnArea = {
      data: {
        staffCheckedData
      },
      fn: {
        movePage: this.movePage,
        bulkCompletionBtn: this.bulkCompletionBtn,
        changeInfoSel: this.changeInfoSel,
        cancelReserve: this.cancelReserve
      }
    };

    return (
      <main className="sm">
        {isLoadingBar ? <Commons.LoadingBar /> : null}
        <div className="info-box">
          <Grid className="dormant-area">
            <Dormant.DormantStaffArea staffCheckedDatadd={staffCheckedData} fn={rightArea.fn} data={rightArea.data} />
          </Grid>
        </div>
        <div className="botton-btn dormant-btn-area">
          <Management.BottomBtnArea data={bottomBtnArea.data} fn={bottomBtnArea.fn} type="dormant" />
        </div>
      </main>
    );
  }
}

DormantStaffManagement.propTypes = propTypes;
DormantStaffManagement.defaultProps = defaultProps;

DormantStaffManagement.contextTypes = {
  router: PropTypes.object.isRequired
};

DormantStaffManagement = connect(
  (state) => ({
    staffList: state.commons.dormant,
    divisionUserCnt: state.company.divisionUserCnt, // 부서별 사용자 수(카운트)
    auth: state.commons.auth,
    text: state.commons.text
  }),
  (dispatch) => ({
    cpAPI: bindActionCreators(
      {
        staffBulkMod: cpAction.StaffBulkMod,
        changeInfoSel: cpAction.ChangeInfoSel,
        cancelReserveDel: cpAction.CancelReserve
      },
      dispatch
    ),

    cmAPI: bindActionCreators(
      {
        dormantStaff: cmAction.dormantStaff
      },
      dispatch
    )
  })
)(DormantStaffManagement);

export default DormantStaffManagement;
