import React, { Component } from 'react';
import { Grid, Image, Checkbox, Modal } from 'semantic-ui-react';
import styled from 'styled-components';
import storage from 'helpers/storage';
import cm from 'helpers/commons';
import ReactGA from 'react-ga4';
import * as marketing_action from 'actions/marketing';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import {
  page_2,
  suggestion,
  suggestion_02,
  page_5,
  button_1,
  button_2,
  button_3,
  trophy,
  envelope,
  message_1,
  message_2,
  message_3,
  notice,
  close
} from 'images/companyEvent';
import { concat } from 'ramda';

class CompanyBenefits extends Component {
  constructor(props) {
    super(props);
    this.time = null;
    this.state = {
      agree: false,
      open: false,
      textCopy: false,
      onlyCodeCopy: false,
      recommended: '',
      info: false,
      sms: false,
      email: false,
      cellphone: false,
      push: false,
      allCheck: false,
      marketing: false
    };
  }

  async componentDidMount() {
    const { marketingAPI } = this.props;
    const params = {
      marketingId: 'recommendedPromotion'
    };
    ReactGA.event({
      category: 'CORP 추천코드 페이지',
      action: 'CORP_페이지뷰'
    });
    const {
      value: {
        data: { recommended }
      }
    } = await marketingAPI.marketingAgreeCheck(params);
    cm.mainTitle('추천 이벤트');
    this.setState({ recommended: recommended });
  }

  componentWillUnmount() {
    this.time = null;
  }

  codeView = async () => {
    const { marketingAPI } = this.props;
    const { agree, open, sms, email, cellphone, push } = this.state;
    const arr = [];

    ReactGA.event({
      category: 'CORP 추천코드 페이지',
      action: 'CORP 내 추천코드 보기 클릭 (all)'
    });

    if (agree) {
      sms ? arr.push({ reception: 'SMS' }) : null;
      email ? arr.push({ reception: 'EMAIL' }) : null;
      cellphone ? arr.push({ reception: 'CELLPHONE' }) : null;
      push ? arr.push({ reception: 'PUSH' }) : null;

      const params = {
        marketingId: 'recommendedPromotion',
        receptions: arr
      };

      const promise = await marketingAPI.marketingAgree(params);
      const data = await Promise.all([promise]);
      const recommended = data[0].value.data.recommended;

      this.setState({ recommended: recommended });
      ReactGA.event({
        category: 'CORP 추천코드 페이지',
        action: 'CORP 내 추천코드 보기 클릭 (동의 클릭 시)'
      });
    } else {
      this.setState({ open: !open });
      this.time = setTimeout(() => this.setState({ open: false }), 3000);
    }
  };

  messigeCopy = (val) => {
    const t = document.createElement('textarea');
    const { textCopy } = this.state;
    document.body.appendChild(t);
    t.value = val;
    t.select();
    document.execCommand('copy');
    document.body.removeChild(t);

    ReactGA.event({
      category: 'CORP 추천코드 페이지',
      action: 'CORP 이메일용 코드복사 클릭'
    });

    this.setState({
      textCopy: !textCopy,
      onlyCodeCopy: false
    });

    this.time = setTimeout(() => this.setState({ textCopy: false }), 3000);
  };

  codeCopy = (val) => {
    const t = document.createElement('textarea');
    const { onlyCodeCopy } = this.state;
    document.body.appendChild(t);
    t.value = `* 추천코드
${val}

* 문의경로
https://sikdae.com/?consultation=1&pmID=corp&reCode=${val}`;
    t.select();
    document.execCommand('copy');
    document.body.removeChild(t);

    ReactGA.event({
      category: 'CORP 추천코드 페이지',
      action: 'CORP 카카오톡 코드복사 클릭'
    });

    this.setState({
      onlyCodeCopy: !onlyCodeCopy,
      textCopy: false
    });

    this.time = setTimeout(() => this.setState({ onlyCodeCopy: false }), 3000);
  };

  marketingAgree = (type) => {
    const key = type.toLowerCase();

    this.setState({
      [key]: !this.state[key]
    });

    if (this.state[key]) {
      this.setState({ allCheck: false });
    }
  };

  allCheck = () => {
    const { allCheck } = this.state;

    if (!allCheck) {
      this.setState({
        agree: true,
        sms: true,
        email: true,
        cellphone: true,
        push: true,
        allCheck: !allCheck
      });
    }
    this.setState({ allCheck: !allCheck });
  };

  render() {
    const {
      recommended,
      agree,
      open,
      textCopy,
      onlyCodeCopy,
      info,
      sms,
      email,
      cellphone,
      push,
      allCheck,
      marketing
    } = this.state;
    const data = storage.get('company');
    const companyName = data.info.name;
    const userName = data.user.name;

    const url = `https://sikdae.com/?consultation=1&pmID=corp&reCode=${recommended}`;

    const sampleMessage = `저와 같은 고민을 하고 계실 것 같은 담당자님께
저희 회사에서 유용하게 사용하고 있는 기업 서비스 [식권대장]을 소개 드리고자 합니다.

식권대장은 직원들이 가장 좋아하는 식대복지를 실현할 수 있으면서도
비용절감, 식대 업무 효율화, 오남용 방지 효과까지 경험하실 수 있습니다.
더불어 이러한 효과는 직원들의 업무 생산성 향상뿐만 아니라 사내 평가 및 인사고과의 결과에도 긍정적이었습니다.

저희 회사는 너무 만족스럽게 이용 중이기에
담당자님께서도 관심 있으시다면 식권대장 서비스를 한 번 검토해보시면 좋을 것 같습니다.


*관리의 장점
01. 근태 기반 사용내역 대조 등 비효율 업무 약 59% 절감
02. 오남용 근절 및 평균 식대 약 18% 절감
03. 간편한 정산으로 회계/세무적 부담 축소  
04. 입/퇴사자 및 조직 그룹 관리의 편의성으로 복잡한 정책도 손쉽게 처리
05. 사용내역 및 다양한 통계 제공으로 데이터 기반 의사 결정에 용이


*사용의 장점
01. 재택근무 전환 시에도 재택근무지로 식대 지원 가능
02. 당일, 앱으로 주문한 식사를 점심시간에 받아볼 수 있는 기업전용배달 기능
03. 회사 주변 식당, 구내식당, 카페, 편의점, 베이커리, 유명 프랜차이즈 등 다양한 제휴점
04. 사내 복지몰처럼 이용할 수 있는 온라인 최저가 식품몰 등 앱 내 다양한 혜택 존재 
05. 식대 '지원'에서 '복지'로의 인식 변화로 직원 만족도 향상  


아래 URL로 문의 시, 저희 회사 추천코드를 입력하시면
도입 단계에서 쿠폰/할인/지원 등 다양한 혜택도 받으실 수 있습니다.
저희의 경험 사례가 담당자님께 모쪼록 도움 되길 바라며 코드와 문의 경로를 전달드립니다.

추천코드
${recommended}

문의URL
${url}`;

    return (
      <main>
        <WarpperContainer>
          <PBGrid>
            <Grid.Column width={16}>
              <MainTitle>{companyName}</MainTitle>
              <MainTitle>
                식권대장 우수 활용 고객사
                <Image src={trophy} verticalAlign="bottom" style={{ width: 56, margin: '0 5px 15px' }} />
              </MainTitle>
            </Grid.Column>

            <Grid.Column width={16}>
              <SubTitle>
                {companyName}의 운영 및 관리 효율이 우수함에 따라{' '}
                <Decoration>전용 추천코드를 발급 드립니다.</Decoration>
                <br />
                추천코드를 활용하면 우수 고객사만의 <Decoration>혜택을 무제한으로 받을 수 있어요!</Decoration>
              </SubTitle>
            </Grid.Column>
          </PBGrid>

          <Image src={suggestion} centered />

          <PBGrid style={{ textAlign: 'center' }}>
            <Grid.Column width={16}>
              <BoldText padding={5} fontSize={34}>
                {companyName} 담당자님의 추천 혜택
              </BoldText>
              <br />
              <BoldText padding={5} fontSize={34}>
                추천받은 회사가 도입할 때마다 중복 지급
              </BoldText>
            </Grid.Column>
          </PBGrid>

          <Image src={page_2} centered />
          <Image src={suggestion_02} centered style={{ paddingBottom: 100 }} />

          <Grid centered style={{ margin: 0 }}>
            <ViewGrid centered>
              <Box style={{ padding: 40 }}>
                <BoldText style={{ paddingBottom: 10 }}>지금 추천코드를 복사해 전달하며</BoldText>
                <BoldText>
                  식권대장 사용 경험을 전해주세요!{' '}
                  <Image src={envelope} verticalAlign="middle" style={{ width: 32, marginBottom: 5 }} />
                </BoldText>

                <Text color="#646464" paddingTop={30}>
                  담당자님의 사례는 식권대장 미경험 기업에게 좋은 래퍼런스가 됩니다.
                </Text>
              </Box>

              {!recommended ? (
                <Grid>
                  <CodeGrid centered>
                    <Grid.Column width={16} style={{ textAlign: 'center' }}>
                      <BoldText padding={30} fontSize={22}>
                        개인정보 활용에 <span style={{ textDecoration: 'underline' }}>동의</span>하셔야 추천코드를
                        발급받을 수 있습니다.
                      </BoldText>
                    </Grid.Column>

                    <Grid.Column width={9} style={{ marginLeft: 40 }}>
                      <Text>
                        <Checkbox onClick={this.allCheck} checked={allCheck} /> 전체약관에 동의합니다.
                      </Text>
                      <br />
                      <Text style={{ paddingLeft: 20, fontSize: 16 }}>
                        <Check onClick={() => this.marketingAgree('agree')} checked={agree} /> 개인정보 활용 동의 (필수)
                        <Icon src={notice} verticalAlign="middle" onClick={() => this.setState({ info: !info })} />
                      </Text>
                      <Text style={{ paddingLeft: 22, fontSize: 16 }}>
                        &nbsp; &nbsp; &nbsp; 마케팅 수신 동의 (선택){' '}
                        <Icon
                          src={notice}
                          verticalAlign="middle"
                          onClick={() => this.setState({ marketing: !marketing })}
                        />
                        <Span>
                          <Check onChange={() => this.marketingAgree('SMS')} checked={sms} /> SMS
                        </Span>
                        <Span>
                          <Check onChange={() => this.marketingAgree('EMAIL')} checked={email} /> E-mail
                        </Span>
                        <Span>
                          <Check onChange={() => this.marketingAgree('CELLPHONE')} checked={cellphone} /> Tel
                        </Span>
                        <Span>
                          <Check onChange={() => this.marketingAgree('PUSH')} checked={push} /> Push
                        </Span>
                      </Text>
                    </Grid.Column>

                    <Modal open={info} onClose={() => this.setState({ info: false })} style={{ width: 500 }}>
                      <Modal.Header style={{ border: 'none', padding: 0 }}>
                        <div style={{ width: 500 }}>
                          <Close src={close} verticalAlign="middle" onClick={() => this.setState({ info: false })} />
                        </div>
                      </Modal.Header>
                      <div style={{ padding: 10, color: '#747474' }}>
                        <Message>목적: 추천코드 발급 및 프로모션 진행, 리워드 대상자 확인 및 안내</Message>
                        <Message>항목: 아이디, 성명, 전화번호, 이메일, 근무지 정보(회사명, 주소)</Message>
                        <Message>보유기간: 서비스 이용 종료 시까지</Message>
                        <br />
                        <Message>개인 정보 수집, 이용에 대해 거부할 권리가 있습니다.</Message>
                        <Message>단, 동의 거부의 경우 코드 발급 및 리워드 진행이 불가능함을 알려드립니다.</Message>
                      </div>
                    </Modal>

                    <Modal open={marketing} onClose={() => this.setState({ marketing: false })} style={{ width: 500 }}>
                      <Modal.Header style={{ border: 'none', padding: 0 }}>
                        <div style={{ width: 500 }}>
                          <Close
                            src={close}
                            verticalAlign="middle"
                            onClick={() => this.setState({ marketing: false })}
                          />
                        </div>
                      </Modal.Header>
                      <div style={{ padding: 10, color: '#747474' }}>
                        <Message>
                          서비스와 관련된 업데이트 소식, 혜택 및 이벤트 안내 등 맞춤형 정보를 제공합니다.
                        </Message>
                      </div>
                    </Modal>

                    <Grid.Column width={16}>
                      <Image src={button_1} onClick={this.codeView} style={{ cursor: 'Pointer' }} centered />
                      <Content open={open}>
                        <Image src={message_1} style={{ left: '50%', transform: 'translate(-50%)' }} />
                      </Content>
                    </Grid.Column>
                  </CodeGrid>
                </Grid>
              ) : (
                <Grid>
                  <CodeGrid>
                    <Grid.Column width={16}>
                      <BoldText padding={10}>
                        {companyName} {userName} 담당자님의 추천 코드
                      </BoldText>
                      <BoldText padding={50} fontSize={62}>
                        {recommended}
                      </BoldText>
                    </Grid.Column>

                    <Grid.Column floated="left" width={8}>
                      <Image
                        src={button_2}
                        onClick={() => this.messigeCopy(sampleMessage)}
                        centered
                        style={{ cursor: 'Pointer' }}
                      />

                      <Content open={textCopy}>
                        <Image src={message_2} centered />
                      </Content>
                    </Grid.Column>

                    <Grid.Column floated="right" width={8}>
                      <Image
                        src={button_3}
                        onClick={() => this.codeCopy(recommended)}
                        centered
                        style={{ cursor: 'Pointer' }}
                      />

                      <Content open={onlyCodeCopy}>
                        <Image src={message_3} centered />
                      </Content>
                    </Grid.Column>
                  </CodeGrid>
                </Grid>
              )}
            </ViewGrid>
          </Grid>
          <Image src={page_5} centered />
        </WarpperContainer>
      </main>
    );
  }
}
const WarpperContainer = styled.div`
  max-width: 1000px;
  margin: auto;
`;

const Content = styled.div`
  opacity: ${(props) => (props.open ? '1' : '0')};
  max-height: ${(props) => (props.open ? '100%' : '0')};
  overflow: hidden;
  transition: all 0.3s;
  padding: ${(props) => (props.open ? '25px' : '0 15px')};
  position: absolute;
  z-index: 2;
  left: 50%;
  width: 800px;
  transform: translate(-50%, -30%);
  text-align: center;

  p {
    margin: 0;
  }
`;

const Decoration = styled.span`
  font-weight: bold;
  box-shadow: rgb(223 251 229) 0px -14px 0px inset;
  line-height: 2;
`;

const MainTitle = styled.h1`
  font-size: 56px;
  margin: 0;
  text-align: center;
  padding: 7px;
`;

const ViewGrid = styled(Grid)`
  && {
    width: 1000px;
    padding: 0px;
    border: solid 0.5px #494b51;
  }
`;

const CodeGrid = styled(Grid)`
  &&&& {
    padding-top: 80px;
    padding-bottom: 80px;
  }
`;

const PBGrid = styled(Grid)`
  &&&& {
    padding-bottom: 50px;
    padding-top: 15px;
  }
`;

const Check = styled(Checkbox)`
  padding-top: 4px;

  label:before {
    width: 15px !important;
    height: 15px !important;
  }
`;

const Span = styled.span`
  padding: 7px;
`;

const Close = styled.img`
  width: 25px;
  height: 25px;
  float: right;
  margin: 8px;
  background-color: white;
  cursor: pointer;
  font-size: 16px;
  border: 1px solid gray;
  border-radius: 5px;
  box-shadow: 0 1px 4px 0 gray;
`;

const Icon = styled.img`
  padding-top: 4px;
  padding-left: 4px
  vertical-align: middle;
  cursor: Pointer;
`;

const Message = styled.div`
  padding: 5px;
`;

const Text = styled.div`
  font-size: 22px;
  color: ${(props) => props.color};
  padding-top: ${(props) => props.paddingTop}px;
`;

const SubTitle = styled.div`
  font-size: 24px;
  padding: 5px;
  text-align: center;
`;

const Box = styled.div`
  background-color: #f7f8f9;
  width: 100%;
  text-align: left;
`;

const BoldText = styled.div`
  font-size: ${(props) => (props.fontSize ? props.fontSize : 24)}px;
  font-weight: bold;
  padding: ${(props) => props.padding}px;
`;

CompanyBenefits = connect(
  (state) => ({}),
  (dispatch) => ({
    marketingAPI: bindActionCreators(
      {
        marketingAgree: marketing_action.MarketingAgree,
        marketingAgreeCheck: marketing_action.MarketingAgreeCheck
      },
      dispatch
    )
  })
)(CompanyBenefits);

export default CompanyBenefits;
