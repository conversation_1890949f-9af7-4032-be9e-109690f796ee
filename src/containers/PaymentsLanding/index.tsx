import React, { ReactElement, useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import useFetch from 'use-http';

import api from 'config';
import { FormattedMessage } from 'react-intl';

import messages from './messages';
import { columns } from './viewMeta';
import { Container, ServiceItem } from './styles';
import { defaultAction } from './actions'
import { Row } from 'antd';

import { ServiceList } from './viewMeta'

interface QuickLandingProps {

}

 function QuickLanding({}: QuickLandingProps): ReactElement {

    return (
        <Container>
          <Row>
            {ServiceList.map(({img, title, info, tag})=>{
              return <ServiceItem xs={24} sm={6} md={6} lg={8} xl={8}>
              <a target="_blank" rel="noopener noreferrer" href="/corporate-business ">
                <img src={img} alt={title} />
                  <h3 className="title">{title}</h3>
                <p className="info">{info}</p>
                {tag && <div className="label-new">
                  <span>{tag}</span>
                </div>}
              </a>
            </ServiceItem>

            })}
          </Row>
        </Container>
    )
}

const mapStateToProps = (state) => {
    // return { todos: state.todos };
    return {

    };
}

const mapDispatchToProps = (dispatch, ownProps) => {
    // const boundActions = bindActionCreators({ defaultAction }, dispatch)
  return {
    // dispatchPlainObject: () => dispatch({ type: 'MY_ACTION' }),
    // dispatchActionCreatedByActionCreator: () => dispatch(createMyAction()),
    // ...boundActions,
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(QuickLanding)
