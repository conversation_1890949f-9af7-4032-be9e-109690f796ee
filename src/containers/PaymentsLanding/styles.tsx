import styled from 'styled-components'
import { Col } from 'antd'
import media from "styled-media-query";

export const Container = styled.div`
  font-family: Apple SD Gothic Neo !important;
  ${media.lessThan("medium")`
    /* screen width is less than 768px (medium) */
    padding: 0 30px!important;
  `}

  ${media.between("medium", "large")`
    /* screen width is between 768px (medium) and 1170px (large) */
    padding: 0 60px!important;
  `}

  ${media.greaterThan("large")`
    /* screen width is greater than 1170px (large) */
    padding: 0 120px;
  `}

`

export const ServiceItem = styled(Col)`

  padding-left: 15px;
  padding-right: 15px;

  margin-top: 45px;
  margin-bottom: 45px;

  & a {
    display: block;
    position: relative;
    z-index: 2;
  }


  & a img {
    box-shadow: 3px 4px 20px 0 rgb(0 0 0 / 10%);
    -webkit-transition: .7s cubic-bezier(.26,.76,.3,.75);
    transition: .7s cubic-bezier(.26,.76,.3,.75);
    width: 100%!important;
    border-radius: 6px;
    max-width: 100%;

  }

  &:hover {
    img {
        -webkit-transform: translate3d(0,-10px,0);
        transform: translate3d(0,-10px,0);
        box-shadow: 0 50px 80px -10px rgba(0,0,0,0.17);
    }
  }

  & a .title {
    display: block;
    -webkit-transition: .3s ease-in-out;
    transition: .3s ease-in-out;

    text-align: left;
    padding: 25px 0 10px;
    color: #1f1f25;
    font-size: 20px;
    font-weight: 500;
    letter-spacing: .2px;
    margin: 0;
  }

  & a .label-new {
    position: absolute;
    top: 5px;
    background: #f81f01;
    background: -webkit-linear-gradient(305deg,#f81f01,#ee076e);
    background: linear-gradient(145deg,#f81f01,#ee076e);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#f81f01",endColorstr="#ee076e",GradientType=1);
    padding: 9px 14px;
    color: #fff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1;
    right: 5px;
    text-transform: uppercase;
    letter-spacing: .5px;
    font-weight: 500;
  }

  & a .info {
    padding: 0;
    color: rgba(29,29,36,.75);
    font-size: 14px;
    font-weight: 300;
    line-height: 25px;
  }

`
