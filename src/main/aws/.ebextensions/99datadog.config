# .ebextensions/99datadog.config
option_settings:
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: DD_AGENT_MAJOR_VERSION
      value: "{{resolve:ssm:/production/dd-agent/major}}"
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: DD_AGENT_MINOR_VERSION
      value: "{{resolve:ssm:/production/dd-agent/minor}}"
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: DD_GIT_REPOSITORY_URL
      value: "**************:vendys/store-api.git"
    - namespace: aws:elasticbeanstalk:application:environment
      option_name: DD_GIT_COMMIT_SHA
      value: "{{resolve:ssm:/production/commit-sha/store-api}}"

files:
  "/etc/datadog-agent/datadog.yaml":
    mode: "000640"
    owner: root
    group: root
    content: |
      api_key: f62ce887cda434c9c6397e552220d35d
      site: datadoghq.com
      process_config:
        enabled: "true"
      logs_enabled: "true"
      apm_config:
        ignore_resources: ["(GET|POST) /health"]
      tags:
        - service:store-api
        - env:production
        - version:0.1.1
      # Add here the Agent configuration

  "/datadog_install_script.sh":
    mode: "000700"
    owner: root
    group: root
    source: https://s3.amazonaws.com/dd-agent/scripts/install_script_agent7.sh

  "/etc/datadog-agent/conf.d/java.d/conf.yaml":
    mode: "000640"
    owner: root
    group: root
    content: |
      logs:
        - type: file
          path: "/var/log/custom/tail-log.log"
          service: store-api
          source: java
          sourcecateogry: sourcecode

container_commands:
    02mkdir:
        command: sudo mkdir -p /var/log/custom
    03touch:
        command: sudo touch /var/log/custom/tail-log.log
    04chmod:
        command: sudo chown webapp:webapp -R /var/log/custom
    05setup_datadog:
        command: "DD_API_KEY=unused /datadog_install_script.sh; sed -i 's/ install_script/ ebs_install_script/' /etc/datadog-agent/install_info"
    05-1setupDatadogUser:
        #command: sudo adduser dd-agent
        command: id -u dd-agent &>/dev/null || sudo adduser dd-agent
    05-2fileChange:
        command: sudo chown dd-agent /etc/datadog-agent/conf.d/java.d/conf.yaml
    05-3fileChange:
        command: sudo chown dd-agent /etc/datadog-agent/datadog.yaml
    06mkdir:
        command: sudo mkdir -p /apm
    07down:
        command: aws s3 cp s3://vendys.devops/datadog-apm-agent/production/dd-java-agent.jar /apm/
    08chown:
        command: sudo chown -R webapp:webapp /apm
