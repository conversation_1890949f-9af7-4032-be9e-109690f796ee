vendys:
  env: local
  services:
    settlement-service:
      serviceName: Settlement-Service
      host: https://dev-settlement.mealc.co.kr
      header:
        key: x-settlement
        client: CorpAPI
        version: 1.0
logging:
  level:
    org:
      hibernate:
        type:
          descriptor:
            sql: debug
spring:
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 16MB
      max-request-size: 16MB
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      master:
        driver-class-name: com.mysql.jdbc.Driver
        jdbc-url: ********************************************************************************************************************
        username: ENC(7LMIY92dhDBix95zLt2fLqb/Z1kzwTTQ)
        password: ENC(QRY3npRjoQe4dtkoJiF41R4MLSLPseSasvqInP54DkQ=)
        pool-name: MASTER-POOL
        connection-test-query: SELECT 1
        connection-init-sql: SELECT 1
        connection-timeout: 240000
        validation-timeout: 240000
        maximum-pool-size: 30
      slave:
        driver-class-name: com.mysql.jdbc.Driver
        jdbc-url: ********************************************************************************************************************
        username: ENC(7LMIY92dhDBix95zLt2fLqb/Z1kzwTTQ)
        password: ENC(QRY3npRjoQe4dtkoJiF41R4MLSLPseSasvqInP54DkQ=)
        pool-name: SLAVE-POOL
        connection-test-query: SELECT 1
        connection-init-sql: SELECT 1
        connection-timeout: 360000
        validation-timeout: 360000
        maximum-pool-size: 30
        read-only: true
      official:
        driver-class-name: com.mysql.jdbc.Driver
        jdbc-url: **********************************************************************************************************************************
        username: ENC(7LMIY92dhDBix95zLt2fLqb/Z1kzwTTQ)
        password: ENC(QRY3npRjoQe4dtkoJiF41R4MLSLPseSasvqInP54DkQ=)
        pool-name: OFFICAL-POOL
        connection-test-query: SELECT 1
        connection-init-sql: SELECT 1
        connection-timeout: 240000
        validation-timeout: 240000
        maximum-pool-size: 30
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
        strategy: org.hibernate.cfg.EJB3NamingStrategy
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: false

server:
  sikdae:
    mode: LOCAL
  port: 18089

filetype:
  excel:
    surfix: .xlsx

storage:
  ucloud:
    timeout: 30000
    filebox: Test-Sikdae
    key: ********************************
    oauth:
      host: https://ssproxy.ucloudbiz.olleh.com/auth/v1.0
    path: excel
    id: <EMAIL>
  aws:
    s3:
      access: ********************
      region: ap-northeast-2
      host: https://s3.ap-northeast-2.amazonaws.com
      bucket: vendys.develop/attachments/company/excel
      secret: 5jQC2CDcY2A9RxLByMA4glyJhEXJDVu0/TXU8FFh
queue:
  aws:
    sqs:
      access: ********************
      secret: 5jQC2CDcY2A9RxLByMA4glyJhEXJDVu0/TXU8FFh
      region: ap-northeast-2
      host:
        organization: https://sqs.ap-northeast-2.amazonaws.com/064020620436/develop-sikdae-organization

custom:
  rest:
    connection:
      connect-timeout: 20000
      connection-request-timeout: 20000
      read-timeout: 20000

invoice:
  from:
    user: <EMAIL>
  done:
    file: plain
  to:
    user: <EMAIL>
  accept:
    file: invoice_approval_processing
  reject:
    file: invoice_approval_rejected
  cancel:
    file: invoice_approval_cancel
  link: https://dev-corp.mealc.co.kr

oauth:
  host: https://dev-auth.mealc.co.kr

sikdae:
  internal:
    key: zuycOZ5vxpMVLxR61K8D/AbsrH0S18/WzIs2XYEV/W8=
  customer:
    api:
      host: https://dev-organization.mealc.co.kr
      header:
        key: X-Customer
        value:
          client: CorpAPI
          version: 1.0
  message:
    api:
      host: https://dev-message.mealc.co.kr
      client: SikdaeCompany
  payment:
    api:
      host: https://dev-payment.mealc.co.kr
      header:
        key: X-Payment
        value:
          client: CorpAPI
          version: 1.0

gogoX:
  host: https://business-staging.gogox.co.kr
  header:
    a2b: false
    code: 3003851
    apikey: 83322A082B0918D618CA8185216C15B3818054E30CF662C5B8E59BEA0527A69D
    userCode: *********

slack:
  serviceApply:
    sikdae:
      host: *******************************************************************************
    welfare :
      host: *******************************************************************************
  inbound:
    sikdae:
      host: *******************************************************************************
    delivery:
      host: *******************************************************************************
    affiliate:
      host: *******************************************************************************
    affiliate-welfare-mall-b2b:
      host: *******************************************************************************
    benefitInquiry :
      host: *******************************************************************************
    benefitServiceApply :
      host: *******************************************************************************
    gift:
      host: *******************************************************************************
    quick:
      host: *******************************************************************************
    compulsory:
      host: *******************************************************************************
  debug:
    host: *******************************************************************************

quick:
  push:
    host: http://dev-sm.mealc.co.kr/business/quick/
  kakao:
    authHost: https://open-api-logistics.kakaomobility.com
    host: https://open-api-logistics.kakaomobility.com/goa-sandbox-service
    apikey: 3fa0bcc5-7bb9-454b-8f92-ba5092e3c9d4
    vendorId: VZQSQC
    callbackUrl: https://dev-corp-api.mealc.co.kr/captain-payment/quick/v2/callbacks/orders

jira:
  host: https://vendysdev.atlassian.net

alimTalk:
  host: https://dev-alimtalk-api.bizmsg.kr:1443

excel:
  sheet:
    pw: vendys123!
