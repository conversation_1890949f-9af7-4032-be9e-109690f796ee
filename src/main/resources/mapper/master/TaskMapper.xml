<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.vendys.company.api.repository.master.TaskMapper">

  <select id="selectTaskTotalCount" parameterType="kr.co.vendys.company.api.vo.TaskVo$TaskEx"
    resultType="kr.co.vendys.company.api.vo.TaskVo$TaskEx">
    SELECT
    COUNT(*) AS totalcount
    FROM
    (
    SELECT
    CPT.taskIdx
    FROM MealGroup MG
    LEFT JOIN CompanyPointTask CPT ON MG.groupIdx = CPT.groupIdx
    LEFT JOIN User U ON U.uid = CPT.orderUserId
    LEFT JOIN CompanyPointTaskUser CPTU ON CPTU.taskIdx = CPT.taskIdx
    <where>
      CPT.comId = #{comId}
      AND MG.groupType = #{groupType}
      <choose>
        <when test="dateSearchType == 'EXECUTE'">
          AND CPT.executeDate &gt;= #{startdate} AND CPT.executeDate &lt; #{enddate}
        </when>
        <otherwise>
          AND CPT.regDate &gt;= #{startdate} AND CPT.regDate &lt; #{enddate}
        </otherwise>
      </choose>
      AND ( (CPT.status = 'RESET' AND CPT.causeType != 'POLICY_ACTIONS') OR (CPT.status != 'RESET' AND CPT.causeType !=
      'SUPER_MODIFY') )
      <if test="keyword != null">
        AND ( LOWER(CPT.cause) like CONCAT('%',LOWER(#{keyword}),'%')
        OR LOWER(CPTU.userName) like CONCAT('%',LOWER(#{keyword}),'%')
        OR LOWER(CPTU.cause) like CONCAT('%',LOWER(#{keyword}),'%')
        OR LOWER(U.name) like CONCAT('%',LOWER(#{keyword}),'%')
        )
      </if>
      <if test="orderUserIdList != null">
        AND CPT.orderUserId IN
        <foreach item="item" index="index" collection="orderUserIdList" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="groupIdx != null">
        AND CPT.groupIdx = #{groupIdx}
      </if>
      <if test="policyIdx != null">
        AND CPT.policyIdx = #{policyIdx}
      </if>
      <if test="code == 'PLUS'">
        AND CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &gt; 0 AND CPT.userCount = CPT.userSuccessCount
      </if>
      <if test="code == 'MINUS'">
        AND CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &lt; 0 AND CPT.userCount = CPT.userSuccessCount
      </if>
      <if test="code == 'RESERVE'">
        AND CPT.chargeType = 'INSERT' AND ( CPT.status = 'RESERVE' OR CPT.status = 'INPUT' OR CPT.status = 'READY' OR CPT.status =
        'EXECUTE')
      </if>
      <if test="code == 'CANCEL'">
        AND CPT.chargeType = 'INSERT' AND CPT.status = 'CANCEL'
      </if>
      <if test="code == 'GROUP_RESET'">
        AND CPT.chargeType = 'RESET' AND CPT.causeType = 'GROUP_CHANGE'
      </if>
      <if test="code == 'WITHRAW_RESET'">
        AND CPT.chargeType = 'RESET' AND CPT.causeType = 'USER_WITHDRAW'
      </if>
      <if test="code == 'POLICY_RESET'">
        AND CPT.chargeType = 'RESET' AND CPT.causeType = 'POLICY_RESET'
      </if>
      <if test="code == 'ETC'">
        AND CPT.chargeType = 'RESET' AND CPT.causeType = 'ETC'
      </if>
    </where>
    GROUP BY
    CPT.taskIdx
    ) TOT
  </select>

  <select id="selectTaskList" parameterType="kr.co.vendys.company.api.vo.TaskVo$TaskEx"
    resultType="kr.co.vendys.company.api.vo.TaskVo$TaskEx">
    SELECT
    CPT.taskIdx,
    CPT.comId,
    CPT.groupIdx,
    CPT.policyIdx,
    CPT.chargeType,
    CPT.totalAmount,
    CPT.userCount,
    CPT.userSuccessCount,
    CPT.userFailCount,
    CPT.causeType,
    CPT.cause,
    CPT.regDate,
    CPT.reserveDate,
    CPT.executeDate,
    CPT.orderUserId,
    CPT.status,
    CASE
    WHEN U.name is not null
    THEN U.name
    ELSE '식권대장'
    END AS userName,
    CASE
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount = 0
    THEN 'PLUS'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &gt; 0 AND CPT.userCount = CPT.userSuccessCount
    THEN 'PLUS'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &gt; 0 AND ( CPT.userCount != CPT.userSuccessCount
    AND CPT.userSuccessCount &gt; 0)
    THEN 'PLUS_SOME_FAIL'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &gt; 0 AND ( CPT.userCount = CPT.userFailCount)
    THEN 'PLUS_ALL_FAIL'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &lt; 0 AND CPT.userCount = CPT.userSuccessCount
    THEN 'MINUS'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &lt; 0 AND ( CPT.userCount != CPT.userSuccessCount
    AND CPT.userSuccessCount &gt; 0)
    THEN 'MINUS_SOME_FAIL'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &lt; 0 AND ( CPT.userCount = CPT.userFailCount)
    THEN 'MINUS_ALL_FAIL'
    WHEN CPT.chargeType = 'INSERT' AND ( CPT.status = 'RESERVE' )
    THEN 'RESERVE'
    WHEN CPT.chargeType = 'INSERT' AND ( CPT.status = 'INPUT' OR CPT.status = 'READY' OR CPT.status = 'EXECUTE' )
    THEN 'ING'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'CANCEL'
    THEN 'CANCEL'
    WHEN CPT.chargeType = 'RESET' AND CPT.causeType = 'GROUP_CHANGE'
    THEN 'GROUP_RESET'
    WHEN CPT.chargeType = 'RESET' AND CPT.causeType = 'USER_WITHDRAW'
    THEN 'WITHRAW_RESET'
    WHEN CPT.chargeType = 'RESET' AND CPT.causeType = 'POLICY_RESET'
    THEN 'POLICY_RESET'
    ELSE 'ETC'
    END AS code,
    MG.name AS groupName,
    MP.name AS policyName
    FROM
    CompanyPointTask CPT
    LEFT JOIN User U ON U.uid = CPT.orderUserId
    LEFT JOIN MealGroup MG ON MG.groupIdx = CPT.groupIdx
    LEFT JOIN MealPolicy MP ON MP.policyIdx = CPT.policyIdx
    LEFT JOIN CompanyPointTaskUser CPTU ON CPTU.taskIdx = CPT.taskIdx
    <where>
      CPT.comId = #{comId}
      AND MG.groupType = #{groupType}
      <choose>
        <when test="dateSearchType == 'EXECUTE'">
          AND CPT.executeDate &gt;= #{startdate} AND CPT.executeDate &lt; #{enddate}
        </when>
        <otherwise>
          AND CPT.regDate &gt;= #{startdate} AND CPT.regDate &lt; #{enddate}
        </otherwise>
      </choose>
      AND ( (CPT.status = 'RESET' AND CPT.causeType != 'POLICY_ACTIONS') OR (CPT.status != 'RESET' AND CPT.causeType !=
      'SUPER_MODIFY') )
      <if test="keyword != null">
        AND ( LOWER(CPT.cause) like CONCAT('%',LOWER(#{keyword}),'%')
        OR LOWER(CPTU.userName) like CONCAT('%',LOWER(#{keyword}),'%')
        OR LOWER(CPTU.cause) like CONCAT('%',LOWER(#{keyword}),'%')
        OR LOWER(U.name) like CONCAT('%',LOWER(#{keyword}),'%')
        )
      </if>
      <if test="orderUserIdList != null">
        AND CPT.orderUserId IN
        <foreach item="item" index="index" collection="orderUserIdList" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="groupIdx != null">
        AND CPT.groupIdx = #{groupIdx}
      </if>
      <if test="policyIdx != null">
        AND CPT.policyIdx = #{policyIdx}
      </if>
      <if test="code == 'PLUS'">
        AND CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &gt; 0
        AND ((CPT.userCount = CPT.userSuccessCount) OR ( CPT.userCount != CPT.userSuccessCount AND CPT.userSuccessCount &gt; 0) OR ( CPT.userCount = CPT.userFailCount))
      </if>
      <if test="code == 'MINUS'">
        AND CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &lt; 0
        AND (CPT.userCount = CPT.userSuccessCount OR ( CPT.userCount != CPT.userSuccessCount AND CPT.userSuccessCount &gt; 0) OR ( CPT.userCount = CPT.userFailCount))
      </if>
      <if test="code == 'RESERVE'">
        AND CPT.chargeType = 'INSERT' AND ( CPT.status = 'RESERVE' OR CPT.status = 'INPUT' OR CPT.status = 'READY' OR CPT.status =
        'EXECUTE')
      </if>
      <if test="code == 'CANCEL'">
        AND CPT.chargeType = 'INSERT' AND CPT.status = 'CANCEL'
      </if>
      <if test="code == 'GROUP_RESET'">
        AND CPT.chargeType = 'RESET' AND CPT.causeType = 'GROUP_CHANGE'
      </if>
      <if test="code == 'WITHRAW_RESET'">
        AND CPT.chargeType = 'RESET' AND CPT.causeType = 'USER_WITHDRAW'
      </if>
      <if test="code == 'POLICY_RESET'">
        AND CPT.chargeType = 'RESET' AND CPT.causeType = 'POLICY_RESET'
      </if>
      <if test="code == 'ETC'">
        AND CPT.chargeType = 'RESET' AND CPT.causeType = 'ETC'
      </if>
    </where>
    GROUP BY
    CPT.taskIdx
    ORDER BY
    <choose>
      <when test="dateSearchType == 'EXECUTE'">
        CPT.executeDate DESC
      </when>
      <otherwise>
        CPT.regDate DESC
      </otherwise>
    </choose>
    <if test="page != null">
      LIMIT
      #{page}, #{pagerow}
    </if>
  </select>

  <select id="selectAllTaskDetail" parameterType="kr.co.vendys.company.api.vo.TaskVo$TaskEx"
    resultType="kr.co.vendys.company.api.vo.TaskVo$TaskEx">
    SELECT
    CPT.taskIdx,
    CPT.comId,
    CPT.groupIdx,
    CPT.policyIdx,
    CPT.chargeType,
    CPT.totalAmount,
    CPT.userCount,
    CPT.userSuccessCount,
    CPT.userFailCount,
    CPT.causeType,
    CPT.cause,
    CPT.regDate,
    CPT.reserveDate,
    CPT.executeDate,
    CPT.orderUserId,
    CPT.status,
    CASE
    WHEN U.name is not null
    THEN U.name
    ELSE '식권대장'
    END AS userName,
    CASE
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount = 0
    THEN 'PLUS'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &gt; 0 AND CPT.userCount = CPT.userSuccessCount
    THEN 'PLUS'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &gt; 0 AND ( CPT.userCount != CPT.userSuccessCount
    AND CPT.userSuccessCount &gt; 0)
    THEN 'PLUS_SOME_FAIL'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &gt; 0 AND ( CPT.userCount = CPT.userFailCount)
    THEN 'PLUS_ALL_FAIL'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &lt; 0 AND CPT.userCount = CPT.userSuccessCount
    THEN 'MINUS'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &lt; 0 AND ( CPT.userCount != CPT.userSuccessCount
    AND CPT.userSuccessCount &gt; 0)
    THEN 'MINUS_SOME_FAIL'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &lt; 0 AND ( CPT.userCount = CPT.userFailCount)
    THEN 'MINUS_ALL_FAIL'
    WHEN CPT.chargeType = 'INSERT' AND ( CPT.status = 'RESERVE' )
    THEN 'RESERVE'
    WHEN CPT.chargeType = 'INSERT' AND ( CPT.status = 'INPUT' OR CPT.status = 'READY' OR CPT.status = 'EXECUTE' )
    THEN 'ING'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'CANCEL'
    THEN 'CANCEL'
    WHEN CPT.chargeType = 'RESET' AND CPT.causeType = 'GROUP_CHANGE'
    THEN 'GROUP_RESET'
    WHEN CPT.chargeType = 'RESET' AND CPT.causeType = 'USER_WITHDRAW'
    THEN 'WITHRAW_RESET'
    WHEN CPT.chargeType = 'RESET' AND CPT.causeType = 'POLICY_RESET'
    THEN 'POLICY_RESET'
    ELSE 'ETC'
    END AS code,
    MG.name AS groupName,
    MP.name AS policyName,
    CPTU.userId AS detailUserId,
    CPTU.userName AS detailUserName,
    UU.signId AS detailSignId,
    UU.orgCode AS detailOrgCode,
    CPTU.cause AS detailCause,
    CPTU.expireDate,
    CPTU.amount,
    CPTU.result,
    CPTU.resultMessage
    FROM CompanyPointTask CPT
    LEFT JOIN User U ON U.uid = CPT.orderUserId
    LEFT JOIN MealGroup MG ON MG.groupIdx = CPT.groupIdx
    LEFT JOIN MealPolicy MP ON MP.policyIdx = CPT.policyIdx
    LEFT JOIN CompanyPointTaskUser CPTU ON CPTU.taskIdx = CPT.taskIdx
    LEFT JOIN User UU ON UU.uid = CPTU.userId
    <where>
      CPT.comId = #{comId}
      <choose>
        <when test="dateSearchType == 'EXECUTE'">
          AND CPT.executeDate &gt;= #{startdate} AND CPT.executeDate &lt; #{enddate}
        </when>
        <otherwise>
          AND CPT.regDate &gt;= #{startdate} AND CPT.regDate &lt; #{enddate}
        </otherwise>
      </choose>
      AND ( (CPT.status = 'RESET' AND CPT.causeType != 'POLICY_ACTIONS') OR (CPT.status != 'RESET' AND CPT.causeType !=
      'SUPER_MODIFY') )
      <if test="groupType != null">
        AND MG.groupType = #{groupType}
      </if>
      <if test="keyword != null">
        AND ( LOWER(CPT.cause) like CONCAT('%',LOWER(#{keyword}),'%')
        OR LOWER(CPTU.userName) like CONCAT('%',LOWER(#{keyword}),'%')
        OR LOWER(CPTU.cause) like CONCAT('%',LOWER(#{keyword}),'%')
        OR LOWER(U.name) like CONCAT('%',LOWER(#{keyword}),'%')
        )
      </if>
      <if test="orderUserIdList != null">
        AND CPT.orderUserId IN
        <foreach item="item" index="index" collection="orderUserIdList" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="groupIdx != null">
        AND CPT.groupIdx = #{groupIdx}
      </if>
      <if test="policyIdx != null">
        AND CPT.policyIdx = #{policyIdx}
      </if>
      <if test="code == 'PLUS'">
        AND CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &gt; 0 AND CPT.userCount = CPT.userSuccessCount
      </if>
      <if test="code == 'MINUS'">
        AND CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &lt; 0 AND CPT.userCount = CPT.userSuccessCount
      </if>
      <if test="code == 'RESERVE'">
        AND CPT.chargeType = 'INSERT' AND ( CPT.status = 'RESERVE' OR CPT.status = 'INPUT' OR CPT.status = 'READY' OR CPT.status =
        'EXECUTE')
      </if>
      <if test="code == 'CANCEL'">
        AND CPT.chargeType = 'INSERT' AND CPT.status = 'CANCEL'
      </if>
      <if test="code == 'GROUP_RESET'">
        AND CPT.chargeType = 'RESET' AND CPT.causeType = 'GROUP_CHANGE'
      </if>
      <if test="code == 'WITHRAW_RESET'">
        AND CPT.chargeType = 'RESET' AND CPT.causeType = 'USER_WITHDRAW'
      </if>
      <if test="code == 'POLICY_RESET'">
        AND CPT.chargeType = 'RESET' AND CPT.causeType = 'POLICY_RESET'
      </if>
      <if test="code == 'ETC'">
        AND CPT.chargeType = 'RESET' AND CPT.causeType = 'ETC'
      </if>
    </where>
    ORDER BY
    <choose>
      <when test="dateSearchType == 'EXECUTE'">
        CPT.executeDate DESC
      </when>
      <otherwise>
        CPT.regDate DESC
      </otherwise>
    </choose>
  </select>

  <select id="selectTask" parameterType="kr.co.vendys.company.api.vo.TaskVo$TaskEx"
    resultType="kr.co.vendys.company.api.vo.TaskVo$TaskEx">
    SELECT
    CPT.taskIdx,
    CPT.comId,
    CPT.groupIdx,
    CPT.policyIdx,
    CPT.chargeType,
    CPT.totalAmount,
    CPT.userCount,
    CPT.userSuccessCount,
    CPT.userFailCount,
    CPT.causeType,
    CPT.cause,
    CPT.regDate,
    CPT.reserveDate,
    CPT.executeDate,
    CPT.orderUserId,
    CPT.status,
    CASE
    WHEN U.name is not null
    THEN U.name
    ELSE '식권대장'
    END AS userName,
    CASE
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount = 0
    THEN 'PLUS'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &gt; 0 AND CPT.userCount = CPT.userSuccessCount
    THEN 'PLUS'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &gt; 0 AND ( CPT.userCount != CPT.userSuccessCount
    AND CPT.userSuccessCount &gt; 0)
    THEN 'PLUS_SOME_FAIL'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &gt; 0 AND ( CPT.userCount = CPT.userFailCount)
    THEN 'PLUS_ALL_FAIL'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &lt; 0 AND CPT.userCount = CPT.userSuccessCount
    THEN 'MINUS'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &lt; 0 AND ( CPT.userCount != CPT.userSuccessCount
    AND CPT.userSuccessCount &gt; 0)
    THEN 'MINUS_SOME_FAIL'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'END' AND CPT.totalAmount &lt; 0 AND ( CPT.userCount = CPT.userFailCount)
    THEN 'MINUS_ALL_FAIL'
    WHEN CPT.chargeType = 'INSERT' AND ( CPT.status = 'RESERVE' )
    THEN 'RESERVE'
    WHEN CPT.chargeType = 'INSERT' AND ( CPT.status = 'INPUT' OR CPT.status = 'READY' OR CPT.status = 'EXECUTE' )
    THEN 'ING'
    WHEN CPT.chargeType = 'INSERT' AND CPT.status = 'CANCEL'
    THEN 'CANCEL'
    WHEN CPT.chargeType = 'RESET' AND CPT.causeType = 'GROUP_CHANGE'
    THEN 'GROUP_RESET'
    WHEN CPT.chargeType = 'RESET' AND CPT.causeType = 'USER_WITHDRAW'
    THEN 'WITHRAW_RESET'
    WHEN CPT.chargeType = 'RESET' AND CPT.causeType = 'POLICY_RESET'
    THEN 'POLICY_RESET'
    ELSE 'ETC'
    END AS code,
    MG.name AS groupName,
    MP.name AS policyName,
    MP.maxHoldLimitAmount
    FROM
    CompanyPointTask CPT
    LEFT JOIN User U ON CPT.orderUserId = U.uid
    LEFT JOIN MealGroup MG ON CPT.groupIdx = MG.groupIdx
    LEFT JOIN MealPolicy MP ON CPT.policyIdx = MP.policyIdx
    <where>
      CPT.taskIdx = #{taskIdx}
      <if test="comId != null">
        AND CPT.comId = #{comId}
      </if>
    </where>
  </select>

  <select id="selectTaskUserTotalCount" parameterType="kr.co.vendys.company.api.vo.TaskVo$TaskUserEx"
    resultType="kr.co.vendys.company.api.vo.TaskVo$TaskUserEx">
    SELECT COUNT(CPTU.taskUserIdx) AS totalcount
    FROM CompanyPointTask CPT
    LEFT JOIN CompanyPointTaskUser CPTU ON CPT.taskIdx = CPTU.taskIdx
    WHERE CPTU.taskIdx = #{taskIdx}
    AND CPT.comId = #{comId}
  </select>

  <select id="selectTaskUserList" parameterType="kr.co.vendys.company.api.vo.TaskVo$TaskUserEx"
    resultType="kr.co.vendys.company.api.vo.TaskVo$TaskUserEx">
    SELECT
    CPTU.taskUserIdx,
    CPTU.taskIdx,
    CPTU.userId,
    CPTU.cause,
    CPTU.expireDate,
    IF(CPT.chargeType = 'RESET', MPL.amount, CPTU.amount) as amount,
    CPTU.result,
    CPTU.resultMessage,
    U.signId,
    U.name AS userName,
    U.comid,
    U.orgCode
    FROM
    CompanyPointTaskUser CPTU
    LEFT JOIN User U ON CPTU.userId = U.uid
    LEFT JOIN MealPointLog MPL ON CPTU.userId = MPL.userId AND CPTU.taskIdx = MPL.causeLink
    LEFT JOIN CompanyPointTask CPT ON CPTU.taskIdx = CPT.taskIdx
    WHERE
    CPTU.taskIdx = #{taskIdx}
    <if test="comId != null">
      AND CPT.comId = #{comId}
    </if>
    group by CPTU.taskUserIdx
    ORDER BY CPTU.result ASC, CPTU.taskUserIdx ASC
    <if test="page != null">
      LIMIT
      #{page}, #{pagerow}
    </if>
  </select>

  <insert id="insertTask" parameterType="kr.co.vendys.company.api.vo.TaskVo$Task" keyProperty="taskIdx" useGeneratedKeys="true">
    INSERT INTO
    CompanyPointTask
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="comId != null">
        comId,
      </if>
      <if test="groupIdx != null">
        groupIdx,
      </if>
      <if test="policyIdx != null">
        policyIdx,
      </if>
      <if test="chargeType != null">
        chargeType,
      </if>
      <if test="totalAmount != null">
        totalAmount,
      </if>
      <if test="userCount != null">
        userCount,
      </if>
      <if test="userSuccessCount != null">
        userSuccessCount,
      </if>
      <if test="userFailCount != null">
        userFailCount,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="causeType != null">
        causeType,
      </if>
      <if test="cause != null">
        cause,
      </if>
      <if test="regDate != null">
        regDate,
      </if>
      <if test="reserveDate != null">
        reserveDate,
      </if>
      <if test="executeDate != null">
        executeDate,
      </if>
      <if test="orderUserId != null">
        orderUserId,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="comId != null">
        #{comId},
      </if>
      <if test="groupIdx != null">
        #{groupIdx},
      </if>
      <if test="policyIdx != null">
        #{policyIdx},
      </if>
      <if test="chargeType != null">
        #{chargeType},
      </if>
      <if test="totalAmount != null">
        #{totalAmount},
      </if>
      <if test="userCount != null">
        #{userCount},
      </if>
      <if test="userSuccessCount != null">
        #{userSuccessCount},
      </if>
      <if test="userFailCount != null">
        #{userFailCount},
      </if>
      <if test="status != null">
        #{status},
      </if>
      <if test="causeType != null">
        #{causeType},
      </if>
      <if test="cause != null">
        #{cause},
      </if>
      <if test="regDate != null">
        #{regDate},
      </if>
      <if test="reserveDate != null">
        #{reserveDate},
      </if>
      <if test="executeDate != null">
        #{executeDate},
      </if>
      <if test="orderUserId != null">
        #{orderUserId},
      </if>
    </trim>
  </insert>

  <insert id="insertTaskUser" parameterType="kr.co.vendys.company.api.vo.TaskVo$TaskUser" keyProperty="taskUserIdx"
          useGeneratedKeys="true">
    INSERT INTO
    CompanyPointTaskUser
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskIdx != null">
        taskIdx,
      </if>
      <if test="userId != null">
        userId,
      </if>
      <if test="userName != null">
        userName,
      </if>
      <if test="cause != null">
        cause,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="result != null">
        result,
      </if>
      <if test="resultMessage != null">
        resultMessage,
      </if>
      <if test="expireDate != null">
        expireDate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskIdx != null">
        #{taskIdx},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
      <if test="userName != null">
        #{userName},
      </if>
      <if test="cause != null">
        #{cause},
      </if>
      <if test="amount != null">
        #{amount},
      </if>
      <if test="result != null">
        #{result},
      </if>
      <if test="resultMessage != null">
        #{resultMessage},
      </if>
      <if test="expireDate != null">
        #{expireDate},
      </if>
    </trim>
  </insert>

  <update id="updateTask" parameterType="kr.co.vendys.company.api.vo.TaskVo$Task">
    UPDATE
      CompanyPointTask
    SET status = #{status}
    WHERE taskIdx = #{taskIdx}
  </update>

</mapper>
