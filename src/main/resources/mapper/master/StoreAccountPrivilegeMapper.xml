<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kr.co.vendys.storeapi.mapper.master.StoreaccountprivilegeMapper" >
    <select id="selectPrivileges" parameterType="string" resultType="string">
        SELECT sid
        FROM StoreAccountPrivilege
        WHERE said = #{value}
    </select>

    <select id="selectPrivilegesAndOwner" parameterType="string" resultType="string">
        SELECT sid
        FROM StoreAccountPrivilege
        WHERE said = #{value}
        AND grade != 'STAFF'
        AND status = 1
    </select>
</mapper>