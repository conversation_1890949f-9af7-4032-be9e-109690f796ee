<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="kr.co.vendys.storeapi.mapper.master.CafeteriaMapper">
    <select id="selectCafeteriaInfo"
            parameterType="String"
            resultType="kr.co.vendys.storeapi.vo.cafeteria.CafeteriaVO$StoreNCompanyLimit" >
        SELECT DISTINCT S.sid,
                        S.id,
                        S.name,
                        S.phone,
                        S.cellphone,
                        S.address,
                        S.cancelfunc,
                        S.cancelpwchange,
                        S.cancelpw,
                        S.pushcoupon,
                        S.intro,
                        S.bizname,
                        S.bizserial,
                        S.bankname,
                        S.bankaccount,
                        S.bankowner,
                        S.supplyType,
                        IF(SUM(CL.isTicketFormat) = 0, 'WON','TICKET') AS ticketType
        FROM Store S
        LEFT JOIN CompanyPrivilege CP ON S.sid = CP.sid
        LEFT JOIN CompanyLimit CL ON CP.comid = CL.comid
        WHERE S.sid = #{storeId}
        GROUP BY S.sid
        ORDER BY S.sid
    </select>
    <select id="selectTimeMenu" resultType="kr.co.vendys.storeapi.vo.cafeteria.CafeteriaVO$TimeMenu" >
          SELECT storeId,
                 storeName,
                 menuId,
                 menuName,
                 groupIdx,
                 startTime,
                 endTime,
                 isActive,
                 regDate
          FROM TimeMenu
          WHERE isActive = 1
    </select>

</mapper>