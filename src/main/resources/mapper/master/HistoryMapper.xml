<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.co.vendys.company.api.repository.master.HistoryMapper">

  <select id="selectHistoryTotalCount" parameterType="kr.co.vendys.company.api.vo.HistoryVo$AdminModifyLog"
          resultType="kr.co.vendys.company.api.vo.HistoryVo$AdminModifyLog">
    SELECT
    COUNT(*) AS totalCount
    FROM
    AdminModifyLog
    <where>
      companyId = #{companyId}
      AND serverName IN (#{serverName}, 'ADMIN')
      <if test="tableName != null">
        AND tableName = #{tableName}
      </if>
      <if test="changeLog != null">
        AND changeLog like CONCAT('%',#{changeLog},'%')
      </if>
      <if test="changeLog == null">
        AND changeLog not like CONCAT('%WELFARE%')
      </if>
    </where>
  </select>

  <select id="selectHistoryList" parameterType="kr.co.vendys.company.api.vo.HistoryVo$AdminModifyLog"
          resultType="kr.co.vendys.company.api.vo.HistoryVo$AdminModifyLog">
    SELECT
    A.logIdx,
    A.userId,
    IF(B.grade = 'SUPER' or C.adminId is not null,'식권대장',A.userName) userName,
    A.companyId,
    A.tableIdx,
    A.tableName,
    A.serverName,
    A.regDate,
    A.beforeData,
    A.changeLog
    FROM
    AdminModifyLog A
    LEFT OUTER JOIN User B
    ON A.userId = B.uid
    LEFT OUTER JOIN AdminUser C
    ON A.userId = C.adminId
    <where>
      companyId = #{companyId}
      AND serverName IN (#{serverName}, 'ADMIN')
      <if test="tableName != null">
        AND tableName = #{tableName}
      </if>
      <if test="changeLog != null">
        AND changeLog like CONCAT('%',#{changeLog},'%')
      </if>
      <if test="changeLog == null">
        AND changeLog not like CONCAT('%WELFARE%')
      </if>
    </where>
    ORDER BY A.logIdx DESC
    <if test="page != null">
      LIMIT
      #{page}, #{pagerow}
    </if>
  </select>

  <select id="selectHistory" parameterType="kr.co.vendys.company.api.vo.HistoryVo$AdminModifyLog"
          resultType="kr.co.vendys.company.api.vo.HistoryVo$AdminModifyLog">
    SELECT
    A.logIdx,
    A.userId,
    IF(B.grade = 'SUPER' or C.adminId is not null,'식권대장',A.userName) userName,
    A.companyId,
    A.tableIdx,
    A.tableName,
    A.serverName,
    A.regDate,
    A.beforeData,
    A.changeLog
    FROM
    AdminModifyLog A
    LEFT OUTER JOIN User B
    ON A.userId = B.uid
    LEFT OUTER JOIN AdminUser C
    ON A.userId = C.adminId
    <where>
      A.serverName = #{serverName}
      AND A.userId = #{userId}
      AND A.companyId = #{companyId}
      AND A.tableName = #{tableName}
      AND A.regDate = #{regDate}
    </where>
  </select>

  <insert id="insertHistory" parameterType="kr.co.vendys.company.api.vo.HistoryVo$AdminModifyLog">
    INSERT INTO
    AdminModifyLog
    <trim prefix="(" suffix=")" suffixOverrides=",">
      logIdx,
      userId,
      userName,
      companyId,
      tableIdx,
      tableName,
      serverName,
      regDate,
      beforeData,
      changeLog
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{logIdx},
      #{userId},
      #{userName},
      #{companyId},
      #{tableIdx},
      #{tableName},
      #{serverName},
      #{regDate},
      #{beforeData},
      #{changeLog}
    </trim>
  </insert>
</mapper>
