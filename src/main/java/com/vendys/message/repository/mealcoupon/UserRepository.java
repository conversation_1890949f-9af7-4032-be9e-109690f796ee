package com.vendys.message.repository.mealcoupon;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

import com.vendys.message.entity.mealcoupon.User;
import com.vendys.message.entity.mealcoupon.User.Status;
import org.springframework.data.jpa.repository.Query;


/**
 * Created by jung<PERSON> on 2019-07-08
 */
public interface UserRepository extends JpaRepository<User, String> {

    List<User> findByComidAndStatusNotAndIsDormantFalse(String comId, Status status);

    @Query(value = "SELECT DISTINCT U.uid "
            + "FROM MarketingAgree MA,  MarketingReceive MR, User U "
            + "WHERE MA.idx = MR.marketingAgreeIdx"
            + "   AND MR.userId = U.uid"
            + "   AND MA.isDelete = FALSE"
            + "   AND MR.isDelete = FALSE"
            + "   AND U.status = 'ACTIVE'"
            + "   AND MR.agreePush = TRUE"
            + "   ORDER BY MA.createDate",
            nativeQuery = true)
    List<String> findMarketingAgreeUserOfUidList();

    @Query(value = "SELECT U.uid "
            + "FROM User U "
            + "WHERE U.status = 'ACTIVE'",
            nativeQuery = true)
    List<String> findActiveUserOfUidList();
}
