package com.vendys.message.service;

import com.vendys.message.constant.PushActionCode;
import com.vendys.message.constant.PushMessageBoxType;
import com.vendys.message.controller.Response;
import com.vendys.message.controller.entity.PushDto;
import com.vendys.message.controller.entity.PushDto.MessageBoxRequest;
import com.vendys.message.controller.entity.PushDto.MessageBoxResponse;
import com.vendys.message.controller.entity.PushDto.PageInfo;
import com.vendys.message.exception.CommonException;
import com.vendys.message.exception.Errors;
import com.vendys.message.queue.PushQueue;
import com.vendys.message.repository.mysql.PushMessageBoxMapper;
import com.vendys.message.repository.mysql.PushsMapper;
import com.vendys.message.task.TaskScheduler;
import com.vendys.message.util.ConverterUtil;
import com.vendys.message.util.ValueUtil;
import com.vendys.message.vo.mysql.PushVo;
import com.vendys.message.vo.mysql.PushVo.MessageBox;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.vendys.message.constant.PushActionCode.RobotDelivery;

/**
 * Created by jinwoo on 2019-03-20
 */
@Service
@Transactional
@RequiredArgsConstructor
public class PushCervice {

    private final TaskScheduler pushTask;
    private final PushsMapper pushsMapper;
    private final PushMessageBoxMapper pushMessageBoxMapper;
    private final ConverterUtil converterUtil;

    public Response company_push_send(PushDto.Request.CompanyData reqParam) throws UnsupportedEncodingException {

        if (ValueUtil.nullChk(reqParam.getComid()).equals("")) {
            throw new CommonException(Errors.ERROR_BLANK_COMID);
        }
        if (ValueUtil.nullChk(reqParam.getContent()).equals("")) {
            throw new CommonException(Errors.ERROR_BLANK_CONTENT);
        }
        if (ValueUtil.nullChk(reqParam.getType()).equals("")) {
            throw new CommonException(Errors.ERROR_BLANK_TYPE);
        }
        if (ValueUtil.nullChk(reqParam.getClient()).equals("")) {
            throw new CommonException(Errors.ERROR_BLANK_CLIENT);
        }

        if (this.pushsMapper.check_client(ValueUtil.nullChk(reqParam.getClient())) == 0) {
            throw new CommonException(Errors.NOT_MATCH_CLIENT);
        }

        PushVo.Push pushVo = new PushVo.Push();
        pushVo.setContent(reqParam.getContent());
        pushVo.setClient(reqParam.getClient());
        pushVo.setComid(reqParam.getComid());
        pushVo.setParam(reqParam.getParam());
        pushVo.setParam2(reqParam.getParam2());
        pushVo.setAction(reqParam.getAction());
        pushVo.setType(reqParam.getType());
        pushVo.setRequestid(reqParam.getRequestid());
        pushVo.setAccounttype("company");
        pushVo.setTitle(reqParam.getTitle());
        pushVo.setCustomScheme(reqParam.getCustomScheme());
        this.pushsMapper.insert_push(pushVo);

        String actionCode;
        switch (PushActionCode.getEnum(reqParam.getAction())) {
            case BookingFinish:
            case BookingDelivery:
            case CompanyNotice:
                actionCode = PushActionCode.CompanyNotice.name();
                break;
            case DefaultPush:
                actionCode = PushActionCode.CompanyAnnounce.name();
                break;
            default:
                actionCode = reqParam.getAction();
                break;
        }

        PushQueue queue = new PushQueue();
        queue.setContent(pushVo.getContent());
        queue.setRequesttime(new Date().getTime());
        queue.setParam(pushVo.getParam());
        queue.setParam2(pushVo.getParam2());
        queue.setAction(actionCode);
        queue.setPushidx(pushVo.getPushidx());
        queue.setComid(pushVo.getComid());
        queue.setType("company");
        queue.setTitle(pushVo.getTitle());
        queue.setCustomScheme(this.encodeCustomScheme(pushVo.getCustomScheme()));

        if (reqParam.getType().equals("priority")) {
            this.pushTask.addPriorityQueue(queue);
        } else {
            this.pushTask.addPushQueue(queue);
        }

        Response rtn = new Response();
        rtn.setContent("SUCCESS");

        return rtn;
    }

    public Response store_push_send(PushDto.Request.StoreData reqParam) throws UnsupportedEncodingException {

        if (ValueUtil.nullChk(reqParam.getSid()).equals("")) {
            throw new CommonException(Errors.ERROR_BLANK_SID);
        }
        if (ValueUtil.nullChk(reqParam.getContent()).equals("")) {
            throw new CommonException(Errors.ERROR_BLANK_CONTENT);
        }
        if (ValueUtil.nullChk(reqParam.getType()).equals("")) {
            throw new CommonException(Errors.ERROR_BLANK_TYPE);
        }
        if (ValueUtil.nullChk(reqParam.getClient()).equals("")) {
            throw new CommonException(Errors.ERROR_BLANK_CLIENT);
        }
        if (this.pushsMapper.check_client(ValueUtil.nullChk(reqParam.getClient())) == 0) {
            throw new CommonException(Errors.NOT_MATCH_CLIENT);
        }

        PushVo.Push pushVo = new PushVo.Push();
        pushVo.setSid(reqParam.getSid());
        pushVo.setContent(reqParam.getContent());
        pushVo.setClient(reqParam.getClient());
        pushVo.setParam(reqParam.getParam());
        pushVo.setParam2(reqParam.getParam2());
        pushVo.setAction(reqParam.getAction());
        pushVo.setType(reqParam.getType());
        pushVo.setRequestid(reqParam.getRequestid());
        pushVo.setAccounttype("store");
        pushVo.setTitle("식권대장 사장님");
        pushVo.setCustomScheme(reqParam.getCustomScheme());
        this.pushsMapper.insert_push(pushVo);

        PushQueue queue = new PushQueue();
        queue.setContent(pushVo.getContent());
        queue.setRequesttime(new Date().getTime());
        queue.setParam(pushVo.getParam());
        queue.setParam2(pushVo.getParam2());
        queue.setAction(pushVo.getAction());
        queue.setPushidx(pushVo.getPushidx());
        queue.setSid(pushVo.getSid());
        queue.setType("store");
        queue.setTitle(pushVo.getTitle());
        queue.setCustomScheme(this.encodeCustomScheme(pushVo.getCustomScheme()));

        if (reqParam.getType().equals("priority")) {
            this.pushTask.addPriorityQueue(queue);
        } else {
            this.pushTask.addPushQueue(queue);
        }

        Response rtn = new Response();
        rtn.setContent("SUCCESS");

        return rtn;
    }

    public Response user_push_send(PushDto.Request.UserData reqParam) throws UnsupportedEncodingException {

        if (reqParam.getUid() == null) {
            throw new CommonException(Errors.ERROR_BLANK_UID);
        }
        if (reqParam.getUid().size() == 0) {
            throw new CommonException(Errors.ERROR_BLANK_UID);
        }

        if (ValueUtil.nullChk(reqParam.getContent()).equals("")) {
            throw new CommonException(Errors.ERROR_BLANK_CONTENT);
        }
        if (ValueUtil.nullChk(reqParam.getType()).equals("")) {
            throw new CommonException(Errors.ERROR_BLANK_TYPE);
        }
        if (ValueUtil.nullChk(reqParam.getClient()).equals("")) {
            throw new CommonException(Errors.ERROR_BLANK_CLIENT);
        }
        if (this.pushsMapper.check_client(ValueUtil.nullChk(reqParam.getClient())) == 0) {
            throw new CommonException(Errors.NOT_MATCH_CLIENT);
        }

        String uid = "";
        for (int i = 0; i < reqParam.getUid().size(); i++) { // 배열
            if (!uid.equals("")) {
                uid += ",";
            }
            uid += reqParam.getUid().get(i);
        }

        PushVo.Push pushVo = new PushVo.Push();
        pushVo.setContent(reqParam.getContent());
        pushVo.setClient(reqParam.getClient());
        pushVo.setParam(reqParam.getParam());
        pushVo.setParam2(reqParam.getParam2());
        pushVo.setChannelId(reqParam.getChannelId());
        pushVo.setAction(reqParam.getAction());
        pushVo.setType(reqParam.getType());
        pushVo.setRequestid(reqParam.getRequestid());
        pushVo.setAccounttype("user");
        pushVo.setUid(uid);
        pushVo.setTitle(reqParam.getTitle());
        pushVo.setCustomScheme(reqParam.getCustomScheme());
        this.pushsMapper.insert_push(pushVo);

        String actionCode;
        switch (PushActionCode.getEnum(reqParam.getAction())) {
            case BookingFinish:
                actionCode = PushActionCode.BookingFinish.name();
                break;
            case BookingDelivery:
                actionCode = PushActionCode.BookingDelivery.name();
                break;
            case CompanyNotice:
            case DefaultPush:
                actionCode = PushActionCode.CompanyAnnounce.name();
                break;
            case ThirdPartyCoupon:
                actionCode = PushActionCode.ThirdPartyCoupon.name();
                break;
            case ThirdPartyCouponFail:
                actionCode = PushActionCode.ThirdPartyCouponFail.name();
                pushVo.setCustomScheme("sikdae://screen/main/alerts?q={pushMessageBoxIdx:" + pushVo.getPushidx() + "}");
                this.pushsMapper.updateCustomScheme(pushVo);
                break;
            case CaptainCouponBox:
                actionCode = PushActionCode.CaptainCouponBox.name();
                break;
            case QuickDetailInfo:
                actionCode = PushActionCode.QuickDetailInfo.name();
                break;
            default:
                actionCode = reqParam.getAction();
                break;
        }

        PushQueue queue = new PushQueue();
        queue.setContent(pushVo.getContent());
        queue.setRequesttime(new Date().getTime());
        queue.setParam(pushVo.getParam());
        queue.setParam2(pushVo.getParam2());
        queue.setChannelId(pushVo.getChannelId());
        queue.setAction(actionCode);
        queue.setPushidx(pushVo.getPushidx());
        queue.setType("user");
        queue.setUid(reqParam.getUid());
        queue.setTitle(pushVo.getTitle());
        queue.setCustomScheme(this.encodeCustomScheme(pushVo.getCustomScheme()));
        queue.setPushTaskId(reqParam.getPushTaskId());

        if (reqParam.getType().equals("priority")) {
            this.pushTask.addPriorityQueue(queue);
        } else {
            this.pushTask.addPushQueue(queue);
        }

        Response rtn = new Response();
        rtn.setContent("SUCCESS");

        return rtn;
    }

    public MessageBoxResponse getMessageBox(MessageBoxRequest mbRequest) throws ParseException {

        MessageBox param = new MessageBox();
        param.setPage(mbRequest.getPage());
        param.setPageRow(mbRequest.getPageRow());
        param.setUserId(mbRequest.getUserId());
        if (!ObjectUtils.isEmpty(mbRequest.getSearchDate())) {
            SimpleDateFormat transFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date to = transFormat.parse(mbRequest.getSearchDate());
            param.setCreated(to);
        }

        PageInfo pageInfo = this.getMessageBoxPageInfo(param);
        List<MessageBox> messageBoxList = this.pushMessageBoxMapper.selectPushMessageBox(param);

        List<MessageBoxResponse.MessageBox> contents = new ArrayList<>();
        messageBoxList.forEach(mb -> {
            if(mb.getAction().equals(PushActionCode.RobotDelivery.name())) mb.setType(PushMessageBoxType.ROBOT_DELIVERY);

            MessageBoxResponse.MessageBox messageBox = MessageBoxResponse.MessageBox.builder()
                    .idx(mb.getIdx())
                    .type(mb.getType())
                    .title(mb.getTitle())
                    .message(mb.getMessage())
                    .action(mb.getAction())
                    .param(mb.getParam())
                    .param2(mb.getParam2())
                    .created(mb.getCreated())
                    .customScheme(mb.getCustomScheme())
                    .build();
            contents.add(messageBox);
        });
        return MessageBoxResponse.builder().pageInfo(pageInfo).contents(contents).build();
    }

    public PageInfo getMessageBoxPageInfo(MessageBox param) {
        int totalCount = this.pushMessageBoxMapper.selectPushMessageBoxTotalCount(param);
        int page = param.getPage() > 1 ? (param.getPage() / param.getPageRow()) : param.getPage();
        return PageInfo.builder().page(page + 1).pageRow(param.getPageRow()).totalCount(totalCount).build();
    }

    public String encodeCustomScheme(String customScheme) throws UnsupportedEncodingException {
        if (ObjectUtils.isEmpty(customScheme)) {
            return "";
        }

        String[] cs = customScheme.split("\\?q=");
        if (cs.length == 1) {
            return cs[0];
        } else {
            return cs[0] + "?q=" + this.converterUtil.urlEncode(cs[1]);
        }
    }
}
