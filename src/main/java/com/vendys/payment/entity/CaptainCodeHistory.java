package com.vendys.payment.entity;

import java.time.LocalDateTime;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters.LocalDateTimeConverter;

import lombok.Data;

@Data
@Entity
public class CaptainCodeHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idx;
    private String captainCode;
    private String orderNo;
    private Integer amount;
    @Enumerated(EnumType.STRING)
    private Status status;
    private String transactionDate;
    private String storeId;
    private String shopCode;
    private String terminalCode;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime created;

    /**
     * USED: 사용
     * CANCELED: 사용취소,샤용 망취소 포함
     */
    public enum Status {
        USED, CANCELED
    }
}
