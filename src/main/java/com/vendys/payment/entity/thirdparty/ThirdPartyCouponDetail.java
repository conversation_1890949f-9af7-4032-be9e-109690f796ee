package com.vendys.payment.entity.thirdparty;

import java.time.LocalDateTime;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters.LocalDateTimeConverter;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

@Getter
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@Setter(value = AccessLevel.PACKAGE)
@Entity
public class ThirdPartyCouponDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idx;
    private Long thirdPartyCouponIdx;
    private String thirdPartyCouponNo;
    @Enumerated(EnumType.STRING)
    private Status status;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime createDate;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime updateDate;
    private String updateUserId;

    @ManyToOne
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "thirdPartyCouponIdx", referencedColumnName = "idx", insertable = false, updatable = false, nullable = false)
    private ThirdPartyCoupon thirdPartyCoupon;

    public void updateStatus(Status status, String updateUserId) {
        this.status = status;
        this.updateUserId = updateUserId;
        this.updateDate = LocalDateTime.now();
    }

    public void updateDetail(Status status, String couponNo) {
        this.thirdPartyCouponNo = couponNo;
        this.status = status;
        this.updateDate = LocalDateTime.now();
    }

    public void updateStatusByIdx(Long idx, Status status, String updateUserId) {
        this.idx = idx;
        this.status = status;
        this.updateUserId = updateUserId;
        this.updateDate = LocalDateTime.now();
    }
}
