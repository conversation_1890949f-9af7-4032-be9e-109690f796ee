package com.vendys.payment.entity;

import com.vendys.payment.controller.mealpolicy.policy.dto.ModifyMealPolicyRequest;
import com.vendys.payment.util.DateUtil;
import lombok.*;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.*;
import java.util.Date;

@Getter
@Setter
@Entity
public class CouponDemand {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "demandIdx")
    private Long demandIdx;

    @Enumerated(EnumType.STRING)
    @ColumnDefault("'APP'")
    @Column(name = "type", nullable = false, columnDefinition = "enum ('APP', 'POS')")
    private DemandType type;

    @Enumerated(EnumType.STRING)
    @Column(name = "acceptType", nullable = false, columnDefinition = "enum ('AUTO', 'MANUAL', 'CONFIRM')")
    private AcceptType acceptType;

    @Enumerated(EnumType.STRING)
    @ColumnDefault("'ALL'")
    @Column(name = "accepter", nullable = false, columnDefinition = "enum ('ALL', 'ADMIN', 'DIVISION')")
    private DemandAccepter accepter;

    @Column(name = "comId", length = 40)
    private String comId;

    @Column(name = "comName", length = 50)
    private String comName;

    @Column(name = "policyIdx", columnDefinition = "bigint")
    private Long policyIdx;

    @Column(name = "policyName", length = 45)
    private String policyName;

    @Column(name = "startTime", columnDefinition = "time")
    private Date startTime;

    @Column(name = "endTime", columnDefinition = "time")
    private Date endTime;

    @ColumnDefault("1")
    @Column(name = "isPointExpire", columnDefinition = "tinyint(1)")
    private Boolean isPointExpire;

    @Column(name = "regDate", columnDefinition = "datetime")
    private Date regDate;

    @Builder
    public CouponDemand(DemandType type, AcceptType acceptType, DemandAccepter accepter, String comId, String comName,
                        Long policyIdx, String policyName, Date startTime, Date endTime,
                        Boolean isPointExpire, Date regDate) {
        this.type = type;
        this.acceptType = acceptType;
        this.accepter = accepter;
        this.comId = comId;
        this.comName = comName;
        this.policyIdx = policyIdx;
        this.policyName = policyName;
        this.startTime = startTime;
        this.endTime = endTime;
        this.isPointExpire = isPointExpire;
        this.regDate = regDate;
    }

    public CouponDemand() {
        
    }

    /**
     * 신청 타입
     * APP - 앱
     * POS - 포스기 (식권 신청 포스기)
     */
    public enum DemandType {
        APP, POS
    }

    /**
     * 승인 방법
     * AUTO - 자동 승인
     * MANUAL - 수동 승인
     * CONFIRM - 결재선 승인
     */
    public enum AcceptType {
        AUTO, MANUAL, CONFIRM
    }

    /**
     * 신청식대 승인자
     * ALL - 모든 사용자
     * ADMIN - 특정 사용자
     * DIVISION - 특정 부서장
     */
    public enum DemandAccepter {
        ALL, ADMIN, DIVISION
    }

    public void update(ModifyMealPolicyRequest request){
        this.type = request.getDemandType() != null ? DemandType.valueOf(request.getDemandType()) : null;
        this.acceptType = request.getAcceptType() != null ? AcceptType.valueOf(request.getAcceptType()) : null;
        this.accepter = request.getAccepter() != null ? DemandAccepter.valueOf(request.getAccepter()) : null;
        this.startTime = DateUtil.localTimeToDate(request.getDemandStartTime());
        this.endTime = DateUtil.localTimeToDate(request.getDemandEndTime());
        this.isPointExpire = request.getIsPointExpire();
    }


}
