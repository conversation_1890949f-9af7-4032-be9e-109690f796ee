package com.vendys.payment.entity;

import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Version;

import lombok.Data;
import lombok.Getter;

@Data
@Entity
public class PaymentLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idx;
    private String  orderid; // 주문번호
    private String  originOrderId; //원번호
    private String  vender; // PG사
    private String  username; // 사용자명
    private String  uid; // 사용자 guid
    private String  email; // 이메일
    private String  prodid; // 상품 ID [mypoint,qpcon_코드번호]
    private String  prodname; // 상품명 충전 금액 1000포인트
    private Integer amount; // 충전 금액
    private String  paymethod; // 충전수단 null SC0010,SC0060,credit,virtual,CAPTAINPAY,ADMIN
    // paytype
    // 결제수단 SC0010:신용카드 ,SC0030:계좌이체, SC0040:무통장 입금, SC0060:소액결제, SC0070:유선전화, SC0090:OK캐쉬백, SC0111:문화상품권, SC0112: 게임문화상품권
    // CAPTAINPAY_CREDOT,CAPTAINPAY_ACCOUNT
    // CAPTAINPOINT
    private String  paytype;
    private Date    regdate; // 등록일시
    private Date    editdate; // 수정일시
    private String  dealid; // 외부연동 승인번호
    private String  financecode;
    private String  financename;
    private String  cashreceiptid; // 사용안함
    private String  resultcode; // 외부연동 응답코드
    // 10: 결제요청, 22:졀제정보작성실패 , 25:결제정보작성 , 28:주문번호 누락 , 32:결제실패, 39:가상계좌 할당 실패, 40:결제대기(무통장), 41:일부입금(무통장), 47:결제완료,
    // 50:결제취소,서비스롤백완료, 51:결제완료 및 서비스 미처리-결제취소필요, 52:입금취소(무통장),서비스롤백완료, 54:결제취소 및 서비스 롤백못함-서비스롤백필요,
    private String  resultmsg; // 외부연동 메시지
    // 10: 결제요청, 22:졀제정보작성실패 , 25:결제정보작성 , 28:주문번호 누락 , 32:결제실패, 39:가상계좌 할당 실패, 40:결제대기(무통장), 41:일부입금(무통장), 47:결제완료,
    // 50:결제취소,서비스롤백완료, 51:결제완료 및 서비스 미처리-결제취소필요, 52:입금취소(무통장),서비스롤백완료, 54:결제취소 및 서비스 롤백못함-서비스롤백필요,
    private Integer status; // 상태
    private Integer servicetype; // 1: 마이포인트 // 2: 모바일 매점 구매, 3: 커머스 구매, 0 불명
    private String  servicelink; // PointLog pid
    private String  virbankaccount; // 가상계좌
    private String  virbankowner; // 계좌 소유자
    private Integer depositamount; // 사용안함
    private Integer usepoint; // 사용안함
    private String  sendtype; // 사용안함
    private String  responseMessage; // 연동 전문
    private String  cardId;
    @Version
    private Date version;


    @Getter
    public enum Vender {
        INICIS_CAPTAINPAY, INICIS, LGD, MOBILIANS, NICE, SETTLE, VENDYS
    }

    @Getter
    public enum PaymentStatus {

        Request(10, "결제요청"),

        /**
         * 결제인증후
         */
        MakeUpFail(22, "졀제정보작성실패"),
        MakeUp(25, "결제정보작성"),
        /**
         * 신용카드 핸드폰의경우 세션 아웃
         */
        MakeUpSessionOut(28, "주문번호 누락"),


        ConfirmFail(32, "결제실패"),
        //======================== 사용자 노출단계 35
        /**
         * 가상계좌 등록
         */
        PendingFail(39, "가상계좌 할당 실패"),
        Pending(40, "결제대기(무통장)"),
        PenddingPart(41, "일부입금(무통장)"),

        /**
         * 결제완료후
         */
        Confirm(47, "결제완료"),

        /**
         * 결제 취소시
         */
        ConfirmRollback(50, "결제취소,서비스롤백완료"),
        // 결제완료 했으나 서비스유효처리 못함
        ConfirmNeedCancel(51, "결제완료 및 서비스 미처리-결제취소필요"),

        // 결제취소 했으나 서비스 롤백 못함
        ServiceNeedCancel(54, "결제취소 및 서비스 롤백못함-서비스롤백필요"),
        // === 무통장 결과
        ConfirmCancelDeposit(52, "입금취소(무통장),서비스롤백완료"),
        Refund(99, "환불완료"),
        ;

        public int status;
        public String name;

        PaymentStatus(int status, String name) {
            this.status = status;
            this.name = name;
        }
    }

    @Getter
    public enum PaymentServiceType {

        /**
         * 개인 포인트 충전
         */
        MyPoint(1, "개인 포인트 충전"),

        /**
         * 모바일 매점 구매
         */
        SnackPurchase(2, "모바일 매점 구매"),

        /**
         * 커머스 구매
         */
        Commerce(3, "커머스 구매"),
        InstantPay(4, "즉시 결제"),

        Unknown(0, "불명");

        private int type;
        private String name;

        public int getType() {
            return type;
        }

        public String getName() {
            return name;
        }

        PaymentServiceType(int type, String name) {
            this.type = type;
            this.name = name;
        }
    }

    @Getter
    public enum PayType {

        CAPTAINPAY_CREDIT("CAPTAINPAY_CREDIT", "간편결제(신용카드)"),
        CAPTAINPAY_ACCOUNT("CAPTAINPAY_ACCOUNT", "간편결제(가상계좌)"),
        NAVERPAY_CREDIT("NAVERPAY_CREDIT", "네이버페이(신용카드)"),
        NAVERPAY_ACCOUNT("NAVERPAY_ACCOUNT", "네이버페이(계좌이체)");

        private String code;
        private String name;

        PayType(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }

    @Getter
    public enum PayMethod {
        CAPTAINPAY, NAVERPAY, INSTANT_PAY
    }

}
