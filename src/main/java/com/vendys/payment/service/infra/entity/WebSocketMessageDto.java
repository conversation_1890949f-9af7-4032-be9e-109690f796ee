package com.vendys.payment.service.infra.entity;

import java.util.Date;
import java.util.List;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;

import com.vendys.payment.controller.captaincode.entity.CaptainCodeDto.CaptainCodeInfo;
import com.vendys.payment.controller.payment.entity.CouponBeatDto.BeatCouponInfo;
import com.vendys.payment.service.entity.AuthDto.Term;

public class WebSocketMessageDto {

    @Builder
    @Getter
    public static class PointEnrollBody {

        private Info info;
        private DailyPolicyPaymentInfo dailyPolicyPaymentInfo;

        public static PointEnrollBody of(Info info, String message) {
            return WebSocketMessageDto.PointEnrollBody
                    .builder()
                    .info(info)
                    .dailyPolicyPaymentInfo(DailyPolicyPaymentInfo.of(message))
                    .build();
        }

        @Builder
        @Getter
        public static class DailyPolicyPaymentInfo {
            private String message;

            public static DailyPolicyPaymentInfo of(String message) {
                return DailyPolicyPaymentInfo.builder()
                        .message(message)
                        .build();
            }
        }

    }


    @Data
    public static class BeatMessage {
        private Info info;
        private BeatCouponInfo beatCouponInfo;
        private User user;
        private List<Term> terms;
    }

    @Data
    public static class CaptainCodeMessage {
        private Info info;
        private CaptainCodeInfo captainCodeInfo;
        private User user;
        private List<Term> terms;
    }

    @Data
    public static class Info {
        private String  method;
        private Integer status;
        private String  message;
    }

    @Data
    public static class User {
        private String uid;
    }

    @Data
    public static class SupplyType {

        private String code;
        private String name;

        public SupplyType(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }

    @Data
    public static class GsWebSocketRequest {
        private GsCouponInfo gsCouponInfo;
    }

    @Data
    public static class GsCouponInfo {
        private String      resultCode;
        private String      resultMsg;
        private PaymentType paymentType;
        private Type        type;
        private String      storeId;
        private String      storeName;
        private List<SupplyType> supplyTypes;
        private String      gsCouponId;
        private String      couponId;
        private Boolean     succeed;
        private Long        requestAmount;
        private Long        approvalAmount;
        private Date approvalDate;

        @Getter
        public enum PaymentType {
            USE, CANCEL
        }

        @Getter
        public enum Type {
            USED,MULTI_USED
        }

        @Data
        public static class SupplyType {

            private String code;
            private String name;
        }
    }
}
