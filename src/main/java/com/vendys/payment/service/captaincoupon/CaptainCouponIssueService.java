package com.vendys.payment.service.captaincoupon;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import com.vendys.payment.constant.Errors;
import com.vendys.payment.constant.XPayment;
import com.vendys.payment.controller.captaincoupon.dto.CaptainCouponDto.CaptainCouponIssueRequestDto;
import com.vendys.payment.entity.Office;
import com.vendys.payment.entity.OfficeStoreRelation;
import com.vendys.payment.entity.Store.SupplyType;
import com.vendys.payment.entity.User;
import com.vendys.payment.entity.captaincoupon.CaptainCoupon;
import com.vendys.payment.entity.captaincoupon.CaptainCouponIssue;
import com.vendys.payment.entity.captaincoupon.CaptainCouponIssue.EstimateExpiryDateType;
import com.vendys.payment.entity.captaincoupon.CaptainCouponIssue.IssueMethod;
import com.vendys.payment.entity.captaincoupon.CaptainCouponIssue.ListingMethod;
import com.vendys.payment.entity.captaincoupon.CaptainCouponIssue.Status;
import com.vendys.payment.entity.captaincoupon.CaptainCouponIssue.SystemTriggerCondition;
import com.vendys.payment.entity.captaincoupon.CaptainCouponIssue.TargetViewMode;
import com.vendys.payment.entity.captaincoupon.CaptainCouponIssueTarget;
import com.vendys.payment.entity.captaincoupon.CaptainCouponIssueTarget.DataType;
import com.vendys.payment.exception.CommonException;
import com.vendys.payment.persist.captaincoupon.CaptainCouponIssuePersist;
import com.vendys.payment.service.MessageService;
import com.vendys.payment.service.OfficeService;
import com.vendys.payment.service.OfficeStoreService;
import com.vendys.payment.service.UserServiceV2;
import com.vendys.payment.service.entity.MessageDto.MessageAction;
import com.vendys.payment.service.entity.MessageDto.MessageType;
import com.vendys.payment.service.entity.MessageDto.UserPushRequest;

@Slf4j(topic = "http")
@Transactional
@Service
@RequiredArgsConstructor
public class CaptainCouponIssueService {

    private final CaptainCouponIssuePersist captainCouponIssuePersist;
    private final OfficeService officeService;
    private final UserServiceV2 userServiceV2;
    private final OfficeStoreService officeStoreService;
    private final MessageService messageService;

    // LAZY
    private CaptainCouponService captainCouponService;

    @Lazy
    @Autowired
    private void setCaptainCouponService(CaptainCouponService captainCouponService) {
        this.captainCouponService = captainCouponService;
    }

    public void postCaptainCouponIssue(
            CaptainCouponIssueRequestDto captainCouponIssueRequestDto, XPayment paymentHeader) {
        // 즉시 발행
        if (captainCouponIssueRequestDto.getIssueMethod() == IssueMethod.IMMEDIATELY) {
            // 즉시 발행의 경우 captainCouponIssueIdx 가 없으면 오류
            if (ObjectUtils.isEmpty(captainCouponIssueRequestDto.getCaptainCouponIssueIdx())
                    || captainCouponIssueRequestDto.getCaptainCouponIssueIdx() == 0) {
                throw new CommonException(Errors.CAPTAIN_COUPON_EMPTY);
            }

            CaptainCouponIssue captainCouponIssue =
                    this.findByCaptainCouponIdxAndInProgress(captainCouponIssueRequestDto.getCaptainCouponIssueIdx());
            if (captainCouponIssue.getTargetViewMode() == TargetViewMode.OFFICE_DENY
                    || captainCouponIssue.getTargetViewMode() == TargetViewMode.OFFICE_ALLOW
                    || captainCouponIssue.getTargetViewMode() == TargetViewMode.USER_ALLOW
                    || captainCouponIssue.getTargetViewMode() == TargetViewMode.USER_EXCEL_ALLOW
                    || captainCouponIssue.getTargetViewMode() == TargetViewMode.STORE_COMPLEX
                    || captainCouponIssue.getTargetViewMode() == TargetViewMode.STORE_ALLOW) {
                String createUser = captainCouponIssue.getCreateUser();
                List<CaptainCoupon> captainCoupons = this.makeCaptainCoupons(captainCouponIssue, createUser);
                captainCouponIssue.updateQuantityAndStatus(
                        ObjectUtils.isEmpty(captainCoupons) ? 0 : captainCoupons.size(), Status.DONE, createUser);
                this.save(captainCouponIssue);
                this.sendCaptainCouponPush(captainCoupons, captainCouponIssue.getName(), captainCouponIssue.getCreateUser());
            }
        } else if (captainCouponIssueRequestDto.getIssueMethod() == IssueMethod.SYSTEM_TRIGGER
                || captainCouponIssueRequestDto.getIssueMethod() == IssueMethod.USER_TRIGGER) {
            User user = this.userServiceV2.findByUserId(captainCouponIssueRequestDto.getUserId());
            if (!ObjectUtils.isEmpty(user) && user.getStatus() == User.Status.ACTIVE) {
                Office office = this.officeService.findOfficeByComId(user.getComid());
                if (captainCouponIssueRequestDto.getSystemTriggerCondition() == SystemTriggerCondition.JOIN) {
                    List<CaptainCouponIssue> captainCouponIssues =
                            this.findAllIssueMethodByTrigger(SystemTriggerCondition.JOIN);
                    for (CaptainCouponIssue captainCouponIssue : captainCouponIssues) {
                        this.captainCouponIssue(captainCouponIssue, user, office);
                    }
                } else if (captainCouponIssueRequestDto.getSystemTriggerCondition() == SystemTriggerCondition.BANNER) {
                    CaptainCouponIssue captainCouponIssues =
                            this.findByCaptainCouponIdxAndInProgress(captainCouponIssueRequestDto.getCaptainCouponIssueIdx());
                    this.captainCouponIssue(captainCouponIssues, user, office);
                }
            }
        }
    }

    /**
     * 쿠폰 지급 로직
     */
    private void captainCouponIssue(CaptainCouponIssue captainCouponIssue, User user, Office office) {
        CaptainCoupon captainCoupon = this.makeCaptainCouponsByTrigger(
                captainCouponIssue, user, office, captainCouponIssue.getCreateUser());
        if (!ObjectUtils.isEmpty(captainCoupon)) {
            captainCouponIssue.updateQuantityAndStatusByTrigger(1, captainCouponIssue.getCreateUser());
            this.save(captainCouponIssue);
            this.sendCaptainCouponPush(
                    Collections.singletonList(captainCoupon),
                    captainCouponIssue.getName(),
                    captainCouponIssue.getCreateUser());
        }
    }

    private void sendCaptainCouponPush(List<CaptainCoupon> captainCoupons, String couponName, String requesterId) {

        try {
            String content = "식권대장 쿠폰이 발급되었습니다.\n"
                    + "쿠폰명 : "
                    + couponName
                    + " (식권대장 내에서 결제 시 사용 가능합니다. 단, 쿠폰에 따라 제한사항이 있을 수 있습니다.)";

            List<String> targetUserIdList = new ArrayList<>();
            for (CaptainCoupon captainCoupon : captainCoupons) {
                targetUserIdList.add(captainCoupon.getUserId());
            }

            UserPushRequest userPushRequest = new UserPushRequest();
            userPushRequest.setTitle("식권대장");
            userPushRequest.setType(MessageType.priority.name());
            userPushRequest.setUid(targetUserIdList);
            userPushRequest.setRequestid(requesterId);
            userPushRequest.setContent(content);
            userPushRequest.setParam("");
            userPushRequest.setAction(MessageAction.CaptainCouponBox.name());
            userPushRequest.setCustomScheme(MessageAction.CaptainCouponBox.makeCustomScheme(null));
            this.messageService.userPush(userPushRequest);
        } catch (Exception exception) {
            log.error("Exception :{}", exception.getClass().getCanonicalName() + exception.getMessage(), exception);
        }
    }

    private void save(CaptainCouponIssue captainCouponIssue) {
        this.captainCouponIssuePersist.save(captainCouponIssue);
    }

    public List<CaptainCouponIssue> findAllIssueMethodByTrigger(SystemTriggerCondition systemTriggerCondition) {
        return this.captainCouponIssuePersist.findAllByIssueMethodInAndSystemTriggerConditionInAndStatus(
                Arrays.asList(IssueMethod.SYSTEM_TRIGGER, IssueMethod.USER_TRIGGER),
                Collections.singletonList(systemTriggerCondition),
                Status.IN_PROGRESS);
    }

    private CaptainCoupon makeCaptainCouponsByTrigger(
            CaptainCouponIssue captainCouponIssue, User user, Office office, String createUser) {
        List<CaptainCoupon> captainCoupons
                = this.captainCouponService.findByCaptainCouponIssueIdxAndUserId(captainCouponIssue.getIdx(), user.getUid());
        if (!ObjectUtils.isEmpty(captainCoupons)) {
            return null;
        }
        if (captainCouponIssue.getTargetListingMethod() == ListingMethod.BLACKLIST) {
            List<Long> targetOffices = this.getTargetsByBigintData(captainCouponIssue, DataType.BIGINT_OFFICE_IDX);
            if (!ObjectUtils.isEmpty(targetOffices)) {
                if (targetOffices.stream().noneMatch(target -> target.equals(office.getOfficeIdx()))) {
                    if (!office.getCompany().getCompanyLimit().getIsMyPoint()
                            || office.getCompany().getCompanyLimit().getIsTicketFormat()) {
                        return null;
                    }
                } else {
                    return null;
                }
            }
        } else {
            // WHITELIST
            if (captainCouponIssue.getTargetViewMode() == TargetViewMode.STORE_ALLOW) {
                List<String> targetStores = this.getTargetsByVarcharData(captainCouponIssue, DataType.VARCHAR_STORE_ID);
                List<OfficeStoreRelation> offices = this.officeStoreService.findAllOfficeByStoreIdsIn(targetStores);
                if (offices.stream().noneMatch(office1 -> office1.getOfficeIdx().equals(office.getOfficeIdx()))) {
                    return null;
                }
            } else if (captainCouponIssue.getTargetViewMode() == TargetViewMode.OFFICE_ALLOW
                    || captainCouponIssue.getTargetViewMode() == TargetViewMode.STORE_COMPLEX) {
                List<Long> targetOffices = this.getTargetsByBigintData(captainCouponIssue, DataType.BIGINT_OFFICE_IDX);
                List<Office> offices = this.officeService.findAllByOfficesIn(targetOffices);
                if (offices.stream().noneMatch(office1 -> office1.getOfficeIdx().equals(office.getOfficeIdx()))) {
                    return null;
                }
            } else if (captainCouponIssue.getTargetViewMode() == TargetViewMode.USER_ALLOW
                    || captainCouponIssue.getTargetViewMode() == TargetViewMode.USER_EXCEL_ALLOW) {
                List<String> targetAllowUsers = this.getTargetsByVarcharData(captainCouponIssue, DataType.VARCHAR_USER_ID);
                if (targetAllowUsers.stream().noneMatch(targetUser -> targetUser.equals(user.getUid()))) {
                    return null;
                }
            }
        }
        LocalDateTime startDate = captainCouponIssue.getStartDate();
        LocalDateTime endDate = captainCouponIssue.getEndDate();
        if (captainCouponIssue.getEstimateExpiryDate() == EstimateExpiryDateType.FIXED_DAY) {
            startDate = LocalDateTime.now();
            endDate = startDate.plusDays(captainCouponIssue.getExpiredPeriod());
        }

        CaptainCoupon captainCoupon = this.captainCouponService.createCaptainCouponEntity(
                user.getUid(),
                captainCouponIssue.getIdx(),
                captainCouponIssue.getAmount(),
                startDate,
                endDate,
                createUser);
        this.captainCouponService.saveCaptainCoupon(captainCoupon);
        return captainCoupon;
    }

    private List<CaptainCoupon> makeCaptainCoupons(
            CaptainCouponIssue captainCouponIssue,
            String createUser) {
        List<CaptainCoupon> captainCoupons = new ArrayList<>();

        List<User> targetUsers = null;
        if (captainCouponIssue.getTargetListingMethod() == ListingMethod.BLACKLIST) {
            List<Long> targetOffices = this.getTargetsByBigintData(captainCouponIssue, DataType.BIGINT_OFFICE_IDX);
            List<Office> offices;
            if (!ObjectUtils.isEmpty(targetOffices)) {
                offices = this.officeService.findAllByOfficesNotIn(targetOffices);
            } else {
                offices = this.officeService.findAllByOffice();
            }
            if (captainCouponIssue.getTargetViewMode() == TargetViewMode.STORE_COMPLEX) {
                List<Byte> supplyTypes =
                        this.officeStoreService.getSupplyTypeCodeByCaptainCoupon(
                                this.getTargetsByVarcharData(captainCouponIssue, DataType.ENUM_SUPPLY_TYPE));
                if (ObjectUtils.isEmpty(supplyTypes)) {
                    supplyTypes = this.officeStoreService.getCaptainCouponUsableSupplyTypes();
                }
                // 배송 제휴점 타입 = 전체 고객사
                if (supplyTypes.contains((byte) SupplyType.SHIPPING.getType())) {
                    offices = this.officeService.findAllByOffice();
                } else {
                    offices = this.officeStoreService.findAllBySupplyTypes(supplyTypes);
                }

            }
            targetUsers = this.userServiceV2.findAllByComIdsIn(offices);
        } else {
            // WHITELIST
            if (captainCouponIssue.getTargetViewMode() == TargetViewMode.STORE_ALLOW) {
                List<String> targetStores = this.getTargetsByVarcharData(captainCouponIssue, DataType.VARCHAR_STORE_ID);
                List<OfficeStoreRelation> officeStoreRelations = this.officeStoreService.findAllOfficeByStoreIdsIn(targetStores);
                List<Office> offices = officeStoreRelations.stream()
                        .map(OfficeStoreRelation::getOffice)
                        .collect(Collectors.toList());
                targetUsers = this.userServiceV2.findAllByComIdsIn(offices);
            } else if (captainCouponIssue.getTargetViewMode() == TargetViewMode.OFFICE_ALLOW
                    || captainCouponIssue.getTargetViewMode() == TargetViewMode.STORE_COMPLEX) {
                List<Long> targetOffices = this.getTargetsByBigintData(captainCouponIssue, DataType.BIGINT_OFFICE_IDX);
                List<Office> offices = this.officeService.findAllByOfficesIn(targetOffices);
                targetUsers = this.userServiceV2.findAllByComIdsIn(offices);
            } else if (captainCouponIssue.getTargetViewMode() == TargetViewMode.USER_ALLOW
                    || captainCouponIssue.getTargetViewMode() == TargetViewMode.USER_EXCEL_ALLOW) {
                List<String> targetAllowUsers = this.getTargetsByVarcharData(captainCouponIssue, DataType.VARCHAR_USER_ID);
                targetUsers = new ArrayList<>();
                for (String targetAllowUser : targetAllowUsers) {
                    User targetUser = this.userServiceV2.findByUserId(targetAllowUser);
                    if (targetUser.getStatus() == User.Status.ACTIVE && !targetUser.getIsDormant()) {
                        targetUsers.add(targetUser);
                    }
                }
            }
        }

        if (!ObjectUtils.isEmpty(targetUsers)) {
            for (User targetUser : targetUsers) {
                LocalDateTime startDate = captainCouponIssue.getStartDate();
                LocalDateTime endDate = captainCouponIssue.getEndDate();
                if (captainCouponIssue.getEstimateExpiryDate() == EstimateExpiryDateType.FIXED_DAY) {
                    startDate = LocalDateTime.now();
                    endDate = startDate.plusDays(captainCouponIssue.getExpiredPeriod());
                }
                CaptainCoupon captainCoupon = this.captainCouponService.createCaptainCouponEntity(
                        targetUser.getUid(),
                        captainCouponIssue.getIdx(),
                        captainCouponIssue.getAmount(),
                        startDate,
                        endDate,
                        createUser);
                captainCoupons.add(captainCoupon);
            }
        }
        this.captainCouponService.saveCaptainCoupons(captainCoupons);
        return captainCoupons;
    }

    private List<Long> getTargetsByBigintData(CaptainCouponIssue captainCouponIssue, DataType dataType) {
        return captainCouponIssue.getCaptainCouponIssueTargets()
                .stream()
                .filter(captainCouponIssueTarget ->
                        captainCouponIssueTarget.getDataType() == dataType)
                .map(CaptainCouponIssueTarget::getBigintData)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<String> getTargetsByVarcharData(CaptainCouponIssue captainCouponIssue, DataType dataType) {
        return captainCouponIssue.getCaptainCouponIssueTargets()
                .stream()
                .filter(captainCouponIssueTarget ->
                        captainCouponIssueTarget.getDataType() == dataType)
                .map(CaptainCouponIssueTarget::getVarcharData)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public CaptainCouponIssue findByCaptainCouponIdxAndInProgress(Long idx) {

        CaptainCouponIssue captainCouponIssue =
                this.captainCouponIssuePersist.findByCaptainCouponIdxAndStatuses(
                        idx, Collections.singletonList(Status.IN_PROGRESS));
        if (ObjectUtils.isEmpty(captainCouponIssue)) {
            throw new CommonException(Errors.CAPTAIN_COUPON_EMPTY);
        }
        return captainCouponIssue;
    }

    public void putCaptainCouponIssueDiscard(Long captainCouponIssueIdx, XPayment paymentHeader) {
        CaptainCouponIssue captainCouponIssue = this.putCaptainCouponDiscard(captainCouponIssueIdx, paymentHeader);
        captainCouponIssue.updateStatus(Status.DISCARD, paymentHeader.getUserId());
        this.save(captainCouponIssue);
    }

    public CaptainCouponIssue putCaptainCouponDiscard(Long captainCouponIssueIdx, XPayment paymentHeader) {
        CaptainCouponIssue captainCouponIssue =
                this.captainCouponIssuePersist.findByCaptainCouponIdxAndStatuses(
                        captainCouponIssueIdx, Arrays.asList(Status.DONE, Status.FORCE_STOP));
        if (ObjectUtils.isEmpty(captainCouponIssue)) {
            throw new CommonException(Errors.CAPTAIN_COUPON_EMPTY);
        }
        List<CaptainCoupon> captainCoupons
                = this.captainCouponService.findByCaptainCouponIssueIdx(captainCouponIssue.getIdx());
        for (CaptainCoupon captainCoupon : captainCoupons) {
            captainCoupon.updateStatus(CaptainCoupon.Status.DISCARDED, null, paymentHeader.getUserId());
        }
        this.captainCouponService.saveCaptainCoupons(captainCoupons);
        return captainCouponIssue;
    }
}
