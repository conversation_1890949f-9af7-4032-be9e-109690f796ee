package com.vendys.payment.service;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.RequiredArgsConstructor;

import com.vendys.payment.entity.MainDeepLink;
import com.vendys.payment.entity.MainDeepLink.TargetType;
import com.vendys.payment.persist.MainDeepLinkPersist;

@Transactional
@RequiredArgsConstructor
@Service
public class MainDeepLinkService {

    private final MainDeepLinkPersist mainDeepLinkPersist;

    public MainDeepLink findByTargetTypeAndTargetParam(TargetType targetType, String targetParam) {
        return this.mainDeepLinkPersist.findByTargetTypeAndTargetParam(targetType, targetParam);
    }
}
