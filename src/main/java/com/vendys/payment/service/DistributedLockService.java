package com.vendys.payment.service;

import com.vendys.payment.constant.Errors;
import com.vendys.payment.exception.CommonException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.integration.redis.util.RedisLockRegistry;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalTime;
import java.util.concurrent.*;
import java.util.concurrent.locks.Lock;
import java.util.function.Supplier;

/**
 * Redis 기반 분산 락을 제공하는 서비스
 * Redis 장애 시에도 애플리케이션이 계속 동작하도록 구현됨
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DistributedLockService {

    private final RedisLockRegistry redisLockRegistry;

    /**
     * 분산 락을 획득하고 작업을 실행한 후 결과를 반환
     * Redis 장애 시에도 락 없이 작업 실행
     *
     * @param lockKey 락 키
     * @param task 실행할 작업
     * @param <T> 반환 타입
     * @return 작업 실행 결과
     */
    public <T> T executeWithLock(String lockKey, Supplier<T> task) {
        Lock lock = null;
        boolean lockAcquired = false;
        
        try {
            // Redis 락 얻기 시도
            lock = redisLockRegistry.obtain(lockKey);
            
            // 10초 동안 락 획득 시도
            lockAcquired = lock.tryLock(10, TimeUnit.SECONDS);

            if (!lockAcquired) {
                log.warn("Failed to acquire lock for key: {}", lockKey);
                throw new CommonException(Errors.GENERAL_LOCK_ACQUISITION_FAILED);
            }
            
            // 락 획득 성공
            return task.get();
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new CommonException(Errors.GENERAL_LOCK_INTERRUPTED, e);
        } catch (RedisConnectionFailureException e) {
            // Redis 연결 실패 - 락 없이 실행
            log.warn("Redis connection failure, executing without distributed lock: {}", e.getMessage());
            return task.get();
        } catch (Exception e) {
            // 기타 Redis 관련 예외 - 락 없이 실행
            if (isRedisException(e)) {
                log.warn("Redis exception occurred, executing without distributed lock: {}", e.getMessage());
                return task.get();
            }
            // Redis 관련 예외가 아닌 경우 그대로 전파
            log.warn("Redis 예외 확인: {}", e.getMessage(), e);
            throw e;
        } finally {
            // 락이 획득된 경우에만 unlock 시도
            if (lock != null && lockAcquired) {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    // unlock 실패 시 경고만 남기고 애플리케이션은 계속 동작
                    log.warn("Failed to release lock for key: {}, error: {}", lockKey, e.getMessage());
                }
            }
        }
    }
    
    /**
     * 주어진 예외가 Redis 관련 예외인지 확인
     */
    private boolean isRedisException(Exception e) {
        // 예외나 원인 예외의 클래스 이름에 "Redis"가 포함되어 있는지 확인
        String exClassName = e.getClass().getName();
        
        if (exClassName.contains("Redis")) {
            return true;
        }
        
        // 원인 예외 체크
        Throwable cause = e.getCause();
        return cause != null && cause.getClass().getName().contains("Redis");
    }

    /**
     * 반환값이 없는 작업에 대해 분산 락을 획득하고 실행
     * Redis 장애 시에도 락 없이 작업 실행
     *
     * @param lockKey 락 키
     * @param task 실행할 작업
     */
    public void executeWithLock(String lockKey, Runnable task) {
        executeWithLock(lockKey, () -> {
            task.run();
            return null;
        });
    }
}
