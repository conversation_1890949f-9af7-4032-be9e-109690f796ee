package com.vendys.payment.service;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.vendys.payment.repository.master.UserCategoryMapper;
import com.vendys.payment.vo.UserCategoryVo;
import com.vendys.payment.vo.UserVo;

/**
 * Created by jungsu on 2016. 9. 26..
 */
@Service
@Transactional
public class UserCategoryService {

    @Autowired
    private UserCategoryMapper userCategoryMapper;

    public void getUserCategorySetDivision(UserVo.User user) {
        user.setDivision("");
        if (user.getDivisionIdx() != null) {
            UserCategoryVo.UserCategory leaderVo = new UserCategoryVo.UserCategory();
            leaderVo.setCateid(user.getDivisionIdx());
            UserCategoryVo.UserCategory leaderUserCategory = this.userCategoryMapper.selectDivision(leaderVo);
            if (leaderUserCategory != null) {
                user.setDivision(leaderUserCategory.getName());
            }
        }
    }

    public UserCategoryVo.UserCategoryUpResult getDevisionHistory(UserCategoryVo.UserCategory userCategory) {
        UserCategoryVo.UserCategoryUpResult resultVo = null;
        List<UserCategoryVo.UserCategoryUpResult> userCategoryList = this.userCategoryMapper.selectUpDivisionList(userCategory);
        String name = null;
        String name2 = null;
        String name3 = null;
        Integer cateId = 0;
        Integer cateId2 = 0;
        Integer cateId3 = 0;
        if (userCategoryList != null) {
            resultVo = new UserCategoryVo.UserCategoryUpResult();
            // 3depth
            if (userCategoryList.size() == 3) {
                if (!"".equals(userCategoryList.get(userCategoryList.size() - 1).getName())) {
                    name = userCategoryList.get(userCategoryList.size() - 1).getName();
                    cateId = userCategoryList.get(userCategoryList.size() - 1).getCateid();
                }
                if (!"".equals(userCategoryList.get(userCategoryList.size() - 1).getName2())) {
                    name2 = userCategoryList.get(userCategoryList.size() - 1).getName2();
                    cateId2 = userCategoryList.get(userCategoryList.size() - 1).getCateid2();
                }
                if (!"".equals(userCategoryList.get(userCategoryList.size() - 1).getName3())) {
                    name3 = userCategoryList.get(userCategoryList.size() - 1).getName3();
                    cateId3 = userCategoryList.get(userCategoryList.size() - 1).getCateid3();
                }
            } else if (userCategoryList.size() == 2) {
                // 2depth
                if (!"".equals(userCategoryList.get(userCategoryList.size() - 1).getName2())) {
                    name = userCategoryList.get(userCategoryList.size() - 1).getName2();
                    cateId = userCategoryList.get(userCategoryList.size() - 1).getCateid2();
                }
                if (!"".equals(userCategoryList.get(userCategoryList.size() - 1).getName3())) {
                    name2 = userCategoryList.get(userCategoryList.size() - 1).getName3();
                    cateId2 = userCategoryList.get(userCategoryList.size() - 1).getCateid3();
                }
            } else if (userCategoryList.size() == 1) {
                // 1depth
                if (!"".equals(userCategoryList.get(userCategoryList.size() - 1).getName3())) {
                    name = userCategoryList.get(userCategoryList.size() - 1).getName3();
                    cateId = userCategoryList.get(userCategoryList.size() - 1).getCateid3();
                }
            }
        }

        resultVo.setCateid(cateId);
        resultVo.setCateid2(cateId2);
        resultVo.setCateid3(cateId3);
        resultVo.setName(name);
        resultVo.setName2(name2);
        resultVo.setName3(name3);

        return resultVo;
    }

}
