package com.vendys.payment.service.allowance.recovery;

import com.vendys.payment.entity.CauseType;
import com.vendys.payment.entity.Company;
import com.vendys.payment.entity.CompanyPointTask;
import com.vendys.payment.entity.CompanyPointTaskUser;
import com.vendys.payment.entity.CouponMember;
import com.vendys.payment.entity.MealGroup;
import com.vendys.payment.entity.MealPointLog;
import com.vendys.payment.entity.MealPolicy;
import com.vendys.payment.persist.CouponMemberPersist;
import com.vendys.payment.persist.MealPointLogPersist;
import com.vendys.payment.persist.TaskPersist;
import com.vendys.payment.persist.UserPersist;
import com.vendys.payment.service.PointPlusService;
import com.vendys.payment.service.refund.TaskService;
import com.vendys.payment.util.DateUtil;
import com.vendys.payment.vo.UserVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class MissedMealPointProcessor {

    private final TaskService taskService;
    private final TaskPersist taskPersist;
    private final PointPlusService pointPlusService;
    private final CouponMemberPersist couponMemberPersist;
    private final MealPointLogPersist mealPointLogPersist;
    private final UserPersist userPersist;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public boolean processPoints(Long taskUserIdx) {
        try {
            CompanyPointTaskUser taskUser = this.taskPersist.findTaskUserById(taskUserIdx);

            if (taskUser == null) {
                log.error("Task user not found after creation: taskUserIdx={}", taskUserIdx);
                return false;
            }

            if (taskUser.getIsCheckBalance()) {
                // 당겨쓰기 식대정책 사용 체크
                CompanyPointTask companyPointTask = taskUser.getCompanyPointTask();
                Long advanceMealPolicyIdx = companyPointTask.getMealPolicy().getAdvanceMealPolicyIdx();
                if(ObjectUtils.isEmpty(advanceMealPolicyIdx)) {
                    // 일일 한도를 확인한 포인트 증가
                    Integer useAmountToday = this.couponMemberPersist.getUseAmountToday(
                            taskUser.getUserId(), taskUser.getCompanyPointTask().getGroupIdx());
                    this.pointPlusService.balancePlus(taskUserIdx, useAmountToday);
                } else {
                    // 당겨쓰기 사용하는 식대 정책일 경우 적용
                    final String comid = companyPointTask.getComId();
                    List<CauseType> causeTypes = Arrays.asList(CauseType.USE_MEALCOUPON, CauseType.REFUND);
                    LocalDateTime yesterdayStartTime = LocalDate.now().minusDays(1L).atStartOfDay();
                    LocalDateTime yesterdayEndTime = LocalDate.now().minusDays(1L).atTime(LocalTime.MAX);

                    /**
                     * 결제 및 환불 기록을 처음에 MealPointLog에서 조회하는 이유는 당겨쓰기 식대정첵에 대해서만 사용기록을 찾아야 하기 때문,
                     * 즉 policyIdx로 검색을 해야 하는데, CouponMember 및 CouponGroup에서는 policyIdx로는 조회할 수 없음
                     */
                    List<MealPointLog> mealPointLogs = this.mealPointLogPersist.findAllByComIdAndUserIdAndPolicyIdxAndCauseTypeInAndUseDateBetween(comid, taskUser.getUserId(), advanceMealPolicyIdx, causeTypes, DateUtil.localDateTimeToDate(yesterdayStartTime), DateUtil.localDateTimeToDate(yesterdayEndTime));
                    // 사용금액이 있을 경우 yesterdayUsedAdvanceMealPolicyAmount 값은 음수가 됨
                    // 이후 로직 계산에서 직관적으로 확인 위해 절대값 처리 하여 양수로 변경
                    // 지급 식대 - 전일 당겨쓰기 사용 식대 = 당겨쓰기 사용 후 지급 할 식대
                    int yesterdayUsedAdvanceMealPolicyAmount = Math.abs(mealPointLogs.stream()
                            .filter(it -> it.getCauseType() == CauseType.USE_MEALCOUPON)
                            .mapToInt(MealPointLog::getAmount).sum());

                    int refundAmount = 0;

                    List<String> refundCouponIds = mealPointLogs.stream()
                            .filter(it -> it.getCauseType() == CauseType.REFUND)
                            .map(MealPointLog::getCauseLink)
                            .collect(Collectors.toList());

                    /**
                     * 환불 금액을 따로 구하는 이유
                     *
                     * 만약 수요일 지급건에 대해서 당겨쓰기 사용금액을 계산해야 할 경우
                     * 일일 지급 식대가 1만원이라고 가정하고 당겨쓰기 정책도 1만원이라고 가정
                     * 월요일에 식대 1만원, 당겨쓰기 5천원을 사용했다고 가정 (15000원)
                     * 월요일 결제건을 화요일에 취소할 경우
                     * 월요일 당겨쓰기 사용 기록 -5000
                     * 화요일 당겨쓰기 환불 기록 +5000
                     * 수요일 지급시 10000만원이 지급되어야 하나 15000원이 지급될 수 있음(즉 화요일 당겨쓰기 환불 기록은 계산에서 제외 되어야함)
                     * why? MealPointLog에서 취소건에 대해서 usedate값이 잘못 기록되고 있음(결제 취소 당시의 시간으로 기록됨)
                     * CouponGroup 및 CouponMember table의 경우는 취소 거래 기록이라도 usedate값은 원 결제 기록의 usedate와 동일
                     * 따라서 MealPointLog에서 결제기록과 환불기록을 조회 한 후 환불기록들의 cid를 CouponMember table에서 조회하여 usedate가 어제날짜보다 이전인 기록들을 제외
                     */

                    if(!ObjectUtils.isEmpty(refundCouponIds)) {
                        List<CouponMember> couponMembers = couponMemberPersist.findAllByCidInAndUsedateGreaterThanEqual(refundCouponIds, yesterdayStartTime);
                        List<String> filteredYesterdayCouponIds = couponMembers.stream().map(CouponMember::getCid).collect(Collectors.toList());
                        List<MealPointLog> refundMealPointLogs = mealPointLogPersist.findAllByComIdAndUserIdAndPolicyIdxAndCauseLinkIn(comid, taskUser.getUserId(), advanceMealPolicyIdx, filteredYesterdayCouponIds);
                        refundAmount = refundMealPointLogs.stream().mapToInt(MealPointLog::getAmount).sum();
                    }

                    yesterdayUsedAdvanceMealPolicyAmount = yesterdayUsedAdvanceMealPolicyAmount - refundAmount;
                    if(ObjectUtils.isEmpty(yesterdayUsedAdvanceMealPolicyAmount) || yesterdayUsedAdvanceMealPolicyAmount == 0) {
                        // 만약 어제자 당겨쓰기 사용 금액이 0원일 경우
                        this.pointPlusService.plus(taskUser);
                    } else {
                        this.pointPlusService.plusAmountAfterAdvanceDeduction(taskUser, yesterdayUsedAdvanceMealPolicyAmount);
                    }
                }

            } else {
                // 일반 포인트 증가
                this.pointPlusService.plus(taskUser);
            }

            return true;
        } catch (Exception e) {
            log.error("Error during point transaction: taskUserIdx={}, error={}",
                    taskUserIdx, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 태스크를 생성하고 DB에 저장 (별도 트랜잭션으로 실행)
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Long createAndPersistTaskWithTransaction(UserVo.User user, Company company, MealGroup mealGroup,
                                                    MealPolicy mealPolicy, Map<Long, String> failureReasons) {
        return createAndPersistTask(user, company, mealGroup, mealPolicy, failureReasons);
    }


    /**
     * 태스크를 생성하고 DB에 저장
     */
    private Long createAndPersistTask(UserVo.User user, Company company, MealGroup mealGroup,
                                      MealPolicy mealPolicy, Map<Long, String> failureReasons) {
        // 태스크 생성
        CompanyPointTask task = this.taskService.createTask(mealPolicy, company);
        task.setCause("사용자 요청에 의한 미지급 식대 지급");
        task.setCauseType(CompanyPointTask.CauseType.POLICY_ACTIONS);
        task.setOrderUserId("payment-api-direct");
        task.setStatus(CompanyPointTask.Status.EXECUTE);
        task.setExecuteDate(new Date());

        // 관계 엔티티 명시적 설정
        task.setMealPolicy(mealPolicy);
        task.setMealGroup(mealGroup);
        this.taskPersist.setTask(task);

        // 태스크 사용자 생성
        CompanyPointTaskUser taskUser = this.taskService.createTaskUser(task, user, mealPolicy);

        // 식대 일일 한도 설정
        taskUser.setIsCheckBalance(this.isCheckBalance(mealGroup, mealPolicy));

        // 관계 엔티티 명시적 설정
        taskUser.setUser(this.userPersist.readUser(user.getUid()));
        taskUser.setCompanyPointTask(task);
        this.taskService.setTaskUser(taskUser);

        // 일일 한도 기반 금액 계산
        // 당겨쓰기의 경우도 isCheckBalance가 true이므로 일일 한도기반 금액 계산은 getOneDayLimitAmount != null일 경우만 계산
        if (taskUser.getIsCheckBalance() && mealGroup.getOneDayLimitAmount() != null) {
            Integer useAmountToday = this.couponMemberPersist.getUseAmountToday(user.getUid(), task.getGroupIdx());
            taskUser.calculateBalanceAmount(task, useAmountToday);
        } else {
            taskUser.setAmount(mealPolicy.getAmount());
        }

        // 계산된 금액이 0 이하인 경우 처리 중단
        if (taskUser.getAmount() <= 0) {
            log.info("Calculated amount is zero or less for policy {} for user {}",
                    mealPolicy.getPolicyIdx(), user.getUid());
            failureReasons.put(mealPolicy.getPolicyIdx(), "사용 가능 한 식대를 모두 지급 받았습니다.[일일 지급한도 초과]");
            return null;
        }

        // 최종 저장
        this.taskService.setTaskUser(taskUser);

        // 태스크 설정 업데이트
        task.setUserCount(1);
        task.setTotalAmount(taskUser.getAmount().longValue());
        this.taskService.update(task);

        log.info("Created task for allowance - policy: {}, user: {}, amount: {}, taskUserIdx: {}",
                mealPolicy.getPolicyIdx(), user.getUid(), taskUser.getAmount(), taskUser.getTaskUserIdx());

        return taskUser.getTaskUserIdx();
    }

    /**
     * 태스크 상태 업데이트 (별도 트랜잭션으로 실행)
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateTaskStatusWithTransaction(Long taskUserIdx, boolean success) {
        this.updateTaskStatus(taskUserIdx, success);
    }

    /**
     * 태스크 상태 업데이트
     */
    private void updateTaskStatus(Long taskUserIdx, boolean success) {
        try {
            CompanyPointTaskUser taskUser = this.taskPersist.findTaskUserById(taskUserIdx);
            if (taskUser == null) {
                log.error("Task user not found during status update: taskUserIdx={}", taskUserIdx);
                return;
            }

            CompanyPointTask task = taskUser.getCompanyPointTask();

            // 태스크 상태를 완료로 업데이트
            task.updateStatusEnd(success ? 1 : 0, success ? 0 : 1);
            this.taskService.update(task);

            log.info("Updated task status - taskUserIdx: {}, success: {}", taskUserIdx, success);
        } catch (Exception e) {
            log.error("Error updating task status: taskUserIdx={}, error={}",
                    taskUserIdx, e.getMessage(), e);
        }
    }

    public Boolean isCheckBalance(MealGroup mealGroup, MealPolicy mealPolicy){
        try {
            if(mealGroup.getOneDayLimitAmount() != null && mealGroup.getOneDayLimitAmount() > 0){
                return true;
            }

            return !ObjectUtils.isEmpty(mealPolicy.getAdvanceMealPolicyIdx());
        } catch (Exception e){
            log.warn("일일 제한 식대지급에 실패 했습니다.\nmealGroup Id: {}\n\n{}: {}", mealGroup.getGroupIdx(), e.getClass().getSimpleName(), e.getMessage(), e);
            return false;
        }
    }
}
