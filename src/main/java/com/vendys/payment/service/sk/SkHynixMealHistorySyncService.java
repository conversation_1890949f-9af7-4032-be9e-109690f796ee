package com.vendys.payment.service.sk;

import com.vendys.payment.constant.InitProperties;
import com.vendys.payment.constant.XPayment;
import com.vendys.payment.controller.manage.entity.ManageDto;
import com.vendys.payment.controller.sk.dto.SkHynixCafeteriaUsedHistory;
import com.vendys.payment.controller.sk.dto.SkHynixCafeteriaUsedHistoryRequest;
import com.vendys.payment.controller.sk.dto.SkHynixCouponUsedHistory;
import com.vendys.payment.controller.sk.dto.SkHynixCouponUsedHistoryRequest;
import com.vendys.payment.entity.CauseType;
import com.vendys.payment.entity.MealAccount;
import com.vendys.payment.entity.MealGroup;
import com.vendys.payment.entity.MealPolicy;
import com.vendys.payment.entity.User;
import com.vendys.payment.entity.sk.SkHynixCafeteriaUsedHistoryEntity;
import com.vendys.payment.persist.MealAccountPersist;
import com.vendys.payment.persist.MealGroupPersist;
import com.vendys.payment.persist.MealPointLogPersist;
import com.vendys.payment.persist.MealPolicyPersist;
import com.vendys.payment.persist.UserPersist;
import com.vendys.payment.persist.sk.SkHynixCafeteriaUsedHistoryPersist;
import com.vendys.payment.service.refund.ManageService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
public class SkHynixMealHistorySyncService {

    private final SkHynixCafeteriaUsedHistoryPersist skHynixCafeteriaUsedHistoryPersist;
    private final MealPolicyPersist mealPolicyPersist;
    private final MealGroupPersist mealGroupPersist;

    private final InitProperties initProperties;
    private final MealAccountPersist mealAccountPersist;
    private final UserPersist userPersist;

    private final ManageService manageService;
    private final MealPointLogPersist mealPointLogPersist;


    public void saveSkHynixCafeteriaUsedHistory(SkHynixCafeteriaUsedHistoryRequest usedHistoryRequest) {
        List<SkHynixCafeteriaUsedHistory> histories = usedHistoryRequest.getMeal_list();

        if (histories != null && !histories.isEmpty()) {
            skHynixCafeteriaUsedHistoryPersist.saveAll(histories.stream()
                    .map(SkHynixCafeteriaUsedHistory::toEntity)
                    .collect(Collectors.toList())
            );
        }
        this.checkAndWithdrawDuplicateMealDemand();
    }

    public void checkAndWithdrawDuplicateMealDemand() {
        final String hynixComid = initProperties.getHynixComid();
        List<MealAccount> hynixMealAccounts = mealAccountPersist.findAllByComIdAndPolicyTypeAndIsActive(hynixComid, MealPolicy.Type.DEMAND, true);

        if (hynixMealAccounts != null && !hynixMealAccounts.isEmpty()) {
            for (MealAccount mealAccount : hynixMealAccounts) {
                // 회수 할 금액이 0이하일 경우 회수 불가하여 0보다 클 경우만 회수
                if (mealAccount.getAmount() > 0) {
                    Long policyIdx = mealAccount.getPolicyIdx();
                    MealPolicy mealPolicy = mealPolicyPersist.findByOne(policyIdx);

                    if (mealPolicy != null) {
                        User user = userPersist.find(mealAccount.getUserId());
                        final LocalDate date = mealAccount.getRegDate().toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate();

                        // 식수 중복 수급 여부 확인 및 식대 회수
                        this.findDuplicateMealDemand(date, user, mealPolicy)
                                .ifPresent(skHynixCafeteriaUsedHistoryEntity -> this.withdrawMealCoupon(user, policyIdx, mealAccount));
                    }
                }
            }
        }
    }

    private Optional<SkHynixCafeteriaUsedHistoryEntity> findDuplicateMealDemand(LocalDate date, User user, MealPolicy mealPolicy) {
        return skHynixCafeteriaUsedHistoryPersist.findByDateAndCumidnumAndMealPolicy(date, user.getComidnum(), mealPolicy.getName());
    }

    private void withdrawMealCoupon(User user, Long policyIdx, MealAccount mealAccount) {
        ManageDto.RequestPoint requestBody = new ManageDto.RequestPoint();
        requestBody.setType(ManageDto.RequestPoint.Type.ADMIN);
        requestBody.setUid(user.getUid());
        requestBody.setPolicyIdx(policyIdx);
        requestBody.setAmount(mealAccount.getAmount() * -1);
        requestBody.setCause("식대 중복 지급에 의한 소멸처리");

        XPayment paymentHeader = new XPayment();
        paymentHeader.setUserId("system");
        manageService.managePoint(requestBody, paymentHeader);
    }

    public List<SkHynixCouponUsedHistory> getSkHinixMealCouponUsedHistory(SkHynixCouponUsedHistoryRequest usedHistoryRequest) {
        final String hynixComid = initProperties.getHynixComid();
        final List<Long> policyIdxs = this.findActivePolicyIdxListByComId(hynixComid);
        if(!policyIdxs.isEmpty()) {
            return mealPointLogPersist
                    .findAllSkHynixCouponUsedHistoryByComIdAndPolicyIdxInUseDateLteAndUseDateGteAndCauseType(
                            hynixComid
                            , policyIdxs
                            , usedHistoryRequest.getStartDateTime()
                            , usedHistoryRequest.getEndDateTime()
                            , CauseType.USE_MEALCOUPON);
        } else {
            return Collections.emptyList();
        }

    }

    private List<Long> findActivePolicyIdxListByComId(String comid) {
        List<MealGroup> mealGroups = mealGroupPersist.findAllByComIdAndIsActive(comid, true);
        if(mealGroups != null && !mealGroups.isEmpty()) {
            List<Long> groupIdxs = mealGroups.stream().map(MealGroup::getGroupIdx).collect(Collectors.toList());
            List<MealPolicy> policies = mealPolicyPersist.findAllByGroupIdxInAndTypeAndIsActive(groupIdxs, MealPolicy.Type.DEMAND, true);
            return policies.stream().map(MealPolicy::getPolicyIdx).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }
    
    public int clearOldCafeteriaUsedHistory(int batchSize) {
        LocalDate cutoffDate = LocalDate.now().minusDays(30);
        return skHynixCafeteriaUsedHistoryPersist.deleteRecordsOlderThan(cutoffDate, batchSize);
    }
}
