package com.vendys.payment.interceptor;

import com.vendys.payment.constant.InitProperties;
import com.vendys.payment.entity.ExternalApiInOutLog;
import com.vendys.payment.persist.ExternalApiInOutLogPersist;
import com.vendys.payment.service.infra.SlackMessageSenderService;
import com.vendys.payment.service.infra.entity.SlackDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
@RequiredArgsConstructor
public class BGFSyncStoreInterceptor implements HandlerInterceptor {

    private final ExternalApiInOutLogPersist externalApiInOutLogPersist;

    private final SlackMessageSenderService slackMessageSenderService;

    private final InitProperties initProperties;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object o, ModelAndView modelAndView) {
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object o, Exception e) throws Exception {
        ExternalApiInOutLog log = ExternalApiInOutLog.of(request, response, e, "PAYMENT");
        log = externalApiInOutLogPersist.save(log);

        final String syncResultMessage = this.generateCuStoreSyncResultMessage(request, log.getIdx());

        SlackDto.SlackBody syncResultMessageBody = new SlackDto.SlackBody();
        syncResultMessageBody.getAttachments()
                .add(new SlackDto.SlackBody.Attachments("BGF 점포 갱신 처리 결과", "", syncResultMessage, "PAYMENT"));

        this.sendSlackMessage(syncResultMessageBody);

        if(request.getRequestURI().equals("/bgf_mt/v3/sync_store.json") && (request.getAttribute("error") != null || request.getAttribute("warn") != null)) {
            SlackDto.SlackBody body = new SlackDto.SlackBody();
            String pretext = "[BGF] 점포갱신 실패 알림";
            String title  = "BGF 점포 갱신 API 처리 과정중 오류가 발생했습니다.";
            String bodyText = String.format("Log Idx : %d", log.getIdx());
            body.getAttachments().add(new SlackDto.SlackBody.Attachments(pretext, title, bodyText, "PAYMENT"));

            this.sendSlackMessage(body);
        }
    }

    private String generateCuStoreSyncResultMessage(HttpServletRequest request, Long logIdx) {
        StringBuilder builder = new StringBuilder();
        builder.append("BGF 점포 갱신 처리 결과")
                .append("\r\n========================")
                .append(String.format("\r\nLog Idx : %d", logIdx))
                .append(String.format("\r\n갱신 시간 : %s", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));

        if (request.getAttribute("newlySavedStoreIds") != null) {
            String newlySavedStoreIds = (String) request.getAttribute("newlySavedStoreIds");
            builder.append("\r\n------------------------")
                    .append(String.format("\r\n\r\n*신규등록 제휴점 수* : %d", newlySavedStoreIds.split("\r\n").length))
                    .append("\r\n제휴점 ID 목록:")
                    .append(String.format("\r\n%s", newlySavedStoreIds));
        }

        if (request.getAttribute("updatedStoreIds") != null) {
            String updatedStoreIds = (String) request.getAttribute("updatedStoreIds");
            builder.append("\r\n------------------------")
                    .append(String.format("\r\n\r\n*정보수정 제휴점 수* : %d", updatedStoreIds.split("\r\n").length))
                    .append("\r\n제휴점 ID 목록:")
                    .append(String.format("\r\n%s", updatedStoreIds));
        }

        if (request.getAttribute("lackOfInfoStoreIds") != null) {
            String lackOfInfoStoreIds = (String) request.getAttribute("lackOfInfoStoreIds");
            builder.append("\r\n------------------------")
                    .append(String.format("\r\n\r\n*정보부족 제휴점 수* : %d", lackOfInfoStoreIds.split("\r\n").length))
                    .append("\r\n제휴점 ID 목록:")
                    .append(String.format("\r\n%s", lackOfInfoStoreIds));
        }

        if (request.getAttribute("inActiveStoreIds") != null) {
            String inActiveStoreIds = (String) request.getAttribute("inActiveStoreIds");
            builder.append("\r\n------------------------")
                    .append(String.format("\r\n\r\n*비활성화 처리 제휴점 수* : %d", inActiveStoreIds.split("\r\n").length))
                    .append("\r\n제휴점 ID 목록:")
                    .append(String.format("\r\n%s", inActiveStoreIds));
        }

        return builder.toString();
    }

    private void sendSlackMessage(SlackDto.SlackBody message) {
        if ("production".equalsIgnoreCase(this.initProperties.getVendysEnv())) {
            this.slackMessageSenderService.sendSlackMessage(message);
        } else {
            this.slackMessageSenderService.sendDebugMessage(message);
        }
    }
}
