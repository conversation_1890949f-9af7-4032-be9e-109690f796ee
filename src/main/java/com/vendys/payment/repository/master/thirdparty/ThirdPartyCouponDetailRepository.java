package com.vendys.payment.repository.master.thirdparty;

import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.vendys.payment.entity.thirdparty.Status;
import com.vendys.payment.entity.thirdparty.ThirdPartyCouponDetail;

public interface ThirdPartyCouponDetailRepository extends JpaRepository<ThirdPartyCouponDetail, Long> {
    List<ThirdPartyCouponDetail> findAllByThirdPartyCouponIdx(Long thirdPartyCouponIdx);

    ThirdPartyCouponDetail findAllByIdx(Long thirdPartyCouponDetailIdx);

    @Query(value = "SELECT A "
            + "FROM ThirdPartyCouponDetail A "
            + "INNER JOIN A.thirdPartyCoupon B "
            + "WHERE A.status = :status "
            + "AND B.endDate < :nowDate")
    List<ThirdPartyCouponDetail> findAllByThirdPartyCouponDetailByStatus(
            @Param("status") Status status, @Param("nowDate") LocalDateTime nowDate);

    @Query(value = "SELECT A "
            + "FROM ThirdPartyCouponDetail A "
            + "INNER JOIN A.thirdPartyCoupon B "
            + "INNER JOIN B.menu C "
            + "INNER JOIN B.couponGroup D "
            + "WHERE D.leaderid = :userId "
            + "AND A.status IN :statusList "
            + "AND D.status >= 0 "
            + "ORDER BY B.idx DESC")
    List<ThirdPartyCouponDetail> findAllByThirdPartyCouponInfoList(
            @Param("userId") String userId, @Param("statusList") List<Status> statusList, Pageable pageable);
}
