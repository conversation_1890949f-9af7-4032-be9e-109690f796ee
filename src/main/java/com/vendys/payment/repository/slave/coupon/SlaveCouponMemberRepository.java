package com.vendys.payment.repository.slave.coupon;

import com.vendys.payment.entity.CouponMember;
import com.vendys.payment.entity.CouponMemberKey;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by jin<PERSON><PERSON> on 2018. 7. 24.
 */
public interface SlaveCouponMemberRepository extends JpaRepository<CouponMember, CouponMemberKey> {
    @Query(value =
            "select CM.* "
            + "from CouponMember CM "
            + "left join CouponIndex CI on CM.cid = CI.cid "
            + "left join MealPointLog MPL on CI.pointLogIdx = MPL.pointLogIdx "
            + "where CM.uid = :uid "
            + "and CM.usedate >= :start "
            + "and CM.usedate < :end "
            + "and MPL.groupIdx = :groupIdx "
            + "and MPL.type = 'DAY'",
            nativeQuery = true)
    List<CouponMember> findByUidAndGroupIdxOnToday(@Param("uid") String uid, @Param("start") Date start,
                                                   @Param("end") Date end, @Param("groupIdx") Long groupIdx);

    List<CouponMember> findAllByCidInAndUsedateGreaterThanEqual(List<String> couponIds, Date date);
}
