package com.vendys.payment.controller.mealpolicy.group;

import com.vendys.payment.constant.XPayment;
import com.vendys.payment.controller.CommonController;
import com.vendys.payment.controller.mealpolicy.group.dto.CreateMealGroupRequest;
import com.vendys.payment.controller.mealpolicy.group.dto.CreateMealGroupResponse;
import com.vendys.payment.controller.mealpolicy.group.dto.ModifyMealGroupRequest;
import com.vendys.payment.entity.MealGroup;
import com.vendys.payment.service.mealpolicy.group.MealGroupService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j(topic = "http")
@RequestMapping("/meal-group")
@RestController
@RequiredArgsConstructor
public class MealGroupController extends CommonController {

    private final MealGroupService mealGroupService;


    @PostMapping("{companyId}/v1")
    public ResponseEntity<CreateMealGroupResponse> addMealGroup(@RequestAttribute("xpayment") XPayment paymentHeader,
                                                                @PathVariable String companyId,
                                                                @RequestBody CreateMealGroupRequest request){
        MealGroup mealGroup = mealGroupService.addMealGroup(this.readAdmin(paymentHeader.getUserId()), companyId, request);

        return ResponseEntity.ok(new CreateMealGroupResponse(mealGroup.getGroupIdx()));
    }

    @PutMapping("{companyId}/v1/{groupId}")
    public ResponseEntity<Void> modifyMealGroup(@RequestAttribute("xpayment") XPayment paymentHeader,
                                                                @PathVariable String companyId,
                                                                @PathVariable Long groupId,
                                                                @RequestBody ModifyMealGroupRequest request){

        mealGroupService.modifyMealGroup(this.readAdmin(paymentHeader.getUserId()), companyId, groupId, request);

        return new ResponseEntity<>(HttpStatus.OK);
    }
}
