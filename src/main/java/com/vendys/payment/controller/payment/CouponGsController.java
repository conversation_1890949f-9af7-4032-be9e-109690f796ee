package com.vendys.payment.controller.payment;

import javax.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

import com.vendys.payment.constant.XPayment;
import com.vendys.payment.controller.CommonController;
import com.vendys.payment.controller.payment.entity.CouponGsDto;
import com.vendys.payment.controller.payment.entity.CouponGsDto.CouponGsInfo;
import com.vendys.payment.controller.payment.entity.CouponGsDto.GsCouponRequest;
import com.vendys.payment.controller.payment.entity.CouponGsDto.GsCouponResponse;
import com.vendys.payment.controller.payment.entity.CouponGsDto.HistoryRequest;
import com.vendys.payment.controller.payment.entity.CouponGsDto.UseCancelRequest;
import com.vendys.payment.controller.payment.entity.CouponGsDto.UseCancelResponse;
import com.vendys.payment.controller.payment.entity.CouponGsDto.UseResponse;
import com.vendys.payment.entity.PayRoom.Status;
import com.vendys.payment.exception.BindingException;
import com.vendys.payment.service.infra.entity.WebSocketMessageDto;
import com.vendys.payment.service.payment.CouponGsService;
import com.vendys.payment.service.payment.PayRoomService;

/**
 * Created by sungha on 2019. 12. 10..
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/payment")
public class CouponGsController extends CommonController {

    private final CouponGsService couponGsService;
    private final PayRoomService payRoomService;

    /**
     * GS 식권 발급.
     */
    @PostMapping(value = "/v1/gs/coupon")
    public ResponseEntity<GsCouponResponse> createGsCoupon(
            @ModelAttribute("xPayment") XPayment paymentHeader,
            @RequestBody @Valid GsCouponRequest gsCouponRequest,
            BindingResult result) {
        if (result.hasErrors()) {
            throw new BindingException(result.getFieldError());
        }

        return new ResponseEntity<>(this.couponGsService.createGsCoupon(gsCouponRequest), HttpStatus.CREATED);
    }

    /**
     * GS 쿠폰 조회.
     */
    @GetMapping(value = "/v1/gs/coupon")
    public ResponseEntity<CouponGsInfo> getGsCoupon(
            @RequestParam(name = "gs_coupon_id") String urlEncodedGsCouponId,
            @RequestParam(name = "shop_code") String shopCode) {
        return new ResponseEntity<>(this.couponGsService.getCouponGsInfo(urlEncodedGsCouponId, shopCode), HttpStatus.OK);
    }

    /**
     * GS 쿠폰 사용.
     */
    @PutMapping(value = "/v1/gs/coupon")
    public ResponseEntity<UseResponse> putGsCoupon(
            @RequestParam(name = "gs_coupon_id") String urlEncodedGsCouponId,
            @Valid @RequestBody CouponGsDto.UseRequest useRequest,
            BindingResult result) {
        if (result.hasErrors()) {
            throw new BindingException(result.getFieldError());
        }

        return new ResponseEntity<>(this.couponGsService.useCouponGs(urlEncodedGsCouponId, useRequest), HttpStatus.OK);
    }

    /**
     * gatewaytcp 에서 호출
     * GS 쿠폰사용(바코드 결제) 후 웹소켓 발송(결제 완료 처리 화면)
     * Host, Guest 서로 다른 웹소켓 발송
     */
    @PostMapping(value = "/v1/gs/websocket/{payRoomIdx}")
    public ResponseEntity<Void> sendSocketToPayRoomMembers(
            @ModelAttribute("xPayment") XPayment header, @PathVariable(value = "payRoomIdx") Long payRoomIdx,
            @RequestBody WebSocketMessageDto.GsWebSocketRequest requestBody, BindingResult result) {

        if (result.hasErrors()) {
            throw new BindingException(result.getFieldError());
        }

        this.payRoomService.sendWebSocketToHostAndGuests(payRoomIdx, Status.USED, requestBody);

        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * 쿠폰 사용취소(환불)
     */
    @DeleteMapping(value = "/v1/gs/coupon")
    public ResponseEntity<UseCancelResponse> useCancel(
            @ModelAttribute("xPayment") XPayment header,
            @Valid @ModelAttribute UseCancelRequest param,
            BindingResult result) {
        if (result.hasErrors()) {
            throw new BindingException(result.getFieldError());
        }

        return new ResponseEntity<>(this.couponGsService.cancel(header, param), HttpStatus.OK);
    }

    /**
     * GS 일대사 조회
     */
    @GetMapping(value = "/v1/gs/coupon/history")
    public ResponseEntity<CouponGsDto.HistoryResponse> getGsCouponHistory(
            @ModelAttribute("xPayment") XPayment header,
            @Valid @ModelAttribute HistoryRequest param,
            BindingResult result) {
        if (result.hasErrors()) {
            throw new BindingException(result.getFieldError());
        }
        CouponGsDto.HistoryResponse response = this.couponGsService.getGsCouponHistory(param);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    /**
     * GS 쿠폰 조회.
     */
    @GetMapping(value = "/v1/gs/coupon/info")
    public ResponseEntity<CouponGsDto.GsCouponInfo> getGsCouponInfo(
            @RequestParam(name = "gs_coupon_id") String urlEncodedGsCouponId) {
        CouponGsDto.GsCouponInfo response = this.couponGsService.getGsCouponInfo(urlEncodedGsCouponId);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    /**
     * GS 쿠폰(바코드) 존재여부 조회.
     */
    @GetMapping(value = "/v1/gs/barcode")
    public ResponseEntity<CouponGsDto.GsBarCodeInfo> getGsBarCode(
            @RequestParam(name = "roomIdx") Long roomIdx) {
        CouponGsDto.GsBarCodeInfo response = this.couponGsService.readGsBarCode(roomIdx);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    /**
     * GS 쿠폰 취소 처리
     */
    @PutMapping(value = "/v1/gs/coupon/cancel/{payRoomIdx}")
    public ResponseEntity<Void> cancelGsCoupon(@PathVariable Long payRoomIdx) {
        this.couponGsService.cancelGsCoupon(payRoomIdx);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
