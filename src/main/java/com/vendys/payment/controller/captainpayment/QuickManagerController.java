package com.vendys.payment.controller.captainpayment;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import com.vendys.payment.annotation.VendysIamAuth;
import com.vendys.payment.constant.VendysIam;
import com.vendys.payment.service.captainpayment.QuickService;

@Slf4j
@RequestMapping("/captain-payment/quick/v1")
@RestController
@RequiredArgsConstructor
public class QuickManagerController {

    private final QuickService quickService;

    @VendysIamAuth
    @PostMapping("/booking")
    public ResponseEntity<Void> quickModify(
            @RequestAttribute("vendysIam") VendysIam vendysIam,
            @RequestBody(required = false) String payload) {
        this.quickService.quickModify(payload);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}

