package com.vendys.customer.persist.payment;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import com.vendys.customer.constant.Errors;
import com.vendys.customer.exception.CommonException;
import com.vendys.customer.persist.common.RemoteHeader;
import com.vendys.customer.persist.payment.dto.CaptainPayRemoteDto.CardsResponse;
import com.vendys.customer.service.common.CommonRestfulService;

@Service
public class CaptainPayRemotePersist {

    private final RemoteHeader remoteHeader;
    private final RestTemplate customRestTemplate;
    private final CommonRestfulService commonRestfulService;
    private final String paymentHost;

    public CaptainPayRemotePersist(
            RemoteHeader remoteHeader,
            RestTemplate customRestTemplate,
            CommonRestfulService commonRestfulService,
            @Value(value = "${vendys.services.payment-service.host}") String paymentHost) {

        this.remoteHeader = remoteHeader;
        this.customRestTemplate = customRestTemplate;
        this.commonRestfulService = commonRestfulService;
        this.paymentHost = paymentHost;
    }

    /**
     * 사용자 카드 목록 조회
     */
    public CardsResponse readUserCards(String userId, Integer page, Integer pageRow) {

        HttpHeaders httpHeaders = new HttpHeaders();
        HttpEntity<Object> httpEntity = new HttpEntity<>(null, httpHeaders);

        Map<String, String> pathParams = new HashMap<>();
        pathParams.put("userId", userId);

        URI uri = UriComponentsBuilder
                .fromUriString(this.paymentHost + "/captainpay/v1/{userId}/cards")
                .queryParam("page", page)
                .queryParam("pageRow", pageRow)
                .buildAndExpand(pathParams).toUri();

        ResponseEntity<CardsResponse> responseEntity = null;
        try {
            responseEntity = this.customRestTemplate.exchange(
                    uri, HttpMethod.GET, httpEntity, CardsResponse.class);
        } catch (HttpServerErrorException hse) {
            this.commonRestfulService.commonException(hse);
        } catch (HttpClientErrorException hce) {
            this.commonRestfulService.commonException(hce);
        }
        if (ObjectUtils.isEmpty(responseEntity)) {
            throw new CommonException(Errors.MSA_DATA_EMPTY);
        }
        return responseEntity.getBody();
    }

    /**
     * 사용자 카드 정보 삭제
     * */
    public void deleteUserCard(String userId, String userCardId) {
        HttpEntity<Object> httpEntity = new HttpEntity<>(null, this.remoteHeader.getPaymentHeaders(userId));
        Map<String, String> pathParams = new HashMap<>();
        pathParams.put("userId", userId);
        pathParams.put("userCardId", userCardId);

        URI uri = UriComponentsBuilder
                .fromUriString(this.paymentHost + "/captainpay/v1/{userId}/cards/{userCardId}?withdrawYn=true")
                .buildAndExpand(pathParams).toUri();

        ResponseEntity<String> responseEntity = null;
        try {
            responseEntity = this.customRestTemplate.exchange(uri, HttpMethod.DELETE, httpEntity, String.class);
        } catch (HttpServerErrorException hse) {
            this.commonRestfulService.commonException(hse);
        } catch (HttpClientErrorException hce) {
            this.commonRestfulService.commonException(hce);
        }
        if (ObjectUtils.isEmpty(responseEntity)) {
            throw new CommonException(Errors.MSA_INTERFACE_ERROR);
        }
    }
}
