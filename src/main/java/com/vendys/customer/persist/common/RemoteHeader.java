package com.vendys.customer.persist.common;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.vendys.customer.persist.common.entity.RemoteHeaderDto;
import com.vendys.customer.util.ConverterUtil;


/**
 * create by leedo on 2017. 10. 12.
 */
@Service
public class RemoteHeader {

    @Autowired
    private ConverterUtil converterUtil;
    @Value(value = "${vendys.services.payment-service.header.key}")
    private String paymentHeaderKey;
    @Value(value = "${vendys.services.payment-service.header.value.client}")
    private String paymentHeaderClient;
    @Value(value = "${vendys.services.payment-service.header.value.version}")
    private String paymentHeaderVersion;

    /**
     * 공통 헤더 처리
     */
    public HttpHeaders getPaymentHeaders(String userid) {

        RemoteHeaderDto.Requse header = new RemoteHeaderDto.Requse();
        header.setClient(this.paymentHeaderClient);
        header.setUserId(userid);
        header.setVersion(this.paymentHeaderVersion);
        HttpHeaders httpHeaders = new HttpHeaders();

        String headerJson = this.converterUtil.toJsonString(header);
        if (StringUtils.isEmpty(headerJson)) {
            return null;
        }

        httpHeaders.set(this.paymentHeaderKey, headerJson);
        return httpHeaders;
    }
}
