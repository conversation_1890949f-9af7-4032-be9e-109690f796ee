package com.vendys.customer.persist;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.RequiredArgsConstructor;

import com.vendys.customer.entity.CaptainPaymentCompanyInfoHst;
import com.vendys.customer.repository.CaptainPaymentCompanyInfoHstRepository;

@Service
@Transactional
@RequiredArgsConstructor
public class CaptainPaymentCompanyInfoHstPersist {

    private final CaptainPaymentCompanyInfoHstRepository historyRepository;

    public void save(CaptainPaymentCompanyInfoHst history) {
        this.historyRepository.save(history);
    }
}
