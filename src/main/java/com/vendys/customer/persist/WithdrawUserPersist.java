package com.vendys.customer.persist;

import com.vendys.customer.entity.User;
import com.vendys.customer.entity.User.Grade;
import com.vendys.customer.entity.User.Level;
import com.vendys.customer.entity.WithdrawUser;
import com.vendys.customer.repository.WithdrawUserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
@Transactional
@RequiredArgsConstructor
public class WithdrawUserPersist {

    private final WithdrawUserRepository withdrawUserRepository;

    /**
     * User -> WithdrawUser 이관 (실제 탈퇴 처리시)
     * */
    public void saveWithdrawUser(User user) {
        WithdrawUser withdrawUser = WithdrawUser.builder()
                .uid(user.getUid())
                .signId(user.getAccesstoken())
                .name(user.getName())
                .mypoint(user.getMypoint())
                .comid(user.getComid())
                .comname(user.getComname())
                .level(user.getLevel())
                .divisionIdx(user.getDivisionIdx())
                .division(user.getDivision())
                .orgCode(user.getOrgCode())
                .rankposition(user.getRankposition()) // 직위
                .position(user.getPosition()) // 직책
                .comidnum(user.getComidnum()) // 사원 번호
                .joindate(user.getJoindate())
                .level(Level.ACTIVE.level)
                .grade(Grade.USER)
                .version(user.getVersion())
                .isAgree(user.getIsAgree())
                .agreeDate(user.getAgreeDate())
                .createDate(new Date()).build();
        this.withdrawUserRepository.save(withdrawUser);
    }
}
