package com.vendys.customer.persist.aws;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.MessageAttributeValue;
import com.amazonaws.services.sqs.model.MessageSystemAttributeValue;
import com.amazonaws.services.sqs.model.SendMessageBatchRequest;
import com.amazonaws.services.sqs.model.SendMessageBatchRequestEntry;
import com.amazonaws.services.sqs.model.SendMessageBatchResult;
import com.amazonaws.services.sqs.model.SendMessageRequest;
import com.amazonaws.services.sqs.model.SendMessageResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import com.vendys.customer.constant.InitProperties;
import com.vendys.customer.constant.XCustomer;
import com.vendys.customer.interceptor.XCustomerHeader;

@Service
@Slf4j(topic = "http")
@RequiredArgsConstructor
public class AwsSqsRemote {

    private final AmazonSQS amazonSqs;

    private final InitProperties initProperties;

    /**
     * password 초기화
     */
    public void sendPasswordReset(List<String> bodies, String uri, String token, String comId, String userId) {
        this.sendSqsBatch(bodies, this.initProperties.getAwsSqsAuthHost(), uri, token, comId, userId);
    }

    /**
     * point init 초기화
     */
    public void sendPointInit(List<String> bodies, String uri, String token, String comId, String userId) {
        this.sendSqsBatch(bodies, this.initProperties.getAwsSqsPaymentHost(), uri, token, comId, userId);
    }

    /**
     * finance 서비스 SQS 발송
     * @param processor 처리자
     * @param message 내용
     */
    public void sendFinanceMessage(String processor, String message) {
        this.sendSqsMessage(processor, message, this.initProperties.getAwsSqsFinanceHost());
    }

    /**
     * SQS 에 저장
     * @param processor 처리자
     * @param message 내용
     * @param host SQS 주소
     */
    private void sendSqsMessage(String processor, String message, String host) {
        MessageAttributeValue publisherAttribute = new MessageAttributeValue();
        publisherAttribute.setStringValue("CUSTOMER-SERVICE");
        publisherAttribute.setDataType("String");

        MessageAttributeValue processorAttribute = new MessageAttributeValue();
        processorAttribute.setStringValue(processor);
        processorAttribute.setDataType("String");

        SendMessageRequest request = new SendMessageRequest()
                .withQueueUrl(host)
                .addMessageAttributesEntry("publisher", publisherAttribute)
                .addMessageAttributesEntry("processor", processorAttribute)
                .withMessageBody(message);

        SendMessageResult result = this.amazonSqs.sendMessage(request);
        log.info("sendSqsMessage :: {}", result.getMessageId());
    }

    /**
     * SQS 에 저장
     */
    private void sendSqsBatch(List<String> bodies, String host, String uri, String token, String comId, String userId) {

        log.info("sendSqsBatch uids :: {}", bodies.size());

        // Attribute Set
        Map<String, MessageAttributeValue> attributeValueMap = this.getAttributeMap(uri, token, comId, userId);

        List<SendMessageBatchRequestEntry> entries = new ArrayList<>();
        bodies.forEach(body -> {
            String id = UUID.randomUUID().toString().toUpperCase();
            SendMessageBatchRequestEntry entry = new SendMessageBatchRequestEntry(id, body);
            entry.withMessageAttributes(attributeValueMap);
            entries.add(entry);
        });

        SendMessageBatchRequest sendMessageBatchRequest = new SendMessageBatchRequest()
                .withQueueUrl(host)
                .withEntries(entries);

        SendMessageBatchResult result = this.amazonSqs.sendMessageBatch(sendMessageBatchRequest);
        if (!ObjectUtils.isEmpty(result.getFailed())) {
            log.error("sendSqsBatchFailed :: {}", result.getFailed());
        }
    }

    private Map<String, MessageAttributeValue> getAttributeMap(String uri, String token, String comId, String userId) {

        MessageAttributeValue uriValue = new MessageAttributeValue();
        uriValue.setStringValue(uri);
        uriValue.setDataType("String");

        MessageAttributeValue tokenValue = new MessageAttributeValue();
        if (token.contains("Bearer")) {
            tokenValue.setStringValue(token);
        } else {
            tokenValue.setStringValue("Bearer " + token);
        }
        tokenValue.setDataType("String");

        MessageAttributeValue comIdValue = new MessageAttributeValue();
        comIdValue.setStringValue(comId);
        comIdValue.setDataType("String");

        MessageAttributeValue userIdValue = new MessageAttributeValue();
        userIdValue.setStringValue(userId);
        userIdValue.setDataType("String");

        Map<String, MessageAttributeValue> map = new HashMap<>();
        map.put("uri", uriValue);
        map.put("token", tokenValue);
        map.put("comId", comIdValue);
        map.put("userId", userIdValue);

        return map;
    }
}
