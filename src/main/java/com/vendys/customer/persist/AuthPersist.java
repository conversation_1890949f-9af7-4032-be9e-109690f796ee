package com.vendys.customer.persist;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import lombok.extern.slf4j.Slf4j;

import com.vendys.customer.persist.entity.AuthDto;

/**
 * Created by jangjungsu on 2017. 2. 13..
 */
@Slf4j
@Service
public class AuthPersist {

    @Autowired
    private RestTemplate customRestTemplate;

    @Value(value = "${oauth.host}")
    private String host;

    /**
     * 사용자 인증토큰 삭제
     */
    public void expireUserToken(String userId) {
        AuthDto.AuthToken authToken = this.getUserToken(userId);
        if (!ObjectUtils.isEmpty(authToken) && !ObjectUtils.isEmpty(authToken.getTokens())) {
            for (String token : authToken.getTokens()) {
                this.delUerToken(token);
            }
        }
    }

    /**
     * 사용자의 token 조회
     */
    public AuthDto.AuthToken getUserToken(String userId) {
        // URI (URL) path parameters
        Map<String, String> pathParams = new HashMap<>();
        pathParams.put("userId", userId);

        // API Calling
        URI uri = UriComponentsBuilder.fromUriString(this.host + "/oauth2/v1/{userId}/tokens").buildAndExpand(pathParams).toUri();

        ResponseEntity<AuthDto.AuthToken> responseEntity = null;
        try {
            responseEntity = this.customRestTemplate.exchange(uri, HttpMethod.GET, null, AuthDto.AuthToken.class);
        } catch (HttpServerErrorException hse) {
            log.info("해당 사용자 {} 의 토큰이 없습니다.", userId);
        } catch (HttpClientErrorException hce) {
            log.info("해당 사용자 {} 의 토큰이 없습니다.", userId);
        }

        if (ObjectUtils.isEmpty(responseEntity)) {
            log.info("해당 사용자 {} 의 토큰이 없습니다.", userId);
        }

        return responseEntity.getBody();
    }

    /**
     * 사용자의 token 삭제
     */
    public AuthDto.AuthToken delUerToken(String accessToken) {
        // URI (URL) path parameters
        Map<String, String> pathParams = new HashMap<>();
        pathParams.put("accessToken", accessToken);

        // API Calling
        URI uri = UriComponentsBuilder.fromUriString(
                this.host + "/oauth2/tokens/{accessToken}").buildAndExpand(pathParams).toUri();

        ResponseEntity<AuthDto.AuthToken> responseEntity = null;
        try {
            responseEntity = this.customRestTemplate.exchange(uri, HttpMethod.DELETE, null, AuthDto.AuthToken.class);
        } catch (HttpServerErrorException hse) {
            log.error("해당 토큰 {} 삭제에 실패 하였습니다.", accessToken);
        } catch (HttpClientErrorException hce) {
            log.error("해당 토큰 {} 삭제에 실패 하였습니다.", accessToken);
        }

        if (ObjectUtils.isEmpty(responseEntity)) {
            log.error("해당 토큰 {} 삭제에 실패 하였습니다.", accessToken);
        }

        return responseEntity.getBody();
    }

}
