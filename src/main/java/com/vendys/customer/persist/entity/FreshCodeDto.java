package com.vendys.customer.persist.entity;

import java.util.List;

import lombok.Data;

/**
 * Created by jin<PERSON><PERSON> on 2019-09-02
 */
public class FreshCodeDto {
    @Data
    public static class FreshCode {
        private Pagination pagination;
        private List<Menu> menus;

        @Data
        public static class Pagination {
            private Integer page;
            private Integer size;
            private Integer count;
            private Integer totalPage;
        }

        @Data
        public static class Menu {
            private Long id;
            private String type;
            private String name;
            private String summary;
            private String description;
            private String imgUrl;
            private Integer imgWidth;
            private Integer imgHeight;
            private Integer price;
            private Integer limitDaily;
        }
    }
}
