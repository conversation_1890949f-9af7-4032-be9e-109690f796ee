package com.vendys.customer.persist;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.vendys.customer.entity.AdminModifyLog;
import com.vendys.customer.repository.AdminModifyLogRepository;

/**
 * Created by jungsu on 2020. 2. 11.
 */
@Service
@Transactional
public class AdminModifyLogPersist {

    @Autowired
    private AdminModifyLogRepository adminModifyLogRepository;

    /**
     * 로그 저장
     */
    public void createAdminModifyLog(AdminModifyLog log) {
        this.adminModifyLogRepository.save(log);
    }
}
