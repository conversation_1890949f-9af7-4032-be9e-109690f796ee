package com.vendys.customer.persist;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.vendys.customer.entity.BarCodeStatic;
import com.vendys.customer.entity.BarCodeUser;
import com.vendys.customer.entity.BarCodeUser.BarcodeType;
import com.vendys.customer.repository.BarCodePaperRepository;
import com.vendys.customer.repository.BarCodeStaticRepository;
import com.vendys.customer.repository.BarCodeUserRepository;

/**
 * Created by leedo on 07/11/2018
 */
@Service
public class BarcodePersist {

    @Autowired
    private BarCodePaperRepository barCodePaperRepository;

    @Autowired
    private BarCodeStaticRepository barCodeStaticRepository;

    @Autowired
    private BarCodeUserRepository barCodeUserRepository;


    public BarCodeUser readBarcodeUserInfo(String userId, BarcodeType barcodeType, Boolean isActive) {
        return this.barCodeUserRepository.findByUserIdAndBarCodeTypeAndIsActive(userId, barcodeType, isActive);
    }

    public BarCodeUser createBarcodeUser(BarCodeUser barcodeUser) {
        return this.barCodeUserRepository.save(barcodeUser);
    }

    public BarCodeStatic createBarCodeStatic(BarCodeStatic barCodeStatic) {
        return this.barCodeStaticRepository.save(barCodeStatic);
    }
}
