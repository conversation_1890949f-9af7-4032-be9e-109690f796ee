package com.vendys.customer.persist.thirdParty;

import java.util.Optional;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

import com.vendys.customer.entity.ThirdPartyConsent;
import com.vendys.customer.entity.ThirdPartyConsent.RequestType;
import com.vendys.customer.repository.ThirdPartyConsentRepository;

@Service
@RequiredArgsConstructor
public class ThirdPartyConsentPersist {

    private final ThirdPartyConsentRepository thirdPartyConsentRepository;

    public Optional<ThirdPartyConsent> findByUserId(String userid, RequestType requestType) {
        return this.thirdPartyConsentRepository.findByUserIdAndRequestType(userid, requestType);
    }
}
