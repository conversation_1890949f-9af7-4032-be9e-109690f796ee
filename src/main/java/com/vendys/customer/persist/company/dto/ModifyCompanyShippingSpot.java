package com.vendys.customer.persist.company.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ModifyCompanyShippingSpot {

    private String spotName; // 배달지

    private String addressType; // 주소 타입 R(도로명) / J(지번)

    private String roadAddress; // 도로명 주소

    private String jibunAddress; // 지번 주소

    private String addressDetail; // 상세 주소

    private String zoneCode; // 우편번호

    private Double lat; // 위도

    private Double lon; // 경도

    private Boolean isActive; // 활성화 유무

    @Builder
    public ModifyCompanyShippingSpot(String spotName, String addressType, String roadAddress, String jibunAddress, String addressDetail, String zoneCode, Double lat, Double lon, Boolean isActive) {
        this.spotName = spotName;
        this.addressType = addressType;
        this.roadAddress = roadAddress;
        this.jibunAddress = jibunAddress;
        this.addressDetail = addressDetail;
        this.zoneCode = zoneCode;
        this.lat = lat;
        this.lon = lon;
        this.isActive = isActive;
    }
}
