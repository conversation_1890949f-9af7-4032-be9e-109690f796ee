package com.vendys.customer.persist;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.RequiredArgsConstructor;

import com.vendys.customer.entity.ProhibitiveWord;
import com.vendys.customer.repository.ProhibitiveWordRepository;

@Service
@Transactional
@RequiredArgsConstructor
public class ProhibitiveWordPersist {

    private final ProhibitiveWordRepository prohibitiveWordRepository;

    /**
     * 금칙어 체크
     * */
    public List<ProhibitiveWord> checkProhibitiveWord() {
        return this.prohibitiveWordRepository.findAllByIsDeleteFalse();
    }
}
