package com.vendys.customer.controller.company;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.vendys.customer.controller.company.entity.OfficeDto;
import com.vendys.customer.service.office.OfficeService;

/**
 * Created by jinwoo on 2017. 9. 27.
 */
@RestController
@RequestMapping("/company")
public class OfficeController {

    @Autowired
    private OfficeService officeService;

    /**
     * 회사 소속 사업장 정보
     */
    @GetMapping(value = "/v1/{comId}/office")
    public ResponseEntity getList(@PathVariable String comId) {
        List<OfficeDto.Office> result = this.officeService.getOfficeList(comId);

        OfficeDto.ResponseOffice response = new OfficeDto.ResponseOffice();
        response.setOffice(result);
        return new ResponseEntity(response, HttpStatus.OK);
    }

    /**
     * 사업장 상세 정보
     */
    @GetMapping(value = "/v1/{comId}/office/{officeIdx}")
    public ResponseEntity getDetail() {
        return new ResponseEntity(null, HttpStatus.OK);
    }

    /**
     * 사업장에 속한 제휴식당 정보
     */
    @GetMapping(value = "/v1/{comId}/office/{officeIdx}/store")
    public ResponseEntity getStore(
            @PathVariable String comId,
            @PathVariable Long officeIdx) {
        List<OfficeDto.Store> result = this.officeService.getStoreList(officeIdx);
        OfficeDto.ResponseStore response = new OfficeDto.ResponseStore();
        response.setStore(result);
        return new ResponseEntity(response, HttpStatus.OK);
    }
}
