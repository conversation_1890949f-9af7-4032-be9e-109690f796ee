package com.vendys.customer.controller.company.entity;

import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import com.vendys.customer.persist.company.dto.*;
import lombok.*;

import com.vendys.customer.entity.CompanyLimit.PolicyOrder;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * Created by jinwoo on 2018. 3. 12.
 */
public class CompanyDto {

    @Data
    public static class Create {

        @NotNull(message = "고객사 이름 정보가 없습니다.")
        @Size(min = 1, max = 50, message = "5자 이상 40자 이하로 입력 해주세요.")
        private String name;

        private String companyCi;

        @NotNull(message = "주소 정보가 없습니다.")
        private String address;

        private String addressRoad;

        private String addressJibun;

        private String addressDetail;

        private String addressSido;

        private String addressSigugun;

        private String addressDongmyun;

        private String addressZipCode;
        @NotNull(message = "위도 정보가 없습니다.")
        private Double gpslat;
        @NotNull(message = "경도 정보가 없습니다.")
        private Double gpslon;
        @NotNull(message = "지역 정보가 없습니다.")
        private String region;
        @NotNull(message = "담당자 정보가 없습니다.")
        private String incharge;
        @NotNull(message = "연락처 정보가 없습니다.")
        private String phone;
        @NotNull(message = "정산일 정보가 없습니다.")
        private String calculate;

        private String memo;

        @NotNull(message = "임직원 수 정보가 없습니다.")
        private int employee;
        private Boolean active = true;
        private Boolean isTest = false;
        private String userName;
        private Boolean sikdaeEnable;
        private Boolean welfareEnable;
        private Boolean useSikdaeInMarket;
        private Boolean isEzwelWelfare;
        private Boolean storeConfirm = false;
    }

    /**
     * 고객사 일반 정보 수정
     */
    @Getter
    @Setter
    public static class ModifyGeneralInformationRequest {

        @NotNull(message = "고객사 이름 정보가 없습니다.")
        @Size(min = 1, max = 50, message = "5자 이상 40자 이하로 입력 해주세요.")
        private String name;

        private String companyCi;

        @NotNull(message = "주소 정보가 없습니다.")
        private String address;


        private String addressRoad;


        private String addressJibun;


        private String addressDetail;


        private String addressSido;


        private String addressSigugun;


        private String addressDongmyun;


        private String addressZipCode;

        @NotNull(message = "위도 정보가 없습니다.")
        private Double gpslat;

        @NotNull(message = "경도 정보가 없습니다.")
        private Double gpslon;

        @NotNull(message = "지역 정보가 없습니다.")
        private String region;

        private String intro;

        @NotNull(message = "임직원 수 정보가 없습니다.")
        private int employNum;

        @NotNull(message = "담당자 정보가 없습니다.")
        private String chargeName;

        @NotNull(message = "연락처 정보가 없습니다.")
        @Pattern(regexp = "^\\d{1,11}$", message = "휴대폰 번호는 11자리 이하 숫자여야 합니다.")
        private String phone;

        private String salesContents;

        public ModifyCompanyGeneralInformation toModifyCompanyGeneralInformation() {
            return ModifyCompanyGeneralInformation.builder()
                    .name(name)
                    .companyCi(companyCi)
                    .address(address)
                    .addressRoad(addressRoad)
                    .addressJibun(addressJibun)
                    .addressDetail(addressDetail)
                    .addressSido(addressSido)
                    .addressSigugun(addressSigugun)
                    .addressDongmyun(addressDongmyun)
                    .addressZipCode(addressZipCode)
                    .gpslat(gpslat)
                    .gpslon(gpslon)
                    .region(region)
                    .intro(intro)
                    .employNum(employNum)
                    .chargeName(chargeName)
                    .phone(phone)
                    .salesContents(salesContents)
                    .build();
        }
    }

    /**
     * 고객사 서비스 정보 수정
     */
    @Getter
    @Setter
    @NoArgsConstructor(access = AccessLevel.PROTECTED)
    public static class ModifyServiceInformationRequest {

        @NotNull
        private Boolean isStatus; // 활성화 유무

        @NotNull
        private Boolean isSikdaeUse; // 식권대장 사용 유무

        @NotNull
        private Boolean isWelfareUse; // 복지대장 사용 유무


        @NotNull
        private Boolean isTest; // 테스트용 유무

        @NotNull
        private Boolean isStoreConfirm; // 고객사 제휴점 확인 필요 유무

        public ModifyCompanyServiceInformation toModifyCompanyServiceInformation() {
            return ModifyCompanyServiceInformation.builder()
                    .isStatus(isStatus)
                    .isSikdaeUse(isSikdaeUse)
                    .isWelfareUse(isWelfareUse)
                    .isTest(isTest)
                    .isStoreConfirm(isStoreConfirm)
                    .build();
        }

    }

    /**
     * 고객사 서비스 정보 수정
     */
    @Getter
    @Setter
    @NoArgsConstructor(access = AccessLevel.PROTECTED)
    public static class ModifyFeatureSettingRequest {

        @NotNull
        private Boolean isJang; // 장단위 유무

        @NotNull
        private Boolean isMultiPayPossible; // 함께 결제 허용 유무

        @NotNull
        private MultiEntrust isMultiEntrustPossible; // 함께 결제 위임 허용 유무

        @NotNull
        private Boolean isMultiMenuPayPossible; // 다중 메뉴 결제 허용 유무

        @NotNull
        private Boolean isDemandPosUse; // 신청 식대 지급기 사용 유무

        @NotNull
        private Boolean isBlockSelfWithdrawalUse; // 사용자 탈퇴 차단 사용 유무

        @NotNull
        private Boolean isOurHomePointUse; // 아워홈 차감 포인트 사용 유무

        @NotNull
        private Boolean isExposeUserSignIdUse; // 사용자 ID 노출 사용 유무

        @NotNull
        private Boolean isCompanyCertificationRequired; // 본인인증 필수 사용 유무

        @NotNull
        private Boolean isBudgetUse; // 예산관리 사용 유무

        @NotNull
        private Boolean isInvoiceUse; // 청구서 사용 유무

        @NotNull
        private Boolean isAdminAuthUse; // 관리자 권한 관리 사용 유무

        @NotNull
        private Boolean isBarcodeUse; // 바코드 관리 사용 유무

        @NotNull
        private Boolean isSettlementUse; // 정산관리 사용 유무

        @NotNull
        private Boolean isFoodMall; // 식품관 사용 유무

        @NotNull
        private Boolean isUseSikdaeInMarket; // 식품관 식대 사용 유무

        @NotNull
        private Boolean isDaejangPayUse; // 대장페이 사용 유무

        @NotNull
        private Boolean isTeuggaUse; // 특가 대장 사용 유무

        @Builder
        public ModifyFeatureSettingRequest(Boolean isJang, Boolean isMultiPayPossible, MultiEntrust isMultiEntrustPossible, Boolean isMultiMenuPayPossible, Boolean isDemandPosUse, Boolean isBlockSelfWithdrawalUse, Boolean isOurHomePointUse, Boolean isExposeUserSignIdUse, Boolean isCompanyCertificationRequired, Boolean isBudgetUse, Boolean isInvoiceUse, Boolean isAdminAuthUse, Boolean isBarcodeUse, Boolean isSettlementUse, Boolean isFoodMall, Boolean isUseSikdaeInMarket, Boolean isDaejangPayUse, Boolean isTeuggaUse) {
            this.isJang = isJang;
            this.isMultiPayPossible = isMultiPayPossible;
            this.isMultiEntrustPossible = isMultiEntrustPossible;
            this.isMultiMenuPayPossible = isMultiMenuPayPossible;
            this.isDemandPosUse = isDemandPosUse;
            this.isBlockSelfWithdrawalUse = isBlockSelfWithdrawalUse;
            this.isOurHomePointUse = isOurHomePointUse;
            this.isExposeUserSignIdUse = isExposeUserSignIdUse;
            this.isCompanyCertificationRequired = isCompanyCertificationRequired;
            this.isBudgetUse = isBudgetUse;
            this.isInvoiceUse = isInvoiceUse;
            this.isAdminAuthUse = isAdminAuthUse;
            this.isBarcodeUse = isBarcodeUse;
            this.isSettlementUse = isSettlementUse;
            this.isFoodMall = isFoodMall;
            this.isUseSikdaeInMarket = isUseSikdaeInMarket;
            this.isDaejangPayUse = isDaejangPayUse;
            this.isTeuggaUse = isTeuggaUse;
        }

        public enum MultiEntrust {
            NONE, // 안함
            MANUAL, // 수동
            AUTOMATIC // 자동
        }

        public ModifyCompanyFeatureSetting toModifyCompanyFeatureSetting() {
            return ModifyCompanyFeatureSetting.builder()
                    .isJang(isJang)
                    .isMultiPayPossible(isMultiPayPossible)
                    .isMultiEntrustPossible(isMultiEntrustPossible)
                    .isMultiMenuPayPossible(isMultiMenuPayPossible)
                    .isDemandPosUse(isDemandPosUse)
                    .isBlockSelfWithdrawalUse(isBlockSelfWithdrawalUse)
                    .isOurHomePointUse(isOurHomePointUse)
                    .isExposeUserSignIdUse(isExposeUserSignIdUse)
                    .isCompanyCertificationRequired(isCompanyCertificationRequired)
                    .isBudgetUse(isBudgetUse)
                    .isInvoiceUse(isInvoiceUse)
                    .isAdminAuthUse(isAdminAuthUse)
                    .isBarcodeUse(isBarcodeUse)
                    .isSettlementUse(isSettlementUse)
                    .isFoodMall(isFoodMall)
                    .isUseSikdaeInMarket(isUseSikdaeInMarket)
                    .isDaejangPayUse(isDaejangPayUse)
                    .isTeuggaUse(isTeuggaUse)
                    .build();
        }

    }

    /**
     * 고객사 배달지 추가
     */
    @Getter
    @Setter
    @NoArgsConstructor(access = AccessLevel.PROTECTED)
    public static class CreateCompanyShippingSpotRequest {

        @NotEmpty
        private String spotName; // 배달지


        private String addressType; // 주소 타입 R(도로명) / J(지번)


        private String roadAddress; // 도로명 주소


        private String jibunAddress; // 지번 주소

        @NotEmpty
        private String addressDetail; // 상세 주소

        private String zoneCode; // 우편번호

        @NotNull
        private Double lat; // 위도

        @NotNull
        private Double lon; // 경도

        @NotNull
        private Boolean isActive; // 활성화 유무

        @Builder
        public CreateCompanyShippingSpotRequest(String spotName, String addressType, String roadAddress, String jibunAddress, String addressDetail, String zoneCode, Double lat, Double lon, Boolean isActive) {
            this.spotName = spotName;
            this.addressType = addressType;
            this.roadAddress = roadAddress;
            this.jibunAddress = jibunAddress;
            this.addressDetail = addressDetail;
            this.zoneCode = zoneCode;
            this.lat = lat;
            this.lon = lon;
            this.isActive = isActive;
        }

        public CreateCompanyShippingSpot toCreateCompanyShippingSpot() {
            return CreateCompanyShippingSpot.builder()
                    .spotName(spotName)
                    .addressType(addressType)
                    .roadAddress(roadAddress)
                    .jibunAddress(jibunAddress)
                    .addressDetail(addressDetail)
                    .zoneCode(zoneCode)
                    .lat(lat)
                    .lon(lon)
                    .isActive(isActive)
                    .build();
        }
    }

    /**
     * 고객사 배달지 수정
     */
    @Getter
    @Setter
    @NoArgsConstructor(access = AccessLevel.PROTECTED)
    public static class ModifyCompanyShippingSpotRequest {

        @NotEmpty
        private String spotName; // 배달지

        private String addressType; // 주소 타입 R(도로명) / J(지번)

        private String roadAddress; // 도로명 주소

        private String jibunAddress; // 지번 주소

        @NotEmpty
        private String addressDetail; // 상세 주소

        private String zoneCode; // 우편번호

        @NotNull
        private Double lat; // 위도

        @NotNull
        private Double lon; // 경도

        @NotNull
        private Boolean isActive;

        @Builder
        public ModifyCompanyShippingSpotRequest(String spotName, String addressType, String roadAddress, String jibunAddress, String addressDetail, String zoneCode, Double lat, Double lon, Boolean isActive) {
            this.spotName = spotName;
            this.addressType = addressType;
            this.roadAddress = roadAddress;
            this.jibunAddress = jibunAddress;
            this.addressDetail = addressDetail;
            this.zoneCode = zoneCode;
            this.lat = lat;
            this.lon = lon;
            this.isActive = isActive;
        }

        public ModifyCompanyShippingSpot toCreateCompanyShippingSpot() {
            return ModifyCompanyShippingSpot.builder()
                    .spotName(spotName)
                    .addressType(addressType)
                    .roadAddress(roadAddress)
                    .jibunAddress(jibunAddress)
                    .addressDetail(addressDetail)
                    .zoneCode(zoneCode)
                    .isActive(isActive)
                    .lat(lat)
                    .lon(lon)
                    .build();
        }
    }

    /**
     * 고객사 배달지 추가 Response
     */
    @Getter
    @Setter
    public static class CreateCompanyShippingSpotResponse {

        private String spotKey;

        public CreateCompanyShippingSpotResponse(String spotKey) {
            this.spotKey = spotKey;
        }
    }

    @Data
    public static class CreateResponse {

        private String companyId;
        private Date regDate;
    }

    @Data
    public static class CompanyResponse {

        private Info info;
        private Types types;
        private Conditions conditions;
        private List<Office> officeList;
        private CaptainPayment captainPayment;

        @Data
        public static class Info {

            private String companyId;
            private String name;
            private String bizName;
            private String bizSerial;
            private String address;
            private String region;
            private String chargeName;
            private String phone;
            private String intro;
            private Integer employNum;
            private String calculateDate;
            private String depositMethod;
            private Date regDate;
        }

        @Data
        public static class Types {

            private String cafeteriaType;
            private Boolean ticketFormat;
            private String version;
        }

        @Data
        public static class Conditions {

            private Integer status;
            private Boolean useMyPoint;
            private Boolean multiPay;
            private Boolean multiEntrust;
            private Boolean multiMenu;
            private Boolean cafeteria;
            private Boolean companyNotice;
            private Boolean cashReceipt;
            private Boolean timeDisplayed;
            private Boolean forceEntrust;
            private Boolean invoice;
            private Boolean adminAuth;
            private Boolean printBarcode;
            private Boolean qrCodeScan;
            private Boolean privacy;
            private Boolean settlement;
            private Boolean budget;
            private Boolean snack;
            private Boolean demandPos;
            private Boolean market;
            private Boolean event;
            private Boolean isPaymentReason;
            private Boolean isEzwelWelfare;
            private PolicyOrder policyOrder;
            private Boolean isCompanyCertificationRequired;
            private Boolean isFoodMall;
        }

        @Data
        public static class Office {
            private Long officeIdx;
            private String orgCode;
            private Double gpslat;
            private Double gpslon;
        }

        @Data
        public static class CaptainPayment {
            private Boolean useSikdae;
            private Boolean useWelfare;
        }
    }

}
