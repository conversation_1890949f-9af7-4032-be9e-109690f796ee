package com.vendys.customer.controller.user.entity;

import java.util.List;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.util.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import com.vendys.customer.entity.User;

public class ElasticUserDto {

    @Getter
    @Setter
    public static class ElasticUser {

        private String name;
        private String companyId;
        private String signId;
        private String userId;
        private String orgCode;
        private String status;
        private boolean dormant;
        private Long groupIdx;
        private String position;
        private String profileImageUrl;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Request {

        private String searchName;
        private String companyId;
        private String orgCode;
        private Integer page = 0;
        private Integer size = 20;
        private Boolean excludeSelf;
        private String excludeUserId;

        public boolean hasOrgCode() {
            return StringUtils.hasText(this.orgCode);
        }

        public Integer from() {
            return this.size * this.page;
        }

        public Pageable pageable(Sort sort) {
            return new PageRequest(page, size, sort);
        }
    }

    @Getter
    @Builder
    public static class Response {

        private String name;
        private String companyId;
        private String signId;
        private String userId;
        private String orgCode;
        private String status;
        private boolean dormant;
        private Long groupIdx;
        private String position;
        private String profileImageUrl;

        public static ElasticUserDto.Response of(ElasticUser elasticUser) {
            return Response.builder()
                    .name(elasticUser.getName())
                    .companyId(elasticUser.getCompanyId())
                    .signId(elasticUser.getSignId())
                    .userId(elasticUser.getUserId())
                    .orgCode(elasticUser.getOrgCode())
                    .status(elasticUser.getStatus())
                    .dormant(elasticUser.isDormant())
                    .groupIdx(elasticUser.getGroupIdx())
                    .position(elasticUser.getPosition())
                    .profileImageUrl(elasticUser.getProfileImageUrl())
                    .build();
        }

        public static ElasticUserDto.Response of(User user) {
            return Response.builder()
                    .name(user.getName())
                    .companyId(user.getComid())
                    .signId(user.getSignId())
                    .orgCode(user.getOrgCode())
                    .status(user.getStatus().name)
                    .dormant(user.getIsDormant())
                    .groupIdx(user.getGroupIdx())
                    .position(user.getPosition())
                    .profileImageUrl(user.getProfileImageUrl())
                    .build();
        }
    }

    @Getter
    @AllArgsConstructor
    public static class ResponseList {

        private List<Response> userList;
    }
}
