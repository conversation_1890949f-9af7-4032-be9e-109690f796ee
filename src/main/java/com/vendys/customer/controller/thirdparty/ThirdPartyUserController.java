package com.vendys.customer.controller.thirdparty;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.RequiredArgsConstructor;

import com.vendys.customer.service.naver.NaverAccountsService;
import com.vendys.customer.thirdparty.naver.dto.NaverAccounts;
import com.vendys.customer.thirdparty.naver.dto.NaverAccounts.Response;

@RestController
@RequiredArgsConstructor
@RequestMapping("/user")
public class ThirdPartyUserController {

    private final NaverAccountsService naverAccountsService;

    @GetMapping("/v1/third-party/{userId}/naver")
    public ResponseEntity<NaverAccounts.Response> getUserNaverAccounts(@PathVariable String userId) {
        NaverAccounts.Response naverAccounts = this.naverAccountsService.getNaverAccounts(userId);
        return ResponseEntity.ok(naverAccounts);
    }

}
