package com.vendys.customer.entity;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters.LocalDateConverter;

import javax.persistence.*;
import java.time.LocalDate;

@Entity
@Table(name = "HolidayCountryInfo")
@Getter
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class HolidayCountryInformation extends BaseEntity {

    @Id
    // 휴일 고유 아이디
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "idx")
    private Long id;

    // 공휴일 년도
    @Column(name = "year", length = 4, nullable = false)
    private String year;

    // 공휴일 시작일
    @Column(name = "startDate", nullable = false, columnDefinition = "DATE")
    @Convert(converter = LocalDateConverter.class)
    private LocalDate startDate;

    // 공휴일 종료일
    @Column(name = "endDate", nullable = false, columnDefinition = "DATE")
    @Convert(converter = LocalDateConverter.class)
    private LocalDate endDate;

    // 공휴일 기간
    @ColumnDefault("0")
    @Column(name = "duration", nullable = false)
    private Integer duration;

    // 공휴일 명
    @Column(name = "name", length = 50, nullable = false)
    private String name;

    // 공휴일 설명
    @Column(name = "description", length = 100)
    private String description;

    // 삭제 여부
    @ColumnDefault("b'0'")
    @Column(name = "isDelete", nullable = false, columnDefinition = "BIT")
    private Boolean isDelete;

    private String updateId;

    @Builder
    public HolidayCountryInformation(Long id, String year, LocalDate startDate, LocalDate endDate, Integer duration, String name, String description, Boolean isDelete, String updateId, String createUser, String updateUser) {
        this.id = id;
        this.year = year;
        this.startDate = startDate;
        this.endDate = endDate;
        this.duration = duration;
        this.name = name;
        this.description = description;
        this.isDelete = isDelete;
        this.updateId = updateId;
        this.createUser = createUser;
        this.updateUser = updateUser;
    }

    public HolidayCompanyInformation toHolidayCompanyInformation(String companyId) {
        return HolidayCompanyInformation.builder()
                .holidayCountryIdx(this.id)
                .companyId(companyId)
                .year(this.year)
                .startDate(this.startDate)
                .endDate(this.endDate)
                .duration(this.duration)
                .name(this.name)
                .description(this.description)
                .isActive(false)
                .updatable(false)
                .deletable(false)
                .updateId(this.updateId)
                .isDelete(this.isDelete)
                .build();
    }
}

















