package com.vendys.customer.entity;

import lombok.*;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters.LocalDateTimeConverter;
import javax.persistence.*;
import java.time.LocalDateTime;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PROTECTED)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(name = "EzwelProductHistory", 
       indexes = {
           @Index(name = "idx_ezwel_prod_hist_date", columnList = "createDate"),
           @Index(name = "idx_ezwel_prod_hist_type_code", columnList = "type, code"),
           @Index(name = "idx_ezwel_prod_hist_op_type", columnList = "operationType"),
           @Index(name = "idx_ezwel_prod_hist_type", columnList = "type")
       })
public class EzwelProductHistory {
    
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idx;
    
    /**
     * 작업 유형 (INSERT, DELETE)
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 10)
    private OperationType operationType;
    
    @Column(nullable = false, length = 10)
    private String type;
    
    @Column(nullable = false, length = 2)
    private String status;
    
    @Column(length = 10)
    private String code;
    
    @Column(length = 300)
    private String name;
    
    @Column
    private Integer price;
    
    @Column(length = 500)
    private String image;
    
    @Column(length = 500)
    private String url;
    
    @Column(name = "sortNo")
    private Integer sortNo;
    
    @Column(length = 100)
    private String labels;
    
    @Column
    private Boolean ad;
    
    @Column(length = 50)
    private String createUser;
    
    @Convert(converter = LocalDateTimeConverter.class)
    @Column
    private LocalDateTime createDate;
    
    /**
     * 작업 유형 정의
     */
    public enum OperationType {
        INSERT, DELETE, UPDATE
    }
    
    /**
     * EzwelProduct 엔터티를 히스토리로 변환하는 팩토리 메소드
     */
    public static EzwelProductHistory fromProduct(EzwelProduct product, OperationType operationType, String historyCreateUser) {
        return EzwelProductHistory.builder()
                .operationType(operationType)
                .type(product.getType())
                .status(product.getStatus())
                .code(product.getCode())
                .name(product.getName())
                .price(product.getPrice())
                .image(product.getImage())
                .url(product.getUrl())
                .sortNo(product.getSortNo())
                .labels(product.getLabels())
                .ad(product.getAd())
                .createUser(product.getCreateUser())
                .createDate(product.getCreateDate())
                .build();
    }
}
