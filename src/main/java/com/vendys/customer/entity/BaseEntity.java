package com.vendys.customer.entity;

import lombok.Getter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters.LocalDateTimeConverter;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;
import java.time.LocalDateTime;

@Getter
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public class BaseEntity {

    // 생성자
    @Column(name = "createUser", updatable = false, nullable = false, length = 36)
    protected String createUser;

    // 생성일시
    @CreatedDate
    @Column(name = "createDate", columnDefinition = "DATETIME", updatable = false, nullable = false)
    @Convert(converter = LocalDateTimeConverter.class)
    protected LocalDateTime createDate;

    // 수정자
    @Column(name = "updateUser", nullable = false, length = 36)
    protected String updateUser;

    // 수정일시
    @LastModifiedDate
    @Column(name = "updateDate", columnDefinition = "DATETIME")
    @Convert(converter = LocalDateTimeConverter.class)
    protected LocalDateTime updateDate;
}
