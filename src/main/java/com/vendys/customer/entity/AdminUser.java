package com.vendys.customer.entity;

import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;

import lombok.Data;

/**
 * Created by jungsu on 2020. 2. 12.
 */
@Data
@Entity
public class AdminUser {

    @Id
    private String adminId;
    private String signId;
    private String name;
    private String phone;
    private String email;
    private String division;
    private Boolean isPrivacy;
    private Boolean isExternal;
    @Enumerated(EnumType.STRING)
    private Status status;
    @Enumerated(EnumType.STRING)
    private Grade grade;
    private Date created;

    public enum Status {
        ACTIVE, INACTIVE, RESET, WITHDRAW, LOCKED
    }

    public enum Grade {
        ADMIN, SUPER
    }
}
