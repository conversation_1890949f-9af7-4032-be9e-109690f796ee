package com.vendys.customer.entity;

import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

import lombok.Data;

/**
 * Created by jungsu on 2020. 2. 11.
 */
@Data
@Entity
public class AdminModifyLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long logIdx;
    private String userId;
    private String userName;
    private String companyId;
    private String tableIdx;
    private String tableName;
    @Enumerated(EnumType.STRING)
    private Server serverName;
    private Date regDate;
    private String beforeData;
    private String changeLog;

    public enum Server {
        SIKDAE, ADMIN, COMPANY, STORE, AUTH, PAYMENT, GATEWAY
    }

    public enum TableName {
        User, UserSmsCertification, UserEmailCertification, Company, CompanyShippingSpot, CompanyLimit
    }
}
