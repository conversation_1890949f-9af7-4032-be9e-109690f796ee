package com.vendys.customer.service.ezwelWelfare;


import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.RequiredArgsConstructor;

import com.vendys.customer.entity.EzwelResponseApiLog;
import com.vendys.customer.persist.ezwel.EzwelResponseApiLogPersist;

@Service
@RequiredArgsConstructor
public class EzwelResponseApiLogService {

    private final EzwelResponseApiLogPersist responseApiLogPersist;

    /**
     * Response 로그 저장
     */
    @Transactional
    public void save(EzwelResponseApiLog ezwelResponseApiLog) {
        this.responseApiLogPersist.save(ezwelResponseApiLog);
    }
}
