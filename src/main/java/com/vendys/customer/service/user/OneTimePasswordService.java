package com.vendys.customer.service.user;

import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.vendys.customer.constant.Errors;
import com.vendys.customer.controller.user.entity.OneTimePasswordDto.ResponseCreateOtp;
import com.vendys.customer.controller.user.entity.OneTimePasswordDto.ResponseGetOtp;
import com.vendys.customer.entity.User;
import com.vendys.customer.entity.User.Status;
import com.vendys.customer.entity.UserOneTimePassword;
import com.vendys.customer.exception.CommonException;
import com.vendys.customer.persist.OneTimePasswordPersist;
import com.vendys.customer.persist.UserPersist;
import com.vendys.customer.service.user.entity.Otp;

/**
 * Created by jinwo<PERSON> on 07/10/2019
 */
@Service
@Transactional
public class OneTimePasswordService {
    @Autowired
    private UserPersist userPersist;
    @Autowired
    private OneTimePasswordPersist otpPersist;

    /**
     * OTP 생성
     */
    public ResponseCreateOtp setOtp(String userId) {
        User user = this.userPersist.readUser(userId);
        if (user.getStatus() != Status.ACTIVE) {
            throw new CommonException(Errors.ACCOUNT_UNKNOWN_USER);
        }

        UserOneTimePassword uotp = this.otpPersist.createOtp(user.getUid());

        ResponseCreateOtp response = new ResponseCreateOtp();
        response.setCode(uotp.getCode());
        response.setSeed(uotp.getSeed());
        response.setCreated(uotp.getCreated());
        return response;
    }

    /**
     * OTP 조회
     */
    public ResponseGetOtp getOtp(String userId, String code) {
        User user = this.userPersist.readUser(userId);
        Otp otp = this.convertOtp(code);

        UserOneTimePassword uotp = this.otpPersist.readOtpByUser(user.getUid());

        ResponseGetOtp response = new ResponseGetOtp();
        response.setCode(code);
        response.setStatus(this.checkValid(uotp, otp));
        return response;
    }

    private Otp convertOtp(String code) {
        Pattern p = Pattern.compile("^(\\d{10})(\\d{2})$");
        Matcher m = p.matcher(code);

        if (!m.find()) {
            throw new CommonException(Errors.ACCOUNT_VALID_OTP);
        }

        Otp otp = new Otp();
        otp.setCode(m.group(1));
        otp.setParity(Integer.valueOf(m.group(2)));
        return otp;
    }

    private ResponseGetOtp.Status checkValid(UserOneTimePassword uotp, Otp otp) {
        if (!otp.getCode().equals(uotp.getCode())) {
            return ResponseGetOtp.Status.INVALID;
        }

        if (uotp.getExpireTime().before(new Date())) {
            return ResponseGetOtp.Status.EXPIRED;
        }

        if (!this.otpPersist.checkParity(uotp, otp.getParity())) {
            return ResponseGetOtp.Status.INVALID;
        }

        return ResponseGetOtp.Status.VALID;
    }
}
