package com.vendys.customer.service.openapi;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import lombok.RequiredArgsConstructor;

import com.vendys.customer.constant.Errors;
import com.vendys.customer.controller.openapi.entity.DivisionDto;
import com.vendys.customer.controller.openapi.entity.DivisionDto.MakeRequest;
import com.vendys.customer.controller.openapi.entity.DivisionDto.Response.Division;
import com.vendys.customer.controller.openapi.entity.DivisionDto.Response.DivisionV2;
import com.vendys.customer.controller.openapi.entity.DivisionDto.UpdateRequest;
import com.vendys.customer.controller.openapi.entity.MealGroupDto;
import com.vendys.customer.controller.openapi.entity.MealGroupDto.Response;
import com.vendys.customer.controller.openapi.entity.RankPositionDto;
import com.vendys.customer.controller.openapi.entity.RankPositionDto.Response.RankPosition;
import com.vendys.customer.controller.openapi.entity.UserDto;
import com.vendys.customer.controller.openapi.entity.UserDto.DivisionInfo;
import com.vendys.customer.controller.openapi.entity.UserDto.InsertUserReq;
import com.vendys.customer.controller.openapi.entity.UserDto.MealGroupInfo;
import com.vendys.customer.controller.openapi.entity.UserDto.Paging;
import com.vendys.customer.controller.openapi.entity.UserDto.UserListResponse;
import com.vendys.customer.controller.openapi.entity.UserDto.UserResponse;
import com.vendys.customer.controller.openapi.entity.UserDto.UserStatus;
import com.vendys.customer.entity.AdminModifyLog.Server;
import com.vendys.customer.entity.AdminModifyLog.TableName;
import com.vendys.customer.entity.CaptainPaymentCompanyInfo;
import com.vendys.customer.entity.CaptainPaymentCompanyInfo.ServiceType;
import com.vendys.customer.entity.Company;
import com.vendys.customer.entity.InfoUser.Type;
import com.vendys.customer.entity.MealGroup;
import com.vendys.customer.entity.MealGroup.GroupType;
import com.vendys.customer.entity.Office;
import com.vendys.customer.entity.Organization;
import com.vendys.customer.entity.User;
import com.vendys.customer.entity.User.Grade;
import com.vendys.customer.entity.User.Level;
import com.vendys.customer.entity.User.Status;
import com.vendys.customer.entity.User.Version;
import com.vendys.customer.entity.UserCategory;
import com.vendys.customer.entity.UserEmailCertification;
import com.vendys.customer.entity.UserSmsCertification;
import com.vendys.customer.exception.CommonException;
import com.vendys.customer.finance.application.ClientOrganizationService;
import com.vendys.customer.finance.domain.OrganizationAdd;
import com.vendys.customer.finance.domain.OrganizationUpdate;
import com.vendys.customer.persist.CaptainPaymentCompanyInfoPersist;
import com.vendys.customer.persist.CompanyPersist;
import com.vendys.customer.persist.MealGroupPersist;
import com.vendys.customer.persist.OfficePersist;
import com.vendys.customer.persist.OrganizationCachePersist;
import com.vendys.customer.persist.UserActivityPersist;
import com.vendys.customer.persist.UserCategoryPersist;
import com.vendys.customer.persist.UserEmailCertificationPersist;
import com.vendys.customer.persist.UserOfficeRelationPersist;
import com.vendys.customer.persist.UserPersist;
import com.vendys.customer.persist.UserSmsCertificationPersist;
import com.vendys.customer.service.AdminModifyLogService;
import com.vendys.customer.service.CompanyPointTaskService;
import com.vendys.customer.service.OrganizationCacheService;
import com.vendys.customer.service.OrganizationService;
import com.vendys.customer.service.UserService;
import com.vendys.customer.service.company.DivisionService;
import com.vendys.customer.service.user.UserCervice;
import com.vendys.customer.service.user.UserStatusService;
import com.vendys.customer.util.DateUtil;
import com.vendys.customer.util.UserUtil;
import com.vendys.customer.vo.OrganizationVo;
import com.vendys.customer.vo.OrganizationVo.OrganizationCache;


@Service
@Transactional
@RequiredArgsConstructor
public class OpenApiService {

    private final CompanyPersist companyPersist;
    @Value(value = "${openapi.workerId}")
    private String openApiWorkerId;

    @Value(value = "${openapi.workerName}")
    private String openApiWorkerName;

    private final UserCategoryPersist userCategoryPersist;
    private final MealGroupPersist mealGroupPersist;
    private final UserPersist userPersist;
    private final UserCervice userCervice;
    private final UserService userService;
    private final OfficePersist officePersist;
    private final UserOfficeRelationPersist userOfficeRelationPersist;
    private final OrganizationCacheService orgCacheService;
    private final UserActivityPersist userActivityPersist;
    private final AdminModifyLogService adminModifyLogService;
    private final UserStatusService userStatusService;
    private final UserSmsCertificationPersist userSmsCertificationPersist;
    private final UserEmailCertificationPersist userEmailCertificationPersist;
    private final UserUtil userUtil;
    private final DivisionService divisionService;
    private final OrganizationService organizationService;
    private final OrganizationCachePersist cachePersist;
    private final CompanyPointTaskService companyPointTaskService;
    private final CaptainPaymentCompanyInfoPersist companyInfoPersist;
    private final ClientOrganizationService clientOrganizationService;

    public RankPositionDto.Response getRankPositionList(String comId) {
        RankPositionDto.Response response = new RankPositionDto.Response();
        List<RankPosition> rankPositionList = new ArrayList<>();

        List<UserCategory> userCategoryList = this.userCategoryPersist.readRankPositionList(comId);
        for (UserCategory userCategory : userCategoryList) {
            RankPosition rankPosition = new RankPosition();
            rankPosition.setId(userCategory.getCateid());
            rankPosition.setName(userCategory.getName());

            rankPositionList.add(rankPosition);
        }

        response.setRankPositionList(rankPositionList);

        return response;
    }

    public MealGroupDto.Response getMealGroupList(String comId) {
        MealGroupDto.Response response = new MealGroupDto.Response();
        List<MealGroupDto.Response.MealGroup> resultGroupList = new ArrayList<>();

        List<MealGroup> mealGroupList = this.mealGroupPersist.readMealGroupList(comId);
        for (MealGroup mealGroup : mealGroupList) {
            MealGroupDto.Response.MealGroup result = new Response.MealGroup();
            result.setGroupIdx(mealGroup.getGroupIdx());
            result.setName(mealGroup.getName());
            result.setMemo(mealGroup.getMemo());
            result.setLimitAmount(mealGroup.getLimitAmount());

            resultGroupList.add(result);
        }

        response.setMealGroupList(resultGroupList);

        return response;
    }

    public UserDto.User setUser(String comId, UserDto.UserCreate userCreate) {
        String userId = UUID.randomUUID().toString().toUpperCase();

        // 등록 가능한 signId 인지 체크
        if (this.userUtil.checkExcludeSignId(userCreate.getSignId())) {
            throw new CommonException(Errors.USER_EXCLUDE_SIGNID);
        }

        // 회사 유효성 검사
        Company company = this.userCervice.validCompany(comId);
        // 식대 그룹 있는지 확인
        this.mealGroupPersist.readMealGroup(comId, userCreate.getMealGroupId(), true, GroupType.MEAL);
        // 부서 유효성 검사
        this.userCervice.validDivision(userCreate.getDivisionCode());

        // 사용자 가입 여부 검사
        this.userService.existUser(comId, userCreate.getSignId(), userCreate.getEmail(), userCreate.getCellphone(),
                userCreate.getEmployeeIdNum());

        User user = new User();
        user.setUid(userId);
        user.setSignId(userCreate.getSignId().toLowerCase());
        user.setName(userCreate.getName());
        user.setComid(company.getComid());
        user.setComname(company.getName());
        user.setLevel(Level.ACTIVE.level);
        user.setGrade(Grade.USER);
        user.setStatus(Status.ACTIVE);
        user.setOrgCode(userCreate.getDivisionCode());
        user.setGroupIdx(userCreate.getMealGroupId());
        user.setMypoint(0);
        user.setIsAgree(false);
        user.setPasswordreset("N");
        user.setJoindate(new Date());
        user.setIsDormant(false);
        user.setVersion(Version.v4);

        if (!ObjectUtils.isEmpty(userCreate.getSex())) {
            user.setSex(userCreate.getSex());
        }

        if (!StringUtils.isEmpty(userCreate.getEmail())) {
            user.setEmail(userCreate.getEmail().trim());
        }

        if (!StringUtils.isEmpty(userCreate.getCellphone())) {
            user.setCellphone(userCreate.getCellphone().trim().replaceAll("-", ""));
        }

        if (!StringUtils.isEmpty(userCreate.getBirthday())) {
            Date birthday = DateUtil.stringToDate(userCreate.getBirthday());
            if (birthday != null) {
                user.setBirthday(birthday);
            }
        }

        if (!StringUtils.isEmpty(userCreate.getPosition())) {
            user.setPosition(userCreate.getPosition());
        }

        if (!ObjectUtils.isEmpty(userCreate.getRankPositionId())) {
            List<UserCategory> userCategoryList = this.userCategoryPersist.readRankPositionList(comId);
            UserCategory rankPosition = userCategoryList.stream()
                    .filter(userCategory -> userCategory.getCateid().equals(userCreate.getRankPositionId())).findFirst()
                    .orElse(null);

            if (!ObjectUtils.isEmpty(rankPosition)) {
                user.setRankposition(rankPosition.getName());
            }
        }

        if (!StringUtils.isEmpty(userCreate.getEmployeeIdNum())) {
            user.setComidnum(userCreate.getEmployeeIdNum());
        }

        this.userPersist.createUser(user);

        // 사업장 정보 조회
        List<Office> officeList = this.officePersist.getActiveOfficeList(comId);
        // 사업장 연결
        Long officeIdx = officeList.stream().findFirst().get().getOfficeIdx();
        this.userOfficeRelationPersist.createUserOffice(user.getUid(), officeIdx);
        // UserActivity 저장
        this.userActivityPersist.createUserActivity(user.getUid());
        // Log
        this.adminModifyLogService.setAdminModifyLog(
                openApiWorkerId, openApiWorkerName, company.getComid(), user.getUid(), TableName.User.name(), Server.GATEWAY,
                null, user
        );

        return this.getUserDetail(comId, userId);
    }

    public UserDto.User updateUser(String comId, String userId, UserDto.UserUpdate userUpdate) {
        // 기존 정보 조회
        User sourceUser = this.userPersist.readUserByComId(comId, userId);
        User beforeUser = new User();

        BeanUtils.copyProperties(sourceUser, beforeUser);

        if (sourceUser.getStatus().equals(Status.WITHDRAW)) {
            throw new CommonException(Errors.ACCOUNT_WITHDRAW);
        }

        if (sourceUser.getIsDormant()) {
            throw new CommonException(Errors.ACCOUNT_DORMANT);
        }

        // phone 대쉬(-) 제거
        if (StringUtils.hasText(userUpdate.getCellphone())) {
            userUpdate.setCellphone(userUpdate.getCellphone().replaceAll("-", "").trim());
        }

        this.userService.validateUserInfo(sourceUser, userUpdate.getSignId(), userUpdate.getEmail(), userUpdate.getCellphone(),
                userUpdate.getEmployeeIdNum());

        // VO 셋팅
        sourceUser.setComid(comId);
        sourceUser.setUid(userId);
        sourceUser.setName(userUpdate.getName());
        if (!ObjectUtils.isEmpty(userUpdate.getSignId())) {
            sourceUser.setSignId(userUpdate.getSignId().toLowerCase());
        }

        if (!StringUtils.isEmpty(userUpdate.getEmail()) && !userUpdate.getEmail().equals(sourceUser.getEmail())) {
            // 이메일 인증 초기화
            UserEmailCertification userEmailCertification = this.userEmailCertificationPersist.readUserEmailCertification(userId);
            this.userEmailCertificationPersist.deleteUserEmailCertification(userId);
            this.adminModifyLogService.setAdminModifyLog(
                    openApiWorkerId, openApiWorkerName, comId, userId, TableName.UserEmailCertification.name(), Server.GATEWAY,
                    userEmailCertification, null
            );
        }

        if (!StringUtils.isEmpty(userUpdate.getCellphone()) && !userUpdate.getCellphone().equals(sourceUser.getCellphone())) {
            // 휴대전화 점유 인증 초기화
            UserSmsCertification userSmsCertification = this.userSmsCertificationPersist.readUserSmsCertification(userId);
            this.userSmsCertificationPersist.deleteUserSmsCertification(userId);
            this.adminModifyLogService.setAdminModifyLog(
                    openApiWorkerId, openApiWorkerName, comId, userId, TableName.UserSmsCertification.name(), Server.GATEWAY,
                    userSmsCertification, null
            );

            // 휴대전화 본인 인증 초기화
            sourceUser.setIsCertification(false);
        }

        sourceUser.setEmail(StringUtils.isEmpty(userUpdate.getEmail()) ? null : userUpdate.getEmail());
        sourceUser.setCellphone(StringUtils.isEmpty(userUpdate.getCellphone()) ? null : userUpdate.getCellphone());
        sourceUser.setPosition(StringUtils.isEmpty(userUpdate.getPosition()) ? null : userUpdate.getPosition());

        if (!ObjectUtils.isEmpty(userUpdate.getRankPositionId())) {
            List<UserCategory> userCategoryList = this.userCategoryPersist.readRankPositionList(comId);
            UserCategory rankPosition = userCategoryList.stream()
                    .filter(userCategory -> userCategory.getCateid().equals(userUpdate.getRankPositionId())).findFirst()
                    .orElse(null);

            sourceUser.setRankposition(ObjectUtils.isEmpty(rankPosition) ? null : rankPosition.getName());
        } else {
            sourceUser.setRankposition(null);
        }

        Date birthday = DateUtil.stringToDate(userUpdate.getBirthday());
        if (birthday != null) {
            sourceUser.setBirthday(birthday);
        }
        sourceUser.setSex(userUpdate.getSex());
        sourceUser.setOrgCode(userUpdate.getDivisionCode());
        sourceUser.setComidnum(StringUtils.isEmpty(userUpdate.getEmployeeIdNum()) ? null : userUpdate.getEmployeeIdNum());

        this.userPersist.createUser(sourceUser);

        // Log
        this.adminModifyLogService.setAdminModifyLog(
                openApiWorkerId, openApiWorkerName, comId, userId, TableName.User.name(), Server.GATEWAY,
                beforeUser, sourceUser
        );

        return this.getUserDetail(comId, userId);
    }

    public void updateUserStatus(String userId, Type status) {
        this.userStatusService.insertUserStatus(openApiWorkerName, userId, status);
    }

    private DivisionDto.Response.Division getOrganizationTree(String comId) {
        DivisionDto.Response.Division root = null;
        Map<Long, Division> temp = new HashMap<>();
        Map<Long, List<DivisionDto.Response.Division>> tree = new HashMap<>();

        List<MealGroup> mealGroupList = this.mealGroupPersist.readMealGroupList(comId);
        List<UserCategory> userCategoryList = this.userCategoryPersist.readRankPositionList(comId);
        List<User> companyUserList = this.userPersist.readUserListByComId(comId);

        OrganizationCache org = this.orgCacheService.getOrganization(comId);
        List<OrganizationCache> organizationCacheList = this.orgCacheService.getDivisionFullTree(org.getRootCode(), comId, true);
        if (organizationCacheList.size() == 0) {
            throw new CommonException(Errors.DIVISION_NOT_FOUND);
        }

        for (OrganizationCache organizationCache : organizationCacheList) {
            DivisionDto.Response.Division node = new DivisionDto.Response.Division();
            node.setDivisionIdx(organizationCache.getOrgIdx());
            node.setDepth(organizationCache.getDepth() - 2);
            node.setDivisionCode(organizationCache.getOrgCode());
            node.setDivisionName(organizationCache.getName());

            List<User> divisionUserList = companyUserList.stream()
                    .filter(user -> organizationCache.getOrgCode().equals(user.getOrgCode())).collect(
                            Collectors.toList());

            List<UserDto.User> divisionUserDtoList = new ArrayList<>();
            for (User user : divisionUserList) {
                UserDto.User userDto = this.convertUserToUserDto(user, mealGroupList, userCategoryList, organizationCache);
                divisionUserDtoList.add(userDto);
            }

            node.setEmployee(divisionUserDtoList);
            temp.put(organizationCache.getOrgMetaIdx(), node);

            if (comId.equals(organizationCache.getOrgCode())) {
                root = node;
            } else {
                if (!tree.containsKey(organizationCache.getParentOrgMetaIdx())) {
                    tree.put(organizationCache.getParentOrgMetaIdx(), new ArrayList<>());
                }
                tree.get(organizationCache.getParentOrgMetaIdx()).add(node);
            }
        }

        tree.forEach((parentIdx, divisionList) -> {
            if (temp.containsKey(parentIdx)) {
                temp.get(parentIdx).setDivision(divisionList);
            }
        });

        return root;
    }

    private DivisionDto.Response.DivisionV2 getOrganizationTreeV2(String orgCode) {
        DivisionDto.Response.DivisionV2 root = null;
        Map<Long, DivisionV2> temp = new HashMap<>();
        Map<Long, List<DivisionDto.Response.DivisionV2>> tree = new HashMap<>();

        OrganizationCache org = this.orgCacheService.getOrganization(orgCode);
        List<OrganizationCache> organizationCacheList =
                this.orgCacheService.getDivisionFullTree(org.getRootCode(), orgCode, true);
        if (organizationCacheList.size() == 0) {
            throw new CommonException(Errors.DIVISION_NOT_FOUND);
        }

        for (OrganizationCache organizationCache : organizationCacheList) {
            DivisionDto.Response.DivisionV2 node = new DivisionDto.Response.DivisionV2();
            node.setDepth(organizationCache.getDepth() - 2);
            node.setOrgCode(organizationCache.getOrgCode());
            node.setCustomOrgCode(organizationCache.getCustomOrgCode());
            node.setName(organizationCache.getName());

            if (node.getDepth() > 1) {
                OrganizationCache parent = organizationCacheList.stream()
                        .filter(data -> data.getOrgMetaIdx().equals(organizationCache.getParentOrgMetaIdx())).findFirst()
                        .orElse(null);

                if (ObjectUtils.isEmpty(parent)) {
                    parent = this.orgCacheService.getOrganization(organizationCache.getParentOrgMetaIdx());
                }
                node.setParentOrgCode(parent.getOrgCode());
                node.setParentCustomOrgCode(parent.getCustomOrgCode());
            }

            temp.put(organizationCache.getOrgMetaIdx(), node);
            if (orgCode.equals(organizationCache.getOrgCode())) {
                root = node;
            } else {
                if (!tree.containsKey(organizationCache.getParentOrgMetaIdx())) {
                    tree.put(organizationCache.getParentOrgMetaIdx(), new ArrayList<>());
                }
                tree.get(organizationCache.getParentOrgMetaIdx()).add(node);
            }
        }

        tree.forEach((parentIdx, divisionList) -> {
            if (temp.containsKey(parentIdx)) {
                temp.get(parentIdx).setDivision(divisionList);
            }
        });

        return root;
    }

    private int getMaxDepth(List<Division> v1) {
        if (ObjectUtils.isEmpty(v1)) {
            return 0;
        }
        Set<Integer> maxDepth = new HashSet<>();
        this.depth(v1, 1, maxDepth);
        return maxDepth.size();
    }

    private int getMaxDepthV2(List<DivisionV2> v2) {
        if (ObjectUtils.isEmpty(v2)) {
            return 0;
        }

        Set<Integer> maxDepth = new HashSet<>();
        this.depthV2(v2, 1, maxDepth);
        return maxDepth.size();
    }

    private void depth(List<Division> divisions, int depth, Set<Integer> maxDepth) {
        if (!maxDepth.contains(depth)) {
            maxDepth.add(depth);
        }
        if (!ObjectUtils.isEmpty(divisions)) {
            for (Division v1 : divisions) {
                if (!ObjectUtils.isEmpty(v1.getDivision())) {
                    depth(v1.getDivision(), depth + 1, maxDepth);
                }
            }
        }
    }

    private void depthV2(List<DivisionV2> divisions, int depth, Set<Integer> maxDepth) {
        if (!maxDepth.contains(depth)) {
            maxDepth.add(depth);
        }
        if (!ObjectUtils.isEmpty(divisions)) {
            for (DivisionV2 v2 : divisions) {
                if (!ObjectUtils.isEmpty(v2.getDivision())) {
                    depthV2(v2.getDivision(), depth + 1, maxDepth);
                }
            }
        }
    }

    private UserDto.User convertUserToUserDto(
            User user, List<MealGroup> mealGroupList, List<UserCategory> userCategoryList,
            OrganizationVo.OrganizationCache orgCache) {
        UserDto.User userDto = new UserDto.User();

        userDto.setUserId(user.getUid());
        userDto.setSignId(user.getSignId());
        userDto.setName(user.getName());
        userDto.setNickname(user.getNickname());
        userDto.setEmail(user.getEmail());
        userDto.setCellphone(user.getCellphone());
        userDto.setBirthday(user.getBirthday());
        userDto.setSex(user.getSex());
        userDto.setPosition(user.getPosition());
        userDto.setEmployeeIdNum(user.getComidnum());
        userDto.setJoinDate(user.getJoindate());

        UserDto.User.Status status = new UserDto.User.Status();
        switch (user.getStatus()) {
            case ACTIVE:
                status.setStatusCode(user.getStatus().name());
                status.setStatusName("활성");
                break;
            case INACTIVE:
                status.setStatusCode(user.getStatus().name());
                status.setStatusName("바활성");
                break;
            default:
                break;
        }
        userDto.setStatus(status);

        mealGroupList.stream()
                .filter(mealGroup -> mealGroup.getGroupIdx().equals(user.getGroupIdx())).findFirst()
                .ifPresent(a -> {
                    UserDto.User.MealGroup mealGroupInfo = new UserDto.User.MealGroup();
                    mealGroupInfo.setMealGroupId(a.getGroupIdx());
                    mealGroupInfo.setMealGroupName(a.getName());
                    userDto.setMealGroupInfo(mealGroupInfo);
                });

        UserCategory userRankPosition = userCategoryList.stream()
                .filter(userCategory -> userCategory.getName().equals(user.getRankposition())).findFirst()
                .orElse(UserCategory.builder().build());
        UserDto.User.RankPosition rankPositionInfo = new UserDto.User.RankPosition();
        rankPositionInfo.setRankPositionId(userRankPosition.getCateid());
        rankPositionInfo.setRankPositionName(userRankPosition.getName());
        userDto.setRankPositionInfo(rankPositionInfo);

        UserDto.User.Division divisionInfo = new UserDto.User.Division();
        divisionInfo.setDivisionCode(user.getOrgCode());
        divisionInfo.setDivisionName(orgCache.getName());
        userDto.setDivisionInfo(divisionInfo);

        return userDto;
    }


    /**
     * 오픈 API 부서 추가
     */
    public DivisionDto.MakeResponse makeOrganizationList(String comId, MakeRequest body) {
        Company company = companyPersist.readCompany(comId);
        if (!body.getIsCustomOrgCode()) {
            body.setCustomOrgCode(null);
        }

        Organization comOrg = this.divisionService.getOrganization(false, comId, comId);

        Organization parentOrg;
        if (!ObjectUtils.isEmpty(body.getParentOrgCode())) {
            parentOrg = this.divisionService.getOrganization(body.getIsCustomOrgCode(), body.getParentOrgCode(), comId);
        } else {
            parentOrg = comOrg;
        }

        // 부서 추가 및 정보
        DivisionV2 division = this.divisionService.addDivisionV2(comOrg, parentOrg, body);
        // settlement sync add division
        this.divisionService
                .asyncAddDivision(comId, parentOrg.getOrgCode(), division.getOrgCode(), body.getName().trim(), "openApi");

        // 정산 동기화 SQS 발송
        if (Objects.equals(Boolean.FALSE, company.getIsTest())) {
            OrganizationAdd organizationAdd = new OrganizationAdd(
                    comId,
                    division.getOrgCode(),
                    body.getName().trim(),
                    parentOrg.getOrgCode(),
                    division.getSeq()
            );
            clientOrganizationService.createOrganization("openApi", organizationAdd);
        }

        return DivisionDto.MakeResponse.from(division);
    }


    /**
     * 오픈 API 부서 전체 정보
     */
    public DivisionDto.Response getOrganizationList(String orgCode, String version) {
        DivisionDto.Response response = new DivisionDto.Response();

        if ("V1".equals(version)) {
            DivisionDto.Response.Division tree = this.getOrganizationTree(orgCode);
            response.setDepth(this.getMaxDepth(tree.getDivision()));
            response.setDivision(tree.getDivision());
        } else if ("V2".equals(version)) {
            DivisionDto.Response.DivisionV2 tree = this.getOrganizationTreeV2(orgCode);
            response.setDepth(this.getMaxDepthV2(tree.getDivision()));
            response.setDivision(tree.getDivision());
        }
        return response;
    }


    /**
     * 오픈 API 부서 상세 정보
     */
    public DivisionV2 getOrganizationDetail(Boolean isCustomOrgCode, String orgCode) {
        if (isCustomOrgCode) {
            orgCode = this.orgCacheService.getCustomOrgCode(orgCode).getOrgCode();
        }
        return this.getOrganizationTreeV2(orgCode);
    }

    /**
     * 오픈 API 부서 삭제
     */
    public void deleteOrganization(Boolean isCustomOrgCode, String comId, String orgCode) {
        if (isCustomOrgCode) {
            orgCode = this.orgCacheService.getCustomOrgCode(orgCode).getOrgCode();
        }
        this.divisionService.deleteDivision(comId, orgCode);
    }

    /**
     * 상위부서 변경시 새로 부서 생성후 기존 부서에 속한 사용자를 모두 옮긴후 기본 부서 삭제
     */
    private DivisionV2 parentOrgMovement(String comId, UpdateRequest body, Organization orgDivision) {
        Company company = companyPersist.readCompany(comId);
        Organization parent = this.divisionService.getOrganization(body.getIsCustomOrgCode(), body.getParentOrgCode(), comId);

        // 상위 부서 변경점이 없을시 단순 업데이트 로직 처리
        if (parent.getCache().getOrgMetaIdx().equals(orgDivision.getCache().getParentOrgMetaIdx())) {
            return this.updateDivision(comId, body, orgDivision);
        }

        // 1. 동일한 부서 생성
        Organization meta = this.organizationService.addDivision(parent, body.getName(), orgDivision.getCustomOrgCode());
        if (meta.getSeq().equals(1)) {
            this.orgCacheService.addChild(meta);
        } else {
            List<OrganizationCache> childList = this.orgCacheService.getDivisionChild(meta.getRootCode(), parent.getOrgCode());
            OrganizationCache cache = childList.get(childList.size() - 1);
            this.orgCacheService.addSibling(cache.getOrgMetaIdx(), meta);
        }

        // settlement sync add division
        this.divisionService
                .asyncAddDivision(comId, parent.getOrgCode(), meta.getOrgCode(), body.getName().trim(), "openApi");

        // 정산 동기화 SQS 발송
        if (Objects.equals(Boolean.FALSE, company.getIsTest())) {
            OrganizationAdd organizationAdd = new OrganizationAdd(
                    comId,
                    meta.getOrgCode(),
                    body.getName().trim(),
                    parent.getOrgCode(),
                    meta.getSeq()
            );
            clientOrganizationService.createOrganization("openApi", organizationAdd);
        }

        // 2. 기존 부서 유저 이동
        List<User> userList = this.userService.findByOrgCode(orgDivision.getOrgCode());
        if (!ObjectUtils.isEmpty(userList)) {
            userList.forEach(user -> {
                user.setOrgCode(meta.getOrgCode());
            });
            this.userPersist.save(userList);
        }

        // 3. 기존 하위 부서가 있으면 하위 부서도 옮겨준다.
        List<OrganizationCache> childDivisionList =
                this.orgCacheService.getDivisionChild(orgDivision.getRootCode(), orgDivision.getOrgCode());
        if (!ObjectUtils.isEmpty(childDivisionList)) {
            for (OrganizationCache cache : childDivisionList) {
                // 새로 옮겨진 상위부서의 orgCode를 부모orgCode로 요청
                UpdateRequest childBody = new UpdateRequest();
                childBody.setName(cache.getName());
                childBody.setIsCustomOrgCode(false);
                childBody.setParentOrgCode(meta.getOrgCode());

                Organization childOrg = this.divisionService.getOrganization(false, cache.getOrgCode(), comId);
                this.parentOrgMovement(comId, childBody, childOrg);
            }
        }

        // 4. 기존 부서 삭제 처리
        this.divisionService.deleteDivision(comId, orgDivision.getOrgCode());
        return this.getOrganizationTreeV2(meta.getOrgCode());
    }

    /**
     * 단순 부서 정보 업데이트
     */
    private DivisionV2 updateDivision(String comId, UpdateRequest body, Organization orgDivision) {
        Company company = companyPersist.readCompany(comId);
        if (orgDivision.getStatus().equals(Organization.Status.INACTIVE)) {
            throw new CommonException(Errors.DIVISION_INACTIVE);
        }

        // 부서명 변경점이 없으면 그냥 리턴
        if (orgDivision.getCache().getName().equals(body.getName())) {
            return this.getOrganizationTreeV2(orgDivision.getOrgCode());
        }

        // 부서명 중복 체크
        com.vendys.customer.entity.OrganizationCache parent = this.cachePersist
                .findByOrgMetaIdxAndActive(orgDivision.getHistory().getParentOrgMetaIdx());
        List<OrganizationCache> childList = this.orgCacheService.getDivisionChild(orgDivision.getRootCode(), parent.getOrgCode());
        if (childList.stream().anyMatch(data -> data.getName().equals(body.getName().trim())
                && !data.getOrgCode().equals(orgDivision.getOrgCode()))) {
            throw new CommonException(Errors.DIVISION_DUPLICATED);
        }

        this.organizationService.setDivisionName(orgDivision.getOrgMetaIdx(), body.getName());
        this.orgCacheService.setDivisionName(orgDivision.getOrgMetaIdx(), body.getName());
        this.divisionService.asyncUpdateDivisionName(comId, orgDivision.getOrgMetaIdx(), "openApi");

        // 정산 동기화 SQS 발송
        if (Objects.equals(Boolean.FALSE, company.getIsTest())) {
            clientOrganizationService.updateOrganization("openApi", new OrganizationUpdate(comId, orgDivision.getOrgCode(), body.getName()));
        }
        return this.getOrganizationTreeV2(orgDivision.getOrgCode());
    }


    /**
     * 오픈 API 부서 수정
     */
    public DivisionV2 updateOrganization(String comId, String orgCode, UpdateRequest body) {
        Organization orgDivision = this.divisionService.getOrganization(body.getIsCustomOrgCode(), orgCode, comId);

        if (ObjectUtils.isEmpty(body.getName())) {
            body.setName(orgDivision.getCache().getName());
        }

        if (!ObjectUtils.isEmpty(body.getParentOrgCode())) {
            return this.parentOrgMovement(comId, body, orgDivision);
        } else {
            return this.updateDivision(comId, body, orgDivision);
        }
    }

    public UserDto.User getUserDetail(String comId, String userId) {
        User user = this.userPersist.readUserByComId(comId, userId);

        if (user.getStatus().equals(Status.WITHDRAW)) {
            throw new CommonException(Errors.ACCOUNT_WITHDRAW);
        }

        if (user.getIsDormant()) {
            throw new CommonException(Errors.ACCOUNT_DORMANT);
        }

        List<MealGroup> mealGroupList = this.mealGroupPersist.readMealGroupList(comId);
        List<UserCategory> userCategoryList = this.userCategoryPersist.readRankPositionList(comId);
        OrganizationVo.OrganizationCache orgCache = this.orgCacheService.getOrganization(user.getOrgCode());

        return this.convertUserToUserDto(user, mealGroupList, userCategoryList, orgCache);
    }

    /**
     * openApi V2 특정 회사 사용자 전체 정보 조회
     */
    public UserListResponse getEmployeeV2(String comId, Integer page) {
        Page<User> userList = this.userService.getUserList(comId, page, 1000);
        Paging paging = this.getPaging(userList);
        List<UserResponse> resUsers = this.makeUserResponseList(userList.getContent());

        return UserListResponse.of(resUsers, paging);
    }

    private Paging getPaging(Page<User> userList) {
        Paging paging = new Paging();
        paging.setPage(userList.getNumber() + 1);
        paging.setPageRow(userList.getSize());
        paging.setTotalCount(userList.getTotalElements());

        return paging;
    }

    private List<UserResponse> makeUserResponseList(List<User> userList) {
        List<UserResponse> resUsers = new ArrayList<>();

        for (User user : userList) {
            MealGroup mealGroup = this.mealGroupPersist.readMealGroup(user.getGroupIdx());

            UserResponse userResponse = UserResponse.builder()
                    .userId(user.getUid())
                    .customUserId(user.getCustomUserId())
                    .signId(user.getSignId())
                    .name(user.getName())
                    .nickname(user.getNickname())
                    .email(user.getEmail())
                    .cellphone(user.getCellphone())
                    .birthday(ObjectUtils.isEmpty(user.getBirthday()) ? null : DateUtil.dateToString(user.getBirthday()))
                    .sex(user.getSex())
                    .divisionInfo(DivisionInfo.from(user.getOrganizationCache()))
                    .mealGroupInfo(MealGroupInfo.from(mealGroup))
                    .rankPosition(user.getRankposition())
                    .position(user.getPosition())
                    .comIdNum(user.getComidnum())
                    .joinDate(user.getJoindate().getTime())
                    .status(UserStatus.from(user.getStatus()))
                    .build();

            resUsers.add(userResponse);
        }

        return resUsers;
    }

    /**
     * openApi V2 특정 회사 사용자 추가
     */
    public UserResponse insertEmployeeV2(String comId, InsertUserReq body) {
        // 등록 가능한 signId 인지 체크
        if (this.userUtil.checkExcludeSignId(body.getSignId())) {
            throw new CommonException(Errors.USER_EXCLUDE_SIGNID);
        }

        // 회사 유효성 검사
        Company company = this.userCervice.validCompany(comId);

        // 식대 그룹 있는지 확인
        this.mealGroupPersist.readMealGroup(comId, body.getMealGroupId(), true, GroupType.MEAL);

        // 부서 정보
        Organization organization = this.divisionService.getOrganization(body.getIsCustomOrgCode(), body.getOrgCode(), comId);

        // 사용자 가입 여부 검사
        this.userService.existUser(comId, body.getSignId(), body.getEmail(), body.getCellphone(), body.getComIdNum());

        User user = new User();
        user.setUid(UUID.randomUUID().toString().toUpperCase());
        user.setSignId(body.getSignId().toLowerCase());
        user.setCustomUserId(body.getCustomUserId());
        user.setName(body.getName());
        user.setComid(company.getComid());
        user.setComname(company.getName());
        user.setLevel(Level.ACTIVE.level);
        user.setGrade(Grade.USER);
        user.setStatus(Status.ACTIVE);
        user.setOrgCode(organization.getOrgCode());
        user.setGroupIdx(body.getMealGroupId());
        user.setMypoint(0);
        user.setIsAgree(false);
        user.setPasswordreset("N");
        user.setJoindate(new Date());
        user.setIsDormant(false);
        user.setVersion(Version.v4);

        if (!ObjectUtils.isEmpty(body.getSex())) {
            user.setSex(body.getSex());
        }

        if (!StringUtils.isEmpty(body.getEmail())) {
            user.setEmail(body.getEmail().trim());
        }

        if (!StringUtils.isEmpty(body.getCellphone())) {
            user.setCellphone(body.getCellphone().trim().replaceAll("-", ""));
        }

        if (!StringUtils.isEmpty(body.getBirthday())) {
            Date birthday = DateUtil.stringToDate(body.getBirthday());
            if (birthday != null) {
                user.setBirthday(birthday);
            }
        }

        if (!StringUtils.isEmpty(body.getPosition())) {
            user.setPosition(body.getPosition());
        }

        if (!ObjectUtils.isEmpty(body.getRankPosition())) {
            UserCategory userCategory = this.userCategoryPersist.readRankPositionByName(comId, body.getRankPosition());

            if (ObjectUtils.isEmpty(userCategory)) {
                userCategory = this.userCategoryPersist.createRankPosition(comId, body.getRankPosition());
            }
            user.setRankposition(userCategory.getName());
        }

        if (!StringUtils.isEmpty(body.getComIdNum())) {
            user.setComidnum(body.getComIdNum());
        }
        User dbUser = this.userPersist.createUser(user);
        MealGroup mealGroup = this.mealGroupPersist.readMealGroup(dbUser.getGroupIdx());
        com.vendys.customer.entity.OrganizationCache cache = this.cachePersist
                .findByOrgMetaIdxAndActive(organization.getOrgMetaIdx());

        // 사업장 정보 조회
        List<Office> officeList = this.officePersist.getActiveOfficeList(comId);
        // 사업장 연결
        Long officeIdx = officeList.stream().findFirst().get().getOfficeIdx();
        this.userOfficeRelationPersist.createUserOffice(user.getUid(), officeIdx);
        // UserActivity 저장
        this.userActivityPersist.createUserActivity(user.getUid());
        // Log
        this.adminModifyLogService.setAdminModifyLog(
                openApiWorkerId, openApiWorkerName, company.getComid(), user.getUid(), TableName.User.name(), Server.GATEWAY,
                null, user
        );

        return UserResponse.from(dbUser, mealGroup, cache);
    }

    /**
     * openApi V2 특정 사용자 조회
     */
    public UserResponse getEmployeeDetailV2(String userId) {
        User user = this.userService.getUserByUserId(userId);
        MealGroup mealGroup = this.mealGroupPersist.readMealGroup(user.getGroupIdx());
        return UserResponse.from(user, mealGroup, user.getOrganizationCache());
    }

    /**
     * 사용자 수정
     */
    public UserResponse updateEmployeeDetailV2(String comId, String userId, InsertUserReq body) {
        // 기존 정보 조회
        User beforeUser = new User();
        User sourceUser = this.userService.getUserByUserId(userId);
        BeanUtils.copyProperties(sourceUser, beforeUser);

        // phone 대쉬(-) 제거
        if (StringUtils.hasText(body.getCellphone())) {
            body.setCellphone(body.getCellphone().replaceAll("-", "").trim());
        }

        this.userService.validateUserInfo(sourceUser, body.getSignId(), body.getEmail(), body.getCellphone(), body.getComIdNum());

        // 식대 그룹 변경
        this.resetMealGroupPoint(sourceUser, body, comId); // 초기화
        MealGroup mealGroup = this.mealGroupPersist.readMealGroup(sourceUser.getGroupIdx());
        if (!ObjectUtils.isEmpty(body.getMealGroupId())) {
            sourceUser.setGroupIdx(body.getMealGroupId()); // User 의 식대그룹 변경
            mealGroup = this.mealGroupPersist.readMealGroup(body.getMealGroupId());
        }

        if (!StringUtils.isEmpty(body.getEmail()) && !body.getEmail().equals(sourceUser.getEmail())) {
            // 이메일 인증 초기화
            UserEmailCertification userEmailCertification = this.userEmailCertificationPersist.readUserEmailCertification(userId);
            if (!ObjectUtils.isEmpty(userEmailCertification)) {
                this.userEmailCertificationPersist.deleteUserEmailCertification(userId);
                this.adminModifyLogService.setAdminModifyLog(
                        openApiWorkerId, openApiWorkerName, comId, userId, TableName.UserEmailCertification.name(),
                        Server.GATEWAY,
                        userEmailCertification, null
                );
            }
        }

        if (!StringUtils.isEmpty(body.getCellphone()) && !body.getCellphone().equals(sourceUser.getCellphone())) {
            // 휴대전화 점유 인증 초기화
            UserSmsCertification userSmsCertification = this.userSmsCertificationPersist.readUserSmsCertification(userId);
            if (!ObjectUtils.isEmpty(userSmsCertification)) {
                this.userSmsCertificationPersist.deleteUserSmsCertification(userId);
                this.adminModifyLogService.setAdminModifyLog(
                        openApiWorkerId, openApiWorkerName, comId, userId, TableName.UserSmsCertification.name(), Server.GATEWAY,
                        userSmsCertification, null
                );
            }
            // 휴대전화 본인 인증 초기화
            sourceUser.setIsCertification(false);
        }

        // VO 셋팅
        sourceUser.setName(body.getName());
        if (!ObjectUtils.isEmpty(body.getSignId())) {
            sourceUser.setSignId(body.getSignId().toLowerCase());
        }
        sourceUser.setEmail(StringUtils.isEmpty(body.getEmail()) ? null : body.getEmail());
        sourceUser.setCellphone(StringUtils.isEmpty(body.getCellphone()) ? null : body.getCellphone());
        sourceUser.setPosition(StringUtils.isEmpty(body.getPosition()) ? null : body.getPosition());

        if (!ObjectUtils.isEmpty(body.getRankPosition())) {
            UserCategory userCategory = this.userCategoryPersist.readRankPositionByName(comId, body.getRankPosition());
            if (ObjectUtils.isEmpty(userCategory)) {
                userCategory = this.userCategoryPersist.createRankPosition(comId, body.getRankPosition());
            }
            sourceUser.setRankposition(userCategory.getName());
        } else {
            sourceUser.setRankposition(null);
        }

        if (!ObjectUtils.isEmpty(body.getBirthday())) {
            sourceUser.setBirthday(DateUtil.stringToDate(body.getBirthday()));
        }
        sourceUser.setSex(body.getSex());
        sourceUser.setComidnum(StringUtils.isEmpty(body.getComIdNum()) ? null : body.getComIdNum());

        // 부서 정보
        Organization organization = this.divisionService.getOrganization(body.getIsCustomOrgCode(), body.getOrgCode(), comId);
        sourceUser.setOrgCode(organization.getOrgCode());

        User user = this.userPersist.createUser(sourceUser);

        // Log
        this.adminModifyLogService.setAdminModifyLog(
                openApiWorkerId, openApiWorkerName, comId, userId, TableName.User.name(), Server.GATEWAY, beforeUser, sourceUser
        );

        return UserResponse.from(user, mealGroup, organization.getCache());
    }

    private void resetMealGroupPoint(User sourceUser, InsertUserReq body, String comId) {
        Long sourceGroupIdx = sourceUser.getGroupIdx();
        Long targetGroupIdx = body.getMealGroupId();

        // targetGroupIdx 없거나 || 기존 그룹이랑 같으면 업데이트 할 필요X
        if (ObjectUtils.isEmpty(targetGroupIdx) || sourceGroupIdx.equals(targetGroupIdx)) {
            return;
        }

        // 존재하는 그룹인지 확인
        this.mealGroupPersist.readMealGroup(comId, targetGroupIdx, true, GroupType.MEAL);

        // 식권대장 사용중인 회사일 경우 포인트 초기화
        CaptainPaymentCompanyInfo cpmiBySikdae = this.companyInfoPersist
                .getCompanyInfoByServiceType(comId, ServiceType.SIKDAE);
        if (!ObjectUtils.isEmpty(cpmiBySikdae)
                && cpmiBySikdae.getStatus() == CaptainPaymentCompanyInfo.Status.ACTIVE) {
            this.companyPointTaskService
                    .makeMealGroupResetTask(Collections.singletonList(sourceUser), sourceGroupIdx, targetGroupIdx, comId,
                            sourceUser.getUid());
        }
    }
}
