package com.vendys.customer.service.elasticSearch;


import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.SortOrder;
import org.opensearch.client.opensearch._types.query_dsl.BoolQuery;
import org.opensearch.client.opensearch._types.query_dsl.Query;
import org.opensearch.client.opensearch.core.SearchRequest;
import org.opensearch.client.opensearch.core.SearchRequest.Builder;
import org.opensearch.client.opensearch.core.SearchResponse;
import org.opensearch.client.opensearch.core.search.Hit;

import com.vendys.customer.constant.Errors;
import com.vendys.customer.controller.user.entity.ElasticUserDto;
import com.vendys.customer.controller.user.entity.ElasticUserDto.Response;
import com.vendys.customer.entity.User;
import com.vendys.customer.exception.CommonException;
import com.vendys.customer.persist.UserPersist;
import com.vendys.customer.util.OpenSearchUtil;

@Slf4j(topic = "http")
@Service
@RequiredArgsConstructor
public class ElasticUserService {

    private final OpenSearchClient openSearchClient;
    private final UserPersist userPersist;

    @CircuitBreaker(name = "openSearchCircuitBreaker", fallbackMethod = "openSearchFallBack")
    public List<ElasticUserDto.Response> findAllByComIdAndNameLikeUsers(ElasticUserDto.Request request) {
        List<Query> queries = Lists.newArrayList(OpenSearchUtil.fieldEqSearch(request.getCompanyId(), "companyId.keyword"),
                OpenSearchUtil.fieldLikeSearch(request.getSearchName(), "name"),
                OpenSearchUtil.fieldEqSearch(false, "dormant"),
                OpenSearchUtil.fieldEqSearch("ACTIVE", "status.keyword"));

        List<Query> notQuery = new ArrayList<>();

        if (request.hasOrgCode()) {
            Query query = OpenSearchUtil.fieldEqSearch(request.getOrgCode(), "orgCode.keyword");
            queries.add(query);
        }
        if (request.getExcludeSelf()) {
            Query query = OpenSearchUtil.fieldEqSearch(request.getExcludeUserId(), "userId.keyword");
            notQuery.add(query);
        }

        Query query = BoolQuery.of(v -> v.must(queries).mustNot(notQuery))._toQuery();
        SearchRequest searchRequest = new Builder()
                .index("users")
                .query(query)
                .from(request.from())
                .size(request.getSize())
                .sort(OpenSearchUtil.fieldSort("name", SortOrder.Asc))
                .build();

        try {
            SearchResponse<ElasticUserDto.ElasticUser> response = this.openSearchClient.search(searchRequest,
                    ElasticUserDto.ElasticUser.class);
            return response.hits().hits().stream()
                    .map(Hit::source)
                    .filter(Objects::nonNull)
                    .map(Response::of)
                    .collect(Collectors.toList());
        } catch (IOException ioe) {
            log.error("ElasticUserService.findByComIdAndNameLike() Error : {} {}", ioe.getMessage(), ioe);
            throw new CommonException(Errors.ELASTIC_SEARCH_USER_ERROR);
        }
    }

    // Transactional(readOnly=true) 해도 프록시 이슈로 적용 안됨
    // CircuitBreaker 가 AspectJ 이용해서 호출할 때 해당 부분과 관련 있는 듯
    public List<ElasticUserDto.Response> openSearchFallBack(ElasticUserDto.Request request, Exception e) {
        log.warn("[OpenSearchCircuitBreaker OPEND] call fallback method, cause : {} {}", e.getMessage(), e);
        List<User> name = this.userPersist.usersByNameSearch(
                request.getCompanyId(),
                request.getSearchName(),
                request.getOrgCode(),
                request.getExcludeUserId(),
                request.getExcludeSelf(),
                request.pageable(new Sort("name"))
        );
        List<Response> collect = name.stream()
                .map(Response::of)
                .collect(Collectors.toList());
        return collect;
    }

}
