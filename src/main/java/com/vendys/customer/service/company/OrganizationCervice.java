package com.vendys.customer.service.company;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.vendys.customer.controller.company.entity.OrganizationCacheResponse;
import com.vendys.customer.controller.company.entity.OrganizationDto.OrganizationKeyInfo;
import com.vendys.customer.service.OrganizationCacheService;
import com.vendys.customer.service.OrganizationService;
import com.vendys.customer.vo.OrganizationVo.Organization;
import com.vendys.customer.vo.OrganizationVo.OrganizationCache;

/**
 * Created by jinwoo on 2018. 7. 24.
 */
@Service
@Transactional
public class OrganizationCervice {

    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private OrganizationCacheService orgCacheService;

    public List<OrganizationCache> getOrganization(String orgCode, String type, LocalDate start, LocalDate end) {
        Organization org = this.organizationService.getOrganization(orgCode);

        List<OrganizationCache> cacheList;
        if ("ALL".equals(type)) {
            cacheList = this.orgCacheService.getDivisionFullTree(org.getRootCode(), orgCode, null);
        } else {
            cacheList = this.orgCacheService.getDivisionFullTree(org.getRootCode(), orgCode, true);
        }

        if (start != null && end != null) {
            cacheList = this.orgCacheService.filterDateRange(cacheList, start, end);
        }

        return cacheList;
    }

    public List<OrganizationCacheResponse> getOrganizationCaches(String companyId) {
        Organization organization = this.organizationService.getOrganization(companyId);
        return this.orgCacheService.getOrganizationCaches(organization.getRootCode())
                .stream()
                .map(OrganizationCacheResponse::new)
                .collect(Collectors.toList());
    }

    public List<OrganizationKeyInfo> organizationToResponse(List<OrganizationCache> cacheList) {
        Map<String, List<Long>> map = new HashMap<>();
        for (OrganizationCache cache : cacheList) {
            if (map.containsKey(cache.getOrgCode())) {
                map.get(cache.getOrgCode()).add(cache.getOrgIdx());
            } else {
                map.put(cache.getOrgCode(), Arrays.asList(cache.getOrgIdx()));
            }
        }

        List<OrganizationKeyInfo> response = new ArrayList<>();
        map.forEach((code, idxList) -> {
            OrganizationKeyInfo item = new OrganizationKeyInfo();
            item.setCode(code);
            item.setIdx(idxList);
            response.add(item);
        });
        return response;
    }
}
