package com.vendys.customer.service.company;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import com.vendys.customer.constant.XCustomer;
import com.vendys.customer.controller.company.entity.CompanyDto;
import com.vendys.customer.controller.company.entity.CompanyDto.CompanyResponse;
import com.vendys.customer.controller.company.entity.CompanyDto.CompanyResponse.CaptainPayment;
import com.vendys.customer.controller.company.entity.CompanyDto.CompanyResponse.Office;
import com.vendys.customer.controller.company.entity.DivisionDto;
import com.vendys.customer.entity.CaptainPaymentCompanyInfo;
import com.vendys.customer.entity.CaptainPaymentCompanyInfo.ServiceType;
import com.vendys.customer.entity.CaptainPaymentCompanyInfo.Status;
import com.vendys.customer.entity.CaptainPaymentCompanyInfoHst;
import com.vendys.customer.entity.Company;
import com.vendys.customer.entity.CompanyBalanceInfo;
import com.vendys.customer.entity.CompanyLimit;
import com.vendys.customer.entity.CompanyShippingSpot;
import com.vendys.customer.entity.InfoHistory;
import com.vendys.customer.entity.InfoUser;
import com.vendys.customer.entity.User;
import com.vendys.customer.finance.application.ClientOrganizationService;
import com.vendys.customer.finance.domain.CreateClientOrganization;
import com.vendys.customer.persist.CaptainPaymentCompanyInfoHstPersist;
import com.vendys.customer.persist.CaptainPaymentCompanyInfoPersist;
import com.vendys.customer.persist.CompanyLimitPersist;
import com.vendys.customer.persist.CompanyPersist;
import com.vendys.customer.persist.CompanyShippingSpotPersist;
import com.vendys.customer.persist.HolidayCompanyInformationPersist;
import com.vendys.customer.persist.InfoHistoryPersist;
import com.vendys.customer.persist.OfficePersist;
import com.vendys.customer.persist.OrganizationCachePersist;
import com.vendys.customer.persist.UserPersist;
import com.vendys.customer.persist.company.dto.CreateCompanyShippingSpot;
import com.vendys.customer.persist.company.dto.ModifyCompanyFeatureSetting;
import com.vendys.customer.persist.company.dto.ModifyCompanyGeneralInformation;
import com.vendys.customer.persist.company.dto.ModifyCompanyServiceInformation;
import com.vendys.customer.persist.company.dto.ModifyCompanyShippingSpot;
import com.vendys.customer.persist.modifylog.BeforeAndAfterDataDto;
import com.vendys.customer.persist.user.entity.UserDto;
import com.vendys.customer.service.OrganizationCacheService;
import com.vendys.customer.service.OrganizationService;
import com.vendys.customer.service.SettleCompanyRemote;
import com.vendys.customer.service.entity.SettlementCompanyDto;
import com.vendys.customer.vo.OrganizationVo;

/**
 * Created by jinwoo on 2017. 9. 28.
 */
@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class CompanyService {

    private final CompanyPersist companyPersist;
    private final HolidayCompanyInformationPersist holidayCompanyInformationPersist;
    private final UserPersist userPersist;
    private final OfficePersist officePersist;
    private final OrganizationService organizationService;
    private final OrganizationCacheService organizationCacheService;
    private final OrganizationCachePersist organizationCachePersist;
    private final SettleCompanyRemote settleCompanyRemote;
    private final DivisionService divisionService;
    private final CaptainPaymentCompanyInfoPersist captainPaymentCompanyInfoPersist;
    private final CaptainPaymentCompanyInfoHstPersist captainPaymentCompanyInfoHstPersist;
    private final CompanyLimitPersist companyLimitPersist;
    private final CompanyShippingSpotPersist companyShippingSpotPersist;
    private final InfoHistoryPersist infoHistoryPersist;
    private final ClientOrganizationService clientOrganizationService;

    /**
     * 고객사 등록
     */
    public CompanyDto.CreateResponse addCompany(XCustomer customer, CompanyDto.Create body) {
        String userId = customer.getUserId();
        // 고객사 등록
        Company company = this.companyPersist.createCompany(body);
        String comId = company.getComid();

        // 식권대장 OR 복지대장 리스트화
        List<CaptainPaymentCompanyInfo> captainPaymentCompanyList = this.setCaptainPaymentList(body, company, userId);

        // 식권대장 or 복지대장 사용등록
        captainPaymentCompanyList.forEach(this.captainPaymentCompanyInfoPersist::save);

        // 사용관련 히스토리 등록
        captainPaymentCompanyList.forEach(captainPaymentRow -> {
            CaptainPaymentCompanyInfoHst captainPaymentCompanyInfoHst = this.generateCaptainPaymentCompanyInfoHst(
                    captainPaymentRow);
            this.captainPaymentCompanyInfoHstPersist.save(captainPaymentCompanyInfoHst);
        });

        // 사업장 등록
        this.officePersist.createOffice(comId, body.getName(), body.getRegion(), body.getGpslat(), body.getGpslon());

        // 조직도 초기화
        this.initOrganization(comId, company.getName());

        // 최초 부서 생성
        DivisionDto.Division basicDivision = this.divisionService.addDivision(comId, comId, "기본부서", null, true);

        // (구) 정산정보 등록
        CompanyBalanceInfo companyBalanceInfo = new CompanyBalanceInfo();
        companyBalanceInfo.setComId(comId);
        this.companyPersist.createCompanyBalanceInfo(companyBalanceInfo);

        if (!company.getIsTest()) {
            // 세틀먼트 동기화
            this.syncSettlementAddCompany(comId, basicDivision.getOrgCode(), body.getUserName());

            // 정산 동기화 SQS 발송
            CreateClientOrganization createClientOrganization = syncClientOrganzation(comId, basicDivision.getOrgCode());
            clientOrganizationService.createClientOrganization(customer.getUserId(), createClientOrganization);
        }

        CompanyDto.CreateResponse response = new CompanyDto.CreateResponse();
        response.setCompanyId(comId);
        response.setRegDate(company.getRegdate());
        return response;
    }

    /**
     * 고객사 배송지 추가
     * @param companyId
     * @param createCompanyShippingSpot
     * @param adminId
     * @return
     */
    public BeforeAndAfterDataDto<CompanyShippingSpot> createCompanyShippingSpot(String companyId, CreateCompanyShippingSpot createCompanyShippingSpot, String adminId) {
        return companyShippingSpotPersist.createCompanyShippingSpot(companyId, createCompanyShippingSpot, adminId);
    }

    /**
     * 고객사 일반 정보 수정
     * @param companyId
     * @param modifyCompanyGeneralInformation
     */
    public BeforeAndAfterDataDto<Company> updateCompanyGeneralInformation(String companyId, ModifyCompanyGeneralInformation modifyCompanyGeneralInformation) {
        userPersist.userBulkUpdateByCompany(companyId, new UserDto.BulkModifyCompanyDTO(modifyCompanyGeneralInformation.getName()));

        BeforeAndAfterDataDto<Company> modifyLog = companyPersist.updateCompanyGeneralInformation(companyId, modifyCompanyGeneralInformation);

        officePersist.updateCompanyGeneralInformation(companyId, modifyCompanyGeneralInformation);

        organizationCachePersist.updateCompanyGeneralInformation(companyId, modifyCompanyGeneralInformation);

        holidayCompanyInformationPersist.initializeCompanyHolidayIfNotExist(companyId);

        return modifyLog;
    }

    /**
     * 고객사 서비스 정보 수정
     * @param companyId
     * @param modifyCompanyServiceInformation
     * @param updateUser
     * @return
     */
    public BeforeAndAfterDataDto<Company> updateCompanyServiceInformation(String companyId, ModifyCompanyServiceInformation modifyCompanyServiceInformation, String updateUser) {
        BeforeAndAfterDataDto<Company> companyBeforeAndAfterDataDto = companyPersist.updateCompanyServiceInformation(companyId, modifyCompanyServiceInformation);

        captainPaymentCompanyInfoPersist.updateCompanyServiceInformation(companyId, modifyCompanyServiceInformation, updateUser);

        return companyBeforeAndAfterDataDto;
    }// updateCompanyServiceInformation

    /**
     * 고객사 서비스 설정 수정
     * @param companyId
     * @param modifyCompanyFeatureSetting
     * @return
     */
    public BeforeAndAfterDataDto<CompanyLimit> updateCompanyFeatureSetting(String companyId, ModifyCompanyFeatureSetting modifyCompanyFeatureSetting) {
        return companyLimitPersist.updateCompanyFeatureSetting(companyId, modifyCompanyFeatureSetting);
    }// updateCompanyServiceSetting

    /**
     * 고객사 배송지 수정
     * @param spotKey
     * @param modifyCompanyShippingSpot
     * @param updateUser
     * @return
     */
    public BeforeAndAfterDataDto<CompanyShippingSpot> updateCompanyShippingSpot(String spotKey, ModifyCompanyShippingSpot modifyCompanyShippingSpot, String updateUser) {
        return companyShippingSpotPersist.updateCompanyShippingSpot(spotKey, modifyCompanyShippingSpot, updateUser);
    }

    /**
     * 고객사 배송지 삭제
     * @param spotKey
     * @param updateUser
     * @return
     */
    public BeforeAndAfterDataDto<CompanyShippingSpot> deleteCompanyShippingSpot(String spotKey, String updateUser) {
        return companyShippingSpotPersist.deleteCompanyShippingSpot(spotKey, updateUser);
    }

    public void bulkDeleteUser(String comId, String updateUser) {
        List<User> users = userPersist.findByComidAndStatusNot(comId, User.Status.WITHDRAW);

        for (User user : users) {
            InfoHistory infoHistory = new InfoHistory();

            infoHistory.setOrgCode(user.getComid());
            infoHistory.setCreatedId(updateUser);
            infoHistory.setReserved(new Date());
            infoHistory.setCreated(new Date());
            infoHistory.setStatus(InfoHistory.Status.READY);

            infoHistoryPersist.saveInfoHistory(infoHistory);
            infoHistoryPersist.insertInfoUser(infoHistory.getInfoHistoryIdx(), InfoUser.Type.WITHDRAW, user.getUid());
        }


    }

    private CaptainPaymentCompanyInfo generateCaptainPaymentCompanyInfo(
            ServiceType serviceType, boolean enable, Company company, String userId) {
        CaptainPaymentCompanyInfo.Status cpmStatus = Status.DISABLED;
        if (!ObjectUtils.isEmpty(enable)
                && enable) {
            cpmStatus = Status.ACTIVE;
        }
        LocalDateTime now = LocalDateTime.now();
        return CaptainPaymentCompanyInfo
                .builder()
                .company(company)
                .serviceType(serviceType)
                .status(cpmStatus)
                .updateDate(now)
                .createDate(now)
                .updateUser(userId)
                .createUser(userId)
                .build();
    }

    private CaptainPaymentCompanyInfoHst generateCaptainPaymentCompanyInfoHst(CaptainPaymentCompanyInfo companyInfo) {
        return CaptainPaymentCompanyInfoHst
                .builder()
                .captainPaymentCompanyInfoIdx(companyInfo.getIdx())
                .company(companyInfo.getCompany())
                .serviceType(companyInfo.getServiceType())
                .status(companyInfo.getStatus())
                .updateDate(companyInfo.getUpdateDate())
                .createDate(companyInfo.getCreateDate())
                .updateUser(companyInfo.getUpdateUser())
                .createUser(companyInfo.getCreateUser())
                .historyDate(LocalDateTime.now())
                .build();
    }

    /**
     * 고객사 등록
     */
    public CompanyDto.CompanyResponse getCompany(final String comId) {

        Company company = companyPersist.readCompany(comId);

        CompanyDto.CompanyResponse.Info info = new CompanyDto.CompanyResponse.Info();
        info.setCompanyId(company.getComid());
        info.setName(company.getName());
        info.setBizName(company.getBizname());
        info.setBizSerial(company.getBizserial());
        info.setAddress(company.getAddress());
        info.setRegion(company.getRegion());
        info.setChargeName(company.getChargename());
        info.setPhone(company.getPhone());
        info.setIntro(company.getIntro());
        info.setEmployNum(company.getEmploynum());
        info.setRegDate(company.getRegdate());
        info.setCalculateDate(company.getCalculatedate());
        info.setDepositMethod(company.getDepositmethod());

        CompanyLimit companyLimit = companyPersist.readCompanyLimit(comId);

        CompanyDto.CompanyResponse.Types types = new CompanyDto.CompanyResponse.Types();
        types.setVersion(company.getVersion());
        types.setTicketFormat(companyLimit.getIsTicketFormat());
        types.setCafeteriaType(companyLimit.getCafeteriaType().name());

        CompanyDto.CompanyResponse.Conditions conditions = new CompanyDto.CompanyResponse.Conditions();
        conditions.setStatus(company.getStatus());
        conditions.setUseMyPoint(companyLimit.getMealcUsemypoint());
        conditions.setMultiPay(companyLimit.getMealcMultipay());
        conditions.setMultiEntrust(companyLimit.getMealcMultientrust());
        conditions.setMultiMenu(companyLimit.getMealmultimenu());
        conditions.setCafeteria(companyLimit.getCafeteria());
        conditions.setCompanyNotice(companyLimit.getCompanyNotice());
        conditions.setCashReceipt(companyLimit.getIsCashReceipt());
        conditions.setTimeDisplayed(companyLimit.getIsTimeDisplayed());
        conditions.setForceEntrust(companyLimit.getIsForceEntrust());
        conditions.setInvoice(companyLimit.getIsInvoice());
        conditions.setAdminAuth(companyLimit.getIsAdminAuth());
        conditions.setPrintBarcode(companyLimit.getIsPrintBarcode());
        conditions.setQrCodeScan(companyLimit.getIsQrCodeScan());
        conditions.setPrivacy(companyLimit.getIsPrivacy());
        conditions.setSettlement(companyLimit.getIsSettlement());
        conditions.setBudget(companyLimit.getIsBudget());
        conditions.setSnack(companyLimit.getIsSnack());
        conditions.setDemandPos(companyLimit.getIsDemandPos());
        conditions.setMarket(companyLimit.getIsMarket());
        conditions.setEvent(companyLimit.getIsEvent());
        conditions.setIsPaymentReason(companyLimit.getIsPaymentReason());
        conditions.setPolicyOrder(companyLimit.getPolicyOrder());
        conditions.setIsEzwelWelfare(companyLimit.getIsEzwelWelfare());
        conditions.setIsCompanyCertificationRequired(companyLimit.getIsCompanyCertificationRequired());
        conditions.setIsFoodMall(companyLimit.getIsFoodMall());

        CompanyDto.CompanyResponse response = new CompanyDto.CompanyResponse();
        response.setInfo(info);
        response.setTypes(types);
        response.setConditions(conditions);

        List<com.vendys.customer.entity.Office> officeList = this.officePersist.getActiveOfficeList(comId);
        List<Office> offices = new ArrayList<>();
        for (com.vendys.customer.entity.Office office : officeList) {
            Office officeVo = new Office();
            officeVo.setOfficeIdx(office.getOfficeIdx());
            officeVo.setOrgCode(office.getOrgCode());
            officeVo.setGpslat(office.getGpslat());
            officeVo.setGpslon(office.getGpslon());
            offices.add(officeVo);
        }
        response.setOfficeList(offices);

        CaptainPaymentCompanyInfo sikdaeCpmInfo = this.captainPaymentCompanyInfoPersist
                .getCompanyInfoByServiceType(comId, ServiceType.SIKDAE);
        Boolean useSikdae = false;
        if (!ObjectUtils.isEmpty(sikdaeCpmInfo) && sikdaeCpmInfo.getStatus() == Status.ACTIVE) {
            useSikdae = true;
        }

        CaptainPaymentCompanyInfo welfareCpmInfo = this.captainPaymentCompanyInfoPersist
                .getCompanyInfoByServiceType(comId, ServiceType.WELFARE);
        Boolean useWelfare = false;
        if (!ObjectUtils.isEmpty(welfareCpmInfo) && welfareCpmInfo.getStatus() == Status.ACTIVE) {
            useWelfare = true;
        }

        CompanyResponse.CaptainPayment captainPayment = new CaptainPayment();
        captainPayment.setUseSikdae(useSikdae);
        captainPayment.setUseWelfare(useWelfare);
        response.setCaptainPayment(captainPayment);

        return response;
    }

    private void initOrganization(String comId, String name) {
        OrganizationVo.Organization group = this.organizationService.initGroup(name);
        this.syncOrganizationCache(group.getOrgCode());

        OrganizationVo.Organization company = this.organizationService.initCompany(group, comId, name);
        this.syncOrganizationCache(company.getOrgCode());
    }

    private void syncOrganizationCache(String orgCode) {
        this.organizationCacheService.addChild(this.divisionService.getOrganization(false, orgCode, null));
    }

    /**
     * Created by huidragon 2019.04.18
     * settlement sync add company
     */
    private void syncSettlementAddCompany(String companyId, String basicDivisionId, String userName) {

        OrganizationVo.OrganizationCache companyCacheInfo = this.organizationCacheService.getOrganization(companyId);
        OrganizationVo.OrganizationCache groupCacheInfo = this.organizationCacheService
                .getOrganization(companyCacheInfo.getRootCode());

        String userId = this.settleCompanyRemote.getUserId();

        SettlementCompanyDto.AddCompanyRequest r
                = new SettlementCompanyDto.AddCompanyRequest(
                groupCacheInfo.getOrgCode(),
                groupCacheInfo.getName(),
                companyCacheInfo.getOrgCode(),
                companyCacheInfo.getName(),
                basicDivisionId,
                userId,
                userName);

        this.settleCompanyRemote.createCompanyBasicInfo(r);
    }

    private CreateClientOrganization syncClientOrganzation(String companyId, String basicDivisionId) {
        OrganizationVo.OrganizationCache companyCacheInfo = this.organizationCacheService.getOrganization(companyId);
        OrganizationVo.OrganizationCache groupCacheInfo = this.organizationCacheService
                .getOrganization(companyCacheInfo.getRootCode());

        return new CreateClientOrganization(
                companyCacheInfo.getOrgCode(),
                companyCacheInfo.getName(),
                groupCacheInfo.getOrgCode(),
                groupCacheInfo.getName(),
                basicDivisionId,
                "기본부서"
        );
    }

    private List<CaptainPaymentCompanyInfo> setCaptainPaymentList(CompanyDto.Create body, Company company, String userId) {
        List<CaptainPaymentCompanyInfo> captainPaymentCompanyList = new ArrayList<>();
        // 식권대장
        captainPaymentCompanyList.add(
                this.generateCaptainPaymentCompanyInfo(ServiceType.SIKDAE, body.getSikdaeEnable(), company, userId));
        // 복지대장
        if (!ObjectUtils.isEmpty(body.getWelfareEnable()) && body.getWelfareEnable()) {
            captainPaymentCompanyList.add(
                    this.generateCaptainPaymentCompanyInfo(ServiceType.WELFARE, body.getWelfareEnable(), company, userId));
        }

        return captainPaymentCompanyList;
    }

}
