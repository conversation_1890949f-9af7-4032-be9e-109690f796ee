package com.vendys.customer.interceptor;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Enumeration;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import org.slf4j.MDC;

import com.vendys.customer.constant.XCustomer;

/**
 * Created by jinwo<PERSON> on 2017. 10. 21.
 */
@Component
public class LogstashLogging {

    public void preHandle(HttpServletRequest request) {

        this.setAppGroupMdc(request);
        this.setHttpReuqestGroupMdc(request);
        this.setHttpRemoteGroupMdc(request);
    }

    public void afterCompletion(HttpServletRequest request, HttpServletResponse response) {

        this.setHttpResponseGroupMdc(request, response);
        this.setUaMdc(request);
    }

    private void setAppGroupMdc(HttpServletRequest request) {
        MDC.put("app-transaction", String.valueOf(System.currentTimeMillis()));
        MDC.put("app-installation",
                ObjectUtils.isEmpty(request.getHeader("app-installation")) ? "" : request.getHeader("app-installation"));
        MDC.put("app-timestamp",
                LocalDateTime.now().atZone(ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    }

    private void setHttpReuqestGroupMdc(HttpServletRequest request) {

        MDC.put("http-request-uri",
                ObjectUtils.isEmpty(request.getAttribute("uri")) ? "" : request.getAttribute("uri").toString()
        );
        MDC.put("http-request-method", request.getMethod());

        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            MDC.put("http-request-header." + headerName, headerValue);
        }
        MDC.put("http-request-body",
                ObjectUtils.isEmpty(request.getAttribute("requestBody"))
                        ? "" : request.getAttribute("requestBody").toString()
        );
    }

    private void setHttpResponseGroupMdc(HttpServletRequest request, HttpServletResponse response) {

        MDC.put("http-response-time", String.valueOf(this.getRuntime(request)));
        MDC.put("http-response-status", String.valueOf(response.getStatus()));
        MDC.put("http-response-body", "");
    }

    private void setHttpRemoteGroupMdc(HttpServletRequest request) {

        MDC.put("http-remote-ip", request.getRemoteHost());
    }

    private void setUaMdc(HttpServletRequest request) {

        XCustomer header = (XCustomer)request.getAttribute("xCustomer");
        MDC.put("ua-user-id", header.getUserId());
        MDC.put("ua-header-client", header.getClient().getType());
        MDC.put("ua-header-version", header.getVersion());
    }

    private long getRuntime(HttpServletRequest request) {
        return System.currentTimeMillis() - (long)request.getAttribute("ts");
    }

    private String getHeaderParse(HttpServletRequest request) {

        StringBuffer sb = new StringBuffer();
        Enumeration headers = request.getHeaderNames();
        while (headers.hasMoreElements()) {

            String headerName = (String) headers.nextElement();
            String value = request.getHeader(headerName);

            sb.append(headerName);
            sb.append(": ");
            sb.append(value);
            sb.append("\n");
        }
        return sb.toString();
    }
}
