package com.vendys.customer.constant;

import java.util.List;

import org.springframework.util.ObjectUtils;

import lombok.Data;

import com.vendys.customer.exception.CommonException;

@Data
public class VendysIam {

    private ClientType clientType; // STORE, COMPANY, USER
    private String clientId;
    private ResourceInfo resourceInfo;
    private List<Service> serviceList;

    @Data
    public static class ResourceInfo {

        private ResourceType resourceType;
        private String resourceCodeType;
        private String resourceId;
    }

    @Data
    public static class Service {

        private String serviceName; // 각 application의 vendys.applicationName
        private List<ServiceFunction> serviceFunctionList;

        @Data
        public static class ServiceFunction {

            private String functionMethod;
            private String functionName;
        }
    }

    public enum ClientType {
        STORE, COMPANY, USER, UNKNOWN
    }

    public enum ResourceType {
        FIXED, VARIABLE
    }

    public static String getFixedResource(VendysIam vendysIam) {
        if (ResourceType.VARIABLE.equals(vendysIam.getResourceInfo().getResourceType())) {
            // 가변형 리소스는 실제 수행되는 API에서 리소스를 취득한다.
            throw new CommonException(Errors.GENERAL_INVALID_IAM_HEADER);
        }

        String currentResourceId = vendysIam.getResourceInfo().getResourceId();

        if (ObjectUtils.isEmpty(currentResourceId)) {
            throw new CommonException(Errors.GENERAL_INVALID_CLIENTSRESOURCE);
        }

        return currentResourceId;
    }
}
