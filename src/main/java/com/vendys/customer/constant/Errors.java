package com.vendys.customer.constant;

import lombok.Getter;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2017. 9. 25..
 */
@Getter
public enum Errors {
    UNKNOWN_ERROR(-0,"에러", "UNKNOWN_ERROR"),
    GENERAL_WRONG_PARAM(-11,"에러", "Wrong Parameters"),

    GENERAL_WARNING_CONTENT(-251, "에러", "작성하신 내용에 금칙어가 포함되어 있어 등록하실 수 없습니다. (금칙어:%금칙어리스트%)"),

    GENERAL_UNKNOWN(-1000, "에러", "Unknown Error"),
    GENERAL_INVALID_HEADER(-1001, "에러", "header 정보가 올바르지 않습니다."),
    GENERAL_INVALID_BODY(-1002, "에러", "body 정보가 올바르지 않습니다."),
    GENERAL_INVALID_PARAM(-1003, "에러", "param 정보가 올바르지 않습니다."),
    GENERAL_INVALID_PATH(-1004, "에러", "path 정보가 올바르지 않습니다."),
    GENERAL_INVALID_ENUMTYPE(-1005, "에러", "type 정보가 올바르지 않습니다."),
    GENERAL_INVALID_IAM_HEADER(-1006, "에러", "iam 정보가 올바르지 않습니다."),
    GENERAL_INVALID_CLIENTSRESOURCE(-1007, "에러", "ClientsResource 정보가 올바르지 않습니다."),

    ACCOUNT_UNKNOWN_USER(-1100, "에러", "사용자 정보를 찾을 수 없습니다."),
    ACCOUNT_INACTIVE(-1101, "에러", "비활성화 된 사용자 입니다."),
    ACCOUNT_WITHDRAW(-1102, "에러", "해당 사용자는 식권대장 서비스에서 탈퇴 상태의 사용자로 정보를 조회 하실수 없습니다."),
    ACCOUNT_DORMANT(-1103, "에러", "해당 사용자는 식권대장 서비스에서 휴면 상태의 사용자로 정보를 조회 하실수 없습니다."),

    ACCOUNT_UNKNOWN_OTP(-1200, "에러", "OTP 정보가 없습니다"),
    ACCOUNT_VALID_OTP(-1201, "에러", "OTP 형식에 맞지 않습니다"),

    MEMBER_SIGNUP_GROUP_ERROR(-1300, "에러", "식대 그룹이 존재 하지 않습니다."),
    MEMBER_MODIFY_GROUP_ERROR(-1301, "에러", "식대 그룹은 필수 정보입니다."),
    MEMBER_SIGNUP_WELFARE_GROUP_ERROR(-1302, "에러", "복지 그룹이 존재 하지 않습니다."),
    MEMBER_MODIFY_WELFARE_GROUP_ERROR(-1303, "에러", "복지 그룹은 필수 정보입니다."),

    OFFICE_NULL_ERROR(-1300, "에러", "사업장이 존재 하지 않습니다."),
    NO_USER_OFFICE(-1301, "에러", "사용자의 사업장이 존재하지 않습니다."),
    ORGANIZATION_INVALID(-2001, "에러", "조직도 기능을 사용하지 않는 고객사입니다."),
    ORGANIZATION_NOT_FOUND(-2002, "에러", "조직도 정보가 올바르지 않습니다."),

    DIVISION_WRONG_PARAMETER(-2003, "에러", "부서 요청 정보가 올바르지 않습니다."),
    DIVISION_NOT_FOUND(-2004, "에러", "부서 정보가 없습니다."),
    DIVISION_LACK_PARAMETER(-2005, "에러", "부서 정보가 부족합니다."),
    DIVISION_PERMISSION(-2006, "에러", "변경 권한이 없습니다."),
    DIVISION_DELETE_SUB(-2010, "에러", "하위 부서가 존재하여 삭제할 수 없습니다."),
    DIVISION_DELETE_USER(-2011, "에러", "부서에 속한 사용자가 존재하여 삭제할 수 없습니다."),
    DIVISION_INACTIVE(-2012, "에러", "삭제된 부서 입니다."),
    DIVISION_DUPLICATED(-2013, "에러", "중복된 부서가 존재합니다."),
    DIVISION_CUSTOM_CODE_DUPLICATED(-2014, "에러", "중복된 고객사 부서 코드가 존재합니다."),

    DIVISION_CACHE_SYNC(-2100, "에러", "부서 동기화 중 에러 발생."),

    USER_UNKNOWN(-2500, "에러","사용자 정보가 올바르지 않습니다."),
    USER_STATUS_ACTIVE_FAIL(-2501, "에러","활성 사용자로 일시정지 해제할 수 없습니다."),
    USER_STATUS_INACTIVE_FAIL(-2502, "에러","이미 일시정지된 사용자입니다."),
    USER_STATUS_WITHDRAW_FAIL(-2503, "에러","이미 탈퇴된 사용자입니다."),
    USER_STATUS_UNKNOWN(-2504, "에러","사용자 상태변경 값이 존재하지 않습니다."),
    USER_IS_DORMANT(-2505,  "에러", "이미 휴면 사용자입니다."),
    USER_IS_RESTORE(-2506,  "에러", "이미 휴면 복구 된 사용자입니다."),

    AUTH_PRESET_NOT_FOUND(-3000, "에러", "관리자 권한 정보가 올바르지 않습니다."),
    AUTH_NOT_FOUND(-3001, "에러", "관리자 권한이 없는 사용자 입니다."),
    AUTH_DUPLICATED(-3002, "에러", "이미 관리자 권한을 가진 사용자입니다. 중복으로 권한을 지정할 수 없습니다."),

    COMPANY_NOT_FOUND(-3100, "에러", "고객사 정보가 없습니다."),
    COMPANY_LIMIT_NOT_FOUND(-3101, "에러", "고객사 제한 정보가 없습니다."),

    STORE_NOT_FOUND(-3200, "에러", "제휴점 정보가 없습니다."),

    USER_EXIST_SIGNID(-4000, "에러", "이미 사용 중인 아이디 입니다."),
    USER_EXIST_SIGNID_IN_DORMANT(-4005, "에러", "휴면 사용자가 이미 사용중인 아이디 입니다."),
    USER_EXIST_EMAIL(-4001, "에러", "이미 사용 중인 이메일 입니다."),
    USER_EXIST_PHONE(-4002, "에러", "이미 사용 중인 폰번호 입니다."),
    USER_EXIST_COMIDNUM(-4003, "에러", "이미 사용 중인 사원번호 입니다."),
    USER_EXCLUDE_SIGNID(-4004, "에러", "아이디는 admin, manager, root, sys 를 포함하여 만들 수 없습니다."),
    USER_GROUP_IDX_NULL(-4005, "에러", "식대그룹 정보가 없습니다."),
    USER_WELFARE_GROUP_IDX_NULL(-4006, "에러", "복지그룹 정보가 없습니다."),

    MSA_INTERFACE_ERROR(-9000, "에러", "외부 서비스 연동에 실패 했습니다"),
    AUTH_SERVICE_PASSWORD_RESET_ERROR(-9100, "에러", "비밀번호 설정에 실패 하였습니다"),
    INFOHISTORY_STATUS_FAIL(-9101, "에러", "사용자 상태 변경처리를 실패 하였습니다"),
    USER_CI_ERROR(-9102, "에러", "CI 획득에 실패했습니다."),
    NO_USER_CI(-9103, "에러", "CI 정보가 존재하지 않습니다."),

    CRYPTO_MODULE_INIT_ERROR(-9200, "에러", "올바른 네이버 암호화 모듈 값을 설정해 주세요."),
    ENCRYPT_ERROR(-9201, "에러", "암복호화 모듈 암호화에 실패했습니다."),
    ENCRYPT_NOT_EMPTY(-9202, "에러", "잘못된 암호화 시도입니다."),
    DECRYPT_ERROR(-9203, "에러", "암복호화 모듈 복호화에 실패했습니다."),
    MSA_DATA_EMPTY(-9301, "에러", "정보가 존재하지 않습니다."),

    NAVER_GATEWAY_ERROR(-9400, "에러", "네이버 연동 오류 입니다. 지속적으로 발생할 경우, 고객센터로 문의해주세요."),
    NAVER_SERVICE_ERROR(-9401, "에러", "네이버 서비스 오류 입니다. 지속적으로 발생할 경우, 고객센터로 문의해주세요."),
    NAVER_PARAM_ERROR(-9402, "에러", "네이버 서비스 연동 오류 입니다. 지속적으로 발생할 경우, 고객센터로 문의해주세요.."),
    NAVER_ACCOUNTS_ERROR(-9403, "에러", "네이버 회원이 아닙니다. 회원가입 후 다시 시도해 주시길 바랍니다."),
    NAVER_PAY_ACCOUNTS_ERROR(-9403, "에러", "네이버페이 회원이 아닙니다. 네이버페이 회원가입 후 다시 시도해 주시길 바랍니다."),
    NAVER_ACCOUNTS_STATUS_ERROR(-9405, "에러", "네이버 회원이 상태가 비정상입니다. 확인 후 다시 시도해 주시길 바랍니다."),
    NAVER_PAY_ACCOUNTS_STATS_ERROR(-9406, "에러", "네이버페이 회원 상태가 비정상입니다. 확인 후 다시 시도해 주시길 바랍니다."),
    NAVER_REMOTE_ACCOUNTS_ERROR(-9410, "에러", "네이버 회원정보를 가져오는데 실패했습니다."),
    ELASTIC_SEARCH_USER_ERROR(-9510, "에러", "회원 검색중 오류가 발생했습니다."),

    TEST(-9999, "에러", "임시 에러");

    private int code;
    private String title;
    private String message;

    Errors(int code, String title, String message) {
        this.code = code;
        this.title = title;
        this.message = message;
    }
}
