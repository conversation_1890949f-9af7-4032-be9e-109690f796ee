package com.vendys.customer.thirdparty.ezwel.code;

import java.util.Arrays;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EzwelPrivacyAgreeCode {
    //### interfaceID : INTER000015
    EZWEL_SUCCESS(1000, "작업성공"),
    EZWEL_CLIENT_ID_NOT_EXISTS_ERROR(2001,"고객사코드(CLIENT_CD) 존재하지 않은 경우"),
    EZWEL_INTER_COM_NOT_EXISTS_ERROR(2003,"INTER_COM 존재하지 않은 경우"),
    EZWEL_UID_NOT_EXISTS_ERROR(2005,"UID 고객이 존재하지 않은 경우"),
    EZWEL_PRIVACYAGREE_NOT_EXISTS_ERROR(2006,"3자정보제공동의 코드가 존재하지 않은 경우"),
    EZWEL_PRIVACYAGREE_WORKING_ERROR(2007,"고객 3자정보제공동의 변경 처리 중 오류"),
    EZWEL_USER_STATUS_COED_NOT_EXISTS_ERROR(2008,"요청 고객 상태 코드가 존재하지 않은 경우"),
    EZWEL_USER_STATUS_COED_WORKING_ERROR(2009,"고객 상태 코드가 변경 처리 중 오류"),
    EZWEL_ACCESS_DENIED_ERROR(9997,"접근 불가한  IP인 경우(사전에 등록된 아이피만 허용)"),
    EZWEL_UNKNOW_ERROR(9999,"그외 기타시스템 오류");

    private final int code;
    private final String desc;

    public static EzwelPrivacyAgreeCode findByCode(String code) {
        return Arrays.stream(EzwelPrivacyAgreeCode.values())
                .filter(r -> r.getCode() == Integer.parseInt(code))
                .findFirst().orElse(EZWEL_UNKNOW_ERROR);
    }
}
