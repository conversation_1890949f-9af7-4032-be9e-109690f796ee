package com.vendys.customer.thirdparty.ezwel.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import com.vendys.customer.thirdparty.ezwel.code.EzwelPrivacyAgreeCode;
import com.vendys.customer.thirdparty.ezwel.code.EzwelUserStatusCode;

public class EzewlWelfareRemoteDto {

    @Getter @Setter
    public static class EzwelRequestCommonProperties {
        @JsonProperty("INTER_KEY")
        private String interKey;
        @JsonProperty("INTER_COM")
        private String interCom;
        @JsonProperty("CLIENT_CD")
        private String clientCd;
        @JsonProperty("REQ_DT")
        private String reqDt;
    }
    @Getter @Setter
    public static class EzwelResponseCommonProperties {
        @JsonProperty("INTER_COM")
        private String interCom;
        @JsonProperty("CLIENT_CD")
        private String clientCd;
        @JsonProperty("PROC_DT")
        private String procDt;
        @JsonProperty("PROC_CD")
        private String procCd;
    }

    //### 개인정보 처리방침 Request or Response
    @Getter @Builder
    public static class PrivacyAgreeRemoteRequestDto {
        @JsonProperty("INTER_INFO")
        private PrivacyAgreeReqeust interInfo;
    }

    @Getter @Setter
    public static class PrivacyAgreeRemoteResponseDto {
        @JsonProperty("INTER_INFO_RESULT")
        private PrivacyAgreeResponse interInfoResult;
    }

    @Getter @Setter
    public static class PrivacyAgreeReqeust extends EzwelRequestCommonProperties {
        @JsonProperty("UID")
        private String uid;
        @JsonProperty("USER_STATUS")
        private String userStatus;
        @JsonProperty("THIRD_PARTY_INFO_PROVISION")
        private String thirdPartyInfoProvision;

        public static PrivacyAgreeReqeust of(PrivacyAgree privacyAgree) {
            PrivacyAgreeReqeust privacyAgreeReqeust = new PrivacyAgreeReqeust();
            privacyAgreeReqeust.setInterKey(privacyAgree.getInterKey());
            privacyAgreeReqeust.setInterCom(privacyAgree.getInterCom());
            privacyAgreeReqeust.setClientCd(privacyAgree.getClientCd());
            privacyAgreeReqeust.setReqDt(privacyAgree.getReqDt());
            privacyAgreeReqeust.setUid(privacyAgree.getUid());
            privacyAgreeReqeust.setUserStatus(privacyAgree.getUserStatus());
            privacyAgreeReqeust.setThirdPartyInfoProvision(privacyAgree.getThirdPartyInfoProvision());
            return privacyAgreeReqeust;
        }
    }

    @Getter @Setter
    public static class PrivacyAgreeResponse extends EzwelResponseCommonProperties {
        @JsonProperty("UID")
        private String uid;
        @JsonProperty("USER_STATUS")
        private String userStatus;
        @JsonProperty("THIRD_PARTY_INFO_PROVISION")
        private String thirdPartyInfoProvision;
    }

    @Getter @Setter
    public static class EzwelPrivacyRemoteResponseDto {
        @JsonProperty("INTER_INFO_RESULT")
        private PrivacyAgreeResponse interInfoResult;
        private EzwelPrivacyAgreeCode code = EzwelPrivacyAgreeCode.EZWEL_UNKNOW_ERROR;
    }


    //### 회원상태 Request or Response
    @Getter @Builder
    public static class UserStatusRemoteRequestDto {
        @JsonProperty("INTER_INFO")
        private UserStatusReqeust interInfo;

        public static UserStatusRemoteRequestDto of(EzwelWelfareUserStatus ezwelWelfareUserStatus) {
            UserStatusReqeust userRquest = new UserStatusReqeust();
            userRquest.setInterKey(ezwelWelfareUserStatus.getInterKey());
            userRquest.setClientCd(ezwelWelfareUserStatus.getClientCd());
            userRquest.setInterCom(ezwelWelfareUserStatus.getInterCom());
            userRquest.setReqDt(ezwelWelfareUserStatus.getReqDt());
            userRquest.setUid(ezwelWelfareUserStatus.getUid());
            return UserStatusRemoteRequestDto.builder().interInfo(userRquest).build();
        }
    }

    @Getter @Setter
    public static class UserStatusRemoteResponseDto {
        @JsonProperty("INTER_INFO_RESULT")
        private UserStatusResponse interInfoResult;
    }

    @Getter @Setter
    public static class UserStatusResponseDto {
        private EzwelUserStatusCode code = EzwelUserStatusCode.EZWEL_UNKNOW_ERROR;
    }

    @Getter @Setter
    public static class UserStatusReqeust extends EzwelRequestCommonProperties {
        @JsonProperty("UID")
        private String uid;
    }

    @Getter @Setter
    public static class UserStatusResponse extends EzwelResponseCommonProperties {
        @JsonProperty("UID")
        private String uid;
    }

    //회원가입 완료 Call Back Response
    @Getter @Setter
    public static class UserCertificationCompleteDto {
        private String userId;
        private String resultCode;
    }

    @Getter
    @Setter
    public static class UserCertificationCompleteRequestDto extends UserCertificationCompleteDto {
        private String resultMsg;

        public static UserCertificationCompleteRequestDto of(String encryptUserId, String resultCode) {
            UserCertificationCompleteRequestDto requestDto = new UserCertificationCompleteRequestDto();
            requestDto.setUserId(encryptUserId);
            requestDto.setResultCode(resultCode);
            return requestDto;
        }
    }
}
