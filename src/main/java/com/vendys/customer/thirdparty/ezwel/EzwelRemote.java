package com.vendys.customer.thirdparty.ezwel;

import java.net.URI;
import java.util.Base64;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import com.vendys.customer.constant.InitProperties;
import com.vendys.customer.controller.ezwelWelfare.entity.EzwelWelfareDto.EzwelPrivacyRequestDto;
import com.vendys.customer.thirdparty.ezwel.code.EzwelPrivacyAgreeCode;
import com.vendys.customer.thirdparty.ezwel.code.EzwelUserStatusCode;
import com.vendys.customer.thirdparty.ezwel.dto.EzewlWelfareRemoteDto.EzwelPrivacyRemoteResponseDto;
import com.vendys.customer.thirdparty.ezwel.dto.EzewlWelfareRemoteDto.PrivacyAgreeRemoteRequestDto;
import com.vendys.customer.thirdparty.ezwel.dto.EzewlWelfareRemoteDto.PrivacyAgreeRemoteResponseDto;
import com.vendys.customer.thirdparty.ezwel.dto.EzewlWelfareRemoteDto.UserStatusRemoteRequestDto;
import com.vendys.customer.thirdparty.ezwel.dto.EzewlWelfareRemoteDto.UserStatusRemoteResponseDto;
import com.vendys.customer.thirdparty.ezwel.dto.EzewlWelfareRemoteDto.UserStatusResponseDto;
import com.vendys.customer.thirdparty.ezwel.dto.common.EzwelInterModel;
import com.vendys.customer.util.ConverterUtil;

@Slf4j(topic = "http")
@Component
@Transactional
@RequiredArgsConstructor
public class EzwelRemote {

    private final InitProperties initProperties;
    private final RestTemplate ezwelRestTemplate;
    private final ConverterUtil converterUtil;
    private final EzwelCrypto ezwelCrypto;
    public static final String EZWEL_HEADER_INTER_ID = "interId";
    public static final String EZWEL_HEADER_USER_ID = "userId";
    public static final String EZWEL_HEADER_PRIVACY_USER_STATUS = "privacyUserStatus";// 특가대장 등록 API(탈퇴 or 가입)

    /**
     * 개인정보 처리 방침 [POST, PUT]
     */
    public EzwelPrivacyRemoteResponseDto privacyAgree(EzwelPrivacyRequestDto requestDto) {
        String userId = "";
        String userStatus = "";
        try {
            userId = this.ezwelCrypto.decrypt(requestDto.getInterInfo().getUid());
            userStatus = this.ezwelCrypto.decrypt(requestDto.getInterInfo().getUserStatus());
        } catch (Exception e) {
            log.error("EzwelRemote.privacyAgree() error {}, {}", e, e.getMessage());
        }
        EzwelPrivacyRemoteResponseDto remoteResponseDto = new EzwelPrivacyRemoteResponseDto();
        HttpHeaders httpHeaders = this.getHeader(EzwelInterModel.privacyAgree, userId, userStatus);
        HttpEntity<PrivacyAgreeRemoteRequestDto> httpEntity = new HttpEntity<>(
                PrivacyAgreeRemoteRequestDto.builder().interInfo(requestDto.getInterInfo()).build(), httpHeaders);
        URI uri = UriComponentsBuilder.fromUriString(this.initProperties.getEzwelHost()
                + "/" + this.initProperties.getEzwelInterServer()).build().toUri();

        try {
            ResponseEntity<String> responseEntity =
                    this.ezwelRestTemplate.exchange(uri, HttpMethod.POST, httpEntity, String.class);
            if (!ObjectUtils.isEmpty(responseEntity.getBody())) {
                PrivacyAgreeRemoteResponseDto privacyAgreeRemoteResponseDto =
                        this.converterUtil.toObject(responseEntity.getBody(), PrivacyAgreeRemoteResponseDto.class);
                remoteResponseDto.setInterInfoResult(privacyAgreeRemoteResponseDto.getInterInfoResult());
                remoteResponseDto.setCode(EzwelPrivacyAgreeCode.findByCode(
                        new String(Base64.getDecoder().decode(privacyAgreeRemoteResponseDto.getInterInfoResult().getProcCd()))));
            }
        } catch (HttpServerErrorException | HttpClientErrorException hse){
            log.error("EzwelRemote.privacyAgree() error : {}, {}", hse, hse.getMessage());
            return remoteResponseDto;
        }

        return remoteResponseDto;
    }

    /**
     * 이지웰 회원상태 조회[POST]
     */
    public UserStatusResponseDto ezwelUserStatus(String userId, UserStatusRemoteRequestDto requestDto) {
        UserStatusResponseDto responseDto = new UserStatusResponseDto();
        HttpHeaders httpHeaders = this.getHeader(EzwelInterModel.welfareUserStatus, userId, "");
        HttpEntity<UserStatusRemoteRequestDto> httpEntity = new HttpEntity<>(requestDto, httpHeaders);
        URI uri = UriComponentsBuilder.fromUriString(this.initProperties.getEzwelHost()
                + "/" + this.initProperties.getEzwelInterServer()).build().toUri();

        try {
            ResponseEntity<String> responseEntity =
                    this.ezwelRestTemplate.exchange(uri, HttpMethod.POST, httpEntity, String.class);
            if (!ObjectUtils.isEmpty(responseEntity.getBody())) {
                UserStatusRemoteResponseDto remoteResponseDto = this.converterUtil.toObject(responseEntity.getBody(),
                        UserStatusRemoteResponseDto.class);
                responseDto.setCode(EzwelUserStatusCode.findByCode(
                        new String(Base64.getDecoder().decode(remoteResponseDto.getInterInfoResult().getProcCd()))));
            }
        } catch (HttpServerErrorException | HttpClientErrorException hse){
            log.error("EzwelRemote.ezwelUserStatus() error : {}, {}", hse, hse.getMessage());
            return responseDto;
        }

        return responseDto;
    }

    private HttpHeaders getHeader(EzwelInterModel interModel, String userId, String userStatus) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(EZWEL_HEADER_USER_ID, userId);
        httpHeaders.add(EZWEL_HEADER_PRIVACY_USER_STATUS, userStatus);
        httpHeaders.add(EZWEL_HEADER_INTER_ID, interModel.getInterId());
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        return httpHeaders;
    }
}
