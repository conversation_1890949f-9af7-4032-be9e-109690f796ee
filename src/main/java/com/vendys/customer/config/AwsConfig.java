package com.vendys.customer.config;

import java.time.Duration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import lombok.RequiredArgsConstructor;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.transport.aws.AwsSdk2Transport;
import org.opensearch.client.transport.aws.AwsSdk2TransportOptions;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.credentials.InstanceProfileCredentialsProvider;
import software.amazon.awssdk.http.SdkHttpClient;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.regions.Region;

import com.vendys.customer.constant.Errors;
import com.vendys.customer.constant.InitProperties;
import com.vendys.customer.exception.CommonException;

@RequiredArgsConstructor
@Configuration
public class AwsConfig {

    private final InitProperties initProperties;

    @Bean
    public AmazonSQS initializeAmazonSqs() {

        if ("production".equals(this.initProperties.getServerEnv())) {
            return AmazonSQSClientBuilder.standard()
                    .withRegion(Regions.fromName(this.initProperties.getAwsSqsRegion()))
                    .build();
        } else {
            AWSCredentials credentials = new BasicAWSCredentials(this.initProperties.getAwsSqsAccess(), this.initProperties.getAwsSqsSecret());
            return AmazonSQSClientBuilder.standard()
                    .withRegion(Regions.fromName(this.initProperties.getAwsSqsRegion()))
                    .withCredentials(new AWSStaticCredentialsProvider(credentials))
                    .build();
        }
    }

    @Bean
    public OpenSearchClient openSearchClient() {
        SdkHttpClient httpClient =
                ApacheHttpClient
                        .builder()
                        .maxConnections(100)
                        .connectionTimeout(Duration.ofSeconds(3L))
                        .build();
        return new OpenSearchClient(
                new AwsSdk2Transport(
                        httpClient,
                        this.initProperties.getAwsOpenSearchHost(),
                        Region.AP_NORTHEAST_2,
                        AwsSdk2TransportOptions.builder()
                                .setCredentials(this.credentialsProvider()).build())
        );
    }

    private AwsCredentialsProvider credentialsProvider() {
        AwsCredentialsProvider credentialsProvider;
        String env = this.initProperties.getServerEnv();
        if ("local".equals(env)) {
            credentialsProvider = DefaultCredentialsProvider.create();
        } else if ("develop".equals(env)) {
            credentialsProvider = DefaultCredentialsProvider.create();
        } else if ("production".equals(env)) {
            credentialsProvider = DefaultCredentialsProvider.create();
        } else {
            throw new CommonException(Errors.UNKNOWN_ERROR);
        }
        return credentialsProvider;
    }
}
