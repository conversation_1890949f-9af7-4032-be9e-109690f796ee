package com.vendys.customer.config;

import java.sql.SQLException;
import java.util.Map;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.LazyConnectionDataSourceProxy;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;

@Configuration
@Slf4j
public class DataSourceConfig {

    private static final String MASTER = "masterDataSource";
    private static final String SLAVE = "slaveDataSource";

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.hikari.master")
    public HikariConfig masterConfig() {
        HikariConfig hikariConfig = new HikariConfig();
        return hikariConfig;
    }

    @Bean(MASTER)
    public DataSource masterDataSource(@Qualifier("masterConfig") HikariConfig masterConfig) {
        return new HikariDataSource(masterConfig);
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.hikari.slave")
    public HikariConfig slaveConfig() {
        return new HikariConfig();
    }

    @Bean(SLAVE)
    public DataSource slaveDataSource(@Qualifier("slaveConfig") HikariConfig slaveConfig) {
        return new HikariDataSource(slaveConfig);
    }

    @Bean
    @DependsOn({MASTER, SLAVE})
    @Primary
    public DataSource routingDataSource(
            @Qualifier(MASTER) DataSource masterDataSource,
            @Qualifier(SLAVE) DataSource slaveDataSource
    ) throws SQLException {
        Map<Object, Object> targetMap = Map.of(
                "master", masterDataSource,
                "slave", slaveDataSource);
        masterDataSource.getConnection();

        RoutingDataSource routingDataSource = new RoutingDataSource();
        routingDataSource.setTargetDataSources(targetMap);
        routingDataSource.setDefaultTargetDataSource(masterDataSource);
        routingDataSource.afterPropertiesSet();

        return new LazyConnectionDataSourceProxy(routingDataSource);
    }


}
