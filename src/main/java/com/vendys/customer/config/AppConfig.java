package com.vendys.customer.config;

import java.util.Collections;
import java.util.List;
import javax.annotation.PostConstruct;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.HttpComponentsAsyncClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.AsyncRestTemplate;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.naver.api.security.client.MACManager;
import lombok.RequiredArgsConstructor;

import com.vendys.customer.interceptor.EzwelRestTemplateInterceptor;
import com.vendys.customer.interceptor.NaverRestTemplateInterceptor;

/**
 * Created by jangjungsu on 2016. 12. 29..
 */
@Configuration
@RequiredArgsConstructor
public class AppConfig {

    private final NaverRestTemplateInterceptor naverRestTemplateInterceptor;
    private final EzwelRestTemplateInterceptor ezwelRestTemplateInterceptor;

    /**
     * RestTemplate 에대한 Connection 관련 Policy등록
     * @return
     */
    @Bean
    @ConfigurationProperties(prefix = "custom.rest.connection")
    public HttpComponentsClientHttpRequestFactory customHttpRequestFactory() {
        return new HttpComponentsClientHttpRequestFactory();
    }

    @Bean
    @ConfigurationProperties(prefix = "custom.rest.connection")
    public HttpComponentsAsyncClientHttpRequestFactory customAsyncHttpRequestFactory() {
        return new HttpComponentsAsyncClientHttpRequestFactory();
    }

    /**
     * RestTemplate Bean 생성
     * @return
     */
    @Bean
    public RestTemplate customRestTemplate() {
        RestTemplate restTemplate = new RestTemplate(customHttpRequestFactory());
        return restTemplate;
    }

    /**
     * Naver 호출 전용 빈 생성, RestTemplate 인터셉터 추가
     * @return
     */
    @Bean
    public RestTemplate naverPayRestTemplate() {
        RestTemplate restTemplate = new RestTemplate(new BufferingClientHttpRequestFactory(customHttpRequestFactory()));

        List<ClientHttpRequestInterceptor> interceptors = restTemplate.getInterceptors();
        if (ObjectUtils.isEmpty(interceptors)) {
            restTemplate.setInterceptors(Collections.singletonList(this.naverRestTemplateInterceptor));
            return restTemplate;
        }

        interceptors.add(this.naverRestTemplateInterceptor);
        restTemplate.setInterceptors(interceptors);
        return restTemplate;
    }


    /**
     * RestTemplate Bean 생성
     * @return
     */
    @Bean
    public AsyncRestTemplate customAsyncRestTemplate() {
        AsyncRestTemplate asyncRestTemplate = new AsyncRestTemplate(customAsyncHttpRequestFactory());
        return asyncRestTemplate;
    }

    /**
     * ObjectMapper Bean 생성
     * @return
     */
    @Bean
    public ObjectMapper customObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        // 정의 되지 않은 property 무시 ( vo 하위 호환 보장 )
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // ENUM 값 없을시 null ( Enum 추가시 무시 )
        objectMapper.configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, true);
        return objectMapper;
    }

    /**
     * 네이버 모듈 사용하기위한 초기화, 앱 구동시 최초 한번 구동
     * @throws Exception
     */
    @PostConstruct
    public void naverHmacInit() throws Exception {
        MACManager.initialize();
    }

    /**
     * Ezwel 빈 생성, RestTemplate 인터셉터 추가
     */
    @Bean
    public RestTemplate ezwelRestTemplate() {
        RestTemplate restTemplate = new RestTemplate(new BufferingClientHttpRequestFactory(customHttpRequestFactory()));
        List<ClientHttpRequestInterceptor> interceptors = restTemplate.getInterceptors();
        if (ObjectUtils.isEmpty(interceptors)) {
            restTemplate.setInterceptors(Collections.singletonList(this.ezwelRestTemplateInterceptor));
            return restTemplate;
        }

        interceptors.add(this.ezwelRestTemplateInterceptor);
        restTemplate.setInterceptors(interceptors);
        return restTemplate;
    }
}
