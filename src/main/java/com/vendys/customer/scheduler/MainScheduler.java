package com.vendys.customer.scheduler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

import com.vendys.customer.service.ScheduleService;

/**
 * Created by lcy on 2017. 6. 28..
 */

@Component
@Slf4j
public class MainScheduler {

    @Autowired
    private ScheduleService scheduleService;

    /**
     * AWS SQS 에 들어있는 큐를 처리하는 부분
     */
    @Scheduled(fixedDelay = 5000)
    public void sqsProcess() {
        try {
            this.scheduleService.receiveSqsCreateUser();
        } catch (Exception e) {
            log.error("{}", e);
        }
    }
}

