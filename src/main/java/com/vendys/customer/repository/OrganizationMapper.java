package com.vendys.customer.repository;

import java.util.List;

import com.vendys.customer.config.MyBatisMapper;
import com.vendys.customer.vo.OrganizationVo;

/**
 * Created by jinwoo on 2017. 9. 26..
 */
@MyBatisMapper
public interface OrganizationMapper {

    OrganizationVo.Organization selectByPrimaryKey(long orgIdx);

    OrganizationVo.Organization selectOrganizationOne(String orgCode);

    void insertOrganization(OrganizationVo.Organization param);

    int updateOrganizationSeq(List<OrganizationVo.Organization> param);

    int updateOrganization(OrganizationVo.Organization param);

    List<OrganizationVo.Organization> selectOrganizationAll(String rootCode);

    int deleteOrganizationAll(String rootCode);
}
