package com.vendys.customer.repository;

import org.springframework.data.jpa.repository.JpaRepository;

import com.vendys.customer.entity.BarCodeUser;
import com.vendys.customer.entity.BarCodeUser.BarcodeType;

/**
 * Created by leedo on 07/11/2018
 */
public interface BarCodeUserRepository extends JpaRepository<BarCodeUser, Long> {

    BarCodeUser findByUserIdAndBarCodeTypeAndIsActive(String userId, BarcodeType barcodeType, Boolean isActive);
}
