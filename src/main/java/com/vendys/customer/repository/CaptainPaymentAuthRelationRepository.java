package com.vendys.customer.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

import com.vendys.customer.entity.CaptainPaymentAuthRelation;

@Repository
public interface CaptainPaymentAuthRelationRepository extends JpaRepository<CaptainPaymentAuthRelation, Long> {

    @Modifying
    void deleteAllByUserId(String userId);

}
