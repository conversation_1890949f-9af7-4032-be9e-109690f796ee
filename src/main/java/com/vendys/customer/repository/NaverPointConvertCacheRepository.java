package com.vendys.customer.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.vendys.customer.entity.NaverPointConvertCache;

@Repository
public interface NaverPointConvertCacheRepository extends JpaRepository<NaverPointConvertCache, Long> {

    @Query("    SELECT npcc "
            + "   FROM NaverPointConvertCache npcc"
            + "  WHERE npcc.userId = :userId")
    NaverPointConvertCache findUserCache(@Param("userId") String userId);
}
