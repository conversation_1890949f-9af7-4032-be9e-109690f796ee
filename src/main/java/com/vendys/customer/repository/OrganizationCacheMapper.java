package com.vendys.customer.repository;

import java.util.List;
import java.util.Map;

import com.vendys.customer.config.MyBatisMapper;
import com.vendys.customer.vo.OrganizationVo;

/**
 * Created by jin<PERSON><PERSON> on 2017. 9. 26.
 */
@MyBatisMapper
public interface OrganizationCacheMapper {

    OrganizationVo.OrganizationCache selectByPrimaryKey(long orgCacheIdx);

    List<OrganizationVo.OrganizationCache> selectByOrgMetaIdx(OrganizationVo.OrganizationCache param);

    OrganizationVo.OrganizationCache selectOne(String orgCode);

    /**
     * 그룹에 속한 회사 정보
     */
    OrganizationVo.OrganizationCache selectCompany(OrganizationVo.OrganizationCache param);

    /**
     * 부서 전체 정보
     */
    List<OrganizationVo.OrganizationCache> selectDivisionFullTree(OrganizationVo.OrganizationCache param);

    /**
     * 부서 전체 정보
     */
    List<OrganizationVo.OrganizationCache> selectOrganizationCacheList(OrganizationVo.OrganizationCache param);

    /**
     * 현재 부서 경로
     */
    List<OrganizationVo.OrganizationCache> selectDivisionPath(OrganizationVo.OrganizationCache param);

    /**
     * 부모 노드
     */
    OrganizationVo.OrganizationCache selectDivisionParent(OrganizationVo.OrganizationCache param);

    /**
     * 형제 노드
     */
    List<OrganizationVo.OrganizationCache> selectDivisionSiblings(OrganizationVo.OrganizationCache param);

    /**
     * 자식 노드
     */
    List<OrganizationVo.OrganizationCache> selectDivisionChild(OrganizationVo.OrganizationCache param);

    int deleteNodeAll(String rootCode);

    int updateNodeRgt(Map<String, Object> param);

    int updateNodeLft(Map<String, Object> param);

    void insertOrganizationCache(OrganizationVo.OrganizationCache param);

    int updateOrganizationCacheSeq(List<OrganizationVo.Organization> param);

    int updateOrganizationCache(OrganizationVo.OrganizationCache param);

    List<OrganizationVo.OrganizationCache> searchDivision(OrganizationVo.OrganizationCache param);
}
