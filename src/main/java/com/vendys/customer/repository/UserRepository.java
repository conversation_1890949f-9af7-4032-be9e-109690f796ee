package com.vendys.customer.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.vendys.customer.entity.User;
import com.vendys.customer.entity.User.Status;

/**
 * Created by leedowon on 2018. 4. 10..
 */
public interface UserRepository extends JpaRepository<User, String> {

    List<User> findAllByComidAndUidInOrderByName(String comId, List<String> userIdLIst, Pageable pageable);

    User findBySignId(String signId);

    User findBySignIdAndUidNot(String signId, String uid);

    User findByEmail(String email);

    User findByEmailAndUidNot(String email, String uid);

    User findByComidAndCellphone(String comId, String phone);

    User findByComidAndCellphoneAndUidNot(String comId, String phone, String uid);

    User findByComidAndComidnumAndStatusNot(String comId, String comidNum, Status withdraw);

    User findByComidAndComidnumAndUidNot(String comId, String comidNum, String uid);

    User findByUid(String userId);

    long countAllByOrgCodeAndStatusNotIn(String orgCode, List<User.Status> statusList);

    @Query(value = "select u from User u "
            + "where u.comid = :comId "
            + "and u.uid in (:userIdList) "
            + "and (u.name like %:name% or u.signId like %:signId%) "
            + "order by u.name ")
    List<User> findAllByKeyword(
            @Param("comId") String comId, @Param("userIdList") List<String> userIdList, @Param("name") String name,
            @Param("signId") String signId, Pageable pageable);

    User findByComidAndUid(String comId, String userId);

    @Query(value = "select u from User u "
            + "where u.comid = :comId "
            + "and u.status in (:statusList) "
            + "and u.isDormant = false ")
    List<User> findAllCompanyUserList(@Param("comId") String comId, @Param("statusList") List<User.Status> statusList);

    /**
     * 탈퇴 사용자 확인 (탈퇴 후 30일이 지난 사용자 존재 여부)
     */
    @Query(value = "SELECT U.* "
            + "FROM User U "
            + "WHERE U.status = 'WITHDRAW' "
            + "AND U.uid NOT IN (SELECT uid FROM WithdrawUser) "
            + "AND DATE_ADD(U.withdrawDate, INTERVAL 1 MONTH) < NOW()"
            + "LIMIT :offset, :limit", nativeQuery = true)
    List<User> readWithdrawUserBySeparation(@Param("offset") Integer offset, @Param("limit") Integer limit);

    Page<User> findByComidAndStatusNot(String comId, Status withdraw, Pageable pageable);

    List<User> findByComidAndStatusNot(String comId, Status withdraw);

    List<User> findByOrgCodeAndStatusNot(String orgCode, Status withdraw);

    @Query(value = ""
            + "     SELECT u "
            + "       FROM User u"
            + "       JOIN FETCH u.organizationCache oc"
            + "      WHERE u.comid = :companyId"
            + "        AND u.status = 'ACTIVE' "
            + "        AND u.isDormant = FALSE "
            + "        AND u.name like :searchName% "
            + "        AND (:orgCode is null OR u.orgCode = :orgCode)"
            + "        AND (:excludeSelf = false OR u.uid <> :selfId) "
            + "      ")
    List<User> userNameSearch(
            @Param("companyId") String companyId,
            @Param("searchName") String searchName,
            @Param("orgCode") String orgCode,
            @Param("selfId") String selfId,
            @Param("excludeSelf") Boolean excludeSelf,
            Pageable pageable);

    /**
     * 고객사 수정시 사용자의 정보 Bulk Update
     * @param comId
     * @param companyName
     */
    @Query("" +
            "UPDATE User user " +
            "SET user.comname = :companyName " +
            "WHERE 1=1 " +
            "AND user.comid = :comId "
    )
    @Modifying(clearAutomatically = true)
    void userBulkUpdateByCompany(@Param("comId") String comId, @Param("companyName") String companyName);

}


























