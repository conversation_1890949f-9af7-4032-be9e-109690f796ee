package com.vendys.customer.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.vendys.customer.entity.UserOfficeRelation;
import com.vendys.customer.entity.UserOfficeRelationKey;

/**
 * Created by jangjungsu on 2020. 2. 7.
 */
public interface UserOfficeRelationRepository extends JpaRepository<UserOfficeRelation, UserOfficeRelationKey> {

    @Query(value = ""
            + " SELECT uor "
            + "   FROM UserOfficeRelation uor"
            + "  WHERE uor.userOfficeRelationKey.userId = :userId")
    UserOfficeRelation findByUserId(@Param("userId") String userId);
}
