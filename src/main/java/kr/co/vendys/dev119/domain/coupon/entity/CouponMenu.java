package kr.co.vendys.dev119.domain.coupon.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

@Entity
@Table(name = "CouponMenu",
    indexes = {
        @Index(name = "index_couponmenu_comid", columnList = "comid"),
        @Index(name = "index_couponmenu_mid", columnList = "mid"),
        @Index(name = "index_couponmenu_sid", columnList = "sid"),
        @Index(name = "index_couponmenu_usedate", columnList = "usedate"),
        @Index(name = "index_couponmenu_usedate_comid", columnList = "comid,usedate")
    })
@IdClass(CouponMenuId.class)
@Getter
@NoArgsConstructor
public class CouponMenu {

    @Id
    @Column(length = 40, columnDefinition = "char(40)")
    private String cid;

    @Id
    @Column(nullable = false)
    private Byte seq;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cid", insertable = false, updatable = false)
    private CouponGroup couponGroup;

    @Column(nullable = false, length = 40, columnDefinition = "char(40)")
    private String mid;

    @Column(length = 50, columnDefinition = "char(50)")
    private String menuname;

    @Column(length = 30, columnDefinition = "char(30)")
    private String productid;

    @Column(length = 30, columnDefinition = "char(30)")
    private String leadername;

    @Column(nullable = false, length = 40, columnDefinition = "char(40)")
    private String comid;

    @Column(length = 30, columnDefinition = "char(30)")
    private String comname;

    @Column(nullable = false, length = 40, columnDefinition = "char(40)")
    private String sid;

    @Column(length = 30, columnDefinition = "char(30)")
    private String storename;

    @Column(nullable = false)
    private Integer price;

    @Column(nullable = false)
    private Integer salesprice;

    @Column(nullable = false)
    private Integer supplyprice;

    @Column(nullable = false)
    private Integer quantity;

    @Column(nullable = false, length = 3, columnDefinition = "char(3)")
    private String coupontype;

    @Column(length = 40, columnDefinition = "char(40)")
    private String calcid;

    @Column(nullable = false)
    private LocalDateTime usedate;

    @Column(nullable = false)
    private Byte status;
}
