package kr.co.vendys.company.api.controller.addons.entity;

import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * Created by jangjungsu on 2017. 5. 30..
 */
public class NoticeDto {

    @Data
    public static class ListRequest {

        private Integer page;
        private Integer pagerow;
    }

    @Data
    public static class ListResponse {

        private Paging paging;
        private List<Notice> notice;

        @Data
        public static class Paging {

            private Integer page;
            private Integer pagerow;
            private Integer totalcount;
        }

        @Data
        public static class Notice {

            private Integer idx;
            private Integer id;
            private String title;
            private String writer;
            private Date lastdate;
            private Boolean status;
            private Date regdate;
        }
    }

    @Data
    public static class PathParam {

        @NotNull
        private Integer id;
    }

    @Data
    public static class DetailResponse {

        private Notice notice;

        @Data
        public static class Notice {

            private Integer id;
            private String title;
            private String content;
            private String writer;
            private Boolean status;
            private Date regdate;
            private Date lastdate;
        }
    }

    @Data
    public static class BodyRequest {

        private Notice notice;

        @Data
        public static class Notice {

            @NotNull
            private String title;
            @NotNull
            private String content;
            @NotNull
            private Boolean status;
            private Boolean push;
        }
    }
}
