package kr.co.vendys.company.api.controller.member;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import kr.co.vendys.company.api.controller.CommonController;
import kr.co.vendys.company.api.controller.ParamDto;
import kr.co.vendys.company.api.controller.member.entity.SignupDto;
import kr.co.vendys.company.api.service.member.SignupService;
import lombok.RequiredArgsConstructor;

/**
 * Created by jangjungsu on 2017. 4. 28..
 */
@RestController
@RequestMapping("/member")
@RequiredArgsConstructor
public class SignupController extends CommonController {

    private final SignupService signupService;

    /**
     * 임직원 회원가입 관리 > 목록 > select box
     */
    @GetMapping(value = "/v1/signup/status")
    public ResponseEntity getStatus(HttpServletRequest request) {

        SignupDto.StatusCodeResponse response = this.signupService.getStatusCode();
        return new ResponseEntity(response, HttpStatus.OK);
    }

    /**
     * 임직원 회원가입 관리 > 목록
     */
    @GetMapping(value = "/v1/signup")
    public ResponseEntity getSignup(HttpServletRequest request, @Valid @ModelAttribute SignupDto.SignupUserRequest userRequest) {

        ParamDto.RequestData requestData = this.getHeaderData(request);

        SignupDto.SignupUserResponse response = this.signupService.getSignupUser(requestData, userRequest);
        return new ResponseEntity(response, HttpStatus.OK);
    }

    /**
     * 임직원 회원가입 관리 > 목록 > 재전송
     */
    @PutMapping(value = "/v1/signup")
    public ResponseEntity setSignupResend(HttpServletRequest request, @Valid @RequestBody SignupDto.ResendRequest resendRequest) {

        ParamDto.RequestData requestData = this.getHeaderData(request);

        this.signupService.setSignupUserResend(requestData, resendRequest);
        return new ResponseEntity(HttpStatus.OK);
    }

    /**
     * 임직원 회원가입 관리 > 목록 체크
     */
    @PostMapping(value = "/v1/signup/check")
    public ResponseEntity setSignupCheck(HttpServletRequest request, @Valid @RequestBody SignupDto.CheckRequest checkRequest) {

        ParamDto.RequestData requestData = this.getHeaderData(request);

        SignupDto.CheckResponse response = this.signupService.getSignupUserCheck(requestData, checkRequest);
        return new ResponseEntity(response, HttpStatus.CREATED);
    }

    /**
     * 임직원 회원가입 관리 > 링크 전송
     */
    @PostMapping(value = "/v1/signup")
    public ResponseEntity setSignupCheck(HttpServletRequest request, @Valid @RequestBody SignupDto.SendRequest sendRequest) {

        ParamDto.RequestData requestData = this.getHeaderData(request);

        this.signupService.setSignup(requestData, sendRequest);
        return new ResponseEntity(HttpStatus.CREATED);
    }

    @DeleteMapping(value = "/v1/signup")
    public ResponseEntity<Void> deleteSignupCheck(
            HttpServletRequest request,
            @Valid @RequestBody SignupDto.CancelRequest cancelRequest) {

        this.signupService.expireSignupUserLink(cancelRequest);
        return ResponseEntity.ok(null);
    }

}
