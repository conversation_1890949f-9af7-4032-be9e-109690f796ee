package kr.co.vendys.company.api.entity.master;


import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

import kr.co.vendys.core.schema.mealcoupon.entity.common.CommonEntityByAuditing;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters.LocalDateTimeConverter;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;


@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
public class BeltBanner extends CommonEntityByAuditing {

    @Column(name = "idx")
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 300)
    private String bannerImgUrl;

    @Column(nullable = false, length = 20)
    private String representativePhrase;

    @Column(nullable = false, length = 35)
    private String detailPhrase;

    @Column(nullable = false, length = 20)
    private String buttonPhrase;

    @Column(nullable = false, length = 200)
    private String buttonLink;

    @Column(nullable = false, length = 7)
    private String bannerBgColorCode;

    @Column(nullable = false, length = 7)
    private String bannerFontColorCode;

    private Byte orderNo = 0;

    private boolean exposureFlag;

    private boolean isDelete = false;

    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime exposureStartDate;

    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime exposureEndDate;
}
