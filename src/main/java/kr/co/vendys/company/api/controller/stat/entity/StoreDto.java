package kr.co.vendys.company.api.controller.stat.entity;

import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * Created by j<PERSON><PERSON><PERSON><PERSON> on 2017. 7. 11..
 */
public class StoreDto {

    @Data
    @Builder
    @AllArgsConstructor
    public static class StoreRequest {
        @NotNull
        private Date startdate;
        @NotNull
        private Date enddate;
        @NotNull
        private String datetype;
        private String storeid;

        public static StoreRequest of(Date startdate, Date enddate, String datetype) {
            return StoreRequest.builder()
                    .startdate(startdate)
                    .enddate(enddate)
                    .datetype(datetype)
                    .build();
        }
    }

    @Data
    public static class ExcelRequest {
        @NotNull
        private Date startdate;
        @NotNull
        private Date enddate;
        @NotNull
        private String datetype;
        private String storeid;
        @NotNull
        private String comid;
        @NotNull
        private String requestid;
        @NotNull
        private String filetype;
    }

    @Data
    public static class StoreResponse {

        private List<Stat> stat;

        @Data
        public static class Stat {

            private String sdate;
            private List<Store> store;

            @Data
            public static class Store {
                private String sdate;
                private String storeid;
                private String storename;
                private Long salesprice;
                private Long companyprice;
                private Long mcount;
                private Long cancelprice;
            }
        }
    }

    @Data
    public static class StoreDashResponse {

        private Dates date;
        private List<Stat> stat;

        @Data
        public static class Dates {

            private Date start;
            private Date end;
        }

        @Data
        public static class Stat {

            private String storeid;
            private String storename;
            private Price price;

            @Data
            public static class Price {
                private Long now;
                private Long pre;
            }
        }
    }

    @Data
    public static class MenuDashReponse {

        private Dates date;
        private List<Stat> stat;

        @Data
        public static class Dates {

            private Date start;
            private Date end;
        }

        @Data
        public static class Stat {

            private Menu menu;
            private String storename;

            @Data
            public static class Menu {
                private String name;
                private Long price;
            }
        }
    }

    @Data
    public static class BookingStatRequest {

        private String start;
        private String end;
        private String datetype;
        private String storeId;
    }

    @Data
    public static class StatBookingResponse {

        private List<Contents> contents;

        @Data
        public static class Contents {

            private String sdate;
            private List<Stores> stores;

            @Data
            public static class Stores {

                private Booking booking;
                private Store store;
                private Count count;

                @Data
                public static class Booking {
                    private Integer booking;
                    private String name;
                }

                @Data
                public static class Store {
                    private String id;
                    private String name;
                }

                @Data
                public static class Count {
                    private Integer price;
                    private Integer companyprice;
                    private Integer booking;
                    private Integer cancel;
                }
            }
        }
    }

    @Data
    public static class StoreDetailRequest {
        @NotNull
        private Date startDate;
        @NotNull
        private Date endDate;
        @NotNull
        private String sid;
        @NotNull
        private String comId;
    }

    @Data
    public static class StoreDetailResponse {
        private List<StoreDetail> details;

        @Data
        public static class StoreDetail {
            private Long payRoomIdx;
            private Date useDate;
            private String comName;
            private String orgName;
            private String username;
            private String menuName;
            private Long companyPrice;
            private Long instantPayPrice;
            private Long price;
        }
    }

    @Data
    public static class BookingExcelRequest {

        @NotNull
        private String startdate;

        @NotNull
        private String enddate;

        @NotNull
        private String datetype;
        private String storeid;

        @NotNull
        private String comid;

        @NotNull
        private String requestid;

        @NotNull
        private String filetype;

        private String uuid;
        private String domain;
        private String filename;
    }
}
