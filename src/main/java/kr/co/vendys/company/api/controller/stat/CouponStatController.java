package kr.co.vendys.company.api.controller.stat;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import kr.co.vendys.company.api.controller.CommonController;
import kr.co.vendys.company.api.controller.ParamDto;
import kr.co.vendys.company.api.controller.stat.entity.CouponDto;
import kr.co.vendys.company.api.service.stat.CouponStatService;

/**
 * Created by jangjungsu on 2017. 7. 19..
 */
@RestController
@RequestMapping("/stat")
public class CouponStatController extends CommonController {

    @Autowired
    private CouponStatService couponStatService;

    /**
     * DashBoard > 시간대별 사용 현황
     */
    @GetMapping(value = "/v1/coupon/hour")
    public ResponseEntity getHourStatDash(
            HttpServletRequest request, @Valid @ModelAttribute CouponDto.HourRequest hourRequest) {

        ParamDto.RequestData requestData = this.getHeaderData(request);

        CouponDto.HourResponse response = this.couponStatService.getCouponHourStatDash(requestData, hourRequest);

        return new ResponseEntity(response, HttpStatus.OK);
    }

    /**
     * DashBoard > 월별 식대 금액
     */
    @GetMapping(value = "/v1/coupon/month")
    public ResponseEntity getMonthStatDash(HttpServletRequest request) {

        ParamDto.RequestData requestData = this.getHeaderData(request);

        CouponDto.MonthResponse response = this.couponStatService.getCouponMonthStatDash(requestData);

        return new ResponseEntity(response, HttpStatus.OK);
    }

    /**
     * DashBoard > 일별 식대 금액
     */
    @GetMapping(value = "/v1/coupon/day")
    public ResponseEntity getDayStatDash(HttpServletRequest request) {

        ParamDto.RequestData requestData = this.getHeaderData(request);

        CouponDto.DayResponse response = this.couponStatService.getCouponDayStatDash(requestData);

        return new ResponseEntity(response, HttpStatus.OK);
    }
}
