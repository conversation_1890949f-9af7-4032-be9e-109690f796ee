package kr.co.vendys.company.api.controller.captainpayment.benefit.response;

import kr.co.vendys.company.api.entity.master.RollingBanner;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@Builder
@AllArgsConstructor
public class RollingBannerResponse {

    private List<RollingBannerDto> rollingBanner;

    public static RollingBannerResponse of(List<RollingBanner> rollingBanners) {
        return RollingBannerResponse.builder()
                .rollingBanner(
                        rollingBanners.stream()
                                .map(RollingBannerDto::new)
                                .collect(Collectors.toList())
                )
                .build();
    }

    @Getter
    public static class RollingBannerDto {
        private long idx;
        private String representativePhrase;
        private String linkUrl;
        private String bannerImgUrl;
        private boolean exposureFlag;
        private String exposureStartDate;
        private String exposureEndDate;
        private int orderNo;
        private boolean isDelete;

        private static final DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
        public RollingBannerDto(RollingBanner rollingBanner) {
            this.idx = rollingBanner.getId();
            this.representativePhrase = rollingBanner.getRepresentativePhrase();
            this.linkUrl = rollingBanner.getLinkUrl();
            this.bannerImgUrl = rollingBanner.getBannerImageUrl();
            this.exposureFlag = rollingBanner.isExposureFlag();
            this.exposureStartDate = rollingBanner.getExposureStartDate().format(formatter);
            this.exposureEndDate = rollingBanner.getExposureEndDate().format(formatter);
            this.orderNo = rollingBanner.getOrderNo();
            this.isDelete = rollingBanner.isDelete();
        }
    }
}
