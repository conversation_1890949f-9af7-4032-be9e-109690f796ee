package kr.co.vendys.company.api.controller.official.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import lombok.Data;

public class OfficialWebPropertyDto {

    @Data
    public static class ListResponse {
        private List<MetricsResponse> metricsResponse;

        @Data
        public static class MetricsResponse {
            private int idx;
            private String id;
            private Long key;
            private String type;
            private String createUser;
            private String updateUser;
        }

    }
}
