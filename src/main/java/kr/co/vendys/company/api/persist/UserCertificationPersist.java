package kr.co.vendys.company.api.persist;

import org.springframework.stereotype.Service;

import kr.co.vendys.company.api.entity.master.UserEmailCertification;
import kr.co.vendys.company.api.entity.master.UserSmsCertification;
import kr.co.vendys.company.api.repository.master.UserEmailCertificationRepository;
import kr.co.vendys.company.api.repository.master.UserSmsCertificationRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class UserCertificationPersist {

    private final UserSmsCertificationRepository smsCertificationRepository;
    private final UserEmailCertificationRepository emailCertificationRepository;

    public UserSmsCertification readSmsCert(String userId) {
        return this.smsCertificationRepository.findById(userId).orElse(null);
    }

    public UserEmailCertification readEmailCert(String userId) {
        return this.emailCertificationRepository.findById(userId).orElse(null);
    }
}
