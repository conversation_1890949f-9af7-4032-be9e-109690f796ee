package kr.co.vendys.company.api.interceptor;

import kr.co.vendys.company.api.constant.Errors;
import kr.co.vendys.company.api.constant.InitProperties;
import kr.co.vendys.company.api.entity.master.CaptainPaymentCompanyInfo;
import kr.co.vendys.company.api.entity.master.CaptainPaymentCompanyInfo.ServiceType;
import kr.co.vendys.company.api.entity.master.User;
import kr.co.vendys.company.api.exception.AuthorizedException;
import kr.co.vendys.company.api.exception.CommonException;
import kr.co.vendys.company.api.interceptor.entity.AccessTokenDto;
import kr.co.vendys.company.api.interceptor.entity.UserAuthDto;
import kr.co.vendys.company.api.interceptor.entity.XCorp;
import kr.co.vendys.company.api.persist.CaptainPaymentCompanyInfoPersist;
import kr.co.vendys.company.api.persist.UserPersist;
import kr.co.vendys.company.api.util.ConverterUtil;
import kr.co.vendys.company.api.vo.UserVo;
import kr.co.vendys.company.api.vo.UserVo.Grade;
import kr.co.vendys.company.api.vo.UserVo.Status;
import java.util.Collections;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

/**
 * Created by jangjungsu on 2017. 4. 24..
 */
@Component
@RequiredArgsConstructor
public class OAuth2Interceptor extends HandlerInterceptorAdapter {

    private final InitProperties initProp;
    private final UserPersist userPersist;
    private final CaptainPaymentCompanyInfoPersist captainPaymentCompanyInfoPersist;
    private final ConverterUtil converterUtil;
    private final RestTemplate customRestTemplate;
    private final XCorpHeader xCorpHeader;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // HttpMethod OPTIONS 는 넘긴다.
        if (request.getMethod().equalsIgnoreCase("OPTIONS")) {
            response.flushBuffer();
            return false;
        }

        // XCorp 헤더 검증 (내부 서비스용)
        XCorp xCorp = this.xCorpHeader.validateAndGetHeader(request);
        if (xCorp != null) {
            UserVo.User internalUser = new UserVo.User();
            internalUser.setUid(xCorp.getUserId());
            internalUser.setName(xCorp.getUserId());
            internalUser.setGrade(Grade.SUPER);
            internalUser.setCaptainPaymentGrade(User.CaptainPaymentGrade.VENDYS_SUPER);
            request.setAttribute("userInfo", internalUser);
            return true;
        }

        // AccessToken 값을 가져온다.
        String accessToken = this.findAccessToken(request);

        if (StringUtils.hasText(accessToken)) {
            // token 정보 저장
            AccessTokenDto accessTokenDto = this.validateAccessToken(accessToken, request);
            request.setAttribute("tokenInfo", accessTokenDto);

            // user 정보 저장
            UserVo.User resultUser = this.validateUser(accessTokenDto, request.getHeader("x-comid"), request.getRequestURI());
            request.setAttribute("userInfo", resultUser);

            // 권한 체크
            UserAuthDto.AuthInfo userAuthDto = this.parsingUserAuth(request.getHeader("x-corp"), request.getRequestURI());
            request.setAttribute("userAuthInfo", userAuthDto);
        } else {
            throw new AuthorizedException(Errors.OAuth_TOKEN_EMPTY);
        }
        return super.preHandle(request, response, handler);
    }

    /**
     * 사용자 권한 Header Parsing 후 DTO Object 로 반환.
     */
    public UserAuthDto.AuthInfo parsingUserAuth(String userAuthStr, String uri) {
        // ME 에서는 권한이 아직 없으므로 팅긴다.
        if ("/addons/v1/me".equals(uri)) {
            return null;
        }

        if (StringUtils.isEmpty(userAuthStr)) {
            throw new AuthorizedException(Errors.GENERAL_HEADER_ERROR);
        }

        UserAuthDto.AuthInfo userAuthDto = this.converterUtil.toObject(userAuthStr, UserAuthDto.AuthInfo.class);
        if (ObjectUtils.isEmpty(userAuthDto)) {
            throw new AuthorizedException(Errors.GENERAL_HEADER_ERROR);
        }
        return userAuthDto;
    }

    /**
     * AccessToken 을 찾는다.
     */
    public String findAccessToken(HttpServletRequest request) {
        String header = request.getHeader("Authorization");
        if (header != null) {
            return this.parsingAccessToken(header);
        }
        return null;
    }

    /**
     * Header 를 Parsing 한다.
     */
    public String parsingAccessToken(String header) {
        Pattern p = Pattern.compile("Bearer\\s(.*)");
        Matcher m = p.matcher(header);
        if (m.find()) {
            return m.group(1);
        }
        return null;
    }

    /**
     * AccessToken Validation
     */
    public AccessTokenDto validateAccessToken(String accessToken, HttpServletRequest request) {
        ResponseEntity<AccessTokenDto> responseEntity;

        HttpHeaders header = new HttpHeaders();
        header.add("X-User-Agent", request.getHeader("User-Agent"));
        header.add("X-Request-Method", request.getMethod());
        header.add("X-Request-Uri", request.getRequestURI());

        try {
            responseEntity = this.customRestTemplate
                    .exchange(this.initProp.getOauthHost() + "/vendys/v1/token/{accessToken}",
                            HttpMethod.GET,
                            new HttpEntity(header),
                            AccessTokenDto.class,
                            accessToken);

        } catch (HttpServerErrorException | HttpClientErrorException e) {
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                // 토큰 시간 초과
                throw new AuthorizedException(Errors.OAuth_TOKEN_OVERTIME);
            } else if (e.getStatusCode() == HttpStatus.FORBIDDEN) {
                // 인증 서비스 점검 중
                throw new AuthorizedException(Errors.OAuth_SERVER_MAINTENANCE);
            } else if (e.getStatusCode() == HttpStatus.NOT_FOUND) {
                // 존재 하지 않는 토큰
                throw new AuthorizedException(Errors.OAuth_TOKEN_NOTFOUND);
            } else if (e.getStatusCode() == HttpStatus.METHOD_NOT_ALLOWED) {
                // 중복 된 토큰
                throw new AuthorizedException(Errors.OAuth_TOKEN_DUPLICATION);
            } else if (e.getStatusCode() == HttpStatus.GONE) {
                // 만료된 토큰
                throw new AuthorizedException(Errors.OAuth_TOKEN_EXPIRED);
            } else {
                throw new AuthorizedException(Errors.OAuth_SERVER_ERROR);
            }
        }

        if (ObjectUtils.isEmpty(responseEntity)) {
            throw new CommonException(Errors.MSA_INTERFACE_ERROR);
        }

        AccessTokenDto accessTokenDto = responseEntity.getBody();
        if (ObjectUtils.isEmpty(accessTokenDto)) {
            throw new CommonException(Errors.MSA_INTERFACE_ERROR);
        }

        return accessTokenDto;
    }

    /**
     * User Validation
     */
    public UserVo.User validateUser(AccessTokenDto accessTokenDto, String comId, String uri) {

        if (!"/addons/v1/me".equals(uri) && StringUtils.isEmpty(comId)) {
            throw new AuthorizedException(Errors.GENERAL_HEADER_ERROR);
        }

        UserVo.User paramUser = new UserVo.User();
        paramUser.setUid(accessTokenDto.getAccount().getGuid());
        paramUser.setStatusList(Collections.singletonList(Status.ACTIVE));
        paramUser.setIsDormant(false);
        UserVo.User resultUser = this.userPersist.getUser(paramUser);

        // SUPER 관리자는 프리패스
        if (resultUser.getGrade() == Grade.SUPER) {
            return resultUser;
        }

        //회사 서비스 이용 정보 확인
        CaptainPaymentCompanyInfo serviceInfo =
                captainPaymentCompanyInfoPersist.getByServiceType(resultUser.getComid(), ServiceType.SIKDAE);
        if (ObjectUtils.isEmpty(serviceInfo) || serviceInfo.getStatus() != CaptainPaymentCompanyInfo.Status.ACTIVE) {
            throw new AuthorizedException(Errors.DISABLED_SERVICE_ERROR);
        }

        // 회사 관리자 권한이 없을경우
        if (resultUser.getGrade() == Grade.USER) {
            throw new AuthorizedException(Errors.MEMBER_LEVEL_COMPANY_ERROR,
                    Errors.MEMBER_LEVEL_COMPANY_ERROR.getMessage().replace("{name}", resultUser.getName()));
        }

        // 본인 회사 정보가 아닌경우 에러처리
        if (!"/addons/v1/me".equals(uri) && !resultUser.getComid().equalsIgnoreCase(comId)) {
            throw new AuthorizedException(Errors.MEMBER_LEVEL_ADMIN_ERROR);
        }
        return resultUser;
    }
}
