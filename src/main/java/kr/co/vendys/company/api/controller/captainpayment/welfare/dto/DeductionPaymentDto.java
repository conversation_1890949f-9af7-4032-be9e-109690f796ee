package kr.co.vendys.company.api.controller.captainpayment.welfare.dto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.util.ObjectUtils;

import kr.co.vendys.company.api.controller.ParamDto;
import kr.co.vendys.company.api.controller.captainpayment.welfare.dto.DealDto.StatusType;
import kr.co.vendys.company.api.entity.master.MealGroup.GroupType;
import kr.co.vendys.company.api.util.DateUtil;
import kr.co.vendys.company.api.util.PagingUtil;
import kr.co.vendys.company.api.vo.ExcelVo;
import kr.co.vendys.company.api.vo.PagingVo;
import kr.co.vendys.company.api.vo.UserVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

public class DeductionPaymentDto {

    @Data
    @EqualsAndHashCode(callSuper = false)
    @AllArgsConstructor
    public static class DeductionPaymentParamDto extends PagingVo {

        private String comId;
        private String counponId;
        private String startDate;
        private String endDate;
        private Integer groupIdx;
        private Integer policyIdx;
        private StatusType statusType;
        private String groupType;
        private List<String> userInfo;
        private List<String> orgCodes;
        private Integer page;
        private Integer pagerow;
        private Integer totalcount;
        private Boolean isExcelRetrieve = false;

        public static DeductionPaymentParamDto of(DeductionPaymentReqeust param, ParamDto.RequestData requestData){
            List<String> useInfo = null;
            if (!ObjectUtils.isEmpty(param.getUserInfo())) {
                useInfo = Arrays.asList(StringUtils.split(param.getUserInfo().get(0), "&"));
            }
            return new DeductionPaymentParamDto(
                    requestData.getComId(),
                    "",
                    param.getStartDate(),
                    DateUtil.dateToLocalDateTime(DateUtil.stringToDate(param.getEndDate(), "yyyy-MM-dd")).plusDays(1).toString(),
                    param.getGroupIdx(),
                    param.getPolicyIdx(),
                    param.getStatusType(),
                    GroupType.WELFARE.toString(),
                    useInfo,
                    new ArrayList<String>(),
                    param.getPage(),
                    param.getPagerow(),
                    0,
                    param.getIsExcelRetrieve()
            );
        }
    }

    @Data
    public static class DeductionPaymentDownRequest {

        private String startDate;
        private String endDate;
        private Integer page;
        private Integer pagerow;
        private Integer policyIdx;
        private Integer groupIdx;
        private DealDto.StatusType statusType;
        private GroupType groupType;
        private String userInfo;
        private String orgCode;


        public static DeductionPaymentReqeust deductionPaymentOf (DeductionPaymentDownRequest downRequest) {
            List<String> userInfo = null;
            if (!ObjectUtils.isEmpty(downRequest.getUserInfo())) {
                userInfo = Arrays.asList(downRequest.getUserInfo());
            }
            DeductionPaymentReqeust deductionPaymentReqeust = new DeductionPaymentReqeust();
            deductionPaymentReqeust.setStartDate(downRequest.getStartDate());
            deductionPaymentReqeust.setEndDate(downRequest.getEndDate());
            deductionPaymentReqeust.setPage(1);
            deductionPaymentReqeust.setPagerow(1000);
            deductionPaymentReqeust.setStatusType(downRequest.getStatusType());
            deductionPaymentReqeust.setGroupType(GroupType.WELFARE);
            deductionPaymentReqeust.setUserInfo(userInfo);
            deductionPaymentReqeust.setOrgCode(downRequest.getOrgCode());
            deductionPaymentReqeust.setGroupIdx(downRequest.getGroupIdx());
            deductionPaymentReqeust.setPolicyIdx(downRequest.getPolicyIdx());
            deductionPaymentReqeust.setIsExcelRetrieve(true);

            return deductionPaymentReqeust;
        }

        @Getter
        @Builder
        public static class DeductionPaymentListExcelDto {
            private UserVo.User user;
            private String comId;
            private ExcelVo.ExcelContentType type;
            private String uuid;
            private String domain;
            private String fileName;
            private Date startDate;
            private Date endDate;
            private List<Map<String, Object>> data;
            private List<ExcelVo.ExcelColumn> coldata;
            private boolean asyncable;

            public static DeductionPaymentListExcelDto of(UserVo.User user
                                            , String comId
                                            , ExcelVo.ExcelContentType type
                                            , String uuid
                                            , String domain
                                            , String fileName
                                            , Date startDate
                                            , Date endDate
                                            , List<Map<String, Object>> data
                                            , List<ExcelVo.ExcelColumn> coldata
                                            , boolean asyncable) {
                return DeductionPaymentListExcelDto.builder()
                        .user(user)
                        .comId(comId)
                        .type(type)
                        .uuid(uuid)
                        .domain(domain)
                        .fileName(fileName)
                        .startDate(startDate)
                        .endDate(endDate)
                        .data(data)
                        .coldata(coldata)
                        .asyncable(asyncable)
                        .build();
            }
        }

    }

    @Data
    public static class DeductionPaymentReqeust {

        private String startDate;
        private String endDate;
        private Integer policyIdx;
        private Integer groupIdx;
        private DealDto.StatusType statusType;
        private GroupType groupType;
        private List<String> userInfo;
        private String orgCode;
        private Integer page;
        private Integer pagerow;
        private Boolean isExcelRetrieve = false;
    }

    @Data
    public static class DeductionPaymentResponse {

        private Paging paging;
        private List<DeductionPayment> taskHistorys;

        @Data
        public static class Paging {

            private Integer page;
            private Integer pagerow;
            private long totalcount;

            public static Paging of(DeductionPaymentReqeust request, long totalcount) {
                Paging paging = new Paging();
                paging.setTotalcount(totalcount);
                paging.setPage(request.getPage());
                paging.setPagerow(request.getPagerow());
                PagingUtil.page(request.getPage(), request.getPagerow());

                return paging;
            }
        }


        @Data
        public static class Status {
            private String value;
            private String text;
        }

        @Data
        public static class Policys {
            private String policyName;
            private Integer amount;
        }

        @Data
        public static class DeductionPayment {

            private String useDate;
            private String detailId;
            private String signId;
            private String userName;
            private String orgCode;
            private String orgCodeName;
            private String groupName;
            private List<DeductionPaymentResponse.Policys> Policys;
            private String policyName;
            private Integer amount;
            private String statusType;
            private Status status;
            private String payRoomIdx;
            private boolean canceled = false;
        }
    }


    @Data
    @AllArgsConstructor
    public static class DeducationPaymentDetailParamDto {

        private String comId;
        private String couponId;
        private String groupType;
        private StatusType statusType;

        public static DeducationPaymentDetailParamDto of(DeducationPaymentDetailReqeust reqeust, ParamDto.RequestData requestData) {
            return new DeducationPaymentDetailParamDto(
                    requestData.getComId(),
                    reqeust.getDetailId(),
                    GroupType.WELFARE.toString(),
                    reqeust.getStatusType()
            );
        }

    }
    @Data
    public static class DeducationPaymentDetailReqeust{
        private String detailId;
        private StatusType statusType;
        private Integer page;
        private Integer pagerow;
    }

    @Data
    public static class DeducationPaymentDetailResponse{

        private Detail detail;

        @Data
        public static class Status {
            private String value;
            private String text;
        }

        @Data
        @EqualsAndHashCode(callSuper = false)
        public static class Detail extends DealDto.DealDetailResponse.Detail {
        }

        @Data
        @EqualsAndHashCode(callSuper = false)
        public static class Welfare extends DealDto.DealDetailResponse.Welfare{
        }

        @Data
        @EqualsAndHashCode(callSuper = false)
        public static class Deal extends DealDto.DealDetailResponse.Deal{
        }


        @Data
        @EqualsAndHashCode(callSuper = false)
        public static class CancelDeal extends DealDto.DealDetailResponse.CancelDeal{
        }


        @Data
        @EqualsAndHashCode(callSuper = false)
        public static class Provision extends DealDto.DealDetailResponse.Provision{
        }


        @Data
        @EqualsAndHashCode(callSuper = false)
        public static class Deduction extends DealDto.DealDetailResponse.Provision {
        }

        @Data
        @EqualsAndHashCode(callSuper = false)
        public static class Extinction extends DealDto.DealDetailResponse.Provision {
        }
    }

}