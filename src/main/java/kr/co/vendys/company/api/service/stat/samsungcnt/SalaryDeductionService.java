package kr.co.vendys.company.api.service.stat.samsungcnt;

import kr.co.vendys.company.api.constant.Errors;
import kr.co.vendys.company.api.constant.policydeduction.MealPolicyDeduction;
import kr.co.vendys.company.api.controller.ParamDto;
import kr.co.vendys.company.api.controller.company.entity.DivisionDto;
import kr.co.vendys.company.api.controller.stat.entity.CompanyDto;
import kr.co.vendys.company.api.controller.stat.salarydeduction.Paging;
import kr.co.vendys.company.api.controller.stat.salarydeduction.Stat;
import kr.co.vendys.company.api.controller.stat.salarydeduction.Table;
import kr.co.vendys.company.api.controller.stat.salarydeduction.Total;
import kr.co.vendys.company.api.controller.stat.samsungcnt.SamsungCnTSalaryDeductionResponse;
import kr.co.vendys.company.api.controller.stat.samsungcnt.SamsungCnTSalaryDeductionInfo;
import kr.co.vendys.company.api.exception.CommonException;
import kr.co.vendys.company.api.persist.StatPersist;
import kr.co.vendys.company.api.persist.UserPersist;
import kr.co.vendys.company.api.persist.organization.OrganizationDivisionRemote;
import kr.co.vendys.company.api.service.ExcelService;
import kr.co.vendys.company.api.util.DateUtil;
import kr.co.vendys.company.api.util.DivisionUtil;
import kr.co.vendys.company.api.util.ExcelUtil;
import kr.co.vendys.company.api.vo.ExcelVo;
import kr.co.vendys.company.api.vo.StatVo;
import kr.co.vendys.company.api.vo.UserVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SalaryDeductionService {

    private final StatPersist statPersist;
    private final OrganizationDivisionRemote organizationDivisionRemote;
    private final UserPersist userPersist;
    private final ExcelService excelService;
    private static final int NORMAL_PAYMENT_STATUS_RANGE_START = 0;
    private static final int NORMAL_PAYMENT_STATUS_RANGE_END = 81;

    private static final int PARTIAL_CANCEL_PAYMENT_STATUS_RANGE_START = 80;
    private static final int PARTIAL_CANCEL_PAYMENT_STATUS_RANGE_END = 84;

    private static final int CANCEL_PAYMENT_STATUS_RANGE_START = 0;
    private static final int CANCEL_PAYMENT_STATUS_RANGE_END = 83;

    public SamsungCnTSalaryDeductionResponse getSamsungCnTSalaryDeductionStat(
            ParamDto.RequestData requestData, CompanyDto.SalaryDeductionCouponRequest couponRequest) {

        StatVo.SalaryDeductionSearchParam searchParam = getSalaryDeductionSearchParam(requestData.getComId(), couponRequest);

        List<StatVo.SamsungCnTPaymentInfo> paymentInfo = statPersist.getSamsungCnTPaymentInfo(searchParam);
        List<StatVo.SamsungCnTPaymentInfo> nextMonthCancelPaymentInfo = statPersist.getSamsungCnTNextMonthCancelPaymentInfo(searchParam);

        paymentInfo.addAll(nextMonthCancelPaymentInfo);

        Map<String, List<StatVo.SamsungCnTPaymentInfo>> salaryDeductionMap = getSamsungCnTSalaryDeductionMap(paymentInfo);

        SamsungCnTSalaryDeductionInfo salaryDeductionInfo = getSamsungCnTSalaryDeductionStat(salaryDeductionMap, searchParam);

        Integer totalCount = salaryDeductionInfo.getStats().size();

        DivisionDto.ResponseTree divisionInfos = organizationDivisionRemote.getAllDivision(
                requestData.getUser(), requestData.getComId()
        );

        SamsungCnTSalaryDeductionInfo divisionNameToStats = setDivisionNameToStats(divisionInfos, salaryDeductionInfo);

        int depth = divisionInfos.getDepth();
        List<Table> tables = this.setDeductionCouponStatCell(depth);

        Paging paging = Paging.of(couponRequest, totalCount);

        return SamsungCnTSalaryDeductionResponse.of(divisionNameToStats, tables, depth, paging);
    }

    private Map<String, List<StatVo.SamsungCnTPaymentInfo>> getSamsungCnTSalaryDeductionMap(
            List<StatVo.SamsungCnTPaymentInfo> samsungCnTSalaryDeduction) {

        Map<String, List<StatVo.SamsungCnTPaymentInfo>> salaryDeductionMap = new HashMap<>();

        StringBuilder keyBuilder = new StringBuilder();

        for (StatVo.SamsungCnTPaymentInfo salaryDeduction : samsungCnTSalaryDeduction) {
            String salaryDeductionKey = makeSalaryDeductionKey(salaryDeduction, keyBuilder);
            Integer status = salaryDeduction.getStatus();

            if (isNormalPayment(status)) {
                addSalaryDeductionToMap(salaryDeduction, salaryDeductionMap, salaryDeductionKey);

            } else if (isPartialCancelPayment(status)) {
                minusPartialCancelPriceToMap(salaryDeduction, salaryDeductionMap, salaryDeductionKey);

            } else if (isCancelPayment(status)) {
                dropCancelPaymentToMap(salaryDeduction, salaryDeductionMap, salaryDeductionKey);
            }

        }

        return salaryDeductionMap;
    }

    private StatVo.SalaryDeductionSearchParam getSalaryDeductionSearchParam(String comId, CompanyDto.SalaryDeductionCouponRequest couponRequest) {
        StatVo.SalaryDeductionSearchParam searchParam = new StatVo.SalaryDeductionSearchParam();

        searchParam.setComId(comId);
        searchParam.setUid(couponRequest.getUserId());
        searchParam.setTotalcount(couponRequest.getTotalCount());
        searchParam.setStartDate(couponRequest.getStartDate());
        searchParam.setEndDate(DateUtil.clearLessOfDay(DateUtil.getDateCustomDay(couponRequest.getEndDate(), 1)));
        if (!ObjectUtils.isEmpty(couponRequest.getPage())) {
            searchParam.setPage(couponRequest.getPage() - 1);
            searchParam.setPagerow(couponRequest.getPagerow());
        }

        // 한 회사에 policy 마다 다른경우도 있을수 있지만, 현재는 제외한다.
        MealPolicyDeduction deduction = Arrays.stream(MealPolicyDeduction.values())
                .filter(policyDeduction -> policyDeduction.getComId().equals(comId))
                .findFirst()
                .orElse(MealPolicyDeduction.EMPTY);

        searchParam.setCompanySupportRate(deduction.getCompanySupportRate());
        searchParam.setCompanySupportMaxPrice(deduction.getMaxSupportPrice());
        searchParam.setSupportPolicyIdxList(deduction.getSupportPolicyIdxList());
        searchParam.setIsOnlyTheFirst(deduction.getIsOnlyTheFirst());
        return searchParam;
    }

    private String makeSalaryDeductionKey(StatVo.SamsungCnTPaymentInfo deduction, StringBuilder sb) {
        String deductionSdate = deduction.getSdate();
        String uid = deduction.getUid();
        Long policyIdx = deduction.getPolicyIdx();

        String key = sb.append(deductionSdate).append(uid).append(policyIdx).toString();
        sb.setLength(0);

        return key;
    }

    private void addSalaryDeductionToMap(StatVo.SamsungCnTPaymentInfo salaryDeduction,
                                         Map<String, List<StatVo.SamsungCnTPaymentInfo>> salaryDeductionMap, String salaryDeductionKey) {
        List<StatVo.SamsungCnTPaymentInfo> deductionCoupon =
                salaryDeductionMap.computeIfPresent(salaryDeductionKey, (k, v) -> {
                    v.add(salaryDeduction);
                    return v;
                });

        if (deductionCoupon == null) {
            salaryDeduction.setFirstCompanyPrice();
            salaryDeductionMap.put(salaryDeductionKey, new ArrayList<>(Arrays.asList(salaryDeduction)));
        }
    }

    private void minusPartialCancelPriceToMap(StatVo.SamsungCnTPaymentInfo salaryDeduction,
                                              Map<String, List<StatVo.SamsungCnTPaymentInfo>> salaryDeductionMap, String salaryDeductionKey) {

        List<StatVo.SamsungCnTPaymentInfo> salaryDeductionList = salaryDeductionMap.getOrDefault(salaryDeductionKey, null);

        if (salaryDeductionList == null) {
            throw new CommonException(Errors.SALARY_REDUCTION_CANCEL_PAYMENT_NOT_FOUND);
        }

        salaryDeductionList.stream()
                .filter(d -> d.getCid().equals(salaryDeduction.getOriginCouponId()))
                .forEach(d -> d.partialCancelPrice(salaryDeduction.getCompoint()));
    }


    private SamsungCnTSalaryDeductionInfo getSamsungCnTSalaryDeductionStat(
            Map<String, List<StatVo.SamsungCnTPaymentInfo>> salaryDeductionMap, StatVo.SalaryDeductionSearchParam searchParam) {

        List<Stat> stats = new ArrayList<>();
        Total total = new Total(null, null, null, 0L, 0L, 0L, 0L);

        for (List<StatVo.SamsungCnTPaymentInfo> value : salaryDeductionMap.values()) {

            StatVo.SamsungCnTPaymentInfo firstPayment = value.get(0);

            long sumComPoint = value.stream().mapToLong(StatVo.SamsungCnTPaymentInfo::getCompoint).sum();
            long sumCompanyPrice = value.stream().mapToLong(StatVo.SamsungCnTPaymentInfo::getCompanyprice).sum();

            long companySupportPrice = 0;
            long salaryDeductionPrice = sumComPoint;

            long firstPaymentPrice = firstPayment.getFirstCompanyPrice();
            Double companySupportRate = searchParam.getCompanySupportRate();
            Integer companySupportMaxPrice = searchParam.getCompanySupportMaxPrice();

            if (isAppliedSalaryDeduction(searchParam.getSupportPolicyIdxList(), firstPayment)) {
                long originCompanySupportPrice = (long) Math.min(firstPaymentPrice * companySupportRate, companySupportMaxPrice);

                if (isAppliedCompanySupportPriceRounding(sumComPoint, originCompanySupportPrice)) {
                    companySupportPrice = originCompanySupportPrice + ((sumComPoint - originCompanySupportPrice) % 100);
                    salaryDeductionPrice = (sumComPoint - originCompanySupportPrice) - (sumComPoint - originCompanySupportPrice) % 100;

                    // 공제 계산 이후 companySupportMaxPrice 넘고 companySupportMaxPrice + 100 이하 일 때 회사지원 금액에서 -100
                    if(companySupportPrice > companySupportMaxPrice && companySupportPrice < companySupportMaxPrice + 100){
                        companySupportPrice -= 100;
                        salaryDeductionPrice += 100;
                    }
                } else {
                    companySupportPrice = originCompanySupportPrice;
                    salaryDeductionPrice = sumComPoint - originCompanySupportPrice;
                }
            }


            Stat stat = Stat.of(firstPayment, sumComPoint, sumCompanyPrice, companySupportPrice, salaryDeductionPrice);
            total.sumStat(stat);

            stats.add(stat);
        }

        stats.sort(Comparator.comparing(Stat::getSdate).reversed());

        return new SamsungCnTSalaryDeductionInfo(total, stats);
    }



    private static boolean isAppliedCompanySupportPriceRounding(long sumComPoint, long originCompanySupportPrice) {
        return (sumComPoint - originCompanySupportPrice) % 100 != 0;
    }

    private boolean isAppliedSalaryDeduction(List<Long> supportPolicyIdxList, StatVo.SamsungCnTPaymentInfo firstPayment) {
        return supportPolicyIdxList.contains(firstPayment.getPolicyIdx());
    }


    private boolean isNormalPayment(Integer status) {
        return status >= NORMAL_PAYMENT_STATUS_RANGE_START && status < NORMAL_PAYMENT_STATUS_RANGE_END;
    }


    private boolean isPartialCancelPayment(Integer status) {
        return status > PARTIAL_CANCEL_PAYMENT_STATUS_RANGE_START && status < PARTIAL_CANCEL_PAYMENT_STATUS_RANGE_END;
    }


    private boolean isCancelPayment(Integer status) {
        return status < CANCEL_PAYMENT_STATUS_RANGE_START || status > CANCEL_PAYMENT_STATUS_RANGE_END;
    }

    private void dropCancelPaymentToMap(StatVo.SamsungCnTPaymentInfo salaryDeduction,
                                        Map<String, List<StatVo.SamsungCnTPaymentInfo>> salaryDeductionMap, String salaryDeductionKey) {

        List<StatVo.SamsungCnTPaymentInfo> salaryDeductionList = salaryDeductionMap.getOrDefault(salaryDeductionKey, null);

        if (salaryDeductionList == null) {
            throw new CommonException(Errors.SALARY_REDUCTION_CANCEL_PAYMENT_NOT_FOUND);
        }

        List<StatVo.SamsungCnTPaymentInfo> collect = salaryDeductionList.stream()
                .dropWhile(d -> d.getCid().equals(salaryDeduction.getOriginCouponId()))
                .collect(Collectors.toList());

        if(collect.isEmpty()){
            salaryDeductionMap.remove(salaryDeductionKey);
        } else {
            collect.get(0).setFirstCompanyPrice();
            salaryDeductionMap.put(salaryDeductionKey, collect);
        }

    }

    private SamsungCnTSalaryDeductionInfo setDivisionNameToStats(DivisionDto.ResponseTree divisionInfos, SamsungCnTSalaryDeductionInfo salaryDeduction) {
        Map<String, DivisionDto.DivisionTree> allDivisionMap = DivisionUtil.getDivisionMap("orgIdx", divisionInfos);

        salaryDeduction.getStats().forEach(stat -> {
            DivisionDto.DivisionTree divInfo = allDivisionMap.get("" + stat.getOrgidx());
            if (!ObjectUtils.isEmpty(divInfo)) {
                stat.setFullname(Arrays.asList(divInfo.getFullName().split("\\^_\\^")));
            }
        });

        return salaryDeduction;
    }

    private List<Table> setDeductionCouponStatCell(int maxDepth) {
        CompanyDto.CouponRequest deductionTempRequest = new CompanyDto.CouponRequest();
        deductionTempRequest.setUsersort(true);
        deductionTempRequest.setPolicysort(true);
        deductionTempRequest.setGroupsort(true);
        return this.setCouponStatCellList(deductionTempRequest, maxDepth, true);
    }

    private List<Table> setCouponStatCellList(
            CompanyDto.CouponRequest couponRequest, int maxDepth, boolean isDeduction) {
        // 합계 선택시 : 날짜 제외.
        // 사용자 선택시 : id 및 이름 추가, 식사인원수 제외
        // 부서 선택시 : 부서를 전부 보여줌, 사용자별만 선택한 경우도 부서 보여줌
        // 제휴식당 선택시 : 제회식당 보여줌
        // 식대그릅 선택시 : 식대그룹추가, 정산금액 제외
        // 식대정책 선택시 :  식대정책 추가, 식사인원수-식사횟수-결제횟수 제외
        List<CouponStatCell> cellList = new ArrayList<>();
        for (int i = 0, len = CouponStatCell.values().length; i < len; i++) {
            cellList.add(CouponStatCell.valueOf(CouponStatCell.values()[i].name()));
        }

        // 공제 합계가 아닌경우 : 급여 공제, 회사지원 금액 제거
        if (!isDeduction) {
            cellList.remove(CouponStatCell.SALARY_DEDUCTION_PRICE);
            cellList.remove(CouponStatCell.SUPPORT_PRICE);
        }

        // 합계 선택시 : 날짜 제외.
        if ("SUM".equals(couponRequest.getDatetype())) {
            cellList.remove(CouponStatCell.SDATE);
        }
        // 사용자 선택시 : id 및 이름 추가, 식사인원수 제외
        if (couponRequest.getUsersort() != null && couponRequest.getUsersort()) {
            cellList.remove(CouponStatCell.COUPONMEMBER);
        } else {
            cellList.remove(CouponStatCell.USERNAME);
            cellList.remove(CouponStatCell.SIGNID);
        }
        // 부서 선택시 : 부서를 전부 보여줌, 사용자별만 선택한 경우도 부서 보여줌
        if (couponRequest.getDivisionsort() == null || !couponRequest.getDivisionsort()) {
            // 사용자별이 없는 경우에만 제외한다.
            if (couponRequest.getUsersort() == null || !couponRequest.getUsersort()) {
                cellList.remove(CouponStatCell.DIVISION);
            } else if (couponRequest.getUsersort()) {
                cellList.remove(CouponStatCell.DIVNUM);
            }
        } else {
            cellList.remove(CouponStatCell.DIVNUM);
        }
        // 제휴식당 선택시 : 제회식당 보여줌
        if (couponRequest.getStoresort() == null || !couponRequest.getStoresort()) {
            cellList.remove(CouponStatCell.STORENAME);
        }
        // 식대그릅 선택시 : 식대그룹추가, 정산금액, 결제횟수, 식사인원수 제외
        if (couponRequest.getGroupsort() != null && couponRequest.getGroupsort()) {
            cellList.remove(CouponStatCell.COMPANYPRICE);
            cellList.remove(CouponStatCell.COUPONMEMBER);
            cellList.remove(CouponStatCell.PAYCOUNT);
        } else {
            cellList.remove(CouponStatCell.GROUPNAME);
        }
        // 식대정책 선택시 :  식대정책 추가, 식사인원수 제외
        if (couponRequest.getPolicysort() != null && couponRequest.getPolicysort()) {
            cellList.remove(CouponStatCell.COUPONCOUNT);
        } else {
            cellList.remove(CouponStatCell.POLICYNAME);
        }

        List<Table> cellTable = new ArrayList<>();

        for (int i = 0, len = cellList.size(); i < len; i++) {
            CouponStatCell cell = cellList.get(i);
            if (CouponStatCell.DIVISION.equals(cell)) {
                for (int y = 0; y < maxDepth; y++) {
                    Table cellInfo = new Table();
                    cellInfo.setName(cell.koName + (y + 1));
                    cellInfo.setProperty(cell.enName + (y + 1));
                    cellTable.add(cellInfo);
                }
                continue;
            }
            Table cellInfo = new Table();
            cellInfo.setName(cell.koName);
            cellInfo.setProperty(cell.enName);
            cellTable.add(cellInfo);
        }

        return cellTable;
    }

    public byte[] getSamsungCnTSalaryDeductionStateExcel(CompanyDto.SalaryDeductionCouponExcelRequest deductionExcelRequest) {

        CompanyDto.SalaryDeductionCouponRequest couponRequest = new CompanyDto.SalaryDeductionCouponRequest();
        BeanUtils.copyProperties(deductionExcelRequest, couponRequest);

        UserVo.User paramUser = new UserVo.User();
        paramUser.setUid(deductionExcelRequest.getRequestid());
        UserVo.User user = this.userPersist.getUser(paramUser);

        ParamDto.RequestData requestData = new ParamDto.RequestData();
        requestData.setComId(deductionExcelRequest.getComid());
        requestData.setUser(user);

        SamsungCnTSalaryDeductionResponse samsungCnTSalaryDeductionStat = getSamsungCnTSalaryDeductionStat(requestData, couponRequest);

        CompanyDto.CouponExcelRequest excelRequest = this.convertDeductionExcelRequest(deductionExcelRequest);

        // return null;
        return this.createSaxCouponStatExcel(user, excelRequest, samsungCnTSalaryDeductionStat);
    }

    private CompanyDto.CouponExcelRequest convertDeductionExcelRequest(CompanyDto.SalaryDeductionCouponExcelRequest request) {
        CompanyDto.CouponExcelRequest couponExcelRequest = new CompanyDto.CouponExcelRequest();
        couponExcelRequest.setStartdate(request.getStartDate());
        couponExcelRequest.setEnddate(request.getEndDate());
        couponExcelRequest.setUid(request.getUserId());
        couponExcelRequest.setComid(request.getComid());
        couponExcelRequest.setRequestid(request.getRequestid());
        couponExcelRequest.setUuid(request.getUuid());
        couponExcelRequest.setDomain(request.getDomain());
        couponExcelRequest.setFilename(request.getFileName());
        couponExcelRequest.setDatetype("DD");
        couponExcelRequest.setUsersort(true);
        couponExcelRequest.setGroupsort(true);
        couponExcelRequest.setPolicysort(true);
        return couponExcelRequest;
    }

    private byte[] createSaxCouponStatExcel(UserVo.User user, CompanyDto.CouponExcelRequest excelRequest,
            SamsungCnTSalaryDeductionResponse samsungCnTSalaryDeductionStat) {
        // 메타 정보 생성(sheet : 0)
        SXSSFWorkbook wb = ExcelUtil.initSaxWorkBook(
                excelRequest.getDomain(), excelRequest.getFilename(), "식대 사용 통계",
                excelRequest.getStartdate(), excelRequest.getEnddate()
        );

        if (samsungCnTSalaryDeductionStat.getPaging().getTotalcount() != 0) {
            // 실제 정보 생성(sheet : 1)
            wb = this.setSaxCouponStatExcelData(wb, samsungCnTSalaryDeductionStat);
        }

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            wb.write(baos);
            byte[] rtn = baos.toByteArray();
            // 엑셀 로그 남김
            this.excelService.insertExcelLog(excelRequest.getUuid(),
                    excelRequest.getFilename(),
                    user,
                    excelRequest.getComid(),
                    ExcelVo.ExcelContentType.V2_STAT_COMPANY_COUPON_LIST.getType(),
                    samsungCnTSalaryDeductionStat.getPaging().getTotalcount(),
                    excelRequest.getStartdate(),
                    excelRequest.getEnddate(),
                    rtn,
                    false);
            return rtn;
        } catch (IOException e) {
            e.printStackTrace();
            throw new CommonException(Errors.FILE_EXCEL_CREATE_ERROR);
        }
    }

    private SXSSFWorkbook setSaxCouponStatExcelData(
            SXSSFWorkbook wb,
            SamsungCnTSalaryDeductionResponse salaryDeductionStat) {
        SXSSFSheet sheet = wb.getSheetAt(1);
        Row row = sheet.createRow(0);
        Cell cell = null;

        Font headerFont = wb.createFont();
        //선택된 부서의 depth를 알고 division을 추가한다
        int depth = salaryDeductionStat.getDepth();
        // header row 생성
        List<Table> headelCellList = salaryDeductionStat.getTable();
        //// cell header 입력
        int cellNum = -1;
        for (int i = 0; i < headelCellList.size(); i++) {
            cell = row.createCell(++cellNum);
            cell.setCellValue(headelCellList.get(i).getName());
            cell.setCellStyle(ExcelUtil.getSaxCellStyleHeader(wb, headerFont, 10));
        }
        // header name으로 분기처리를 하기 위해 map를 생성
        Map<String, String> headerMap = new HashMap<>();
        headelCellList.forEach(headerCellInfo -> headerMap.put(headerCellInfo.getProperty(), headerCellInfo.getProperty()));

        Font blackfont = wb.createFont();
        blackfont.setColor(IndexedColors.BLACK.getIndex());

        Font redfont = wb.createFont();
        redfont.setColor(IndexedColors.RED.getIndex());

        CellStyle minusCellStyle = getSaxCellStyle(wb, redfont, true, 10, "#,##0;[Red]-#,##0");
        CellStyle plusCellStyle = getSaxCellStyle(wb, blackfont, true, 10, "#,##0");
        CellStyle noneCellStyle = getSaxCellStyle(wb, blackfont, true, 10, "");

        int rowNum = 0;
        // 합계 row 추가를 위한 합계 cell의 길이를 구한다
        int colLength = headelCellList.size();
        row = sheet.createRow(++rowNum);
        // 합계 row 추가
        setSaxTotalCouponStatRow(
                wb, row, colLength, headerMap, salaryDeductionStat.getTotal(), minusCellStyle, plusCellStyle, noneCellStyle
        );
        ++rowNum;

        // 실제 데이터 추가
        List<Stat> data = salaryDeductionStat.getStat();
        for (int i = 0; i < data.size(); i++) {
            try {
                row = sheet.createRow(rowNum);
                Stat stat = data.get(i);
                setSaxCouponStatRow(wb, row, depth, headerMap, stat, minusCellStyle, plusCellStyle, noneCellStyle);
                ++rowNum;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return wb;
    }

    private CellStyle getSaxCellStyle(SXSSFWorkbook wb, Font font, boolean type, int size, String format) {
        CellStyle rtn = wb.createCellStyle();
        font.setBold(false);
        font.setFontHeight((short) (size * 20));
        font.setFontName("맑은 고딕");
        rtn.setFont(font);
        if (type) {
            DataFormat numFormat = wb.createDataFormat();
            rtn.setDataFormat(numFormat.getFormat(format));
            rtn.setAlignment(HorizontalAlignment.RIGHT);
        } else {
            rtn.setAlignment(HorizontalAlignment.LEFT);
        }

        rtn.setFillForegroundColor(new XSSFColor(new java.awt.Color(255, 255, 255)).getIndex());
        rtn.setFillPattern(FillPatternType.NO_FILL);
        return rtn;
    }

    private void setSaxTotalCouponStatRow(
            SXSSFWorkbook wb, Row row, int colLength,
            Map<String, String> headerMap,
            Total totalData,
            CellStyle minusCellStyle, CellStyle plusCellStyle, CellStyle noneCellStyle) {

        CellStyle borderStyle = wb.createCellStyle();
        borderStyle.setBorderBottom(BorderStyle.THICK);

        Cell titleCell = row.createCell(0);
        titleCell.setCellValue("합계");
        titleCell.setCellStyle(borderStyle);

        if (headerMap.containsKey(CouponStatCell.SALARY_DEDUCTION_PRICE.enName)) {
            Cell cell = row.createCell(--colLength);
            setSaxCellNumberNStyle(cell, totalData.getSalaryDeductionPrice(), minusCellStyle, plusCellStyle, noneCellStyle);
            CellStyle cellStyle = cell.getCellStyle();
            cellStyle.setBorderBottom(BorderStyle.THICK);
        }
        if (headerMap.containsKey(CouponStatCell.SUPPORT_PRICE.enName)) {
            Cell cell = row.createCell(--colLength);
            setSaxCellNumberNStyle(cell, totalData.getCompanySupportPrice(), minusCellStyle, plusCellStyle, noneCellStyle);
            CellStyle cellStyle = cell.getCellStyle();
            cellStyle.setBorderBottom(BorderStyle.THICK);
        }
        if (headerMap.containsKey(CouponStatCell.COMPANYPRICE.enName)) {
            Cell cell = row.createCell(--colLength);
            setSaxCellNumberNStyle(cell, totalData.getCompanyprice(), minusCellStyle, plusCellStyle, noneCellStyle);
            CellStyle cellStyle = cell.getCellStyle();
            cellStyle.setBorderBottom(BorderStyle.THICK);
        }
        if (headerMap.containsKey(CouponStatCell.SALESPRICE.enName)) {
            Cell cell = row.createCell(--colLength);
            setSaxCellNumberNStyle(cell, totalData.getSalesprice(), minusCellStyle, plusCellStyle, noneCellStyle);
            CellStyle cellStyle = cell.getCellStyle();
            cellStyle.setBorderBottom(BorderStyle.THICK);
        }
        if (headerMap.containsKey(CouponStatCell.PAYCOUNT.enName)) {
            Cell cell = row.createCell(--colLength);
            setSaxCellNumberNStyle(cell, totalData.getPaycount(), minusCellStyle, plusCellStyle, noneCellStyle);
            CellStyle cellStyle = cell.getCellStyle();
            cellStyle.setBorderBottom(BorderStyle.THICK);
        }
        if (headerMap.containsKey(CouponStatCell.COUPONCOUNT.enName)) {
            Cell cell = row.createCell(--colLength);
            setSaxCellNumberNStyle(cell, totalData.getCouponcount(), minusCellStyle, plusCellStyle, noneCellStyle);
            CellStyle cellStyle = cell.getCellStyle();
            cellStyle.setBorderBottom(BorderStyle.THICK);
        }
        if (headerMap.containsKey(CouponStatCell.COUPONMEMBER.enName)) {
            Cell cell = row.createCell(--colLength);
            setSaxCellNumberNStyle(cell, totalData.getCouponmember(), minusCellStyle, plusCellStyle, noneCellStyle);
            CellStyle cellStyle = cell.getCellStyle();
            cellStyle.setBorderBottom(BorderStyle.THICK);
        }

        for (int i = 1; i < colLength; i++) {
            Cell noneCell = row.createCell(i);
            noneCell.setCellStyle(borderStyle);
        }
    }

    private void setSaxCellNumberNStyle(
            Cell cell, Long value, CellStyle minusCellStyle, CellStyle plusCellStyle, CellStyle noneCellStyle) {

        cell.setCellValue(value);
        if (value < 0) {
            cell.setCellStyle(minusCellStyle);
        } else if (value > 0) {
            cell.setCellStyle(plusCellStyle);
        } else {
            cell.setCellStyle(noneCellStyle);
        }
    }

    private void setSaxCouponStatRow(
            SXSSFWorkbook wb, Row row, int depth,
            Map<String, String> headerMap,
            Stat stat,
            CellStyle minusCellStyle, CellStyle plusCellStyle, CellStyle noneCellStyle) {

        // CouponStatCell 의 enName 과 headerMap의 key를 비교하여 필요값만 입력
        int cellCnt = -1;
        if (headerMap.containsKey(CouponStatCell.SDATE.enName)) {
            setSaxCellStringNStyle(wb, row.createCell(++cellCnt), stat.getSdate());
        }
        if (headerMap.containsKey(CouponStatCell.SIGNID.enName)) {
            setSaxCellStringNStyle(wb, row.createCell(++cellCnt), stat.getSignid());
        }
        if (headerMap.containsKey(CouponStatCell.USERNAME.enName)) {
            setSaxCellStringNStyle(wb, row.createCell(++cellCnt), stat.getUsername());
        }
        if (headerMap.containsKey(CouponStatCell.COMIDNUM.enName)) {
            setSaxCellStringNStyle(wb, row.createCell(++cellCnt), stat.getComidnum());
        }
        if (headerMap.containsKey(CouponStatCell.DIVNUM.enName)) {
            setSaxCellNumberNStyle(
                    row.createCell(++cellCnt), stat.getDivisioncount(), minusCellStyle, plusCellStyle, noneCellStyle
            );
        } else {
            List<String> divNameList = stat.getFullname();
            ++cellCnt;
            if (divNameList != null) {
                for (int x = 0, len = divNameList.size(); x < len; x++) {
                    setSaxCellStringNStyle(wb, row.createCell(cellCnt + x), divNameList.get(x));
                }
            }
            cellCnt += depth - 1;
        }
        if (headerMap.containsKey(CouponStatCell.STORENAME.enName)) {
            setSaxCellStringNStyle(wb, row.createCell(++cellCnt), stat.getStorename());
        }
        if (headerMap.containsKey(CouponStatCell.GROUPNAME.enName)) {
            setSaxCellStringNStyle(wb, row.createCell(++cellCnt), stat.getGroupname());
        }
        if (headerMap.containsKey(CouponStatCell.POLICYNAME.enName)) {
            setSaxCellStringNStyle(wb, row.createCell(++cellCnt), stat.getPolicyname());
        }
        if (headerMap.containsKey(CouponStatCell.COUPONMEMBER.enName)) {
            setSaxCellNumberNStyle(
                    row.createCell(++cellCnt), stat.getCouponmember(), minusCellStyle, plusCellStyle, noneCellStyle
            );
        }
        if (headerMap.containsKey(CouponStatCell.COUPONCOUNT.enName)) {
            setSaxCellNumberNStyle(
                    row.createCell(++cellCnt), stat.getCouponcount(), minusCellStyle, plusCellStyle, noneCellStyle
            );
        }
        if (headerMap.containsKey(CouponStatCell.PAYCOUNT.enName)) {
            setSaxCellNumberNStyle(row.createCell(++cellCnt), stat.getPaycount(), minusCellStyle, plusCellStyle, noneCellStyle);
        }
        if (headerMap.containsKey(CouponStatCell.SALESPRICE.enName)) {
            setSaxCellNumberNStyle(row.createCell(++cellCnt), stat.getSalesprice(), minusCellStyle, plusCellStyle, noneCellStyle);
        }
        if (headerMap.containsKey(CouponStatCell.COMPANYPRICE.enName)) {
            setSaxCellNumberNStyle(
                    row.createCell(++cellCnt), stat.getCompanyprice(), minusCellStyle, plusCellStyle, noneCellStyle
            );
        }
        if (headerMap.containsKey(CouponStatCell.SUPPORT_PRICE.enName)) {
            setSaxCellNumberNStyle(row.createCell(++cellCnt), stat.getCompanySupportPrice(), minusCellStyle, plusCellStyle,
                    noneCellStyle);
        }
        if (headerMap.containsKey(CouponStatCell.SALARY_DEDUCTION_PRICE.enName)) {
            setSaxCellNumberNStyle(row.createCell(++cellCnt), stat.getSalaryDeductionPrice(), minusCellStyle, plusCellStyle,
                    noneCellStyle);
        }
    }

    private void setSaxCellStringNStyle(SXSSFWorkbook wb, Cell cell, String value) {
        cell.setCellValue(value);
    }
}
