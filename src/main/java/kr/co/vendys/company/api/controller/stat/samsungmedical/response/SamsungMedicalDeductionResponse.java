package kr.co.vendys.company.api.controller.stat.samsungmedical.response;

import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SamsungMedicalDeductionResponse {
    private Paging paging;
    private Integer depth;
    private List<Table> table;
    private List<Stat> stat;
    private Total total;

    public static SamsungMedicalDeductionResponse of(Total total, List<Stat> stats, List<Table> tables, Paging paging, Integer depth) {
        SamsungMedicalDeductionResponse response = new SamsungMedicalDeductionResponse();
        response.setDepth(depth);
        response.setTable(tables);
        response.setStat(getPageItems(stats, paging));
        response.setPaging(paging);
        response.setTotal(total);
        return response;
    }

    public static List<Stat> getPageItems(List<Stat> items, Paging paging) {
        if (paging.getPage() == null || paging.getPageRow() == null){
            return items;
        }
        int fromIndex = (paging.getPage() - 1) * paging.getPageRow();
        int toIndex = Math.min(fromIndex + paging.getPageRow(), items.size());

        if (fromIndex > items.size() - 1) {
            return new ArrayList<>(); // 요청된 페이지가 리스트 범위를 초과하는 경우, 빈 리스트 반환
        }

        return new ArrayList<>(items.subList(fromIndex, toIndex));
    }
}
