package kr.co.vendys.company.api.controller.member;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.websocket.server.PathParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import kr.co.vendys.company.api.controller.CommonController;
import kr.co.vendys.company.api.controller.ParamDto;
import kr.co.vendys.company.api.controller.member.entity.GroupDto;
import kr.co.vendys.company.api.service.member.MemberGroupService;

/**
 * Created by jangjungsu on 2017. 6. 1..
 */
@RestController(value = "memberGroupController")
@RequestMapping("/member")
public class GroupController extends CommonController {

    @Autowired
    private MemberGroupService memberGroupService;

    /**
     * 임직원 관리 > 목록 / 식대 지급 내역 > 목록
     */
    @GetMapping(value = "/v1/group")
    public ResponseEntity getGroupUser(HttpServletRequest request, @Valid @ModelAttribute GroupDto.ListRequest listRequest) {

        ParamDto.RequestData requestData = this.getHeaderData(request);

        GroupDto.ListResponse response = this.memberGroupService.getGroupUserList(requestData, listRequest);

        return new ResponseEntity(response, HttpStatus.OK);
    }

    /**
     * 임직원 관리 > 목록 / 식대 지급 내역 > 목록
     */
    @GetMapping(value = "/v1/group/{id}")
    public ResponseEntity getGroupByUser(HttpServletRequest request,
                                         @Valid @PathParam(value = "id") GroupDto.PathParam pathParam,
                                         @Valid @ModelAttribute GroupDto.ListRequest listRequest) {

        ParamDto.RequestData requestData = this.getHeaderData(request);

        GroupDto.GroupResponse response = this.memberGroupService.getGroupUser(requestData, pathParam, listRequest);

        return new ResponseEntity(response, HttpStatus.OK);
    }

}
