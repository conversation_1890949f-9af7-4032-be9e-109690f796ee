package kr.co.vendys.company.api.entity.master;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import lombok.Data;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

@Data
@Entity
public class CouponMember {

    @EmbeddedId
    private CouponMemberId couponMemberId;

    private String username;
    @Column(name = "menuname")
    private String menuName;
    @Column(name = "comid")
    private String comId;
    @Column(name = "comname")
    private String comName;
    private Long officeIdx;
    private Long orgIdx;
    private String division;
    private String division2;
    private String division3;
    private String sid;
    @Column(name = "storename")
    private String storeName;
    @Column(name = "compoint")
    private Long comPoint;
    @Column(name = "mypoint")
    private Long myPoint;
    @Column(name = "instantPayPrice")
    private Long instantPayPrice;
    @Column(name = "paytype")
    private Long payType;
    private Long price;
    @Column(name = "companyprice")
    private Long companyPrice;
    @Column(name = "supplyprice")
    private Long supplyPrice;
    @Column(name = "coupontype")
    private String couponType;
    @Column(name = "usedate")
    @Temporal(TemporalType.TIMESTAMP)
    private Date useDate;
    @Column(name = "eatmid")
    private String eatMid;
    @Column(name = "eatmenuname")
    private String eatMenuName;
    @Column(name = "eatcalory")
    private Long eatCalory;

    @Fetch(FetchMode.JOIN)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "cid", insertable = false, updatable = false)
    private CouponGroup couponGroup;

    @Fetch(FetchMode.JOIN)
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "uid", insertable = false, updatable = false)
    private User user;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "cid", referencedColumnName = "couponId", insertable = false, updatable = false)
    private PayRoom payRoom;

}
