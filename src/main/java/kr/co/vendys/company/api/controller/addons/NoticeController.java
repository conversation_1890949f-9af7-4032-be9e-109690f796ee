package kr.co.vendys.company.api.controller.addons;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.websocket.server.PathParam;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import kr.co.vendys.company.api.controller.CommonController;
import kr.co.vendys.company.api.controller.ParamDto;
import kr.co.vendys.company.api.controller.addons.entity.NoticeDto;
import kr.co.vendys.company.api.service.addons.NoticeService;
import lombok.RequiredArgsConstructor;

/**
 * Created by jangjungsu on 2017. 5. 30..
 */
@RestController
@RequestMapping("/addons")
@RequiredArgsConstructor
public class NoticeController extends CommonController {

    private final NoticeService noticeService;

    /**
     * 회사관리 > 회사 공지사항
     */
    @GetMapping(value = "/v1/notice")
    public ResponseEntity<NoticeDto.ListResponse> getNoticeList(
            HttpServletRequest request, @Valid @ModelAttribute NoticeDto.ListRequest listRequest) {

        ParamDto.RequestData requestData = this.getHeaderData(request);

        NoticeDto.ListResponse response = this.noticeService.getNoticeList(requestData.getComId(), listRequest);

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    /**
     * 회사관리 > 회사 공지사항
     */
    @GetMapping(value = "/v1/notice/{id}")
    public ResponseEntity<NoticeDto.DetailResponse> getNoticeDetail(
            HttpServletRequest request, @Valid @PathParam(value = "id") NoticeDto.PathParam pathParam) {

        ParamDto.RequestData requestData = this.getHeaderData(request);

        NoticeDto.DetailResponse response = this.noticeService.getNotice(requestData.getComId(), pathParam.getId());

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    /**
     * 회사관리 > 회사 공지사항
     */
    @PostMapping(value = "/v1/notice")
    public ResponseEntity<Void> setNotice(HttpServletRequest request, @Valid @RequestBody NoticeDto.BodyRequest bodyRequest) {

        ParamDto.RequestData requestData = this.getHeaderData(request);

        this.noticeService.setNotice(requestData.getUser(), requestData.getComId(), bodyRequest);

        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    /**
     * 회사관리 > 회사 공지사항
     */
    @PutMapping(value = "/v1/notice/{id}")
    public ResponseEntity<Void> setNoticeUpd(
            HttpServletRequest request,
            @Valid @PathParam(value = "id") NoticeDto.PathParam pathParam,
            @Valid @RequestBody NoticeDto.BodyRequest bodyRequest) {

        ParamDto.RequestData requestData = this.getHeaderData(request);

        this.noticeService.setNoticeUpd(requestData.getUser(), requestData.getComId(), pathParam.getId(), bodyRequest);

        return new ResponseEntity<>(HttpStatus.OK);
    }
}
