package kr.co.vendys.company.api.interceptor;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Enumeration;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import org.slf4j.MDC;
import kr.co.vendys.company.api.vo.UserVo;

/**
 * Created by jinwoo on 2017. 11. 8.
 */
@Component
public class LogstashLogging {

    public void preHandle(HttpServletRequest request) {
        this.setAppGroupMdc(request);
        this.setHttpReuqestGroupMdc(request);
        this.setHttpRemoteGroupMdc(request);
    }

    public void afterCompletion(HttpServletRequest request, HttpServletResponse response) {

        this.setHttpResponseGroupMdc(request, response);
        this.setUaMdc(request);
    }

    private void setAppGroupMdc(HttpServletRequest request) {
        MDC.put("app-transaction", String.valueOf(System.currentTimeMillis()));
        MDC.put("app-installation",
                ObjectUtils.isEmpty(request.getHeader("app-installation")) ? "" : request.getHeader("app-installation"));
        MDC.put("app-timestamp",
                LocalDateTime.now().atZone(ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        MDC.put("app-access-token",ObjectUtils.isEmpty(request.getHeader("authorization")) ? "" :
                        this.parsingAccessToken(request.getHeader("authorization"))
        );
    }

    private void setHttpReuqestGroupMdc(HttpServletRequest request) {

        MDC.put("http-request-uri",
                ObjectUtils.isEmpty(request.getAttribute("uri"))
                        ? "" : request.getAttribute("uri").toString()
        );
        MDC.put("http-request-method", request.getMethod());

        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            MDC.put("http-request-header." + headerName, headerValue);
        }
        MDC.put("http-request-body",
                ObjectUtils.isEmpty(request.getAttribute("requestBody"))
                        ? "" : request.getAttribute("requestBody").toString()
        );
    }

    private void setHttpResponseGroupMdc(HttpServletRequest request, HttpServletResponse response) {

        MDC.put("http-response-time", String.valueOf(this.getRuntime(request)));
        MDC.put("http-response-status", String.valueOf(response.getStatus()));
        MDC.put("http-response-body", "");
    }

    private void setHttpRemoteGroupMdc(HttpServletRequest request) {

        MDC.put("http-remote-ip", request.getRemoteHost());
    }

    private void setUaMdc(HttpServletRequest request) {
        String userId = request.getHeader("x-userid");
        String comId = request.getHeader("x-comid");
        
        // 헤더에 값이 없으면 userInfo에서 가져오기
        if (ObjectUtils.isEmpty(userId) || ObjectUtils.isEmpty(comId)) {
            Object userInfoObj = request.getAttribute("userInfo");
            if (userInfoObj instanceof UserVo.User) {
                UserVo.User userInfo = (UserVo.User) userInfoObj;
                
                if (ObjectUtils.isEmpty(userId) && !ObjectUtils.isEmpty(userInfo.getUid())) {
                    userId = userInfo.getUid();
                }
                if (ObjectUtils.isEmpty(comId) && !ObjectUtils.isEmpty(userInfo.getComid())) {
                    comId = userInfo.getComid();
                }
            }
        }
        
        MDC.put("ua-user-id", ObjectUtils.isEmpty(userId) ? "" : String.valueOf(userId));
        MDC.put("ua-com-id", ObjectUtils.isEmpty(comId) ? "" : comId);
    }

    private long getRuntime(HttpServletRequest request) {
        return System.currentTimeMillis() - (long) request.getAttribute("ts");
    }

    private String getHeaderParse(HttpServletRequest request) {

        StringBuffer sb = new StringBuffer();
        Enumeration headers = request.getHeaderNames();
        while (headers.hasMoreElements()) {

            String headerName = (String) headers.nextElement();
            String value = request.getHeader(headerName);

            sb.append(headerName);
            sb.append(": ");
            sb.append(value);
            sb.append("\n");
        }
        return sb.toString();
    }

    private String parsingAccessToken(String header) {
        Pattern p = Pattern.compile("Bearer\\s(.*)");
        Matcher m = p.matcher(header);
        if (m.find()) {
            return m.group(1);
        }
        return "";
    }
}
