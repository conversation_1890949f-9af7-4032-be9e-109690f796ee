package kr.co.vendys.company.api.repository.master;

import java.util.List;

import kr.co.vendys.company.api.vo.BookingReportVo;

public interface BookingReportMapper {
    List<BookingReportVo.BookingReport> selectBookingReport(BookingReportVo.BookingReportEx bookingReportEx);

    List<BookingReportVo.BookingReportV2> selectBookingReportV2(BookingReportVo.BookingReportEx bookingReportEx);

    List<BookingReportVo.BookingReportHistory> selectBookingReportHistory(
            BookingReportVo.BookingReportHistoryEx bookingReportEx
    );

    BookingReportVo.BookingReportHistoryCount selectBookingReportHistoryCount(
            BookingReportVo.BookingReportHistoryEx bookingReportEx
    );

    List<BookingReportVo.BookingReportHistoryV2> selectBookingReportHistoryV2(
            BookingReportVo.BookingReportHistoryEx bookingReportEx
    );

    BookingReportVo.BookingReportHistoryCount selectBookingReportHistoryCountV2(
            BookingReportVo.BookingReportHistoryEx bookingReportEx
    );

    List<BookingReportVo.BookingMenu> selectBookingMenu(List<Long> bookingHistoryIdxList);
}
