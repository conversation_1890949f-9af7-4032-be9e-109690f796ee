package kr.co.vendys.company.api.persist.quick.kakao.dto;

import kr.co.vendys.company.api.exception.CommonException;
import kr.co.vendys.core.schema.mealcoupon.entity.QuickStatusLog;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public class KakaoApiResult<T> {
    private T response;
    private CommonException exception;
    private QuickStatusLog log;

    public boolean isSuccess() {
        return exception == null;
    }

    public static <T> KakaoApiResult<T> success(T response, QuickStatusLog log) {
        return new KakaoApiResult<>(response, null, log);
    }

    public static <T> KakaoApiResult<T> failure(CommonException exception, QuickStatusLog log) {
        return new KakaoApiResult<>(null, exception, log);
    }
}
