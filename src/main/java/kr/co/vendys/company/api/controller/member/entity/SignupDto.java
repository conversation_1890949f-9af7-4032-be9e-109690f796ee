package kr.co.vendys.company.api.controller.member.entity;

import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * Created by jangjungsu on 2017. 5. 25..
 */
public class SignupDto {

    @Data
    public static class StatusCodeResponse {

        private List<Status> status;

        @Data
        public static class Status {

            private String value;
            private String text;
        }
    }

    @Data
    public static class SignupUserRequest {

        private String keyword;
        private String status;
        private Integer page;
        private Integer pagerow;
    }

    @Data
    public static class SignupUserResponse {

        private Paging paging;
        private List<Send> send;

        @Data
        public static class Paging {

            private Integer page;
            private Integer pagerow;
            private Integer totalcount;
        }

        @Data
        public static class Send {

            private Long signupUserIdx;
            private Integer idx;
            private Date senddate;
            private Date expiredate;
            private Value value;
            private String signid;
            private Status status;
            private Group group;

            @Data
            public static class Value {

                private String text;
                private String type;
            }

            @Data
            public static class Status {

                private String value;
                private String text;
            }

            @Data
            public static class Group {

                private Long idx;
                private String name;
                private Boolean isActive;
            }
        }
    }

    @Data
    public static class ResendRequest {

        @NotNull
        private Resend resend;

        @Data
        public static class Resend {

            private Value value;
            @NotNull
            private Long groupidx;

            @Data
            public static class Value {

                @NotNull
                private String text;
                @NotNull
                private String type;
            }
        }
    }

    @Data
    public static class CancelRequest {

        private Long cancelIdx;
    }

    @Data
    public static class CheckRequest {

        private Check check;

        @Data
        public static class Check {

            private List<String> phone;
            private List<String> email;
        }
    }

    @Data
    public static class CheckResponse {

        private Check check;

        @Data
        public static class Check {

            private List<Valid> valid;
            private List<InValid> invalid;

            @Data
            public static class Valid {

                private String value;
                private String type;
            }

            @Data
            public static class InValid {

                private String value;
                private String type;
                private String reason;
            }
        }
    }

    @Data
    public static class SendRequest {

        private Send send;

        @Data
        public static class Send {

            private List<String> phone;
            private List<String> email;
            private Long groupidx;
        }
    }
}
