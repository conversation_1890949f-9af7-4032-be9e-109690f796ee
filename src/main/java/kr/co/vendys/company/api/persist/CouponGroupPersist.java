package kr.co.vendys.company.api.persist;

import java.time.LocalDateTime;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import kr.co.vendys.company.api.entity.master.CouponGroup;
import kr.co.vendys.company.api.repository.master.CouponGroupRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class CouponGroupPersist {

    private final CouponGroupRepository couponGroupRepository;

    public Optional<CouponGroup> find(String cid) {
        return this.couponGroupRepository.findById(cid);
    }

    public Page<Object[]> findCompointSum(String comId, String keyword, LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        return this.couponGroupRepository.findCompointSum(comId, keyword, startDate, endDate, pageable);
    }
}
