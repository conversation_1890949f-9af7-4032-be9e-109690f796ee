package kr.co.vendys.company.api.persist;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import kr.co.vendys.company.api.controller.captainpayment.benefit.dto.BenefitDto.BenefitInquiryRequest;
import kr.co.vendys.company.api.entity.master.BenefitInquiry;
import kr.co.vendys.company.api.repository.master.BenefitInquiryRepository;
import lombok.RequiredArgsConstructor;

@Service
@Transactional
@RequiredArgsConstructor
public class BenefitInquiryPersist {

    private final BenefitInquiryRepository benefitInquiryRepository;

    public BenefitInquiry save(BenefitInquiryRequest postRequest) {
        return benefitInquiryRepository.save(
            BenefitInquiry.builder()
                .comId(postRequest.getComId())
                .managerName(postRequest.getManagerName())
                .managerEmail(postRequest.getManagerEmail())
                .managerTel(postRequest.getManagerTel())
                .content(postRequest.getContent())
            .build()
        );
    }
}
