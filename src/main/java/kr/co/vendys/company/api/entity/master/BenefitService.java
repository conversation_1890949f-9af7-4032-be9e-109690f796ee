package kr.co.vendys.company.api.entity.master;


import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;

import kr.co.vendys.core.schema.mealcoupon.entity.common.CommonEntityByAuditing;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters.LocalDateTimeConverter;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;


@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
public class BenefitService extends CommonEntityByAuditing {

    @Column(name = "benefitServiceId")
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 15)
    private String serviceName;

    @Column(nullable = false, length = 15)
    private String serviceOfferCompanyName;

    @Column(nullable = false, length = 35)
    private String benefitName;

    @Column(nullable = false, length = 300)
    private String companyLogoImgUrl;

    @Column(nullable = false, length = 300)
    private String descImgUrl;

    @Column(length = 200)
    private String urlLink = "";

    @Column(nullable = false, length = 255)
    private String managerEmail = "";

    private String memo;

    private Byte orderNo = 0;

    private boolean exposureFlag;

    private boolean isDelete = false;

    private boolean priorityFlag;

    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime exposureStartDate;

    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime exposureEndDate;

    @OneToOne(mappedBy = "benefitService")
    private BenefitDetail benefitDetail;

    @OneToOne
    @JoinColumn(name = "hashTagIdx")
    private HashTag hashTag;
}