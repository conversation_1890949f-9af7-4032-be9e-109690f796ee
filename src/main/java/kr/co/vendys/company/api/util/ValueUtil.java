package kr.co.vendys.company.api.util;

import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by jangjungsu on 2017. 7. 6..
 */
public class ValueUtil {

    /**
     * HashCode
     */
    public static String getHash() {
        SecureRandom sr = new SecureRandom();
        return new BigInteger(200, sr).toString(32);
    }

    public static String getRandomStringEightLength() {
        List<String> tempCharter = new ArrayList<>();
        double min = 65;
        double max = 90;
        double minNumber = 0;
        double maxNumber = 9;
        for (int i = 0; i < 8; i++) {
            int random;
            if (i % 2 == 0) {
                random = (int) ((Math.random() * (maxNumber - minNumber)) + minNumber);
                tempCharter.add(String.valueOf(random));
            } else {
                random = (int) ((Math.random() * (max - min)) + min);
                tempCharter.add(String.valueOf((char) random));
            }
        }
        Collections.shuffle(tempCharter);
        return String.join("", tempCharter);
    }
}
