package kr.co.vendys.company.api.persist.quick.kakao.exception;

import kr.co.vendys.company.api.constant.Errors;
import lombok.Getter;

@Getter
public enum KakaoErrors {
    INVALID_ORDER(-20006, "올바르지 못한 주문입니다."),
    ADDRESS_NOT_FOUND(-20001, "주소가 없거나, 찾지 못했습니다."),
    INVALID_PICKUP_TIME(-20009, "픽업시간이 올바르지 않습니다."),
    INVALID_WALK_DELIVERY_DISTANCE(-20002, "도보 배송 가능 거리가 아닙니다."),
    ORDER_NOT_FOUND(-20010, "존재하지 않는 주문 입니다."),
    INVALID_ORDER_STATUS(-20013, "오더의 상태가 유효하지 않습니다."),
    ORDER_ALREADY_CANCELLED(-20019, "이미 취소되었거나 취소 불가능한 오더입니다."),
    COORDINATE_ADDRESS_MISMATCH(-20020, "좌표와 주소지가 5 KM 이상 차이납니다."),
    DRIVER_NOT_FOUND(-30001, "기사를 찾을 수 없습니다."),
    OUT_OF_OPERATION_HOURS(-50001, "운영 시간이 아닙니다."),
    SERVER_ERROR(-50003, "카카오 주문 서버 에러가 발생했습니다."),
    UNSUPPORTED_PACKAGE_SIZE(-50004, "지원하지 않는 배송 상품 크기 입니다."),
    SAFE_DELIVERY_NOT_SUPPORTED(-50006, "안전 배송을 지원하지 않습니다."),
    BILLING_MISSING_PARAMETER(-60001, "빌링 필수 파라미터중에 없는 것이 있습니다."),
    BILLING_INVALID_PAYMENT_TYPE(-60002, "빌링 결제 타입이 올바르지 않습니다."),
    BILLING_INVALID_VERTICAL_CODE(-60003, "빌링 버티컬 코드가 유효하지 않습니다."),
    BILLING_INVALID_ITEM(-60004, "빌링 아이템이 유효하지 않습니다."),
    BILLING_GROUP_CANNOT_USE_VERTICAL(-60005, "그룹이 해당 버티컬을 이용할 수 없습니다."),
    BILLING_NO_CARD_ASSIGNED(-60006, "그룹에 할당된 카드가 없습니다."),
    BILLING_INVALID_CARD(-60007, "카드가 유효하지 않습니다."),
    BILLING_GROUP_NOT_FOUND(-60008, "존재하지 않는 그룹입니다."),
    BILLING_ITEM_NOT_AVAILABLE_FOR_GROUP(-60009, "해당 빌링 아이템을 이용할 수 없는 그룹입니다."),
    BILLING_OUTSTANDING_BALANCE(-60010, "미수 내역이 존재합니다."),
    INVALID_TOKEN(90002, "유효하지 않거나 만료된 토큰입니다."),
    UNAUTHORIZED_ACCESS(90006, "허용 되지 않은 접근 입니다."),
    UNREGISTERED_VENDOR(90007, "등록 되지 않은 vendor 정보 입니다."),
    MISSING_VENDOR(90011, "필수 파라미터(vendor)를 확인해주세요."),
    MISSING_AUTHORIZATION(90012, "필수 파라미터(authorization)를 확인해주세요."),
    GENERAL_SERVER_ERROR(90001, "카카오 인증 서버 에러가 발생하였습니다.");

    private final int code;
    private final String message;
    private final Errors baseError = Errors.QUICK_REMOTE_API_ERROR;

    KakaoErrors(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static KakaoErrors fromCode(int code) {
        for (KakaoErrors error : values()) {
            if (error.getCode() == code) {
                return error;
            }
        }
        return null;
    }
}
