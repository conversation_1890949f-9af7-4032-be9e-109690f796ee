package kr.co.vendys.company.api.vo;

import java.util.Date;
import java.util.List;

import kr.co.vendys.company.api.controller.coupon.entity.RoomDto;
import kr.co.vendys.company.api.entity.master.MealGroup.GroupType;
import kr.co.vendys.company.api.util.DateUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by jangjungsu on 2017. 6. 29..
 */
public class PayRoomVo {

    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class PayRoomEx extends PagingVo {
        private Date startdate;
        private Date enddate;
        private Date starttime;
        private Date endtime;
        private List<String> uidList;
        private List<String> sidList;
        private List<Long> groupList;
        private List<Long> policyList;
        private List<String> orgCodeList;
        private String keyword;
        private Boolean sglUse;
        private Boolean mulUse;
        private Boolean sglCancel;
        private Boolean mulCancel;

        private String payRoomIdx;
        private String cid;
        private Integer status;
        private String  coupontype;
        private String couponname;
        private Date usedate;
        private String signId;
        private String uid;
        private String username;
        private String orgCode;
        private Integer orgIdx;
        private Integer paytype;
        private Long groupIdx;
        private String groupName;
        private String storename;
        private Integer tprice;         //메뉴판 금액
        private Integer tsalesprice;
        private Integer saleprice;      //할인금액
        private Integer compoint;       //회사 식대
        private Integer mypoint;        //내포인트
        private Integer tcompanyprice;  //정산금액
        private String comid;
        private Boolean isTicketFormat;
        private Integer shippingFee;
        private Integer captainCouponPrice;
        private GroupType groupType; // 식대 포인트, 복지 포인트 구분
        private String comidnum; //회원 사번
        private String policyName; //정책명

        private String orderBy = "DESC";

        public void setCode(RoomDto.PutDownRequest.Code code) {
            this.sglUse = code.getSgl_use();
            this.mulUse = code.getMul_use();
            this.sglCancel = code.getSgl_cancel();
            this.mulCancel = code.getMul_cancel();
        }
    }

    @Data
    public static class PayRoomDataDTO {
        private String usedate;             // 사용 날짜 (포맷된 문자열)
        private String signid;              // 사용자 ID
        private String username;            // 사용자 이름
        private String storename;           // 가게 이름
        private Integer captainCouponPrice; // 대장 쿠폰 금액
        private String paytype;             // 결제 유형
        private String comidnum;            // 회원 사번
        private String policyName;          // 정책명
        private String orgCode;             // 부서코드

        // payRoomIdx가 있을 때만 추가되는 필드
        private String payroomidx;          // PayRoom 고유 ID
        private Integer compoint;           // 회사 식대
        private Integer tsalesprice;        // 총 판매 금액
        private Integer tcompanyprice;      // 정산 금액

        public void setPayRoomIdxRelatedFields(PayRoomVo.PayRoomEx vo) {
            this.payroomidx = vo.getPayRoomIdx();
            this.tsalesprice = vo.getTsalesprice();
            this.tcompanyprice = vo.getTcompanyprice();
        }

        public static PayRoomDataDTO from(PayRoomVo.PayRoomEx vo) {
            PayRoomVo.PayRoomDataDTO data = new PayRoomVo.PayRoomDataDTO();
            data.setUsedate(DateUtil.timeToStdString(vo.getUsedate()));
            data.setSignid(vo.getSignId());
            data.setUsername(vo.getUsername());
            data.setStorename(vo.getStorename());
            data.setCompoint(vo.getCompoint());
            data.setCaptainCouponPrice(vo.getCaptainCouponPrice());
            data.setPaytype(vo.getCouponname());
            data.setComidnum(vo.getComidnum());
            data.setPolicyName(vo.getPolicyName());
            data.setOrgCode(vo.getOrgCode());
            return data;
        }
    }

    @Data
    public static class CouponGroupEx {
        private String cid;
        private String comid;
        private String comname;
        private String sid;
        private String storename;
        private Integer tprice;
        private Integer tsalesprice;
        private Integer tpersonalpay;
        private Integer tcompanyprice;
        private Integer tcaptainCouponPrice;
        private Integer shippingFee;
        private String coupontype;
        private String couponname;
        private String venddertype;
        private String vendername;
        private Date usedate;
        private Integer status;
        private String statusname;
        private String caculate;
        private String payRoomIdx;
    }

    @Data
    public static class CouponMenuEx {
        private String cid;
        private Integer seq;
        private String mid;
        private String menuname;
        private String comid;
        private String sid;
        private String storename;
        private Integer price;
        private Integer quantity;
        private Integer salesprice;
        private String coupontype;
        private Date usedate;
        private Integer status;
        private Integer saleprice;
        private Integer companyPrice;
    }

    @Data
    public static class CouponMemberEx {
        private String cid;
        private String uid;
        private String username;
        private String signId;
        private String orgCode;
        private Long groupIdx;
        private String groupname;
        private Long policyIdx;
        private String policyName;
        private Integer amount;
        private Integer mypoint;
        private Integer paytype;
    }

    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class PaymentReasonEx extends PagingVo {
        private Date startdate;
        private Date enddate;
        private List<String> orgCodeList;

        private String cid;
        private Integer status;
        private Date usedate;
        private String comId;
        private String uid;
        private String username;
        private String orgCode;
        private Integer paytype;
        private String storename;
        private Integer tsalesprice;
        private String paymentReasonType = "ALL";
        private String paymentReasonText;
        private Long idx;
        private String paymentReason;
        private String createUserName;
        private String updateUserName;
        private String updateDate;

    }
}
