package kr.co.vendys.company.api.util;

import kr.co.vendys.company.api.constant.Errors;
import kr.co.vendys.company.api.controller.ParamDto.RequestDataByCpm;
import kr.co.vendys.company.api.entity.master.User.CaptainPaymentGrade;
import kr.co.vendys.company.api.exception.CommonException;
import kr.co.vendys.company.api.interceptor.entity.AccessTokenDto;
import kr.co.vendys.company.api.vo.UserVo;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

public class UserUtil {

    /**
     * 현재 요청에서 대장 페이먼츠용 request 정보를 얻어옵니다.
     * @return RequestDataByCpm 객체
     */
    public static RequestDataByCpm getCurrentHeaderDataByCpm() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            throw new CommonException(Errors.GENERAL_HEADER_ERROR);
        }

        HttpServletRequest request = attributes.getRequest();
        AccessTokenDto tokenInfo = (AccessTokenDto) request.getAttribute("tokenInfo");
        UserVo.User user = (UserVo.User) request.getAttribute("userInfo");

        RequestDataByCpm requestData = new RequestDataByCpm();
        requestData.setTokenDTO(tokenInfo);
        requestData.setUser(user);
        requestData.setUserId(user.getUid());
        requestData.setComId(user.getComid());

        if (user.getCaptainPaymentGrade() == CaptainPaymentGrade.VENDYS_SUPER) {
            String comId = request.getHeader("x-comid");
            if (!ObjectUtils.isEmpty(comId)) {
                requestData.setComId(comId);
            }
        }

        return requestData;
    }
}
