package kr.co.vendys.company.api.service.company;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import kr.co.vendys.company.api.constant.Errors;
import kr.co.vendys.company.api.controller.captainpayment.member.dto.CaptainPaymentRankPositionDto.RankPositionResponse;
import kr.co.vendys.company.api.controller.captainpayment.member.dto.CaptainPaymentRankPositionDto.RankPositionResponse.RankPositionDetail;
import kr.co.vendys.company.api.controller.company.entity.RankPositionDto;
import kr.co.vendys.company.api.entity.master.UserCategory;
import kr.co.vendys.company.api.exception.CommonException;
import kr.co.vendys.company.api.persist.UserCategoryPersist;
import kr.co.vendys.company.api.service.history.AuditLogService;
import kr.co.vendys.company.api.vo.UserCategoryVo;
import kr.co.vendys.company.api.vo.UserVo;
import lombok.RequiredArgsConstructor;

/**
 * Created by jangjungsu on 2017. 5. 15..
 */
@Service
@Transactional
@RequiredArgsConstructor
public class RankPositionService {

    private final UserCategoryPersist userCategoryPersist;
    private final AuditLogService auditLogService;

    /**
     * 직위 조회
     */
    public RankPositionDto.Response getRankPosition(String comId) {
        List<UserCategoryVo.UserCategory> resultRankPosition = this.userCategoryPersist.getUserCategoryRankPosition(comId);
        List<RankPositionDto.Response.RankPosition> rankPositionList = new ArrayList<>();
        if (resultRankPosition != null && resultRankPosition.size() > 0) {
            for (UserCategoryVo.UserCategory vo : resultRankPosition) {
                RankPositionDto.Response.RankPosition rankPosition = new RankPositionDto.Response.RankPosition();
                rankPosition.setId(vo.getCateid());
                rankPosition.setName(vo.getName());
                rankPositionList.add(rankPosition);
            }
        }

        RankPositionDto.Response response = new RankPositionDto.Response();
        response.setRankposition(rankPositionList);
        return response;
    }

    /**
     * 직위 조회 CaptainPayment 용
     */
    public RankPositionResponse getRankPositionCpm(String comId) {
        List<UserCategoryVo.UserCategory> resultRankPosition = this.userCategoryPersist.getUserCategoryRankPosition(comId);

        List<RankPositionResponse.RankPositionDetail> list = new ArrayList<>();
        resultRankPosition
                .forEach(userCategory -> {
                    RankPositionResponse.RankPositionDetail detail = new RankPositionDetail();
                    detail.setId(userCategory.getCateid().longValue());
                    detail.setName(userCategory.getName());
                    list.add(detail);
                });

        RankPositionResponse response = new RankPositionResponse();
        response.setRankPosition(list);
        return response;
    }

    /**
     * 직위 추가
     */
    public void setRankPosition(String comId, UserVo.User user, String name) {

        List<UserCategoryVo.UserCategory> rankPositionList = this.userCategoryPersist
                .getUserCategoryRankPositionByName(comId, name);
        if (!ObjectUtils.isEmpty(rankPositionList)) {
            throw new CommonException(Errors.COMPANY_RANKPOSITION_DUPLICATED);
        }
        Integer cateId = this.userCategoryPersist.setUserCategoryRankPosition(comId, name);

        UserCategory createdUserCategory = this.userCategoryPersist
                .readUserCategoryById(Long.valueOf(cateId));
        this.auditLogService
                .setHistory(comId, user, String.valueOf(createdUserCategory.getCateid()),
                        new Date(), "UserCategory", null, createdUserCategory);
    }

    /**
     * 직위 수정
     */
    public void updateRankPosition(String comId, UserVo.User user, Integer cateId, String name) {
        List<UserCategoryVo.UserCategory> rankPositionList = this.userCategoryPersist
                .getUserCategoryRankPositionByName(comId, name);
        if (!ObjectUtils.isEmpty(rankPositionList)) {
            throw new CommonException(Errors.COMPANY_RANKPOSITION_DUPLICATED);
        }
        UserCategory userCategory = this.userCategoryPersist
                .readUserCategoryById(Long.valueOf(cateId));

        UserCategory beforeUserCategory = userCategory.toBuilder().build();
        userCategory.update(name);
        this.auditLogService
                .setHistory(comId, user, String.valueOf(userCategory.getCateid()),
                        new Date(), "UserCategory", beforeUserCategory, userCategory);
    }

    /**
     * 직위 삭제
     */
    public void deleteRankPosition(String comId, UserVo.User user, Integer cateId) {

        UserCategory userCategory = this.userCategoryPersist
                .readUserCategoryById(Long.valueOf(cateId));

        UserCategory beforeUserCategory = userCategory.toBuilder().build();

        this.auditLogService
                .setHistory(comId, user, String.valueOf(userCategory.getCateid()),
                        new Date(), "UserCategory", beforeUserCategory, null);

        this.userCategoryPersist.delUserCategoryRankPosition(comId, cateId);
    }
}
