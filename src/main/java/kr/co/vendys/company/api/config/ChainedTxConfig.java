package kr.co.vendys.company.api.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.transaction.ChainedTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
public class ChainedTxConfig {

    @Bean
    @Primary
    public PlatformTransactionManager transactionManager(
            @Qualifier("masterTxManager") PlatformTransactionManager masterTxManager,
            @Qualifier("coreLibTransactionManager") PlatformTransactionManager coreLibTransactionManager,
            @Qualifier("slaveTxManager") PlatformTransactionManager slaveTxManager,
            @Qualifier("officialTransactionManager") PlatformTransactionManager officialTransactionManager) {
        return new ChainedTransactionManager(officialTransactionManager, masterTxManager, coreLibTransactionManager, slaveTxManager);
    }
}
