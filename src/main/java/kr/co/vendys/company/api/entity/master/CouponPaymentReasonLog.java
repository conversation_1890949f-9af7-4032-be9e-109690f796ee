package kr.co.vendys.company.api.entity.master;

import java.time.LocalDateTime;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters.LocalDateTimeConverter;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@Setter
@Getter
@Builder(toBuilder = true)
public class CouponPaymentReasonLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long logIdx;
    private Long idx;
    private String couponId;
    private String userId;
    private String companyId;
    private String paymentReason;
    private String createUser;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime createDate;
    private String updateUser;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime updateDate;
    private Boolean isDelete;

}
