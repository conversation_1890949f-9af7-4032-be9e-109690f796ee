package kr.co.vendys.company.api.persist;

import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import kr.co.vendys.company.api.controller.captainpayment.benefit.dto.BenefitDto.BenefitRequest;
import kr.co.vendys.company.api.entity.master.BenefitService;
import kr.co.vendys.company.api.repository.master.BenefitServiceRepository;
import lombok.RequiredArgsConstructor;

@Service
@Transactional
@RequiredArgsConstructor
public class BenefitServicePersist {

    private final BenefitServiceRepository benefitServiceRepository;

    public Page<BenefitService> getBenefitServiceListV1(BenefitRequest getRequest, Pageable pageable) {
        Page<BenefitService> listV1 = null;
        if (getRequest.getIsSearched()) {//최초 검색여부
            listV1 = benefitServiceRepository.findBenefitServiceMainListV1(getRequest.getKeyword(), pageable);
        } else {
            listV1 = benefitServiceRepository.findBenefitServiceAllListV1(getRequest.getKeyword(), pageable);
        }
        return listV1;
    }

    public Optional<BenefitService> getBenefitServiceDetailV1(Long id) {
        return benefitServiceRepository.findBenefitServiceDetailV1(id);
    }
}