package kr.co.vendys.company.api.persist.official;

import java.time.LocalDateTime;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import kr.co.vendys.company.api.controller.official.entity.InboundDto.ConsultationInboundRequest;
import kr.co.vendys.company.api.entity.official.Inbound;
import kr.co.vendys.company.api.repository.official.InboundRepository;
import lombok.RequiredArgsConstructor;

/**
 * Created by chulgy<PERSON> on 2021-10-02
 */
@Service
@Transactional
@RequiredArgsConstructor
public class InboundPersist {

    private final InboundRepository inboundRepository;

    public Inbound createInbound(ConsultationInboundRequest inboundDto, String formDataJsonString) {

        Inbound inbound = Inbound.builder()
                .host(inboundDto.getReqUri())
                .endpoint("consultation")
                .userId(inboundDto.getUserId())
                .createDate(LocalDateTime.now())
                .originInboundData(formDataJsonString)
                .build();
        return inboundRepository.save(inbound);
    }

    public void updateInbound(Inbound updateInbound) {
        inboundRepository.save(updateInbound);
    }
}
