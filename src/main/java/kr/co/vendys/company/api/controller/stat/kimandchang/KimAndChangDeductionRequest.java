package kr.co.vendys.company.api.controller.stat.kimandchang;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.AssertTrue;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@Getter
@Setter
public class KimAndChangDeductionRequest {
    private static final int MAX_DATE_RANGE_DAYS = 62;
    private static final int DEFAULT_PAGE = 1;
    private static final int DEFAULT_PAGE_ROW = 10;
    private static final int MAX_PAGE_ROW = 100;

    @NotNull(message = "시작일자는 필수입니다")
    private Date startDate;
    
    @NotNull(message = "종료일자는 필수입니다")
    private Date endDate;
    
    private String userId;

    @AssertTrue(message = "검색 기간은 최대 62일까지 가능합니다")
    public boolean isValidDateRange() {
        if (startDate == null || endDate == null) {
            return true; // @NotNull에서 처리됨
        }
        
        // 종료일이 시작일보다 이전인 경우
        if (endDate.before(startDate)) {
            return false;
        }
        
        // 일수 차이 계산 (밀리초를 일 단위로 변환)
        long diffInDays = TimeUnit.DAYS.convert(
            endDate.getTime() - startDate.getTime(), 
            TimeUnit.MILLISECONDS
        );
        
        return diffInDays <= MAX_DATE_RANGE_DAYS;
    }
}
