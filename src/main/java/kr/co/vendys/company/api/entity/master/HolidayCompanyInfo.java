package kr.co.vendys.company.api.entity.master;

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import lombok.Data;

@Data
@Entity
public class HolidayCompanyInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idx;
    private Long holidayCountryIdx;
    private String comId;
    private String year;
    private Date startDate;
    private Date endDate;
    private Integer duration;
    private String name;
    private String description;
    private Boolean isActive;
    private Boolean updatable;
    private Boolean deletable;
    @CreatedDate
    @Temporal(TemporalType.TIMESTAMP)
    @Column(insertable = false, updatable = false)
    private Date createDate;
    @LastModifiedDate
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;
    private String updateId;
    private Boolean isDelete;
}
