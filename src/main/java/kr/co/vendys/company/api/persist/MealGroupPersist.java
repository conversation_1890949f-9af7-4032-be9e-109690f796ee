package kr.co.vendys.company.api.persist;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import kr.co.vendys.company.api.constant.Errors;
import kr.co.vendys.company.api.entity.master.MealGroup;
import kr.co.vendys.company.api.entity.master.MealGroup.GroupType;
import kr.co.vendys.company.api.exception.CommonException;
import kr.co.vendys.company.api.repository.master.MealGroupRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
@Transactional
public class MealGroupPersist {

    private final MealGroupRepository mealGroupRepository;

    public List<MealGroup> getActiveMealGroup(String comId, GroupType type) {
        return this.mealGroupRepository.findActiveMealGroup(comId, type);
    }

    public List<MealGroup> getMealGroup(String comId, GroupType type) {
        return this.mealGroupRepository.findMealGroup(comId, type);
    }

    public MealGroup save(MealGroup group) {
        return this.mealGroupRepository.save(group);
    }

    public MealGroup findById(Long groupIdx) {
        return this.mealGroupRepository.findById(groupIdx).orElseThrow(() -> new CommonException(Errors.TASK_GROUP_NULL_ERROR));
    }

    public List<MealGroup> getWelfareMealGroups(String comId) {
        return this.mealGroupRepository.findAllByComIdAndGroupType(comId, GroupType.WELFARE);
    }
}
