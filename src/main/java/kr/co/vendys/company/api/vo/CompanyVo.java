package kr.co.vendys.company.api.vo;

import java.util.Date;
import java.util.List;

import kr.co.vendys.company.api.entity.master.MealGroup.GroupType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * Created by jangjungsu on 2017. 4. 26..
 */
public class CompanyVo {

    @Data
    public static class Company {

        private String comid;
        private String name;
        private String icon;
        private String bizname;
        private String bizserial;
        private String address;
        private String region;
        private String chargename;
        private String phone;
        private String bankname;
        private String bankaccount;
        private String bankowner;
        private String intro;
        private Long cash;
        private Long trustcash;
        private Integer employnum;
        private Long duesmonth;
        private Date regdate;
        private Boolean paymulti;
        private Integer status;
        private String calculatedate;
        private Boolean isTicketFormat;
        private String version;
        private Boolean isPaymentReason;
    }

    @Data
    public static class CompanyBalanceInfo {

        private String comId;
        private String bizSerial;
        private String bizSubSerial;
        private String bizName;
        private String bizPresidentName;
        private String bizConditions;
        private String bizType;
        private String bizAddress;
        private Integer monthlyFee;
        private Integer feeByUser;
        private Float feeBySales;
        private Boolean feeVatInclude;
        private Integer balancePeriod;
        private String balanceDepositDate;
        private BalanceMethodType balanceMethod;
        private BalanceProofMethodType balanceProofMethod;
        private BalanceProofNumberType balanceProofNumber;
        private BalanceProofWriteDateType balanceProofWriteDate;
        private String niceBankName;
        private String niceBankOwner;
        private String niceBankAccount;
        private Date contractDate;
        private Date serviceStartDate;
        private Date serviceEndDate;
        private String chargeName;
        private String chargePhone;
        private String chargeEmail;
        private String memo;

        @Getter
        public enum BalanceMethodType {
            TRANSFER("TRANSFER", "은행 계좌이체"),
            NICE("NICE", "나이스 가상 계좌이체"),
            SELF("SELF", "회사정산"),
            NONE("NONE", "정산안함");

            private String code;
            private String desc;

            BalanceMethodType(String code, String desc) {
                this.code = code;
                this.desc = desc;
            }
        }

        @Getter
        public enum BalanceProofMethodType {
            NORMAL("NORMAL", "일반 세금계산서"),
            TRUST("TRUST", "위수탁 세금계산서"),
            PERSONAL("PERSONAL", "개인 현금영수증"),
            NONE("NONE", "안함");

            private String code;
            private String desc;

            BalanceProofMethodType(String code, String desc) {
                this.code = code;
                this.desc = desc;
            }
        }

        @Getter
        public enum BalanceProofNumberType {
            COMBAIN("COMBAIN", "증빙방식 COMBAIN"),
            SEPERATE("SEPERATE", "증빙방식 SEPERATE");

            private String code;
            private String desc;

            BalanceProofNumberType(String code, String desc) {
                this.code = code;
                this.desc = desc;
            }
        }

        @Getter
        public enum BalanceProofWriteDateType {
            LAST("LAST", "주기 종료일"),
            FIRST("FIRST", "다음 주기 시작일"),
            WRITEDATE("WRITEDATE", "발행일자"),
            ETC("ETC", "기타");

            private String code;
            private String desc;

            BalanceProofWriteDateType(String code, String desc) {
                this.code = code;
                this.desc = desc;
            }
        }

    }

    @Data
    public static class BalanceInfoEmail {

        private Integer emailIdx;
        private String comId;
        private String storeId;
        private String email;
        private String name;
        private String phone;
        private String mobile;
        private UseType useType;

        @Getter
        public enum UseType {
            CHECK("CHECK", "세금계산서 수취"),
            INFO("INFO", "입금합계표"),
            CHARGE("CHARGE", "담당자");
            private String code;
            private String desc;

            UseType(String code, String desc) {
                this.code = code;
                this.desc = desc;
            }
        }
    }

    @Data
    public static class CompanyFavoriteMenu {

        private Integer favoriteIdx;
        private String comId;
        private String userId;
        private String code;
        private Date regDate;
    }

    @Data
    public static class MealGroup {

        private List<String> orgCodeList;

        private GroupType groupType;
        private Long groupIdx;
        private String comId;
        private String name;
        private String memo;
        private Boolean isDefault;
        private Boolean isActive;
        private Date regDate;
        private Integer mcount;
    }

    @Data
    public static class MealPolicy {

        private Long groupIdx;
        private String groupName;
        private GroupType groupType;
        private Long policyIdx;
        private String name;
        private PolicyType type;
        private String offset;
        private String expireRange;
        private Date startTime;
        private Date startTime2;
        private Date startTime3;
        private Date endTime;
        private Date endTime2;
        private Date endTime3;
        private Date expireTime;
        private String day;
        private Integer amount;
        private HolidayUseOption holidayUseOption;
        private Boolean isActive;
        private Boolean isGroupPay;
        private Boolean isPresent;
        private Integer expireCount;
        private String expireNotice;
        private Integer onceLimitAmount;
        private Integer limitAmount;
        private Integer maxHoldLimitAmount;
        private Boolean priority;
        private Float promotion;
        private Date promotionStartDate;
        private Date promotionExpireDate;
        private Date regDate;
        private String extra;
    }

    @Data
    public static class MealPolicyAdmin {

        private Long policyidx;
        private Long groupidx;
        private String groupname;
        private String name;
        private PolicyType type;
        private String offset;
        private String expirerange;
        private Date starttime;
        private Date expiretime;
        private String day;
        private Integer amount;
        private Boolean isGroupPay;
        private HolidayUseOption holidayUseOption;
        private Boolean isactive;
        private Integer priority;
        private Date regDate;
        private String extra;
        private Integer expireCount;
        private Integer limitAmount;
        private Integer maxHoldLimitAmount;
        private Boolean isPresent;
        private String expireNotice;
        private Long demandIdx;
        private DemandAcceptType acceptType;
        private DemandType demandType;
        private DemandAccepter accepter;
        private String policyName;
        private Date demandStartTime;
        private Date demandEndTime;
        private Boolean expire;

        @Getter
        public enum DemandAcceptType {
            AUTO("AUTO", "자동 승인"),
            MANUAL("MANUAL", "수동 승인"),
            CONFIRM("CONFIRM", "결재선 승인");
            private String code;
            private String desc;

            DemandAcceptType(String code, String desc) {
                this.code = code;
                this.desc = desc;
            }

        }

        @Getter
        public enum DemandType {
            APP("APP"),
            POS("POS");
            public String code;

            DemandType(String code) {
                this.code = code;
            }
        }

        @Getter
        public enum DemandAccepter {
            ALL("ALL", "전체사용자"),
            ADMIN("ADMIN", "지정사용자"),
            DIVISION("DIVISION", "부서사용자");
            private String code;
            private String desc;

            DemandAccepter(String code, String desc) {
                this.code = code;
                this.desc = desc;
            }
        }
    }

    @Getter
    public enum PolicyType {
        DAY("DAY", "일일"),
        LONGTERM("LONGTERM", "장기"),
        INFINITE("INFINITE", "무제한"),
        DEMAND("DEMAND", "신청");
        private String code;
        private String desc;

        PolicyType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    public enum HolidayUseOption {
        DAY_AND_HOLIDAY("DAY_AND_HOLIDAY", "설정 안함"),
        DAY("DAY", "공휴일 사용불가"),
        DAY_OR_HOLIDAY("DAY_OR_HOLIDAY", "공휴일 사용");

        public String type;
        public String name;

        public String getType() {
            return type;
        }

        public String getName() {
            return name;
        }

        HolidayUseOption(String type, String name) {
            this.type = type;
            this.name = name;
        }

        public static HolidayUseOption getEnum(String type) {
            for (int i = 0; i < HolidayUseOption.values().length; i++) {
                if (HolidayUseOption.values()[i].type.equalsIgnoreCase(type)) {
                    return HolidayUseOption.values()[i];
                }
            }
            throw new IllegalArgumentException();
        }
    }

    @Data
    public static class MealPolicyEx {

        private Long groupIdx;
        private Long policyIdx;
        private String name;
        private PolicyType type;
        private String offset;
        private String expireRange;
        private String startTime;
        private String endTime;
        private String startTime2;
        private String endTime2;
        private String startTime3;
        private String endTime3;
        private Date expireTime;
        private String day;
        private Integer amount;
        private Integer limitAmount;
        private Integer onceLimitAmount;
        private Integer maxHoldLimitAmount;
        private HolidayUseOption holidayUseOption;
        private Boolean isActive;
        private SupplyType supplyType;
        private Boolean isGroupPay;
        private Date regDate;
        private Long demandIdx;
        private DemandAcceptType demandAcceptType;
        private Date demandStartTime;
        private Date demandEndTime;
        private DemandAccepter demandAccepter;

        @Getter
        public enum PolicyType {
            DAY("DAY", "일일"),
            LONGTERM("LONGTERM", "장기"),
            INFINITE("INFINITE", "무제한"),
            DEMAND("DEMAND", "신청");
            private String code;
            private String desc;

            PolicyType(String code, String desc) {
                this.code = code;
                this.desc = desc;
            }
        }

        @Getter
        public enum DemandAcceptType {
            AUTO("AUTO", "자동 승인"),
            MANUAL("MANUAL", "수동 승인"),
            CONFIRM("CONFIRM", "결재선 승인");
            private String code;
            private String desc;

            DemandAcceptType(String code, String desc) {
                this.code = code;
                this.desc = desc;
            }
        }

        @Getter
        public enum DemandAccepter {
            ALL("ALL", "전체사용자"),
            ADMIN("ADMIN", "지정사용자"),
            DIVISION("DIVISION", "부서사용자");
            private String code;
            private String desc;

            DemandAccepter(String code, String desc) {
                this.code = code;
                this.desc = desc;
            }
        }

        @Getter
        public enum SupplyType {
            NONE("NONE","비활성"),
            BATCH("BATCH","일괄 지급"),
            WORKINGDAY("WORKINGDAY","영업일"),
            MONTHDAY("MONTHDAY", "총 일수");
            private final String type;
            private final String name;

            SupplyType(String type, String name) {
                this.type = type;
                this.name = name;
            }
        }
    }

    @Data
    public static class OfficeEx {

        private String orgCode;
        private Long officeIdx;
        private String name;
        private String officialName;
        private Double gpslat;
        private Double gpslon;
        private String region;
        private Date regDate;
    }

    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class OfficeStoreRelationEx extends PagingVo {

        private List<Long> officeIdxList;

        private Long relationIdx;
        private Long officeIdx;
        private String storeId;
        private PriceType priceType;
        private Float salesPrice;
        private Float companyPrice;
        private Float supplyPrice;
        private Boolean isActive;
        private Date regDate;
        private Date inactiveDate;

        private String sid;
        private String name;
        private String address;
        private String category;
        private String phone;
        private String region;
        private Boolean status;
        private String filter;
        private Boolean isBooking;
        private Boolean isCoupon;
        private Boolean onlyVisibleForUser;
        private Double gpslat;
        private Double gpslon;
        private String addressSido;
        private String addressSigugun;
        private String addressDongmyun;

        private List<Integer> supplyTypes;

        @Getter
        public enum PriceType {
            RATE("RATE", "정률"),
            FIXED("FIXED", "정액");
            private String code;
            private String desc;

            PriceType(String code, String desc) {
                this.code = code;
                this.desc = desc;
            }
        }
    }


    @Data
    public static class StoreBlacklistEx {

        private String comId;
        private Long policyIdx;

        private String sid;
        private String name;
        private String category;
        private Boolean status;
    }
}
