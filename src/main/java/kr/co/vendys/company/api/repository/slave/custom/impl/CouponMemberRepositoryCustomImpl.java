package kr.co.vendys.company.api.repository.slave.custom.impl;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.CaseBuilder;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.Wildcard;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import kr.co.vendys.company.api.entity.master.QCouponGroup;
import kr.co.vendys.company.api.entity.master.QCouponMember;
import kr.co.vendys.company.api.entity.master.QMealGroup;
import kr.co.vendys.company.api.entity.master.QPayRoom;
import kr.co.vendys.company.api.entity.master.QUser;
import kr.co.vendys.company.api.repository.slave.custom.CouponMemberRepositoryCustom;
import kr.co.vendys.company.api.vo.CouponStateDto;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@RequiredArgsConstructor
public class CouponMemberRepositoryCustomImpl implements CouponMemberRepositoryCustom {
    private final JPAQueryFactory queryFactory;

    @Override
    public CouponStateDto.CouponUsedStatsPage findUserCouponUsageByComIdAndSidNotInAndUseDateBetweenAndSearchCondition(
            String comid, CouponStateDto.CouponUsedLogSelectCondition condition, Pageable pageable
    ) {
        LocalDateTime startDateTime = condition.getStartDate().atStartOfDay();
        LocalDateTime endDateTime = condition.getEndDate().plusDays(1L).atStartOfDay();

        QCouponMember cm = QCouponMember.couponMember;
        QCouponGroup cg = QCouponGroup.couponGroup;
        QUser u = QUser.user;
        QMealGroup mg = QMealGroup.mealGroup;

        // 동적 조건 생성
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(cm.comId.eq(comid))
                .and(cm.comPoint.ne(0L))
                .and(cm.useDate.goe(java.sql.Timestamp.valueOf(startDateTime)))
                .and(cm.useDate.lt(java.sql.Timestamp.valueOf(endDateTime)));

        if (!ObjectUtils.isEmpty(condition.getMealGroupIdx())) {
            builder.and(mg.groupIdx.eq(condition.getMealGroupIdx()));
        }

        if (!ObjectUtils.isEmpty(condition.getUserIds())) {
            builder.and(u.uid.in(condition.getUserIds()));
        }

        if(!ObjectUtils.isEmpty(condition.getOrgCodeList())) {
            builder.and(u.orgCode.in(condition.getOrgCodeList()));
        }

        if (!ObjectUtils.isEmpty(condition.getNotInCouponIds())) {
            builder.and(cm.couponMemberId.cid.notIn(condition.getNotInCouponIds()));
        }

        // GROUP BY와 ORDER BY 동적 설정
        Expression<String> usingDateExpression;
        List<Expression<?>> groupByFields;
        List<OrderSpecifier<?>> orderBySpecifiers;

        if (condition.getGroupingCondition().equalsIgnoreCase("SUM")) {
            // SUM 조건: MIN/MAX를 계산한 usingDate 생성
            Expression<String> minDate = Expressions.stringTemplate("DATE_FORMAT(MIN({0}), '%Y-%m-%d')", condition.getStartDate());
            Expression<String> maxDate = Expressions.stringTemplate("DATE_FORMAT(MAX({0}), '%Y-%m-%d')", condition.getEndDate());
            usingDateExpression = Expressions.stringTemplate("CONCAT({0}, ' ~ ', {1})", minDate, maxDate);

            groupByFields = List.of(cm.couponMemberId.uid, cm.username); // 이름과 사용자 ID로 그룹화
            orderBySpecifiers = List.of(cm.username.asc());
        } else {
            // 날짜 포맷 조건: usingDate 생성
            String dateFormat = condition.getGroupingCondition().equalsIgnoreCase("MM") ? "%Y-%m" : "%Y-%m-%d";
            usingDateExpression = Expressions.stringTemplate("DATE_FORMAT({0}, '" + dateFormat + "')", cm.useDate);

            groupByFields = List.of(cm.couponMemberId.uid, usingDateExpression, cm.username);
            orderBySpecifiers = List.of(
                    cm.username.asc(),
                    new OrderSpecifier<>(Order.DESC, usingDateExpression)
            );
        }

        Expression<String> usingDateWithAlias = ExpressionUtils.as(usingDateExpression, "usingDate");

        // 메인 쿼리 작성
        JPAQuery<CouponStateDto.CouponUsedStats> mainQuery = queryFactory
                .select(Projections.constructor(
                        CouponStateDto.CouponUsedStats.class,
                        usingDateWithAlias,
                        cm.couponMemberId.uid,
                        u.name,
                        u.signId,
                        u.comIdNum,
                        u.cellphone,
                        u.email,
                        u.orgCode,
                        u.position,
                        u.rankPosition,
                        mg.name.as("mealGroupName"),
                        cm.comPoint.sum().as("comPointSum"),
                        new CaseBuilder()
                                .when(cg.status.goe(0).and(cg.status.lt(80))).then(1)
                                .otherwise(-1)
                                .sum().as("mealCount")
                ))
                .from(cm)
                .innerJoin(u).on(cm.couponMemberId.uid.eq(u.uid))
                .innerJoin(mg).on(u.groupIdx.eq(mg.groupIdx))
                .innerJoin(cg).on(cg.cid.eq(cm.couponMemberId.cid).and(cg.comid.eq(cm.comId)))
                .where(builder)
                .groupBy(groupByFields.toArray(new Expression<?>[0]))
                .orderBy(orderBySpecifiers.toArray(new OrderSpecifier<?>[0]));




        // 페이징 처리
        List<CouponStateDto.CouponUsedStats> content;

        if(!ObjectUtils.isEmpty(pageable)) {
            // Pageable 조정
            Pageable adjustedPageable = PageRequest.of(
                    Math.max(0, pageable.getPageNumber() - 1),
                    pageable.getPageSize()
            );

            content = mainQuery
                    .offset(adjustedPageable.getOffset())
                    .limit(adjustedPageable.getPageSize())
                    .fetch();

            // 카운트 쿼리 작성
            long total = queryFactory
                    .select(Wildcard.count) // 그룹화된 결과의 개수를 계산
                    .from(cm)
                    .innerJoin(u).on(cm.couponMemberId.uid.eq(u.uid))
                    .innerJoin(mg).on(u.groupIdx.eq(mg.groupIdx))
                    .innerJoin(cg).on(cg.cid.eq(cm.couponMemberId.cid).and(cg.comid.eq(cm.comId)))
                    .where(builder)
                    .groupBy(groupByFields.toArray(new Expression<?>[0]))
                    .fetch()
                    .size();
            // Page 객체 반환
            Page<CouponStateDto.CouponUsedStats> pageRows = new PageImpl<>(content, adjustedPageable, total);
            return new CouponStateDto.CouponUsedStatsPage(pageRows.getNumber() + 1, (long) pageRows.getSize(), total,pageRows.getTotalPages(), content);
        } else {
            content = mainQuery.fetch();
            return new CouponStateDto.CouponUsedStatsPage(1, (long) content.size(), (long) content.size(), 1, content);
        }


    }

    @Override
    public CouponStateDto.CouponUsedStatsSum findCouponTotalUsageByComIdAndSidNotInAndUseDateBetweenAndSearchCondition(String comid, CouponStateDto.CouponUsedLogSelectCondition condition) {
        LocalDateTime startDateTime = condition.getStartDate().atStartOfDay();
        LocalDateTime endDateTime = condition.getEndDate().plusDays(1L).atStartOfDay();

        QCouponMember cm = QCouponMember.couponMember;
        QCouponGroup cg = QCouponGroup.couponGroup;
        QUser u = QUser.user;
        QMealGroup mg = QMealGroup.mealGroup;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(cm.comId.eq(comid))
                .and(cm.comPoint.ne(0L))
                .and(cm.useDate.goe(java.sql.Timestamp.valueOf(startDateTime)))
                .and(cm.useDate.lt(java.sql.Timestamp.valueOf(endDateTime)));

        if(!ObjectUtils.isEmpty(condition.getMealGroupIdx())) {
            builder.and(mg.groupIdx.eq(condition.getMealGroupIdx()));
        }

        if(!ObjectUtils.isEmpty(condition.getOrgCodeList())) {
            builder.and(u.orgCode.in(condition.getOrgCodeList()));
        }


        if(!ObjectUtils.isEmpty(condition.getUserIds())) {
            builder.and(u.uid.in(condition.getUserIds()));
        }

        if(!ObjectUtils.isEmpty(condition.getNotInCouponIds())) {
            builder.and(cm.couponMemberId.cid.notIn(condition.getNotInCouponIds()));
        }

        return queryFactory
                .select(Projections.constructor(
                        CouponStateDto.CouponUsedStatsSum.class,
                        cm.comPoint.sum().as("comPointSum"),
                        new CaseBuilder()
                                .when(cg.status.goe(0).and(cg.status.lt(80))).then(1)
                                .otherwise(-1)
                                .sum().as("mealCount")
                ))
                .from(cm)
                .innerJoin(u).on(cm.couponMemberId.uid.eq(u.uid))
                .innerJoin(mg).on(u.groupIdx.eq(mg.groupIdx))
                .innerJoin(cg).on(cg.cid.eq(cm.couponMemberId.cid).and(cg.comid.eq(cm.comId)))
                .where(builder)
                .fetchOne();
    }

    @Override
    public CouponStateDto.CouponUsedDetailPage findCouponUsageDetails(String comid, String uid, LocalDateTime startDate, LocalDateTime endDate,
            Set<String> cids, Pageable pageable) {

        QCouponMember cm = QCouponMember.couponMember;
        QPayRoom pm = QPayRoom.payRoom;

        // 메인 쿼리 정의
        JPAQuery<CouponStateDto.CouponUsedDetail> query = queryFactory
                .select(Projections.constructor(
                        CouponStateDto.CouponUsedDetail.class,
                        pm.payRoomIdx,
                        cm.username,
                        cm.storeName,
                        cm.comPoint,
                        cm.useDate
                ))
                .from(cm)
                .leftJoin(pm).on(cm.couponMemberId.cid.eq(pm.couponId))
                .where(cm.comId.eq(comid)
                        .and(cm.couponMemberId.uid.eq(uid))
                        .and(cm.useDate.goe(java.sql.Timestamp.valueOf(startDate)))
                        .and(cm.useDate.lt(java.sql.Timestamp.valueOf(endDate)))
                        .and(cm.couponMemberId.cid.notIn(cids)))
                .orderBy(cm.useDate.desc());

        // 카운트 쿼리 정의
        JPAQuery<Long> countQuery = queryFactory
                .select(cm.count())
                .from(cm)
                .leftJoin(pm).on(cm.couponMemberId.cid.eq(pm.couponId))
                .where(cm.comId.eq(comid)
                        .and(cm.couponMemberId.uid.eq(uid))
                        .and(cm.useDate.goe(java.sql.Timestamp.valueOf(startDate)))
                        .and(cm.useDate.lt(java.sql.Timestamp.valueOf(endDate)))
                        .and(cm.couponMemberId.cid.notIn(cids)));

        List<CouponStateDto.CouponUsedDetail> results;
        // 전체 데이터 수 계산
        long total = countQuery.fetchOne();

        Long sumPoint = this.findCouponUsageDetailSum(comid, uid, startDate, endDate, cids);

        if(!ObjectUtils.isEmpty(pageable)) {
            // Pageable 조정 (1번 페이지부터 시작)
            Pageable adjustedPageable = PageRequest.of(
                    Math.max(0, pageable.getPageNumber() - 1),
                    pageable.getPageSize()
            );
            results = query
                    .offset(adjustedPageable.getOffset())
                    .limit(adjustedPageable.getPageSize())
                    .fetch();
            // Page 객체 반환
            Page<CouponStateDto.CouponUsedDetail> pageRows = new PageImpl<>(results, adjustedPageable, total);
            return new CouponStateDto.CouponUsedDetailPage(pageRows.getNumber() + 1, (long) pageRows.getSize(), total, pageRows.getTotalPages(), sumPoint, pageRows.getContent());
        } else {
            results = query.fetch();
            return new CouponStateDto.CouponUsedDetailPage(1, total, total, 1, sumPoint, results);
        }
    }

    @Override
    public Long findCouponUsageDetailSum(String comid, String uid, LocalDateTime startDate, LocalDateTime endDate, Set<String> cids) {
        QCouponMember cm = QCouponMember.couponMember;
        QPayRoom pm = QPayRoom.payRoom;

        // 메인 쿼리 정의
        JPAQuery<Long> query = queryFactory
                .select(cm.comPoint.sum())
                .from(cm)
                .leftJoin(pm).on(cm.couponMemberId.cid.eq(pm.couponId))
                .where(cm.comId.eq(comid)
                        .and(cm.couponMemberId.uid.eq(uid))
                        .and(cm.useDate.goe(java.sql.Timestamp.valueOf(startDate)))
                        .and(cm.useDate.lt(java.sql.Timestamp.valueOf(endDate)))
                        .and(cm.couponMemberId.cid.notIn(cids)))
                .groupBy(cm.couponMemberId.uid);

        return query.fetchOne();
    }

}
