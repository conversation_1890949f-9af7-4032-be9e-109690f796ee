package kr.co.vendys.company.api.service.coupon;

import java.util.Date;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import kr.co.vendys.company.api.controller.ParamDto;
import kr.co.vendys.company.api.controller.ParamDto.RequestData;
import kr.co.vendys.company.api.controller.coupon.entity.RoomDto.PutDownRequest;
import kr.co.vendys.company.api.controller.coupon.entity.TaskDto;
import kr.co.vendys.company.api.controller.coupon.entity.TaskDto.ExcelType;
import kr.co.vendys.company.api.controller.coupon.entity.UserDto.DownRequest;
import kr.co.vendys.company.api.entity.master.MealGroup.GroupType;
import kr.co.vendys.company.api.persist.UserPersist;
import kr.co.vendys.company.api.service.ExcelService;
import kr.co.vendys.company.api.vo.ExcelVo.ExcelContentType;
import kr.co.vendys.company.api.vo.UserVo;
import lombok.RequiredArgsConstructor;

@Service
@Transactional
@RequiredArgsConstructor
public class BridgeService {

    private final TaskService taskService;
    private final RoomService roomService;
    private final UserPointService userPointService;
    private final ExcelService excelService;
    private final UserPersist userPersist;

    public byte[] setTaskExcel(
            TaskDto.ExcelType excelType, ParamDto.RequestData requestData, String uuid, String domain, String fileName,
            TaskDto.ListExcelRequest excelRequest,
            GroupType groupType,
            Long userCount) {

        byte[] file;
        if (ExcelType.LIST.equals(excelType)) { // 식대내역 > 엑셀다운로드
            if (userCount > 1000L) {
                // status = 2(생성중)
                UserVo.User excelUser = this.getUserVo(requestData.getUserId());
                this.excelService.insertGeneratingExcelLog(uuid, fileName, excelUser, excelRequest.getComid(),
                        ExcelContentType.V2_COUPON_TASK_LIST.getType(), 0, excelRequest.getStartdate(),
                        excelRequest.getEnddate(), true);
                // 엑셀 생성
                file = this.taskService.getTaskExcelAsync(requestData, uuid, domain, fileName, excelRequest, groupType);
            } else {
                file = this.taskService.getTaskExcel(requestData, uuid, domain, fileName, excelRequest, groupType);
            }
        } else {// 식대내역 > 전체 상세내역 엑셀 다운로드
            if (userCount > 1000L) {
                // status = 2(생성중)
                UserVo.User excelUser = this.getUserVo(requestData.getUserId());
                this.excelService.insertGeneratingExcelLog(uuid, fileName, excelUser, excelRequest.getComid(),
                        ExcelContentType.V2_COUPON_ALL_TASK_DETAIL.getType(), 0, excelRequest.getStartdate(),
                        excelRequest.getEnddate(), true);
                // 엑셀 생성
                file = this.taskService.getAllTaskDetailExcelAsync(requestData, uuid, domain, fileName, excelRequest, groupType);
            } else {
                file = this.taskService.getAllTaskDetailExcel(requestData, uuid, domain, fileName, excelRequest, groupType);
            }
        }

        return file;
    }

    public byte[] setRoomUserListExcel(
            RequestData requestData, String uuid, String domain, String fileName, PutDownRequest putRequest,
            GroupType groupType,
            Long userCount) {
        byte[] file = new byte[0];

        if (userCount > 1000L) { // 식대 상세조회 > 엑셀 다운로드
            // status = 2(생성중)
            UserVo.User excelUser = this.getUserVo(requestData.getUserId());
            this.excelService.insertGeneratingExcelLog(uuid, fileName, excelUser, putRequest.getComid(),
                    ExcelContentType.V2_COUPON_ROOM_LIST.getType(), 0,
                    putRequest.getDate() == null ? new Date() : putRequest.getDate().getStartdate(),
                    putRequest.getDate() == null ? new Date() : putRequest.getDate().getEnddate(), true);
            // 엑셀 생성
            this.roomService.getRoomUserListExcelAsync(requestData, uuid, domain, fileName, putRequest, groupType);
        } else {
            file = this.roomService.getRoomUserListExcel(requestData, uuid, domain, fileName, putRequest, groupType);
        }

        return file;
    }

    public byte[] setPointLogUserExcel(
            RequestData requestData, String uuid, String domain, String fileName, DownRequest paramRequest, Long userCount) {

        byte[] file = new byte[0];

        if (StringUtils.isEmpty(paramRequest.getData()) || "LIST".equals(paramRequest.getData())) {
            if (userCount > 1000L) { // 사용자별 식대 내역 > 엑셀 다운로드
                // status = 2(생성중)
                UserVo.User excelUser = this.getUserVo(requestData.getUserId());
                this.excelService.insertGeneratingExcelLog(uuid, fileName, excelUser, paramRequest.getComid(),
                        ExcelContentType.V2_COUPON_USER_LIST.getType(), 0, paramRequest.getStartdate(),
                        paramRequest.getEnddate(), true);
                // 엑셀 생성
                this.userPointService.getPointLogUserExcelAsync(requestData, uuid, domain, fileName, paramRequest);
            } else {
                file = this.userPointService.getPointLogUserExcel(requestData, uuid, domain, fileName, paramRequest);
            }
        } else {
            if (userCount > 1000L) {  // 사용자별 식대 내역 > 전체 상세내역 엑셀 다운로드
                // status = 2(생성중)
                UserVo.User excelUser = this.getUserVo(requestData.getUserId());
                this.excelService.insertGeneratingExcelLog(uuid, fileName, excelUser, paramRequest.getComid(),
                        ExcelContentType.V2_COUPON_ALL_USER_DETAIL.getType(), 0, paramRequest.getStartdate(),
                        paramRequest.getEnddate(), true);
                // 엑셀 생성
                this.userPointService
                        .getPointLogAllUserDetailExcelAsync(requestData, uuid, domain, fileName, paramRequest);
            } else {
                file = this.userPointService.getPointLogAllUserDetailExcel(requestData, uuid, domain, fileName, paramRequest);
            }
        }

        return file;
    }

    private UserVo.User getUserVo(String userId) {
        UserVo.User excelUserParam = new UserVo.User();
        excelUserParam.setUid(userId);
        return this.userPersist.getUser(excelUserParam);
    }

}
