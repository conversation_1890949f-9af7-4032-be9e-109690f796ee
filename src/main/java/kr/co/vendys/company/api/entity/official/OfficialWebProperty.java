package kr.co.vendys.company.api.entity.official;

import java.util.Arrays;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

import kr.co.vendys.company.api.constant.Errors;
import kr.co.vendys.company.api.entity.master.CommonEntity;
import kr.co.vendys.company.api.exception.CommonException;
import lombok.Getter;

/**
 * Created by chul-gyun on 2021-11-02
 */
@Getter
@Entity
public class OfficialWebProperty extends CommonEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(length = 20)
    private Integer idx;

    @Column(nullable = false, length = 45)
    private String id;  // 타입(직장인 수, 누적 결제 수, 식권대장 제휴점 수, 고객사 수)

    @Column(nullable = false, length = 45)
    private String key; // 숫자 20 , 500

    @Enumerated(EnumType.STRING)
    private KeyType type;

    public enum KeyType {
        JIRA, WEB   // WEB 추가
    }

    public String findIdType (String paramId) {
        IdType idType = Arrays.stream(IdType.values())
                .filter(s -> s.getType().equals(paramId.toUpperCase()))
                .findAny()
                .orElseThrow(() -> new CommonException(Errors.GENERAL_PARAM_ERROR));
        return idType.getType();
    }

    public enum IdType {

        SIKDAE_USER("SIKDAE_USER", "식권대장 사용 직장인 수"),
        SIKDAE_CUMULATIVE_PAYMENT("SIKDAE_CUMULATIVE_PAYMENT", "식권대장 누적 결제 수"),
        SIKDAE_STORE("SIKDAE_STORE", "식권대장 제휴점 수"),
        COMPANY("COMPANY", "고객사 수"),
        MONTH_AVRG_CUSTOMER_INQUIRIES("MONTH_AVRG_CUSTOMER_INQUIRIES", "월 평균 고객 문의 수")
        ;

        public String type;
        public String name;

        public String getType() {
            return type;
        }

        public String getName() {
            return name;
        }

        IdType(String type, String name) {
            this.type = type;
            this.name = name;
        }
    }

}
