package kr.co.vendys.company.api.interceptor;

import kr.co.vendys.company.api.constant.Errors;
import kr.co.vendys.company.api.constant.InitProperties;
import kr.co.vendys.company.api.exception.AuthorizedException;
import kr.co.vendys.company.api.interceptor.entity.XCorp;
import kr.co.vendys.company.api.util.ConverterUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;

@Component
@RequiredArgsConstructor
public class XCorpHeader {

    private final ConverterUtil converterUtil;
    private final InitProperties initProperties;

    public XCorp getHeader(HttpServletRequest request) {
        String header = request.getHeader("x-corp");
        if (header == null) {
            throw new AuthorizedException(Errors.OAuth_TOKEN_EMPTY);
        }

        XCorp corpHeader = this.converterUtil.toObject(header, XCorp.class);
        if (corpHeader == null) {
            throw new AuthorizedException(Errors.OAuth_TOKEN_EMPTY);
        }

        if (ObjectUtils.isEmpty(corpHeader.getKey()) || 
            !StringUtils.hasText(corpHeader.getKey()) || 
            !corpHeader.getKey().equals(this.initProperties.getInternalKey())) {
            throw new AuthorizedException(Errors.OAuth_TOKEN_NOTFOUND);
        }

        return corpHeader;
    }
    
    public XCorp validateAndGetHeader(HttpServletRequest request) {
        try {
            return getHeader(request);
        } catch (Exception e) {
            return null;
        }
    }
}
