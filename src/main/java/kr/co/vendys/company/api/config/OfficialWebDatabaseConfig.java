package kr.co.vendys.company.api.config;

import java.util.Properties;
import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.LazyConnectionDataSourceProxy;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.hibernate.cfg.AvailableSettings;

/**
 * Created by chul-gyun on 2021-11-02
 */
@Configuration
@EnableJpaAuditing
@ConfigurationProperties(prefix = "spring.datasource.hikari.official")
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = "kr.co.vendys.company.api.repository.official",
        entityManagerFactoryRef = "officialEntityManager",
        transactionManagerRef = "officialTransactionManager"
)
public class OfficialWebDatabaseConfig extends HikariConfig {

    @Autowired
    private Environment env;

    @Bean
    public DataSource officialDataSource() {
        return new LazyConnectionDataSourceProxy(new HikariDataSource(this));
    }

    public Properties getJpaProperties() {
        final Properties hibernateProperties = new Properties();
        hibernateProperties.setProperty(
                AvailableSettings.PHYSICAL_NAMING_STRATEGY, env.getProperty("spring.jpa.hibernate.naming.physical-strategy")
        );
        return hibernateProperties;
    }

    @Bean
    public LocalContainerEntityManagerFactoryBean officialEntityManager() {
        final LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(this.officialDataSource());
        em.setPackagesToScan("kr.co.vendys.company.api.entity.official");
        em.setJpaVendorAdapter(new HibernateJpaVendorAdapter());
        em.setJpaProperties(getJpaProperties());
        em.afterPropertiesSet();
        return em;
    }

    @Bean
    public PlatformTransactionManager officialTransactionManager(
            @Qualifier("officialEntityManager") EntityManagerFactory officialEntityManager) {
        JpaTransactionManager transactionManager = new JpaTransactionManager(officialEntityManager);
        return transactionManager;
    }

}
