package kr.co.vendys.company.api.entity.master;

import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

import lombok.Data;

@Data
@Entity
public class BillingCriteria {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer billingCriteriaIdx;
    private Integer billingAccountIdx;
    @Enumerated(EnumType.STRING)
    private Type type;
    private String value;
    private Date created;

    public enum Type {
        OFFICE, DIVISION, MEALGROUP
    }

}
