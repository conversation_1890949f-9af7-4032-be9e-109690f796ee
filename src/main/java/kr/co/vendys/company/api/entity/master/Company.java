package kr.co.vendys.company.api.entity.master;

import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
public class Company {

    @Id @Column(name = "comid", columnDefinition = "char(50) COLLATE utf8_bin NOT NULL COMMENT '고객사 고유 아이디'")
    private String comId;

    @Column(columnDefinition = "char(50) COLLATE utf8_bin NOT NULL COMMENT '고객사명'")
    private String name;

    @Column(columnDefinition = "char(30) COLLATE utf8_bin DEFAULT NULL COMMENT '아이콘 파일 이름'")
    private String icon;

    @Column(name = "bizname", columnDefinition = "char(255) COLLATE utf8_bin NOT NULL COMMENT '사업자명'")
    private String bizName;

    @Column(name = "bizserial", columnDefinition = "char(30) COLLATE utf8_bin NOT NULL COMMENT '사업자 번호'")
    private String bizSerial;

    @Column(columnDefinition = "char(140) COLLATE utf8_bin DEFAULT NULL COMMENT '주소'")
    private String address;

    @Column(columnDefinition = "char(20) COLLATE utf8_bin NOT NULL COMMENT '지역'")
    private String region;

    @Column(name = "chargename", columnDefinition = "varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '담당자'")
    private String chargeName;

    @Column(columnDefinition = "char(20) COLLATE utf8_bin DEFAULT NULL COMMENT '연락처'")
    private String phone;

    @Column(name = "bankname", columnDefinition = "char(10) COLLATE utf8_bin DEFAULT NULL COMMENT 'deprecated'")
    private String bankName;

    @Column(name = "bankaccount", columnDefinition = "char(20) COLLATE utf8_bin DEFAULT NULL COMMENT 'deprecated'")
    private String bankAccount;

    @Column(name = "bankowner", columnDefinition = "char(30) COLLATE utf8_bin DEFAULT NULL COMMENT 'deprecated'")
    private String bankOwner;

    @Column(columnDefinition = "text COLLATE utf8_bin COMMENT '설명'")
    private String intro;

    @Column(columnDefinition = "bigint(20) NOT NULL DEFAULT '0' COMMENT 'deprecated'")
    private Integer cash;

    @Column(name = "trustcash", columnDefinition = "bigint(20) NOT NULL DEFAULT '0' COMMENT 'deprecated'")
    private Integer trustCash;

    @Column(name = "employnum", columnDefinition = "int(11) NOT NULL DEFAULT '0' COMMENT '임직원 수'")
    private Integer employNum;

    @Column(name = "duesmonth", columnDefinition = "bigint(20) NOT NULL DEFAULT '0' COMMENT 'deprecated'")
    private Integer duesMonth;

    @Column(name = "regdate", columnDefinition = "datetime NOT NULL COMMENT '등록 시간'")
    private LocalDateTime regDate;

    @Column(name = "paymulti", columnDefinition = "tinyint(1) NOT NULL DEFAULT '0' COMMENT 'deprecated'")
    private boolean payMulti;

    @Column(columnDefinition = "tinyint(2) NOT NULL DEFAULT '0' COMMENT '활성화 여부'")
    private boolean status;

    @Column(columnDefinition = "text COLLATE utf8_bin COMMENT '영업노트'")
    private String salesContents;

    @Column(name = "calculatedate", columnDefinition = "char(50) COLLATE utf8_bin DEFAULT NULL COMMENT 'deprecated'")
    private String calculateDate;

    @Column(name = "depositmethod", columnDefinition = "varchar(10) COLLATE utf8_bin DEFAULT NULL COMMENT '정산방식 (우리은행, 나이스)'")
    private String depositMethod;

    @Column(columnDefinition = "varchar(100) COLLATE utf8_bin DEFAULT NULL COMMENT '베니피아 고유 아이디(deprecated)'")
    private String benepiaComId;

    @Column(columnDefinition = "varchar(2) COLLATE utf8_bin DEFAULT NULL COMMENT '고객사 버전(null: 기존 고객사, v1: 그룹사 기능 고객사)'")
    private String version;

    @Column(columnDefinition = "tinyint(1) NOT NULL DEFAULT '0' COMMENT '테스트 고객사 유무'")
    private boolean isTest;

    @Column(columnDefinition = "varchar(200) COLLATE utf8_bin DEFAULT NULL COMMENT '고객사 CI 이미지'")
    private String companyCi;
}