package kr.co.vendys.company.api.repository.master;

import java.util.List;

import kr.co.vendys.company.api.vo.UserVo;

/**
 * Created by jangjungsu on 2017. 4. 24..
 */
public interface UserMapper {
    List<UserVo.User> selectUser(UserVo.User paramUser);

    Integer selectUserCount(UserVo.User paramUser);

    Integer selectUserCountEx(UserVo.UserEx param);

    Integer selectDuplicateUser(UserVo.User paramUser);

    UserVo.UserEx selectEmployeeTotalCount(UserVo.UserEx paramUserEx);

    UserVo.UserEx selectEmployeeTotalCountByWelfare(UserVo.UserEx paramUserEx);

    List<UserVo.UserEx> selectEmployee(UserVo.UserEx paramUserEx);

    UserVo.UserEx selectEmployeeDetail(UserVo.UserEx paramUserEx);

    List<UserVo.UserEx> selectEmployeePolicy(UserVo.UserEx paramUserEx);

    List<UserVo.UserEx> selectEmployeePolicyByWelfare(UserVo.UserEx paramUserEx);

    List<UserVo.UserEx> selectEmployeeDetailPolicy(UserVo.UserEx paramUserEx);

    void insertUser(UserVo.User paramUser);

    void updateUser(UserVo.User paramUser);

    void updateUserBulk(UserVo.BulkUser paramBulkUser);

    void upsertUserEmailCertification(UserVo.UserEmailCertification paramUserEmailCertification);

    Integer selectGroupUserCount(UserVo.GroupUser paramGroupUSer);

    Integer selectWelfareGroupUserCount(UserVo.GroupUser paramGroupUSer);

    List<UserVo.UserEx> selectTaskUploadUserList(UserVo.UserEx paramUserEx);

    List<UserVo.UserEx> selectAllEmployee(UserVo.UserEx param);

    List<UserVo.UserExcelDto> selectEmployeesForExcel(UserVo.UserExcelSelectCondition comId);
}
