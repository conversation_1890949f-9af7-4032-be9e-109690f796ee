package kr.co.vendys.company.api.persist.official;

import java.net.URI;
import java.util.List;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import kr.co.vendys.company.api.constant.InitProperties;
import kr.co.vendys.company.api.persist.common.RemoteCommonException;
import kr.co.vendys.company.api.persist.official.entity.AlimTalkDto.AlimTalkData;
import kr.co.vendys.company.api.persist.official.entity.AlimTalkDto.AlimTalkResponse;
import kr.co.vendys.company.api.repository.official.OfficialWebPropertyRepository;
import kr.co.vendys.company.api.util.ConverterUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Created by chul-gyun on 2021-11-02
 */
@Slf4j(topic = "http")
@Service
@RequiredArgsConstructor
public class AlimTalkRestRemote {

    private final RestTemplate customRestTemplate;
    private final InitProperties initProperties;
    private final RemoteCommonException remoteCommonException;
    private final ConverterUtil converterUtil;
    private final OfficialWebPropertyRepository officialWebPropertyRepository;

    /**
     * 비즈엠  TODO ㄱㅐ발중
     */
    public List<AlimTalkResponse> sendAlimTalk(List<AlimTalkData> body) {

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<List<AlimTalkData>> httpEntity = new HttpEntity<>(body, httpHeaders);
        URI uri = UriComponentsBuilder.fromUriString(this.initProperties.getAlimTalkHost() + "/v2/sender/send")
                .build().toUri();

        ResponseEntity<List<AlimTalkResponse>> response = null;
        try {
            response = this.customRestTemplate.exchange(uri, HttpMethod.POST, httpEntity,
                    new ParameterizedTypeReference<List<AlimTalkResponse>>() {});
        } catch (HttpServerErrorException hse) {
            this.remoteCommonException.serverException(hse);
        } catch (HttpClientErrorException hce) {
            this.remoteCommonException.clientException(hce);
        }
        return response.getBody();
    }
}
