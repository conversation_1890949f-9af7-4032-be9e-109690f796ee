package kr.co.vendys.company.api.repository.slave;

import kr.co.vendys.company.api.entity.master.CouponMember;
import kr.co.vendys.company.api.entity.master.CouponMemberId;
import kr.co.vendys.company.api.entity.master.PayRoom;
import kr.co.vendys.company.api.repository.slave.custom.CouponMemberRepositoryCustom;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Repository
public interface CouponMemberSlaveRepository extends JpaRepository<CouponMember, CouponMemberId>, CouponMemberRepositoryCustom {

    @Query("SELECT cm FROM kr.co.vendys.company.api.entity.master.CouponMember cm " +
           "LEFT JOIN FETCH cm.payRoom " +
           "LEFT JOIN FETCH cm.couponGroup " +
           "WHERE cm.sid = :sid " +
           "AND cm.comId = :comId " +
           "AND cm.useDate >= :startDate " +
           "AND cm.useDate <= :endDate " +
           "ORDER BY cm.useDate DESC")
    List<CouponMember> findStoreCouponsBySidAndDateRange(
            @Param("sid") String sid,
            @Param("comId") String comId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate);

    @Query("SELECT pr FROM PayRoom pr WHERE pr.couponId IN :couponIds")
    List<PayRoom> findPayRoomsByCouponIds(@Param("couponIds") List<String> couponIds);
}
