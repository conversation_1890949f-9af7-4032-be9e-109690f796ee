package kr.co.vendys.company.api.service.stat.kimandchang;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KimAndChangDeductionDto {
    private String signId;           // 사용자 ID
    private String name;             // 이름
    private String comIdNum;         // 사번
    private String cellphone;        // 휴대폰 번호
    private Long companyPrice; // 식대 사용 금액
}
