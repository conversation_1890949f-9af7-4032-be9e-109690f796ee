package kr.co.vendys.company.api.entity.master;

import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Entity
@Getter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor(access = AccessLevel.PROTECTED)
public class OrganizationCache implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long orgCacheIdx;
    private Long orgIdx;
    private Long orgMetaIdx;
    private Long parentOrgMetaIdx;
    private String rootCode;
    private String orgCode;
    private Integer depth;
    private Integer seq;
    private String name;
    private Boolean isActive;
    private Integer lft;
    private Integer rgt;
    @Enumerated(EnumType.STRING)
    private OrgType type;

    @Getter
    public enum OrgType {
        GROUP("GROUP", "그룹 정보"),
        COMPANY("COMPANY", "회사 정보"),
        DIVISION("DIVISION", "부서 정보");

        private String code;
        private String desc;

        OrgType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

}
