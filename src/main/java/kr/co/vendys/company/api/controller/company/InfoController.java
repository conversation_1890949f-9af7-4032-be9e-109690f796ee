package kr.co.vendys.company.api.controller.company;

import javax.servlet.http.HttpServletRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import kr.co.vendys.company.api.controller.CommonController;
import kr.co.vendys.company.api.controller.ParamDto;
import kr.co.vendys.company.api.controller.company.entity.InfoDto;
import kr.co.vendys.company.api.service.company.InfoService;
import lombok.RequiredArgsConstructor;

/**
 * Created by jangjungsu on 2017. 4. 26..
 */
@RestController
@RequestMapping("/company")
@RequiredArgsConstructor
public class InfoController extends CommonController {

    private final InfoService infoService;

    /**
     * DashBoard > 회사정보
     */
    @GetMapping(value = "/v1/info")
    public ResponseEntity<InfoDto.InfoResponse> getInfo(HttpServletRequest request) {

        ParamDto.RequestData requestData = this.getHeaderData(request);
        InfoDto.InfoResponse response = this.infoService.generateCompanyInfo(requestData.getComId());
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

}
