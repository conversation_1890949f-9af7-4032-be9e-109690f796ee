package kr.co.vendys.company.api.controller.captainpayment.member.dto;

import java.util.List;
import javax.validation.constraints.NotEmpty;

import lombok.Data;

public class CaptainPaymentRankPositionDto {

    @Data
    public static class CreateParam {

        @NotEmpty
        private String name;
    }

    @Data
    public static class ModifyParam {

        @NotEmpty
        private String name;
    }

    @Data
    public static class RankPositionResponse {

        private List<RankPositionDetail> rankPosition;

        @Data
        public static class RankPositionDetail {

            private Long id;
            private String name;
        }
    }
}
