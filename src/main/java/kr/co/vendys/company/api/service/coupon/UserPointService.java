package kr.co.vendys.company.api.service.coupon;

import kr.co.vendys.company.api.constant.Errors;
import kr.co.vendys.company.api.constant.customerserver.UserAuthType;
import kr.co.vendys.company.api.controller.ParamDto;
import kr.co.vendys.company.api.controller.company.entity.DivisionDto;
import kr.co.vendys.company.api.controller.coupon.entity.UserDto;
import kr.co.vendys.company.api.entity.master.MealGroup;
import kr.co.vendys.company.api.entity.master.MealGroup.GroupType;
import kr.co.vendys.company.api.entity.master.User;
import kr.co.vendys.company.api.exception.CommonException;
import kr.co.vendys.company.api.persist.MealGroupPersist;
import kr.co.vendys.company.api.persist.MealPointLogPersist;
import kr.co.vendys.company.api.persist.UserPersist;
import kr.co.vendys.company.api.persist.coupon.CouponMemberPersist;
import kr.co.vendys.company.api.persist.organization.OrganizationDivisionRemote;
import kr.co.vendys.company.api.service.DivisionService;
import kr.co.vendys.company.api.service.ExcelService;
import kr.co.vendys.company.api.util.DateUtil;
import kr.co.vendys.company.api.util.DivisionUtil;
import kr.co.vendys.company.api.util.PagingUtil;
import kr.co.vendys.company.api.vo.CouponStateDto;
import kr.co.vendys.company.api.vo.ExcelVo;
import kr.co.vendys.company.api.vo.MealPointLogVo;
import kr.co.vendys.company.api.vo.UserVo;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by jangjungsu on 2017. 6. 21..
 */
@Service
@Transactional
@RequiredArgsConstructor
public class UserPointService {

    private final MealPointLogPersist mealPointLogPersist;
    private final UserPersist userPersist;
    private final ExcelService excelService;
    private final OrganizationDivisionRemote organizationDivisionRemote;
    private final DivisionService divisionService;
    private final CouponMemberPersist couponMemberPersist;
    private final MealGroupPersist mealGroupPersist;

    /**
     * 임직원 식대 지급/사용 내역 총합계
     */
    public UserDto.ListTotalSumResponse getPointLogUserTotalSum(
            ParamDto.RequestData requestData, UserDto.ParamRequest paramRequest) {
        MealPointLogVo.MealPointLogTotalSum paramVo = new MealPointLogVo.MealPointLogTotalSum();

        if (paramRequest.getKeyword() != null) {
            paramVo.setKeyword(paramRequest.getKeyword());
        }
        if (paramRequest.getGroupid() != null) {
            paramVo.setGroupIdx(paramRequest.getGroupid());
        }

        String orgaCode = "";
        if (requestData.getUserAuthDTO().getType().equals(UserAuthType.DIVISION.type)) {
            orgaCode = requestData.getUserAuthDTO().getOrganization();
        }
        List<String> orgCodeList = this.divisionService.getSearchOrgCodeList(
                requestData.getUser(), requestData.getComId(), orgaCode, paramRequest.getOrgCode()
        );
        paramVo.setOrgCodeList(orgCodeList);
        paramVo.setComid(requestData.getComId());
        paramVo.setStartdate(DateUtil.clearLessOfDay(paramRequest.getStartdate()));
        paramVo.setEnddate(DateUtil.clearLessOfDay(DateUtil.getDateCustomDay(paramRequest.getEnddate(), 1)));
        if (paramRequest.getPage() != null) {
            paramVo.setPage(PagingUtil.page(paramRequest.getPage(), paramRequest.getPagerow()));
            paramVo.setPagerow(paramRequest.getPagerow());
        }

        // 사용 금액
        MealPointLogVo.MealPointLogTotalSum useSum = this.mealPointLogPersist.getCouponUseSum(paramVo);
        // 지급 금액
        MealPointLogVo.MealPointLogTotalSum depositSum = this.mealPointLogPersist.getCouponDepositSum(paramVo);

        UserDto.ListTotalSumResponse.Total total = new UserDto.ListTotalSumResponse.Total();
        total.setInamount(0L);
        total.setOutamount(0L);
        total.setCouponcount(0);
        if (!ObjectUtils.isEmpty(useSum)) {
            total.setOutamount(useSum.getOutTotalAmount() == null ? 0 : useSum.getOutTotalAmount());
            total.setCouponcount(useSum.getCouponCount() == null ? 0 : useSum.getCouponCount());
        }
        if (!ObjectUtils.isEmpty(depositSum)) {
            total.setInamount(depositSum.getInTotalAmount() == null ? 0 : depositSum.getInTotalAmount());
        }
        UserDto.ListTotalSumResponse response = new UserDto.ListTotalSumResponse();
        response.setTotal(total);
        return response;
    }

    /**
     * 임직원 식대 지급/사용 내역
     */
    public UserDto.ListResponse getPointLogUser(
            ParamDto.RequestData requestData, UserDto.ParamRequest paramRequest, GroupType groupType) {

        UserDto.ListResponse.Paging paging = new UserDto.ListResponse.Paging();
        MealPointLogVo.MealPointLogTotalSum paramVo = new MealPointLogVo.MealPointLogTotalSum();

        if (paramRequest.getKeyword() != null) {
            paramVo.setKeyword(paramRequest.getKeyword());
        }
        if (paramRequest.getGroupid() != null) {
            paramVo.setGroupIdx(paramRequest.getGroupid());
        }
        paramVo.setGroupType(groupType.toString());

        String orgCode = "";
        if (requestData.getUserAuthDTO().getType().equals(UserAuthType.DIVISION.type)) {
            orgCode = requestData.getUserAuthDTO().getOrganization();
        }
        List<String> orgCodeList = this.divisionService.getSearchOrgCodeList(
                requestData.getUser(), requestData.getComId(), orgCode, paramRequest.getOrgCode()
        );
        paramVo.setOrgCodeList(orgCodeList);

        paramVo.setComid(requestData.getComId());
        paramVo.setStartdate(DateUtil.clearLessOfDay(paramRequest.getStartdate()));
        paramVo.setEnddate(DateUtil.clearLessOfDay(DateUtil.getDateCustomDay(paramRequest.getEnddate(), 1)));
        if (paramRequest.getPage() != null) {
            paramVo.setPage(PagingUtil.page(paramRequest.getPage(), paramRequest.getPagerow()));
            paramVo.setPagerow(paramRequest.getPagerow());
        }

        UserDto.ListResponse response = new UserDto.ListResponse();
        // totalcount 가 없으면 조회 한다.
        if (ObjectUtils.isEmpty(paramRequest.getTotalcount())) {
            MealPointLogVo.MealPointLogTotalSum totalCountVo = this.mealPointLogPersist.getMealPointUserListTotalCount(paramVo);
            if (totalCountVo == null || totalCountVo.getTotalcount() < 1) {
                paging.setTotalcount(0);
                paging.setPage(paramRequest.getPage() != null ? paramRequest.getPage() : null);
                paging.setPagerow(paramRequest.getPagerow() != null ? paramRequest.getPagerow() : null);
                response.setPaging(paging);
                response.setUser(null);
                return response;
            }
            paramRequest.setTotalcount(totalCountVo.getTotalcount());
        }

        // 사용자 조회
        List<MealPointLogVo.MealPointLogTotalSum> resultTotalUserList = this.mealPointLogPersist.getMealPointUserList(paramVo);
        List<UserDto.ListResponse.User> userList = new ArrayList<>();
        for (MealPointLogVo.MealPointLogTotalSum vo : resultTotalUserList) {
            UserDto.ListResponse.User user = new UserDto.ListResponse.User();
            user.setId(vo.getUid());
            user.setSignid(vo.getSignId());
            user.setName(vo.getName());
            user.setRankposition(vo.getRankposition());
            user.setPosition(vo.getPosition());
            user.setComidnum(vo.getComidnum());
            UserDto.ListResponse.User.Group group = new UserDto.ListResponse.User.Group();
            group.setIdx(vo.getGroupIdx());
            group.setName(vo.getGroupName());
            user.setGroup(group);

            // division
            user.setOrgCode(vo.getOrgCode());

            UserDto.ListResponse.User.Coupon coupon = new UserDto.ListResponse.User.Coupon();
            coupon.setInamount(vo.getInTotalAmount() == null ? 0 : vo.getInTotalAmount());
            coupon.setOutamount(vo.getOutTotalAmount() == null ? 0 : vo.getOutTotalAmount());
            coupon.setCount(vo.getCouponCount() == null ? 0 : vo.getCouponCount());
            user.setCoupon(coupon);
            userList.add(user);
        }

        paging.setTotalcount(paramRequest.getTotalcount());
        paging.setPage(paramRequest.getPage() != null ? paramRequest.getPage() : null);
        paging.setPagerow(paramRequest.getPagerow() != null ? paramRequest.getPagerow() : null);
        response.setPaging(paging);
        response.setUser(userList);
        return response;
    }

    /**
     * 임직원 식대 지급/사용 내역 엑셀 다운로드 (비동기)
     */
    @Async
    public byte[] getPointLogUserExcelAsync(
            ParamDto.RequestData requestData, String uuid, String domain, String fileName, UserDto.DownRequest paramRequest) {

        MealPointLogVo.MealPointLogTotalSum paramVo = new MealPointLogVo.MealPointLogTotalSum();

        if (paramRequest.getKeyword() != null) {
            paramVo.setKeyword(paramRequest.getKeyword());
        }
        if (paramRequest.getGroupid() != null) {
            paramVo.setGroupIdx(paramRequest.getGroupid());
        }
        paramVo.setGroupType(GroupType.MEAL.toString());

        String orgaCode = "";
        if (requestData.getUserAuthDTO().getType().equals(UserAuthType.DIVISION.type)) {
            orgaCode = requestData.getUserAuthDTO().getOrganization();
        }
        List<String> orgCodeList = this.divisionService.getSearchOrgCodeList(
                requestData.getUser(), requestData.getComId(), orgaCode, paramRequest.getOrgCode()
        );
        paramVo.setOrgCodeList(orgCodeList);

        paramVo.setComid(requestData.getComId());
        paramVo.setStartdate(DateUtil.clearLessOfDay(paramRequest.getStartdate()));
        paramVo.setEnddate(DateUtil.clearLessOfDay(DateUtil.getDateCustomDay(paramRequest.getEnddate(), 1)));

        // 사용 금액
        MealPointLogVo.MealPointLogTotalSum useSum = this.mealPointLogPersist.getCouponUseSum(paramVo);
        // 지급 금액
        MealPointLogVo.MealPointLogTotalSum depositSum = this.mealPointLogPersist.getCouponDepositSum(paramVo);

        List<Map<String, Object>> userList = new ArrayList<Map<String, Object>>();
        Map<String, Object> totalData = new HashMap<String, Object>();
        totalData.put("rowColor", IndexedColors.WHITE.getIndex());
        totalData.put("signid", "합 계");

        totalData.put("usepoint", 0);
        totalData.put("taskpoint", 0);
        totalData.put("count", 0);
        if (!ObjectUtils.isEmpty(useSum)) {
            totalData.put("usepoint", useSum.getOutTotalAmount() == null ? 0 : useSum.getOutTotalAmount());
            totalData.put("count", useSum.getCouponCount() == null ? 0 : useSum.getCouponCount());
        }
        if (!ObjectUtils.isEmpty(depositSum)) {
            totalData.put("taskpoint", depositSum.getInTotalAmount() == null ? 0 : depositSum.getInTotalAmount());
        }
        userList.add(totalData);

        // 부서 full path 조회
        UserVo.User excelUserParam = new UserVo.User();
        excelUserParam.setUid(paramRequest.getRequestid());
        UserVo.User excelUser = this.userPersist.getUser(excelUserParam);

        DivisionDto.ResponseTree divisionInfos = this.organizationDivisionRemote.getDivision(excelUser, paramRequest.getComid());
        Map<String, DivisionDto.DivisionTree> allDivisionMap = DivisionUtil.getDivisionMap("orgCode", divisionInfos);

        List<MealPointLogVo.MealPointLogTotalSum> resultTotalUserList = this.mealPointLogPersist.getMealPointUserList(paramVo);
        for (MealPointLogVo.MealPointLogTotalSum vo : resultTotalUserList) {
            Map<String, Object> data = new HashMap<>();

            data.put("signid", vo.getSignId());
            data.put("comidnum", vo.getComidnum());
            data.put("name", vo.getName());
            data.put("rankposition", vo.getRankposition());
            data.put("position", vo.getPosition());
            data.put("group", vo.getGroupName());

            // 부서
            String fullDivision = "";
            if (allDivisionMap.containsKey(vo.getOrgCode())) {
                fullDivision = allDivisionMap.get(vo.getOrgCode()).getFullName();
            }
            List<String> divList = Arrays.asList(fullDivision.split("\\^_\\^"));
            for (int i = 0; i < divList.size(); i++) {
                data.put("division" + (i + 1), divList.get(i));
            }

            data.put("taskpoint", vo.getInTotalAmount() == null ? 0 : vo.getInTotalAmount());
            data.put("usepoint", vo.getOutTotalAmount() == null ? 0 : vo.getOutTotalAmount());
            data.put("count", vo.getCouponCount() == null ? 0 : vo.getCouponCount());

            userList.add(data);
        }

        // 컬럼 만들기
        List<ExcelVo.ExcelColumn> excelColumnList =
                this.excelService.getExcelColumnByOrgCode(ExcelVo.ExcelContentType.V2_COUPON_USER_LIST, divisionInfos.getDepth());

        byte[] rtn = this.excelService.createSaxExcel(
                excelUser, paramRequest.getComid(), ExcelVo.ExcelContentType.V2_COUPON_USER_LIST,
                uuid, domain, fileName,
                paramRequest.getStartdate(), paramRequest.getEnddate(), userList, excelColumnList, true);

        return rtn;

    }

    /**
     * 임직원 식대 지급/사용 내역 엑셀 다운로드
     */
    public byte[] getPointLogUserExcel(
            ParamDto.RequestData requestData, String uuid, String domain, String fileName, UserDto.DownRequest paramRequest) {

        MealPointLogVo.MealPointLogTotalSum paramVo = new MealPointLogVo.MealPointLogTotalSum();

        if (paramRequest.getKeyword() != null) {
            paramVo.setKeyword(paramRequest.getKeyword());
        }
        if (paramRequest.getGroupid() != null) {
            paramVo.setGroupIdx(paramRequest.getGroupid());
        }
        paramVo.setGroupType(GroupType.MEAL.toString());

        String orgaCode = "";
        if (requestData.getUserAuthDTO().getType().equals(UserAuthType.DIVISION.type)) {
            orgaCode = requestData.getUserAuthDTO().getOrganization();
        }
        List<String> orgCodeList = this.divisionService.getSearchOrgCodeList(
                requestData.getUser(), requestData.getComId(), orgaCode, paramRequest.getOrgCode()
        );
        paramVo.setOrgCodeList(orgCodeList);

        paramVo.setComid(requestData.getComId());
        paramVo.setStartdate(DateUtil.clearLessOfDay(paramRequest.getStartdate()));
        paramVo.setEnddate(DateUtil.clearLessOfDay(DateUtil.getDateCustomDay(paramRequest.getEnddate(), 1)));

        // 사용 금액
        MealPointLogVo.MealPointLogTotalSum useSum = this.mealPointLogPersist.getCouponUseSum(paramVo);
        // 지급 금액
        MealPointLogVo.MealPointLogTotalSum depositSum = this.mealPointLogPersist.getCouponDepositSum(paramVo);

        List<Map<String, Object>> userList = new ArrayList<Map<String, Object>>();
        Map<String, Object> totalData = new HashMap<String, Object>();
        totalData.put("rowColor", IndexedColors.WHITE.getIndex());
        totalData.put("signid", "합 계");

        totalData.put("usepoint", 0);
        totalData.put("taskpoint", 0);
        totalData.put("count", 0);
        if (!ObjectUtils.isEmpty(useSum)) {
            totalData.put("usepoint", useSum.getOutTotalAmount() == null ? 0 : useSum.getOutTotalAmount());
            totalData.put("count", useSum.getCouponCount() == null ? 0 : useSum.getCouponCount());
        }
        if (!ObjectUtils.isEmpty(depositSum)) {
            totalData.put("taskpoint", depositSum.getInTotalAmount() == null ? 0 : depositSum.getInTotalAmount());
        }
        userList.add(totalData);

        // 부서 full path 조회
        UserVo.User excelUserParam = new UserVo.User();
        excelUserParam.setUid(paramRequest.getRequestid());
        UserVo.User excelUser = this.userPersist.getUser(excelUserParam);

        DivisionDto.ResponseTree divisionInfos = this.organizationDivisionRemote.getDivision(excelUser, paramRequest.getComid());
        Map<String, DivisionDto.DivisionTree> allDivisionMap = DivisionUtil.getDivisionMap("orgCode", divisionInfos);

        List<MealPointLogVo.MealPointLogTotalSum> resultTotalUserList = this.mealPointLogPersist.getMealPointUserList(paramVo);
        for (MealPointLogVo.MealPointLogTotalSum vo : resultTotalUserList) {
            Map<String, Object> data = new HashMap<>();

            data.put("signid", vo.getSignId());
            data.put("comidnum", vo.getComidnum());
            data.put("name", vo.getName());
            data.put("rankposition", vo.getRankposition());
            data.put("position", vo.getPosition());
            data.put("group", vo.getGroupName());

            // 부서
            String fullDivision = "";
            if (allDivisionMap.containsKey(vo.getOrgCode())) {
                fullDivision = allDivisionMap.get(vo.getOrgCode()).getFullName();
            }
            List<String> divList = Arrays.asList(fullDivision.split("\\^_\\^"));
            for (int i = 0; i < divList.size(); i++) {
                data.put("division" + (i + 1), divList.get(i));
            }

            data.put("taskpoint", vo.getInTotalAmount() == null ? 0 : vo.getInTotalAmount());
            data.put("usepoint", vo.getOutTotalAmount() == null ? 0 : vo.getOutTotalAmount());
            data.put("count", vo.getCouponCount() == null ? 0 : vo.getCouponCount());

            userList.add(data);
        }

        // 컬럼 만들기
        List<ExcelVo.ExcelColumn> excelColumnList =
                this.excelService.getExcelColumnByOrgCode(ExcelVo.ExcelContentType.V2_COUPON_USER_LIST, divisionInfos.getDepth());

        byte[] rtn = this.excelService.createSaxExcel(
                excelUser, paramRequest.getComid(), ExcelVo.ExcelContentType.V2_COUPON_USER_LIST,
                uuid, domain, fileName,
                paramRequest.getStartdate(), paramRequest.getEnddate(), userList, excelColumnList, false);

        return rtn;

    }

    /**
     * 식대 내역 엑셀 다운로드 (전체 사용자 상세) (비동기)
     */
    @Async
    public byte[] getPointLogAllUserDetailExcelAsync(
            ParamDto.RequestData requestData, String uuid, String domain, String fileName, UserDto.DownRequest paramRequest) {

        // list 조회
        UserVo.User excelUserParam = new UserVo.User();
        excelUserParam.setUid(paramRequest.getRequestid());
        UserVo.User excelUser = this.userPersist.getUser(excelUserParam);

        MealPointLogVo.MealPointLogTotalSum paramVo = new MealPointLogVo.MealPointLogTotalSum();

        if (paramRequest.getKeyword() != null) {
            paramVo.setKeyword(paramRequest.getKeyword());
        }
        if (paramRequest.getGroupid() != null) {
            paramVo.setGroupIdx(paramRequest.getGroupid());
        }

        String orgaCode = "";
        if (requestData.getUserAuthDTO().getType().equals(UserAuthType.DIVISION.type)) {
            orgaCode = requestData.getUserAuthDTO().getOrganization();
        }
        List<String> orgCodeList = this.divisionService.getSearchOrgCodeList(
                requestData.getUser(), requestData.getComId(), orgaCode, paramRequest.getOrgCode());

        paramVo.setOrgCodeList(orgCodeList);

        paramVo.setComid(requestData.getComId());
        paramVo.setStartdate(DateUtil.clearLessOfDay(paramRequest.getStartdate()));
        paramVo.setEnddate(DateUtil.clearLessOfDay(DateUtil.getDateCustomDay(paramRequest.getEnddate(), 1)));

        List<Map<String, Object>> userList = new ArrayList<>();

        // 부서 full path 조회
        DivisionDto.ResponseTree divisionInfos = this.organizationDivisionRemote.getDivision(excelUser, paramRequest.getComid());
        Map<String, DivisionDto.DivisionTree> allDivisionMap = DivisionUtil.getDivisionMap("orgCode", divisionInfos);

        List<MealPointLogVo.MealPointLogTotalSum> resultTotalUserList = this.mealPointLogPersist.getMealPointUserList(paramVo);

        // 상세 조회
        MealPointLogVo.MealPointLogEx detailParam = new MealPointLogVo.MealPointLogEx();
        detailParam.setComId(requestData.getComId());
        detailParam.setStartdate(DateUtil.clearLessOfDay(paramRequest.getStartdate()));
        detailParam.setEnddate(DateUtil.clearLessOfDay(DateUtil.getDateCustomDay(paramRequest.getEnddate(), 1)));

        resultTotalUserList.forEach(userVo -> {

            detailParam.setUserId(userVo.getUid());
            List<MealPointLogVo.MealPointLogEx> resultPointLogEx = this.mealPointLogPersist.getPointList(detailParam);
            resultPointLogEx.forEach(vo -> {

                Map<String, Object> data = new HashMap<>();
                data.put("usedate", DateUtil.timeToStdString(vo.getRegDate()));
                data.put("signid", userVo.getSignId());
                data.put("comidnum", userVo.getComidnum());
                data.put("name", userVo.getName());
                // 부서
                String fullDivision = "";
                if (allDivisionMap.containsKey(userVo.getOrgCode())) {
                    fullDivision = allDivisionMap.get(userVo.getOrgCode()).getFullName();
                }
                List<String> divList = Arrays.asList(fullDivision.split("\\^_\\^"));
                for (int i = 0; i < divList.size(); i++) {
                    data.put("division" + (i + 1), divList.get(i));
                }
                data.put("payroomidx", vo.getPayRoomIdx());
                data.put("causetext", vo.getCauseText());
                data.put("group", vo.getGroupName());
                data.put("policy", vo.getPolicyName());
                if (vo.getType().equals("I")) {
                    data.put("taskpoint", vo.getAmount());
                    data.put("usepoint", 0);
                } else {
                    data.put("taskpoint", 0);
                    data.put("usepoint", vo.getAmount());
                }
                data.put("balance", vo.getBalance());
                data.put("cause", vo.getCause());

                userList.add(data);
            });

        });

        // 컬럼 만들기
        List<ExcelVo.ExcelColumn> excelColumnList = this.excelService.getExcelColumnByOrgCode(
                ExcelVo.ExcelContentType.V2_COUPON_ALL_USER_DETAIL, divisionInfos.getDepth()
        );

        byte[] rtn = this.excelService.createSaxExcel(
                excelUser, paramRequest.getComid(), ExcelVo.ExcelContentType.V2_COUPON_ALL_USER_DETAIL,
                uuid, domain, fileName,
                paramRequest.getStartdate(), paramRequest.getEnddate(), userList, excelColumnList, true);

        return rtn;
    }

    /**
     * 식대 내역 엑셀 다운로드 (전체 사용자 상세)
     */
    public byte[] getPointLogAllUserDetailExcel(
            ParamDto.RequestData requestData, String uuid, String domain, String fileName, UserDto.DownRequest paramRequest) {

        // list 조회
        UserVo.User excelUserParam = new UserVo.User();
        excelUserParam.setUid(paramRequest.getRequestid());
        UserVo.User excelUser = this.userPersist.getUser(excelUserParam);

        MealPointLogVo.MealPointLogTotalSum paramVo = new MealPointLogVo.MealPointLogTotalSum();

        if (paramRequest.getKeyword() != null) {
            paramVo.setKeyword(paramRequest.getKeyword());
        }
        if (paramRequest.getGroupid() != null) {
            paramVo.setGroupIdx(paramRequest.getGroupid());
        }

        String orgaCode = "";
        if (requestData.getUserAuthDTO().getType().equals(UserAuthType.DIVISION.type)) {
            orgaCode = requestData.getUserAuthDTO().getOrganization();
        }
        List<String> orgCodeList = this.divisionService.getSearchOrgCodeList(
                requestData.getUser(), requestData.getComId(), orgaCode, paramRequest.getOrgCode());

        paramVo.setOrgCodeList(orgCodeList);

        paramVo.setComid(requestData.getComId());
        paramVo.setStartdate(DateUtil.clearLessOfDay(paramRequest.getStartdate()));
        paramVo.setEnddate(DateUtil.clearLessOfDay(DateUtil.getDateCustomDay(paramRequest.getEnddate(), 1)));

        List<Map<String, Object>> userList = new ArrayList<>();

        // 부서 full path 조회
        DivisionDto.ResponseTree divisionInfos = this.organizationDivisionRemote.getDivision(excelUser, paramRequest.getComid());
        Map<String, DivisionDto.DivisionTree> allDivisionMap = DivisionUtil.getDivisionMap("orgCode", divisionInfos);

        List<MealPointLogVo.MealPointLogTotalSum> resultTotalUserList = this.mealPointLogPersist.getMealPointUserList(paramVo);

        // 상세 조회
        MealPointLogVo.MealPointLogEx detailParam = new MealPointLogVo.MealPointLogEx();
        detailParam.setComId(requestData.getComId());
        detailParam.setStartdate(DateUtil.clearLessOfDay(paramRequest.getStartdate()));
        detailParam.setEnddate(DateUtil.clearLessOfDay(DateUtil.getDateCustomDay(paramRequest.getEnddate(), 1)));

        resultTotalUserList.forEach(userVo -> {

            detailParam.setUserId(userVo.getUid());
            List<MealPointLogVo.MealPointLogEx> resultPointLogEx = this.mealPointLogPersist.getPointList(detailParam);
            resultPointLogEx.forEach(vo -> {

                Map<String, Object> data = new HashMap<>();
                data.put("usedate", DateUtil.timeToStdString(vo.getRegDate()));
                data.put("signid", userVo.getSignId());
                data.put("comidnum", userVo.getComidnum());
                data.put("name", userVo.getName());
                // 부서
                String fullDivision = "";
                if (allDivisionMap.containsKey(userVo.getOrgCode())) {
                    fullDivision = allDivisionMap.get(userVo.getOrgCode()).getFullName();
                }
                List<String> divList = Arrays.asList(fullDivision.split("\\^_\\^"));
                for (int i = 0; i < divList.size(); i++) {
                    data.put("division" + (i + 1), divList.get(i));
                }
                data.put("payroomidx", vo.getPayRoomIdx());
                data.put("causetext", vo.getCauseText());
                data.put("group", vo.getGroupName());
                data.put("policy", vo.getPolicyName());
                if (vo.getType().equals("I")) {
                    data.put("taskpoint", vo.getAmount());
                    data.put("usepoint", 0);
                } else {
                    data.put("taskpoint", 0);
                    data.put("usepoint", vo.getAmount());
                }
                data.put("balance", vo.getBalance());
                data.put("cause", vo.getCause());

                userList.add(data);
            });

        });

        // 컬럼 만들기
        List<ExcelVo.ExcelColumn> excelColumnList = this.excelService.getExcelColumnByOrgCode(
                ExcelVo.ExcelContentType.V2_COUPON_ALL_USER_DETAIL, divisionInfos.getDepth()
        );

        byte[] rtn = this.excelService.createSaxExcel(
                excelUser, paramRequest.getComid(), ExcelVo.ExcelContentType.V2_COUPON_ALL_USER_DETAIL,
                uuid, domain, fileName,
                paramRequest.getStartdate(), paramRequest.getEnddate(), userList, excelColumnList, false);

        return rtn;
    }

    /**
     * 임직원 식대 지급/사용 내역 상세
     */
    public UserDto.DetailResponse getPointLogUserDetail(
            ParamDto.RequestData requestData, UserDto.PathParam pathParam, UserDto.DetailRequest detailRequest) {

        UserDto.DetailResponse.Paging paging = new UserDto.DetailResponse.Paging();

        UserVo.UserEx resultUserVo = this.userPersist.getEmployeeDetail(requestData.getComId(), pathParam.getId());
        if (resultUserVo == null) {
            throw new CommonException(Errors.MEMBER_USER_NOTFOUND_ERROR);
        }

        UserDto.DetailResponse.Total total = new UserDto.DetailResponse.Total();
        MealPointLogVo.MealPointLogTotalSum totalVo = new MealPointLogVo.MealPointLogTotalSum();
        totalVo.setComid(requestData.getComId());
        totalVo.setStartdate(DateUtil.clearLessOfDay(detailRequest.getStartdate()));
        totalVo.setEnddate(DateUtil.clearLessOfDay(DateUtil.getDateCustomDay(detailRequest.getEnddate(), 1)));
        totalVo.setUid(pathParam.getId());

        // 사용 금액
        MealPointLogVo.MealPointLogTotalSum useSum = this.mealPointLogPersist.getCouponUseSum(totalVo);
        // 지급 금액
        MealPointLogVo.MealPointLogTotalSum depositSum = this.mealPointLogPersist.getCouponDepositSum(totalVo);

        total.setInamount(0L);
        total.setOutamount(0L);
        if (!ObjectUtils.isEmpty(useSum)) {
            total.setOutamount(useSum.getOutTotalAmount() == null ? 0L : useSum.getOutTotalAmount());
        }
        if (!ObjectUtils.isEmpty(depositSum)) {
            total.setInamount(depositSum.getInTotalAmount() == null ? 0L : depositSum.getInTotalAmount());
        }
        UserDto.DetailResponse response = new UserDto.DetailResponse();
        response.setTotal(total);

        UserDto.DetailResponse.Info info = new UserDto.DetailResponse.Info();
        UserDto.DetailResponse.Info.Dates dates = new UserDto.DetailResponse.Info.Dates();
        dates.setStartdate(detailRequest.getStartdate());
        dates.setEnddate(detailRequest.getEnddate());
        info.setDate(dates);
        UserDto.DetailResponse.Info.User user = new UserDto.DetailResponse.Info.User();
        user.setId(resultUserVo.getUid());
        user.setName(resultUserVo.getName());
        UserDto.DetailResponse.Info.User.Group group = new UserDto.DetailResponse.Info.User.Group();
        group.setIdx(resultUserVo.getGroupIdx());
        group.setName(resultUserVo.getGroupName());
        user.setGroup(group);
        user.setOrgCode(resultUserVo.getOrgCode());

        info.setUser(user);
        response.setInfo(info);

        MealPointLogVo.MealPointLogEx paramVo = new MealPointLogVo.MealPointLogEx();
        paramVo.setComId(requestData.getComId());
        paramVo.setUserId(pathParam.getId());
        paramVo.setStartdate(DateUtil.clearLessOfDay(detailRequest.getStartdate()));
        paramVo.setEnddate(DateUtil.clearLessOfDay(DateUtil.getDateCustomDay(detailRequest.getEnddate(), 1)));
        if (detailRequest.getPage() != null) {
            paramVo.setPage(PagingUtil.page(detailRequest.getPage(), detailRequest.getPagerow()));
            paramVo.setPagerow(detailRequest.getPagerow());
        }

        if (ObjectUtils.isEmpty(detailRequest.getTotalcount())) {
            MealPointLogVo.MealPointLogEx totalCountVo = this.mealPointLogPersist.getPointListTotalCount(paramVo);
            if (totalCountVo == null || totalCountVo.getTotalcount() < 1) {
                paging.setTotalcount(0);
                paging.setPage(detailRequest.getPage() != null ? detailRequest.getPage() : null);
                paging.setPagerow(detailRequest.getPagerow() != null ? detailRequest.getPagerow() : null);
                response.setPaging(paging);
                total.setInamount(0L);
                total.setOutamount(0L);
                response.setTotal(total);
                response.setInfo(info);
                response.setPoint(null);
                return response;
            }
            detailRequest.setTotalcount(totalCountVo.getTotalcount());
        }

        List<MealPointLogVo.MealPointLogEx> resultPointLogEx = this.mealPointLogPersist.getPointList(paramVo);
        List<UserDto.DetailResponse.Point> pointList = new ArrayList<>();
        for (MealPointLogVo.MealPointLogEx vo : resultPointLogEx) {
            UserDto.DetailResponse.Point point = new UserDto.DetailResponse.Point();
            point.setId(vo.getPointLogIdx());
            point.setUsedate(vo.getUseDate());
            point.setRoomidx(vo.getPayRoomIdx());
            point.setCause(vo.getCause());
            point.setRegdate(vo.getRegDate());

            UserDto.DetailResponse.Point.Group groupEx = new UserDto.DetailResponse.Point.Group();
            groupEx.setIdx(vo.getGroupIdx());
            groupEx.setName(vo.getGroupName());
            point.setGroup(groupEx);

            UserDto.DetailResponse.Point.Policy policy = new UserDto.DetailResponse.Point.Policy();
            policy.setIdx(vo.getPolicyIdx());
            policy.setName(vo.getPolicyName());
            policy.setType(vo.getPolicyType().getCode());
            point.setPolicy(policy);

            UserDto.DetailResponse.Point.Coupon coupon = new UserDto.DetailResponse.Point.Coupon();
            coupon.setType(vo.getType());
            coupon.setAmount(vo.getAmount());
            coupon.setBalance(vo.getBalance());
            point.setCoupon(coupon);

            UserDto.DetailResponse.Point.Status status = new UserDto.DetailResponse.Point.Status();
            status.setValue(vo.getCauseType().getCode());
            status.setText(vo.getCauseText());

            point.setStatus(status);

            pointList.add(point);
        }

        paging.setTotalcount(detailRequest.getTotalcount());
        paging.setPage(detailRequest.getPage() != null ? detailRequest.getPage() : null);
        paging.setPagerow(detailRequest.getPagerow() != null ? detailRequest.getPagerow() : null);
        response.setPaging(paging);
        response.setPoint(pointList);
        return response;
    }

    /**
     * 임직원 식대 지급/사용 내역 상세 엑셀 다운로드
     */

    @Async
    public void getPointLogUserDetailExcelAsync(
            String uuid, String domain, String fileName, UserDto.PathParam pathParam, UserDto.DetailDownRequest detailRequest) {

        UserVo.UserEx resultUserVo = this.userPersist.getEmployeeDetail(detailRequest.getComid(), pathParam.getId());
        if (resultUserVo == null) {
            throw new CommonException(Errors.MEMBER_USER_NOTFOUND_ERROR);
        }

        // 부서
        UserVo.User excelUserParam = new UserVo.User();
        excelUserParam.setUid(detailRequest.getRequestid());
        UserVo.User excelUser = this.userPersist.getUser(excelUserParam);
        // 부서 full path 조회
        DivisionDto.ResponseTree divisionInfos = this.organizationDivisionRemote.getDivision(
                excelUser, detailRequest.getComid()
        );
        Map<String, DivisionDto.DivisionTree> allDivisionMap = DivisionUtil.getDivisionMap("orgCode", divisionInfos);
        // 부서
        String fullDivision = "";
        if (allDivisionMap.containsKey(resultUserVo.getOrgCode())) {
            fullDivision = allDivisionMap.get(resultUserVo.getOrgCode()).getFullName();
        }
        List<String> divList = Arrays.asList(fullDivision.split("\\^_\\^"));
        String division = "";
        for (int i = 0; i < divList.size(); i++) {
            if (!StringUtils.hasText(division)) {
                division += divList.get(i);
            } else {
                division += (" > " + divList.get(i));
            }
        }

        Map<String, Object> infodata = new HashMap<String, Object>();
        infodata.put("content0", resultUserVo.getName());
        infodata.put("content1",
                DateUtil.dayToStdString(DateUtil.clearLessOfDay(detailRequest.getStartdate()))
                        + " ~ "
                        + DateUtil.dayToStdString(DateUtil.clearLessOfDay(detailRequest.getEnddate()))
        );
        infodata.put("content2", resultUserVo.getGroupName() != null ? resultUserVo.getGroupName() : "");
        infodata.put("content3", division);
        infodata.put("header0", "이름");
        infodata.put("header1", "조회기간");
        infodata.put("header2", "식대그룹");
        infodata.put("header3", "부서");
        infodata.put("cnt", 4);

        MealPointLogVo.MealPointLogEx paramVo = new MealPointLogVo.MealPointLogEx();
        paramVo.setComId(detailRequest.getComid());
        paramVo.setUserId(pathParam.getId());
        paramVo.setStartdate(DateUtil.clearLessOfDay(detailRequest.getStartdate()));
        paramVo.setEnddate(DateUtil.clearLessOfDay(DateUtil.getDateCustomDay(detailRequest.getEnddate(), 1)));

        MealPointLogVo.MealPointLogTotalSum totalVo = new MealPointLogVo.MealPointLogTotalSum();
        totalVo.setComid(detailRequest.getComid());
        totalVo.setStartdate(DateUtil.clearLessOfDay(detailRequest.getStartdate()));
        totalVo.setEnddate(DateUtil.clearLessOfDay(DateUtil.getDateCustomDay(detailRequest.getEnddate(), 1)));
        totalVo.setUid(pathParam.getId());

        // 사용 금액
        MealPointLogVo.MealPointLogTotalSum useSum = this.mealPointLogPersist.getCouponUseSum(totalVo);
        // 지급 금액
        MealPointLogVo.MealPointLogTotalSum depositSum = this.mealPointLogPersist.getCouponDepositSum(totalVo);

        Map<String, Object> total = new HashMap<String, Object>();
        total.put("rowColor", IndexedColors.WHITE.getIndex());
        total.put("usedate", "합 계");
        total.put("taskpoint", 0);
        total.put("usepoint", 0);
        if (!ObjectUtils.isEmpty(useSum)) {
            total.put("usepoint", useSum.getOutTotalAmount() == null ? 0 : useSum.getOutTotalAmount());
        }
        if (!ObjectUtils.isEmpty(depositSum)) {
            total.put("taskpoint", depositSum.getInTotalAmount() == null ? 0 : depositSum.getInTotalAmount());
        }

        List<Map<String, Object>> pointList = new ArrayList<>();
        pointList.add(total);

        List<MealPointLogVo.MealPointLogEx> resultPointLogEx = this.mealPointLogPersist.getPointList(paramVo);
        for (MealPointLogVo.MealPointLogEx vo : resultPointLogEx) {

            Map<String, Object> data = new HashMap<String, Object>();
            data.put("usedate", DateUtil.timeToStdString(vo.getRegDate()));
            data.put("payroomidx", vo.getPayRoomIdx());
            data.put("causetext", vo.getCauseText());
            data.put("group", vo.getGroupName());
            data.put("policy", vo.getPolicyName());
            if (vo.getType().equals("I")) {
                data.put("taskpoint", vo.getAmount());
                data.put("usepoint", 0);
            } else {
                data.put("taskpoint", 0);
                data.put("usepoint", vo.getAmount());
            }
            data.put("balance", vo.getBalance());
            data.put("cause", vo.getCause());
            pointList.add(data);
        }

        UserVo.User paramUser = new UserVo.User();
        paramUser.setUid(detailRequest.getRequestid());
        UserVo.User user = this.userPersist.getUser(paramUser);

        this.excelService.createSaxExcel(
                user, detailRequest.getComid(), ExcelVo.ExcelContentType.V2_COUPON_USER_DETAIL,
                uuid, domain, fileName, detailRequest.getStartdate(), detailRequest.getEnddate(), pointList, infodata, true
        );
    }

    public byte[] getPointLogUserDetailExcel(
            String uuid, String domain, String fileName, UserDto.PathParam pathParam, UserDto.DetailDownRequest detailRequest) {

        UserVo.UserEx resultUserVo = this.userPersist.getEmployeeDetail(detailRequest.getComid(), pathParam.getId());
        if (resultUserVo == null) {
            throw new CommonException(Errors.MEMBER_USER_NOTFOUND_ERROR);
        }

        // 부서
        UserVo.User excelUserParam = new UserVo.User();
        excelUserParam.setUid(detailRequest.getRequestid());
        UserVo.User excelUser = this.userPersist.getUser(excelUserParam);
        // 부서 full path 조회
        DivisionDto.ResponseTree divisionInfos = this.organizationDivisionRemote.getDivision(
                excelUser, detailRequest.getComid()
        );
        Map<String, DivisionDto.DivisionTree> allDivisionMap = DivisionUtil.getDivisionMap("orgCode", divisionInfos);
        // 부서
        String fullDivision = "";
        if (allDivisionMap.containsKey(resultUserVo.getOrgCode())) {
            fullDivision = allDivisionMap.get(resultUserVo.getOrgCode()).getFullName();
        }
        List<String> divList = Arrays.asList(fullDivision.split("\\^_\\^"));
        String division = "";
        for (int i = 0; i < divList.size(); i++) {
            if (!StringUtils.hasText(division)) {
                division += divList.get(i);
            } else {
                division += (" > " + divList.get(i));
            }
        }

        Map<String, Object> infodata = new HashMap<String, Object>();
        infodata.put("content0", resultUserVo.getName());
        infodata.put("content1",
                DateUtil.dayToStdString(DateUtil.clearLessOfDay(detailRequest.getStartdate()))
                        + " ~ "
                        + DateUtil.dayToStdString(DateUtil.clearLessOfDay(detailRequest.getEnddate()))
        );
        infodata.put("content2", resultUserVo.getGroupName() != null ? resultUserVo.getGroupName() : "");
        infodata.put("content3", division);
        infodata.put("header0", "이름");
        infodata.put("header1", "조회기간");
        infodata.put("header2", "식대그룹");
        infodata.put("header3", "부서");
        infodata.put("cnt", 4);

        MealPointLogVo.MealPointLogEx paramVo = new MealPointLogVo.MealPointLogEx();
        paramVo.setComId(detailRequest.getComid());
        paramVo.setUserId(pathParam.getId());
        paramVo.setStartdate(DateUtil.clearLessOfDay(detailRequest.getStartdate()));
        paramVo.setEnddate(DateUtil.clearLessOfDay(DateUtil.getDateCustomDay(detailRequest.getEnddate(), 1)));

        MealPointLogVo.MealPointLogTotalSum totalVo = new MealPointLogVo.MealPointLogTotalSum();
        totalVo.setComid(detailRequest.getComid());
        totalVo.setStartdate(DateUtil.clearLessOfDay(detailRequest.getStartdate()));
        totalVo.setEnddate(DateUtil.clearLessOfDay(DateUtil.getDateCustomDay(detailRequest.getEnddate(), 1)));
        totalVo.setUid(pathParam.getId());

        // 사용 금액
        MealPointLogVo.MealPointLogTotalSum useSum = this.mealPointLogPersist.getCouponUseSum(totalVo);
        // 지급 금액
        MealPointLogVo.MealPointLogTotalSum depositSum = this.mealPointLogPersist.getCouponDepositSum(totalVo);

        Map<String, Object> total = new HashMap<String, Object>();
        total.put("rowColor", IndexedColors.WHITE.getIndex());
        total.put("usedate", "합 계");
        total.put("taskpoint", 0);
        total.put("usepoint", 0);
        if (!ObjectUtils.isEmpty(useSum)) {
            total.put("usepoint", useSum.getOutTotalAmount() == null ? 0 : useSum.getOutTotalAmount());
        }
        if (!ObjectUtils.isEmpty(depositSum)) {
            total.put("taskpoint", depositSum.getInTotalAmount() == null ? 0 : depositSum.getInTotalAmount());
        }

        List<Map<String, Object>> pointList = new ArrayList<>();
        pointList.add(total);

        List<MealPointLogVo.MealPointLogEx> resultPointLogEx = this.mealPointLogPersist.getPointList(paramVo);
        for (MealPointLogVo.MealPointLogEx vo : resultPointLogEx) {

            Map<String, Object> data = new HashMap<String, Object>();
            data.put("usedate", DateUtil.timeToStdString(vo.getRegDate()));
            data.put("payroomidx", vo.getPayRoomIdx());
            data.put("causetext", vo.getCauseText());
            data.put("group", vo.getGroupName());
            data.put("policy", vo.getPolicyName());
            if (vo.getType().equals("I")) {
                data.put("taskpoint", vo.getAmount());
                data.put("usepoint", 0);
            } else {
                data.put("taskpoint", 0);
                data.put("usepoint", vo.getAmount());
            }
            data.put("balance", vo.getBalance());
            data.put("cause", vo.getCause());
            pointList.add(data);
        }

        UserVo.User paramUser = new UserVo.User();
        paramUser.setUid(detailRequest.getRequestid());
        UserVo.User user = this.userPersist.getUser(paramUser);

        byte[] rtn = this.excelService.createSaxExcel(
                user, detailRequest.getComid(), ExcelVo.ExcelContentType.V2_COUPON_USER_DETAIL,
                uuid, domain, fileName, detailRequest.getStartdate(), detailRequest.getEnddate(), pointList, infodata, false
        );
        return rtn;
    }

    public CouponStateDto.CouponUsedStatsPage getCouponUsageLog(ParamDto.RequestData requestData, UserDto.ParamRequestV2WithGroupingCondition param) {
        Pageable pageable = null;
        if(!ObjectUtils.isEmpty(param.getPage()) && !ObjectUtils.isEmpty(param.getSize())) {
            pageable = PageRequest.of(param.getPage(), param.getSize());
        }

        // 부서 하위 뎁스 사용자 까지 노출 되야 하므로 tree list 조회
        String orgCode = this.divisionService.loadOrgCode(requestData, "ALL", false);

        List<String> orgCodeList = this.divisionService.getSearchOrgCodeList(requestData.getUser(),
                requestData.getComId(), orgCode, requestData.getComId());

        CouponStateDto.CouponUsedLogSelectCondition condition = this.getCouponUsageLogSelectCondition(requestData, param, orgCodeList);
        if(!ObjectUtils.isEmpty(param.getKeyword()) && ObjectUtils.isEmpty(condition.getUserIds())) {
            return new CouponStateDto.CouponUsedStatsPage(1, 0L, 0L, 1, new ArrayList<>());
        }

        return this.couponMemberPersist.findUserCouponUsageByComIdAndSidNotInAndUseDateBetweenAndSearchCondition(requestData.getComId(), condition, pageable);
    }

    public CouponStateDto.CouponUsedStatsSum getCouponUsageLogSum(ParamDto.RequestData requestData, UserDto.ParamRequestV2WithGroupingCondition param) {
        // 부서 하위 뎁스 사용자 까지 노출 되야 하므로 tree list 조회
        String orgCode = this.divisionService.loadOrgCode(requestData, "ALL", false);

        List<String> orgCodeList = this.divisionService.getSearchOrgCodeList(requestData.getUser(),
                requestData.getComId(), orgCode, requestData.getComId());
        CouponStateDto.CouponUsedLogSelectCondition condition = this.getCouponUsageLogSelectCondition(requestData, param, orgCodeList);
        if(!ObjectUtils.isEmpty(param.getKeyword()) && ObjectUtils.isEmpty(condition.getUserIds())) {
            return new CouponStateDto.CouponUsedStatsSum(0L, 0);
        }

        return this.couponMemberPersist.findCouponTotalUsageByComIdAndSidNotInAndUseDateBetweenAndSearchCondition(requestData.getComId(), condition);
    }

    public MealPointLogVo.MealPointGrantDeductStatsPage getMealPointGrantAndDeductLog(ParamDto.RequestData requestData, UserDto.ParamRequestV2 param) {
        List<String> userIds = new ArrayList<>();
        if(!ObjectUtils.isEmpty(param.getKeyword())) {
            userIds = this.userPersist.findDistinctUserIdsByComIdAndSearchWord(requestData.getComId(), param.getKeyword());
            if(ObjectUtils.isEmpty(userIds)) {
                return MealPointLogVo.MealPointGrantDeductStatsPage.from(param.getPage(), param.getSize(), 0L, new ArrayList<>());
            }
        }

        // 부서 하위 뎁스 사용자 까지 노출 되야 하므로 tree list 조회
        String orgCode = this.divisionService.loadOrgCode(requestData, "ALL", false);

        List<String> orgCodeList = this.divisionService.getSearchOrgCodeList(requestData.getUser(),
                requestData.getComId(), orgCode, requestData.getComId());

        // 복지포인트 사용 내역은 제외
        List<MealGroup> welfareGroups = this.mealGroupPersist.getWelfareMealGroups(requestData.getComId());
        Set<Long> welfareGroupIdxs = welfareGroups.stream().map(MealGroup::getGroupIdx).collect(Collectors.toSet());

        CouponStateDto.CouponGrantDeductSelectCondition selectCondition = CouponStateDto.CouponGrantDeductSelectCondition
                .from(requestData.getComId(), param, new HashSet<>(userIds), welfareGroupIdxs, orgCodeList);

        List<MealPointLogVo.MealPointGrantDeductStats> results = mealPointLogPersist.getMealPointGrantAndDeductLog(selectCondition);

        long totalRow;
        if (!ObjectUtils.isEmpty(selectCondition.getOffset()) && !ObjectUtils.isEmpty(selectCondition.getLimit())) {
            totalRow = mealPointLogPersist.getMealPointGrantAndDeductLogTotalCount(selectCondition);
        } else {
            totalRow = results.size();
        }


        return MealPointLogVo.MealPointGrantDeductStatsPage.from(param.getPage(), param.getSize(), totalRow, results);
    }

    public MealPointLogVo.MealPointDeductStatsSum getMealPointGrantAndDeductLogSum(ParamDto.RequestData requestData, UserDto.ParamRequestV2 param) {
        List<String> userIds = new ArrayList<>();
        if(!ObjectUtils.isEmpty(param.getKeyword())) {
            userIds = this.userPersist.findDistinctUserIdsByComIdAndSearchWord(requestData.getComId(), param.getKeyword());

            if(ObjectUtils.isEmpty(userIds)) {
                return new MealPointLogVo.MealPointDeductStatsSum(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
            }
        }

        // 부서 하위 뎁스 사용자 까지 노출 되야 하므로 tree list 조회
        String orgCode = this.divisionService.loadOrgCode(requestData, "ALL", false);

        List<String> orgCodeList = this.divisionService.getSearchOrgCodeList(requestData.getUser(),
                requestData.getComId(), orgCode, requestData.getComId());


        // 복지포인트 사용 내역은 제외
        List<MealGroup> welfareGroups = this.mealGroupPersist.getWelfareMealGroups(requestData.getComId());
        Set<Long> welfareGroupIdxs = welfareGroups.stream().map(MealGroup::getGroupIdx).collect(Collectors.toSet());

        CouponStateDto.CouponGrantDeductSelectCondition selectCondition = CouponStateDto.CouponGrantDeductSelectCondition
                .from(requestData.getComId(), param, new HashSet<>(userIds), welfareGroupIdxs, orgCodeList);

        return mealPointLogPersist.getMealPointGrantAndDeductLogSum(selectCondition);
    }

    public CouponStateDto.CouponUsedDetailPage getCouponUsageLogDetail(String comId, String userId, UserDto.DetailRequestV2 param) {
        final LocalDateTime startDateTime = param.getStartDate().atStartOfDay();
        final LocalDateTime endDateTime = param.getEndDate().plusDays(1L).atStartOfDay();

        Pageable pageable = null;
        if(!ObjectUtils.isEmpty(param.getPage()) && !ObjectUtils.isEmpty(param.getSize())) {
            pageable = PageRequest.of(param.getPage(), param.getSize());
        }

        // 복지포인트 사용 내역은 제외하므로 복지포인트 사용내역의 couponId를 조회하여 not in 조건으로 사용
        Set<MealPointLogVo.MealPointLogEx.CauseType> causeTypes = Set.of(
                MealPointLogVo.MealPointLogEx.CauseType.USE_MEALCOUPON,
                MealPointLogVo.MealPointLogEx.CauseType.REFUND
        );

        Set<String> welfareCouponIds = mealPointLogPersist.findCauseLinkByComIdAndCauseTypeAndGroupTypeAndUseDateBetween(
                comId, Collections.singletonList(userId), GroupType.WELFARE, causeTypes, startDateTime, endDateTime
        );

        User user = userPersist.getUser(userId);

        if(ObjectUtils.isEmpty(user)) throw new CommonException(Errors.MEMBER_USER_NOTFOUND_ERROR);

        UserVo.SimpleUserInfo userDto = UserVo.SimpleUserInfo.from(user);
        CouponStateDto.CouponUsedDetailPage res = couponMemberPersist.findCouponUsageDetails(comId, userId, startDateTime, endDateTime, welfareCouponIds, pageable);
        res.setUser(userDto);

        return res;
    }


    public MealPointLogVo.MealPointGrantDeductDetailPage getMealPointGrantAndDeductDetailLog(String comId, String userId, UserDto.DetailRequestV2 param) {

        Pageable pageable = null;
        if(!ObjectUtils.isEmpty(param.getPage()) && !ObjectUtils.isEmpty(param.getSize())) {
            pageable = PageRequest.of(param.getPage(), param.getSize());
        }

        User user = userPersist.getUser(userId);
        if(ObjectUtils.isEmpty(user)) throw new CommonException(Errors.MEMBER_USER_NOTFOUND_ERROR);
        UserVo.SimpleUserInfo userDto = UserVo.SimpleUserInfo.from(user);

        CouponStateDto.CouponGrantDeductDetailSelectCondition selectCondition = CouponStateDto.CouponGrantDeductDetailSelectCondition
                .from(comId, userId, param);

        MealPointLogVo.MealPointGrantDeductDetailPage resp =  this.mealPointLogPersist.getMealPointGrantAndDeductDetailLog(selectCondition, pageable);
        resp.setUser(userDto);
        return resp;
    }

    private CouponStateDto.CouponUsedLogSelectCondition getCouponUsageLogSelectCondition(ParamDto.RequestData  requestData, UserDto.ParamRequestV2WithGroupingCondition paramRequest, List<String> orgCodeList) {
        List<String> userIds = new ArrayList<>();
        if(!ObjectUtils.isEmpty(paramRequest.getKeyword())) {
            userIds = this.userPersist.findDistinctUserIdsByComIdAndSearchWord(requestData.getComId(), paramRequest.getKeyword());
        }

        final LocalDateTime startDateTime = paramRequest.getStartDate().atStartOfDay();
        final LocalDateTime endDateTime = paramRequest.getEndDate().plusDays(1L).atStartOfDay();

        // 복지포인트 사용 내역은 제외하므로 복지포인트 사용내역의 couponId를 조회하여 not in 조건으로 사용
        Set<MealPointLogVo.MealPointLogEx.CauseType> causeTypes = Set.of(
                MealPointLogVo.MealPointLogEx.CauseType.USE_MEALCOUPON,
                MealPointLogVo.MealPointLogEx.CauseType.REFUND
        );

        Set<String> couponIds = mealPointLogPersist.findCauseLinkByComIdAndCauseTypeAndGroupTypeAndUseDateBetween(
                requestData.getComId(), userIds, GroupType.WELFARE, causeTypes, startDateTime, endDateTime
        );

        return CouponStateDto.CouponUsedLogSelectCondition.of(couponIds, new HashSet<>(userIds), paramRequest, orgCodeList);
    }
}
