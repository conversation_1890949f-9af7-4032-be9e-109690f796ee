package kr.co.vendys.company.api.persist.coupon;

import kr.co.vendys.company.api.entity.master.CouponMember;
import kr.co.vendys.company.api.entity.master.CouponMenu;
import kr.co.vendys.company.api.entity.master.PayRoom;
import kr.co.vendys.company.api.repository.master.CouponMemberRepository;
import kr.co.vendys.company.api.repository.master.CouponMenuRepository;
import kr.co.vendys.company.api.repository.slave.CouponMemberSlaveRepository;
import kr.co.vendys.company.api.vo.CouponStateDto;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class CouponMemberPersist {

    private final CouponMemberRepository couponMemberRepository;
    private final CouponMemberSlaveRepository couponMemberSlaveRepository;
    private final CouponMenuRepository couponMenuRepository;

    public CouponStateDto.CouponUsedStatsPage findUserCouponUsageByComIdAndSidNotInAndUseDateBetweenAndSearchCondition(
            String comId, CouponStateDto.CouponUsedLogSelectCondition condition, Pageable pageable) {
        return couponMemberSlaveRepository.findUserCouponUsageByComIdAndSidNotInAndUseDateBetweenAndSearchCondition(comId, condition, pageable);
    }

    public CouponStateDto.CouponUsedStatsSum findCouponTotalUsageByComIdAndSidNotInAndUseDateBetweenAndSearchCondition(String comId, CouponStateDto.CouponUsedLogSelectCondition condition) {
        return couponMemberSlaveRepository.findCouponTotalUsageByComIdAndSidNotInAndUseDateBetweenAndSearchCondition(comId, condition);
    }

    public CouponStateDto.CouponUsedDetailPage findCouponUsageDetails(String comid, String userId, LocalDateTime startDate, LocalDateTime endDate, Set<String> couponIds, Pageable pageable) {
        return couponMemberSlaveRepository.findCouponUsageDetails(comid, userId, startDate, endDate, couponIds, pageable);
    }

    public Long findCouponUsageDetailSum(String comid, String userId, LocalDateTime startDate, LocalDateTime endDate, Set<String> couponIds) {
        return couponMemberSlaveRepository.findCouponUsageDetailSum(comid, userId, startDate, endDate, couponIds);
    }

    public List<CouponMember> findStoreCouponsBySidAndDateRange(String sid, String comId, Date startDate, Date endDate) {
        return couponMemberSlaveRepository.findStoreCouponsBySidAndDateRange(sid, comId, startDate, endDate);
    }

    public List<PayRoom> findPayRoomsByCouponIds(List<String> couponIds) {
        return couponMemberSlaveRepository.findPayRoomsByCouponIds(couponIds);
    }

    public List<CouponMenu> findFirstCouponMenusByCids(List<String> cids) {
        return couponMenuRepository.findFirstMenusByCids(cids);
    }
}
