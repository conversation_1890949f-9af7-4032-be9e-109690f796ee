package kr.co.vendys.company.api.util;

import org.springframework.stereotype.Component;

import java.util.regex.Pattern;

/**
 * Created by j<PERSON><PERSON><PERSON><PERSON> on 2017. 5. 25..
 */
@Component
public class DataValidationUtil {

    /**
     * 이메일 형식 체크
     */
    public static Boolean email(String email) {
        return Pattern.matches(
                "^[a-zA-Z0-9+-_.]+@[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*$"
                , email
        );
    }

    /**
     * 전화번호 형식 체크
     */
    public static Boolean phone(String phone) {
        return Pattern.matches("^[0-9]*$", phone);
    }

    /**
     * 휴대전화번호 형식 체크
     */
    public static Boolean cellPhone(String cellphone) {
        return Pattern.matches("^01[0-9]{8,9}$", cellphone);
    }

    /**
     * SignId 형식 체크
     */
    public static Boolean signId(String signId) {
        return Pattern.matches("^([0-9a-zA-Z-_.+@])*", signId) || DataValidationUtil.email(signId);
    }

    /**
     * SignId 길이 체크
     */
    public static Boolean signIdLen(String signId) {
        if (signId == null) {
            return false;
        }
        if (signId.length() < 6 || signId.length() > 50) {
            return false;
        }
        return true;
    }

    /*
     * 직책 길이 체크
     * */
    public static Boolean positionLen(String position) {

        if (position.length() > 20) {
            return false;
        }
        return true;
    }
}
