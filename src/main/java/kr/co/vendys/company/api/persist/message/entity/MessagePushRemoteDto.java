package kr.co.vendys.company.api.persist.message.entity;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Created by j<PERSON><PERSON><PERSON><PERSON> on 2017. 2. 13..
 */
public class MessagePushRemoteDto {

    @Data
    public static class CompanySetPushRequest {
        private String title;
        private String content;
        private String type;
        private String client;
        private String action;
        private String param;
        private String customScheme;
        private String requestid;
        private String comid;
    }

    @Data
    public static class UserSetPushRequest {
        private List<String> uid;
        private String title;
        private String content;
        private String type;
        private String requestid;
        private String client;
        private String param;
        private ChannelId channelId;
        private String action;
        private String customScheme;
    }

    @Data
    public static class CompanyPushRequest {
        private String type;
        private String id;
        private Integer page;
        private Integer pagerow;
        private String query;
    }

    @Data
    public static class CompanyPushResponse {
        private Paging paging;
        private List<Datas> data;

        @Data
        public static class Paging {
            private Integer total;
            private Integer page;
            private Integer pagerow;
        }

        @Data
        public static class Datas {
            private String requestid;
            private String content;
            private Date senddate;
            private String status;
        }
    }

    @Data
    public static class OneSignalBody {
        private String app_id;
        private List<String> included_segments;
        private Contents contents;
        private Datas data;
        private List<Filters> filters;

        @Data
        public static class Filters {
            private String field;
            private String key;
            private String relation;
            private String value;
        }

        @Data
        public static class Datas {
            private Integer postid;
        }

        @Data
        public static class Contents {
            private String en;
            private String ko;
        }
    }

    @Data
    public static class ResponseCompanyNotice {

        private Integer companyNoticeIdx;
    }

    @Data
    public static class ResponseDemandUserReject {

        private Long demandIdx;
        private Long demandUserIdx;
    }

    @Data
    public static class WebCustomScheme {
        private String url;

        //false - App 화면 스택을 유지 한채로 WebView 를 노출하여 해당 링크 로드 / true -화면 스택 초기화 하여 메인 이동후 WebView 오픈 및 해당 링크
        private Animation animation;

        private Boolean clear;
        private String title;

        @Data
        public static class Animation {
            private String type;
            private String direction;
        }
    }

    public enum ChannelId {
        MESSAGE_CHANNEL_QUICK_CAPTAIN
    }

}
