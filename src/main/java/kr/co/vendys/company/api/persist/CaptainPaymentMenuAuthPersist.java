package kr.co.vendys.company.api.persist;

import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import kr.co.vendys.company.api.entity.master.CaptainPaymentMenuAuth;
import kr.co.vendys.company.api.repository.master.CaptainPaymentMenuAuthRepository;
import lombok.RequiredArgsConstructor;

@Service
@Transactional
@RequiredArgsConstructor
public class CaptainPaymentMenuAuthPersist {

    private final CaptainPaymentMenuAuthRepository menuAuthRepository;


    public List<CaptainPaymentMenuAuth> getDefaultAuthExceptMenus(List<Integer> exceptMenus) {
        if (ObjectUtils.isEmpty(exceptMenus)) {
            exceptMenus = null;
        }
        return this.menuAuthRepository.getDefaultAuthNotInMenus(exceptMenus);
    }
}
