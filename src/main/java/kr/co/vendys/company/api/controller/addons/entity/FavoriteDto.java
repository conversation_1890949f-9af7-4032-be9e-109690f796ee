package kr.co.vendys.company.api.controller.addons.entity;

import java.util.List;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * Created by jangjungsu on 2017. 4. 27..
 */
public class FavoriteDto {

    @Data
    public static class RegistRequest {

        @NotNull
        private String code;
    }

    @Data
    public static class DeleteRequest {

        @NotNull
        private Integer idx;
    }

    @Data
    public static class Response {

        List<Favorite> favorite;

        @Data
        public static class Favorite {

            private Integer idx;
            private String code;
        }
    }
}
