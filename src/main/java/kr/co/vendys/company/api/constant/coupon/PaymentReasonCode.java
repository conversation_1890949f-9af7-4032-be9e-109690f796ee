package kr.co.vendys.company.api.constant.coupon;

/**
 * Created by jiwon on 2021. 4. 6..
 */
public enum PaymentReasonCode {
    ALL("ALL", "전체"),
    REGIST("REGIST", "등록"),
    UNREGIST("UNREGIST", "미등록")
    ;

    public String type;
    public String name;


    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }


    PaymentReasonCode(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public static PaymentReasonCode getEnum(String type) {

        int codeSize = PaymentReasonCode.values().length;

        for (int i = 0; i < codeSize; i++) {
            if (PaymentReasonCode.values()[i].type.equalsIgnoreCase(type)) {
                return PaymentReasonCode.values()[i];
            }
        }

        throw new IllegalArgumentException();
    }
}
