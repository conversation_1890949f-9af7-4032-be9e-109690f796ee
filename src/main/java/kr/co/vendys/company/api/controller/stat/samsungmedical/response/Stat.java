package kr.co.vendys.company.api.controller.stat.samsungmedical.response;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.springframework.util.ObjectUtils;

import com.fasterxml.jackson.annotation.JsonProperty;
import kr.co.vendys.company.api.controller.company.entity.DivisionDto;
import kr.co.vendys.company.api.vo.StatPaymentVo;
import lombok.Getter;

@Getter
public class Stat {

    @JsonProperty("sdate")
    private final String useDate;

    @JsonProperty("signid")
    private final String signId;
    private final String username;

    @JsonProperty("comidnum")
    private final String companyIdNum;

    @JsonProperty("groupname")
    private final String groupName;

    @JsonProperty("policyname")
    private final String policyName;

    @JsonProperty("fullname")
    private List<String> fullName;

    @JsonProperty("companyprice")
    private final Long companyPrice;

    @JsonProperty("salesprice")
    private final Long salesPrice;



    private Long companySupportPrice;
    private Long salaryDeductionPrice;

    private Stat(
            String useDate,
            String signId,
            String username,
            String companyIdNum,
            String groupName,
            String policyName,
            Long companyPrice,
            List<String> fullName
    ) {
        this.useDate = useDate;
        this.signId = signId;
        this.username = username;
        this.companyIdNum = companyIdNum;
        this.groupName = groupName;
        this.policyName = policyName;
        this.salesPrice = companyPrice;
        this.companyPrice = companyPrice;
        this.fullName = fullName;
    }

    public static Stat of(StatPaymentVo paymentInfo, DivisionDto.DivisionTree divisionTree) {
        // 부서 설정
        List<String> fullName = Collections.emptyList();
        if (!ObjectUtils.isEmpty(divisionTree)) {
            fullName = Arrays.asList(divisionTree.getFullName().split("\\^_\\^"));
        }

        return new Stat(
                paymentInfo.getUseDateString(),
                paymentInfo.getSignId(),
                paymentInfo.getUsername(),
                paymentInfo.getCompanyIdNum(),
                paymentInfo.getGroupName(),
                paymentInfo.getPolicyName(),
                paymentInfo.getCompanyPrice(),
                fullName
        );
    }


    public void setFullName(List<String> fullName) {
        this.fullName = fullName;
    }

    public void setSalaryDeductionPrice(Long salaryDeductionPrice) {
        this.salaryDeductionPrice = salaryDeductionPrice;
    }

    public void setCompanySupportPrice(Long companySupportPrice) {
        this.companySupportPrice = companySupportPrice;
    }
}
