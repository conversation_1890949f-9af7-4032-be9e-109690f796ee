package kr.co.vendys.company.api.repository.master;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import kr.co.vendys.company.api.entity.master.CaptainPaymentCompanyInfo.ServiceType;
import kr.co.vendys.company.api.entity.master.CaptainPaymentServiceApply;

@Repository
public interface CaptainPaymentServiceApplyRepository extends JpaRepository<CaptainPaymentServiceApply, String> {

    CaptainPaymentServiceApply findByComIdAndServiceType(String comId, ServiceType type);
}
