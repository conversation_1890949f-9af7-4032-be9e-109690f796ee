package kr.co.vendys.company.api.persist.settlement;

import kr.co.vendys.company.api.constant.Errors;
import kr.co.vendys.company.api.constant.InitProperties;
import kr.co.vendys.company.api.controller.exception.entity.ErrorResponse;
import kr.co.vendys.company.api.exception.CommonException;
import kr.co.vendys.company.api.persist.common.RemoteCommonException;
import kr.co.vendys.company.api.persist.settlement.entity.StatusDto.CompanySettleStatus;
import kr.co.vendys.company.api.util.ConverterUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

@Slf4j(topic = "http")
@Service
@RequiredArgsConstructor
public class StatusRemote {
    private final RestTemplate customRestTemplate;
    private final InitProperties initProperties;
    private final RemoteCommonException remoteCommonException;
    private final ConverterUtil converterUtil;

    /**
     * 정산 진행상태
     */
    public CompanySettleStatus getProgressStatus(Long taxUnitSeq) {

        URI uri = UriComponentsBuilder.fromUriString(
                this.initProperties.getSettlementServerHost() + "/settlement/v1/company/status/{taxUnitSeq}"
        ).buildAndExpand(this.getTaxUnitSeqPathParam(taxUnitSeq)).toUri();

        ResponseEntity<CompanySettleStatus> responseEntity = null;
        try {
            responseEntity = this.customRestTemplate.exchange(uri, HttpMethod.GET, null, CompanySettleStatus.class);
        } catch (HttpServerErrorException hse) {
            this.remoteCommonException.serverException(hse);
        } catch (HttpClientErrorException hce) {
            ErrorResponse errorResponse = this.converterUtil.toObject(hce.getResponseBodyAsString(), ErrorResponse.class);
            log.info("errorResponse : {}", errorResponse);
            throw new CommonException(Errors.SETTLEMENT_INTERFACE_ERROR);
        }

        if (ObjectUtils.isEmpty(responseEntity)) {
            return null;
        }

        return responseEntity.getBody();
    }

    private Map<String, String> getTaxUnitSeqPathParam(Long taxUnitSeq) {
        Map<String, String> pathParams = new HashMap<>();
        pathParams.put("taxUnitSeq", String.valueOf(taxUnitSeq));
        return pathParams;
    }
}
