package kr.co.vendys.company.api.repository.master;

import kr.co.vendys.company.api.entity.master.CaptainPaymentCompanyInfo;
import kr.co.vendys.company.api.entity.master.CaptainPaymentCompanyInfo.ServiceType;
import kr.co.vendys.company.api.entity.master.CaptainPaymentCompanyInfo.Status;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CaptainPaymentCompanyInfoRepository extends JpaRepository<CaptainPaymentCompanyInfo, String> {
    CaptainPaymentCompanyInfo findByComIdAndServiceType(String comId, ServiceType serviceType);
    List<CaptainPaymentCompanyInfo> findByComIdAndServiceTypeIn(String comId, List<ServiceType> serviceType);
    List<CaptainPaymentCompanyInfo> findByComId(String comId);
    List<CaptainPaymentCompanyInfo> findByComIdAndStatus(String comId, Status status);
    List<CaptainPaymentCompanyInfo> findByComIdAndServiceTypeNotAndStatus(String comId, ServiceType serviceType, Status status);
}
