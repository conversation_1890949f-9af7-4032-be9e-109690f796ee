package kr.co.vendys.company.api.controller.stat.kimandchang;

import kr.co.vendys.company.api.vo.PagingVo;
import lombok.Builder;
import lombok.Getter;
import java.time.LocalDate;
import java.util.List;

@Getter
@Builder
public class KimAndChangDeductionResponse {
    private List<DeductionDetail> stat;  // 상세 내역
    private DeductionTotal total;  // 합계
    private PagingVo paging;
    private List<TableInfo> table;  // 테이블 정보

    @Getter
    @Builder
    public static class TableInfo {
        private String name;    // 테이블 헤더명
        private String property;  // 매핑될 필드명
    }

    /**
     * 공제 합계
     */
    @Getter
    @Builder
    public static class DeductionTotal {
        private Long totalPrice;             // 총 사용 식대 합계
        private Long companySupportPrice;     // 회사 지원 금액 합계
        private Long salaryDeductionPrice;    // 개인 사용 금액 합계
        private Long personalSupplyAmount;    // 개인 사용 공급가액 합계
        private Long personalVatAmount;       // 개인 사용 부가세 합계
    }

    /**
     * 공제 상세 내역
     */
    @Getter
    @Builder
    public static class DeductionDetail {
        private String useDate;          // 사용 기간 (YYYY-MM-DD~YYYY-MM-DD)
        private LocalDate startDate;
        private LocalDate endDate;
        private String signId;           // 사용자 ID
        private String name;             // 이름
        private String comIdNum;         // 사번
        private String cellphone;        // 휴대폰 번호
        private Long totalPrice;        // 총 사용 식대
        private Long companySupportPrice; // 회사 지원 금액
        private Long salaryDeductionPrice;     // 개인 사용 금액
        private Long personalSupplyAmount; // 개인 사용 공급가액
        private Long personalVatAmount;  // 개인 사용 부가세
    }
}
