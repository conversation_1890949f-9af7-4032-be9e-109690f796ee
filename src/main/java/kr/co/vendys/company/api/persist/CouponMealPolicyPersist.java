package kr.co.vendys.company.api.persist;

import kr.co.vendys.company.api.entity.master.CouponMealPolicy;
import kr.co.vendys.company.api.repository.master.CouponMealPolicyRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created By huidragon 2019-07-17
 */
@Service
@Transactional
public class CouponMealPolicyPersist {

    @Autowired
    CouponMealPolicyRepository couponMealPolicyRepository;

    public List<CouponMealPolicy> getCouponMealPolicy(String couponId, String userId) {
        return this.couponMealPolicyRepository.findByCouponIdAndUserIdOrderByUsedDesc(couponId, userId);
    }
}
