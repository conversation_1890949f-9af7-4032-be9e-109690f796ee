package kr.co.vendys.core.schema.mealcoupon.entity;

import kr.co.vendys.core.schema.mealcoupon.enums.TransactionStatus;
import kr.co.vendys.core.schema.mealcoupon.enums.TransactionType;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 디바이스 고유 사용자 정보
 */
@Getter
@SuperBuilder
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(
    name = "TransactionIssued",
    uniqueConstraints = {
        @UniqueConstraint(name = "TransactionIssued_transactionId_payRoomIdx", columnNames = {"transactionId", "payRoomIdx"})
    },
    indexes = {
        @Index(name = "TransactionIssued_transactionId_payRoomIdx_IDX", columnList = "transactionId,payRoomIdx"),
        @Index(name = "TransactionIssued_createDateYmd_IDX", columnList = "createDateYmd")
    }
)
public class TransactionIssued {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idx;

    @Column(nullable = false, length = 50)
    private String transactionId; // 트랜잭션 고유 ID

    @Column
    private Long deviceBiometricIdx; // 디바이스 정보 고유 ID

    @Column
    private Long payRoomIdx; // 결제방 ID

    @Column(nullable = false, length = 50)
    private String userId; // 사용자 고유 ID

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, columnDefinition = "enum('PAYMENT','AUTH')")
    private TransactionType transactionType; // 트랜잭션 요청타입(PAYMENT: 결제,AUTH: 인증)

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, columnDefinition = "enum('ISSUED','USED')")
    private TransactionStatus transactionStatus; // 트랜잭션 상태(ISSUED: 발급, USED: 사용완료)

    @Column(nullable = false, length = 8, columnDefinition = "char(8)")
    private String createDateYmd; // 생성일 YYYYMMDD

    @Column
    private LocalDateTime createDate; // 등록일

    @Column(length = 36, columnDefinition = "char(36)")
    private String createUser; // 등록자

    @Column
    private LocalDateTime updateDate; // 수정일

    @Column(length = 36, columnDefinition = "char(36)")
    private String updateUser; // 수정자

    @Column(nullable = false, columnDefinition = "bit(1) default b'0'")
    private Boolean isValid = false; // 유효한 트랜잭션 여부

    // 연관관계 객체 필드
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", referencedColumnName = "uid", insertable = false, updatable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "payRoomIdx", referencedColumnName = "payRoomIdx", insertable = false, updatable = false)
    private PayRoom payRoom;
}
