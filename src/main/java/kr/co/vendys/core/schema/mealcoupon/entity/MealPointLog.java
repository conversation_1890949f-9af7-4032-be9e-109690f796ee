package kr.co.vendys.core.schema.mealcoupon.entity;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Getter
@SuperBuilder
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(
    name = "MealPointLog",
    indexes = {
        @Index(name = "fk_MealPointLog_MealGroup1_idx", columnList = "groupIdx"),
        @Index(name = "fk_MealPointLog_MealPolicy1_idx", columnList = "policyIdx"),
        @Index(name = "idx_MealPointLog_comId_causeType", columnList = "comid,causeType"),
        @Index(name = "idx_MealPointLog_userId", columnList = "userId"),
        @Index(name = "idx_MealPointLog_useDate", columnList = "useDate"),
        @Index(name = "idx_MealPointLog_comId_useDate", columnList = "comid,useDate"),
        @Index(name = "MealPointLog_userId_IDX", columnList = "userId,policyIdx,causeType")
    }
)
public class MealPointLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column
    private Long pointLogIdx; // 이력 고유 번호

    @Column(nullable = false)
    private Long groupIdx; // 그룹 고유 번호

    @Column(nullable = false)
    private Long policyIdx; // 정책 고유 번호

    @Column(nullable = false, length = 40)
    private String comid; // 회사 고유 아이디

    @Column(nullable = false, length = 45)
    private String comName;

    @Column(nullable = false, length = 40)
    private String userId; // 사용자 고유 아이디

    @Column(nullable = false, length = 45)
    private String userName;

    @Column(nullable = false, length = 1, columnDefinition = "char(1)")
    private String type; // 입력/출력 구분 (입력:I, 출력:O)

    @Column(nullable = false)
    private Integer amount; // 금액

    @Column
    private Integer balance; // 잔액

    @Column(nullable = false)
    private Boolean isActive; // 활성화 유무

    @Column
    private LocalDateTime expireDate; // 만료 시간

    @Column
    private LocalDateTime useDate; // 포인트 사용 시간.

    @Column(nullable = false)
    private LocalDateTime regDate; // 등록시간

    @Column(nullable = false, length = 45)
    private String policyName; // 정책 이름

    @Column(nullable = false, length = 10)
    private String policyType; // 정책 구분 (일일:DAY, 장기:LONGTERM, 무한:LESS)

    @Column(length = 50)
    private String cause; // 사유

    @Column(length = 20)
    private String causeType; // 사유 타입

    @Column(length = 100)
    private String causeLink; // 사유 관련 정보

    @Column(columnDefinition = "mediumtext")
    private String extra; // 정책 (offset, range, stratum, expertise, day) 필드 json

    // 연관관계 객체 필드
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "groupIdx", referencedColumnName = "groupIdx", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "fk_MealPointLog_MealGroup1"))
    private MealGroup mealGroup;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "policyIdx", referencedColumnName = "policyIdx", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "fk_MealPointLog_MealPolicy1"))
    private MealPolicy mealPolicy;
}
