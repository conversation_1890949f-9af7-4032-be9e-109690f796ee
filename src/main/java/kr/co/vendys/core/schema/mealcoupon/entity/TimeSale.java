package kr.co.vendys.core.schema.mealcoupon.entity;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 타임 세일 정보
 */
@Getter
@SuperBuilder
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(name = "TimeSale")
public class TimeSale {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(columnDefinition = "bigint unsigned")
    private Long idx; // 고유 인덱스

    @Column(length = 50)
    private String name; // 세일명

    @Column
    private LocalDateTime startDate; // 타임 세일 시작 시간

    @Column
    private LocalDateTime endDate; // 타임 세일 종료 시간

    @Column(nullable = false, columnDefinition = "bit(1) default b'0'")
    private Boolean status = false; // 타임 세일 상태

    @Column(length = 40)
    private String createUser; // 생성자

    @Column(columnDefinition = "datetime default CURRENT_TIMESTAMP")
    private LocalDateTime createDate; // 생성일

    @Column(length = 40)
    private String updateUser; // 수정자

    @Column
    private LocalDateTime updateDate; // 수정일

    // 연관관계 객체 필드
    @OneToMany(mappedBy = "timeSale", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<TimeSaleMenu> timeSaleMenus = new ArrayList<>();
}
