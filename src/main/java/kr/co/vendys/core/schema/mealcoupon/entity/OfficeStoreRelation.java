package kr.co.vendys.core.schema.mealcoupon.entity;

import kr.co.vendys.core.schema.mealcoupon.enums.PriceType;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 사업장과 제휴식당 관계
 */
@Getter
@SuperBuilder
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(
    name = "OfficeStoreRelation",
    indexes = {
        @Index(name = "fk_CompanyPlacePrivilege_CompanyPlace1_idx", columnList = "officeIdx"),
        @Index(name = "fk_CompanyPlacePrivilege_Store1_idx", columnList = "storeId"),
        @Index(name = "OfficeStoreRelation_officeIdx_IDX", columnList = "officeIdx,isCoupon"),
        @Index(name = "OfficeStoreRelation_officeIdx_isActive_IDX", columnList = "officeIdx,isActive")
    }
)
@IdClass(OfficeStoreRelationId.class)
public class OfficeStoreRelation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column
    private Long relationIdx; // 관계 고유 번호

    @Id
    @Column(nullable = false)
    private Long officeIdx; // 사업장 고유 번호

    @Id
    @Column(nullable = false, length = 36, columnDefinition = "char(36)")
    private String storeId; // 제휴식당 고유 아이디

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, columnDefinition = "enum('RATE','FIXED')")
    private PriceType priceType; // 정산 유형 (RATE:정률, FIXED:정액)

    @Column(nullable = false)
    private Float salesPrice; // 사용자 결제 금액 비율

    @Column(nullable = false)
    private Float companyPrice; // 회사 정산 금액 비율

    @Column(nullable = false)
    private Float supplyPrice; // 제휴식당 정산 금액 비율

    @Column(nullable = false, columnDefinition = "bit(1) default b'0'")
    private Boolean isAllMenu = false; // 전체 메뉴 노출 권한 유무

    @Column(nullable = false, columnDefinition = "tinyint(1) default 1")
    private Boolean isCoupon = true; // 식사 가능 유무

    @Column(nullable = false, columnDefinition = "tinyint(1) default 0")
    private Boolean isBooking = false; // 예약 가능 유무

    @Column(nullable = false, columnDefinition = "tinyint(1) default 0")
    private Boolean isCustomAmount = false;

    @Column(nullable = false, columnDefinition = "bit(1) default b'0'")
    private Boolean isRecommended = false; // 추천식당 설정 여부 (1: 추천식당설정 O, 0: 추천식당설정 X)

    @Column(nullable = false)
    private Boolean isActive; // 활성화 유무

    @Column(nullable = false, columnDefinition = "tinyint default 0")
    private Byte displayOrder = 0; // (추천식당용) 정렬순서

    @Column(nullable = false)
    private LocalDateTime regDate; // 등록 시간

    @Column
    private LocalDateTime updateDate; // 수정 시간

    @Column
    private LocalDateTime isNewEndDate; // 신규식당 여부 종료 날짜

    @Column
    private LocalDateTime inactiveDate; // 비활성 시간

    @Column
    private LocalDateTime exposureStartDate; // 제휴점 노출 시작 시간

    @Column
    private LocalDateTime exposureEndDate; // 제휴점 노출 종료 시간

    @Column(nullable = false, columnDefinition = "bit(1) default b'0'")
    private Boolean isFirstConnection = false; // 최초 연결여부

    // 연관관계 객체 필드
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "officeIdx", referencedColumnName = "officeIdx", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "fk_CompanyPlacePrivilege_CompanyPlace1"))
    private Office office;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "storeId", referencedColumnName = "sid", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "fk_CompanyPlacePrivilege_Store1"))
    private Store store;
}
