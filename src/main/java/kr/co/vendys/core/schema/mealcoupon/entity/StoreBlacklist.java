package kr.co.vendys.core.schema.mealcoupon.entity;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 결제 제한 제휴식당 (블랙 리스트)
 */
@Getter
@SuperBuilder
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@IdClass(StoreBlacklistId.class)
@Table(
    name = "StoreBlacklist",
    indexes = {
        @Index(name = "fk_StoreBlacklist_MealPolicy1_idx", columnList = "policyIdx"),
        @Index(name = "fk_StoreBlacklist_Store1_idx", columnList = "storeId"),
        @Index(name = "fk_StoreBlacklist_Company1_idx", columnList = "comId")
    }
)
public class StoreBlacklist {

    @Id
    @Column(nullable = false, length = 36)
    private String comId; // 고객사 고유 아이디

    @Id
    @Column(nullable = false)
    private Long policyIdx; // 식대정책 고유번호

    @Id
    @Column(nullable = false, length = 36)
    private String storeId; // 제휴식당 고유 아이디

    @Column
    private LocalTime startTime; // 차단 시작 시간

    @Column
    private LocalTime endTime; // 차단 끝 시간

    @Column(nullable = false)
    private LocalDateTime regDate; // 등록시간

    // 연관관계 객체 필드
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "comId", referencedColumnName = "comid", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "fk_StoreBlacklist_Company1"))
    private Company company;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "policyIdx", referencedColumnName = "policyIdx", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "fk_StoreBlacklist_MealPolicy1"))
    private MealPolicy mealPolicy;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "storeId", referencedColumnName = "sid", insertable = false, updatable = false, foreignKey = @ForeignKey(name = "fk_StoreBlacklist_Store1"))
    private Store store;
}
