package kr.co.vendys.core.schema.mealcoupon.entity;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 고객사 정보
 */
@Getter
@SuperBuilder
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(name = "Company")
public class Company {

    @Id
    @Column(name = "comid", length = 50, columnDefinition = "char(50)")
    private String comId; // 고객사 고유 아이디
    
    @Column(nullable = false, length = 50, columnDefinition = "char(50)")
    private String name; // 고객사명
    
    @Column(length = 30, columnDefinition = "char(30)")
    private String icon; // 아이콘 파일 이름
    
    @Column(nullable = false, length = 255, columnDefinition = "char(255)")
    private String bizname; // 사업자명
    
    @Column(nullable = false, length = 30, columnDefinition = "char(30)")
    private String bizserial; // 사업자 번호
    
    @Column(length = 140, columnDefinition = "char(140)")
    private String address; // 주소
    
    @Column(length = 200)
    private String addressRoad; // 도로명 주소
    
    @Column(length = 200)
    private String addressJibun; // 지번 주소
    
    @Column(length = 200)
    private String addressZipCode; // 우편 번호
    
    @Column(length = 200)
    private String addressDetail; // 상세 주소
    
    @Column(length = 200)
    private String addressSido; // 시,도
    
    @Column(length = 200)
    private String addressSigugun; // 시,군,구
    
    @Column(length = 200)
    private String addressDongmyun; // 동면
    
    @Column(nullable = false, length = 20, columnDefinition = "char(20)")
    private String region; // 지역
    
    @Column(length = 50)
    private String chargename; // 담당자
    
    @Column(length = 20, columnDefinition = "char(20)")
    private String phone; // 연락처
    
    @Column(length = 10, columnDefinition = "char(10)")
    private String bankname; // deprecated
    
    @Column(length = 20, columnDefinition = "char(20)")
    private String bankaccount; // deprecated
    
    @Column(length = 30, columnDefinition = "char(30)")
    private String bankowner; // deprecated
    
    @Column(columnDefinition = "text")
    private String intro; // 설명
    
    @Column(nullable = false, columnDefinition = "bigint default 0")
    private Long cash = 0L; // deprecated
    
    @Column(nullable = false, columnDefinition = "bigint default 0")
    private Long trustcash = 0L; // deprecated
    
    @Column(nullable = false, columnDefinition = "int default 0")
    private Integer employnum = 0; // 임직원 수
    
    @Column(nullable = false, columnDefinition = "bigint default 0")
    private Long duesmonth = 0L; // deprecated
    
    @Column(nullable = false)
    private LocalDateTime regdate; // 등록 시간
    
    @Column(nullable = false, columnDefinition = "tinyint(1) default 0")
    private Boolean paymulti = false; // deprecated
    
    @Column(nullable = false, columnDefinition = "tinyint default 0")
    private Byte status = 0; // 활성화 여부
    
    @Column(columnDefinition = "text")
    private String salesContents; // 영업노트
    
    @Column(length = 50, columnDefinition = "char(50)")
    private String calculatedate; // deprecated
    
    @Column(length = 10)
    private String depositmethod; // 정산방식 (우리은행, 나이스)
    
    @Column(length = 100)
    private String benepiaComId; // 베니피아 고유 아이디(deprecated)
    
    @Column(length = 2)
    private String version; // 고객사 버전(null: 기존 고객사, v1: 그룹사 기능 고객사)
    
    @Column(nullable = false, columnDefinition = "tinyint(1) default 0")
    private Boolean isTest = false; // 테스트 고객사 유무
    
    @Column(length = 200)
    private String companyCi; // 고객사 CI 이미지
    
    @Column(nullable = false, columnDefinition = "tinyint default 0")
    private Byte storeConfirm = 0; // 제휴점 확인여부(1:필요, 0:불필요)
    
    @OneToOne(mappedBy = "company", fetch = FetchType.LAZY)
    private CompanyLimit companyLimit;
}
