package kr.co.vendys.core.schema.mealcoupon.enums.converter;

import kr.co.vendys.core.domain.quick.dto.KakaoTModel;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = false)
public class OrderTypeConverter implements AttributeConverter<KakaoTModel.OrderType, String> {
    @Override
    public String convertToDatabaseColumn(KakaoTModel.OrderType attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.name();
    }

    @Override
    public KakaoTModel.OrderType convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        try {
            return KakaoTModel.OrderType.valueOf(dbData);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}
