package kr.co.vendys.core.schema.mealcoupon.enums.converter;

import kr.co.vendys.core.schema.mealcoupon.enums.QuickStatus;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = false)
public class QuickStatusConverter implements AttributeConverter<QuickStatus, String> {
    @Override
    public String convertToDatabaseColumn(QuickStatus attribute) {
        if (attribute == null) {
            return null;
        }
        return attribute.name();
    }

    @Override
    public QuickStatus convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        try {
            return QuickStatus.valueOf(dbData);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}
