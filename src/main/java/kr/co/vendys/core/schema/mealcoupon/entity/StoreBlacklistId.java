package kr.co.vendys.core.schema.mealcoupon.entity;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Getter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@EqualsAndHashCode
public class StoreBlacklistId implements Serializable {

    private static final long serialVersionUID = 1L;

    private String comId;
    private Long policyIdx;
    private String storeId;
}
