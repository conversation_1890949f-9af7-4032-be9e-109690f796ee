package kr.co.vendys.storeapi.dto.robot;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

public class RobotDeliveryDto {


    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RobotDeliverySpotResponse {
        public List<RobotDeliverySpotDto> spots;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RobotDeliveryOrderResponse {
        private Integer confirmOrderCount;
        private Integer canceledOrderCount;

        private List<RobotDeliveryOrderWithCouponsDTO> confirmOrders;
        private List<RobotDeliveryOrderWithCouponsDTO> canceledOrders;

    }

    @Data
    public static class RobotDeliveryOrderDetailResponse {
        private PaymentInfo paymentInfo;
        private DeliveryInfo deliveryInfo;
        private OrderSummary orderSummary;
        private List<OrderMenu> orderMenus;

        @Data
        public static class PaymentInfo {
            private Long payRoomIdx;
            private String status;
            private String statusName;
            private String buyer;
            private String useType;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING)
            private Date useDate;
            private String companyName;
        }

        @Data
        public static class DeliveryInfo {
            private String receiverName;
            private String receiverPhone;
            private String deliverySpotName;
            private String deliveryMemo;
        }

        @Data
        public static class OrderSummary {
            private Integer totalOrderCount;
            private Integer totalOrderAmount;
            private Long approvalNo;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING, timezone = "Asia/Seoul")
            private Date approvalDate;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING, timezone = "Asia/Seoul")
            private Date canceledDate;

            private String deliveryRobotName;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING, timezone = "Asia/Seoul")
            private Date deliveryStartTime;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING, timezone = "Asia/Seoul")
            private Date deliveryEndTime;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING, timezone = "Asia/Seoul")
            private Date pickedUpTime;
        }

        @Data
        public static class OrderMenu {
            private String menuName;
            private Integer count;
            private Integer price;
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RobotDeliverySpotDto {
        private Long spotIdx;
        private String spotName;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RobotDeliveryOrderWithCouponsDTO {
        private Long payRoomIdx;
        private String couponId;
        private Long robotDeliverySpotIdx;
        private String spotName;
        private String receiverName;
        private String receiverPhone;
        private String deliveryMemo;
        private Integer deliveryFee;
        private String status;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING)
        private LocalDateTime createDate;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING)
        private LocalDateTime canceledDate;

        private List<CouponMenuDto> menus;

        public RobotDeliveryOrderWithCouponsDTO(String couponId,
                                                Long robotDeliverySpotIdx, String spotName, String receiverName,
                                                String receiverPhone, String deliveryMemo, Integer deliveryFee,
                                                String status, LocalDateTime createDate) {
            this.couponId = couponId;
            this.robotDeliverySpotIdx = robotDeliverySpotIdx;
            this.spotName = spotName;
            this.receiverName = receiverName;
            this.receiverPhone = receiverPhone;
            this.deliveryMemo = deliveryMemo;
            this.deliveryFee = deliveryFee;
            this.status = status;
            this.createDate = createDate;
            this.menus = new ArrayList<>(); // 기본값 설정
        }

        @Getter
        @Setter
        @AllArgsConstructor
        @NoArgsConstructor
        public static class CouponMenuDto {
            private String cid;
            private String menuName;
            private Integer count;
            private Integer price;
        }
    }

    @Getter
    public static class AggregateOrderRequest {
        @NotEmpty
        private String scheduleId;
        @NotEmpty
        private Set<String> couponIds;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @Builder
    public static class AggregatedOrderResponse {
        private List<AggregatedOrder> orders;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AggregatedOrder {
        private Long robotDeliveryIdx;
        private String storeName;
        private Long spotIdx;
        private String spotName;
        private String deliveryRobot;
        private Integer totalCount;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING)
        private LocalDateTime aggregatedDate;
        private String deliveryStatus;
        @JsonFormat(pattern = "HH:mm:ss", shape = JsonFormat.Shape.STRING)
        private LocalTime deliveryStartTime;
        @JsonFormat(pattern = "HH:mm:ss", shape = JsonFormat.Shape.STRING)
        private LocalTime expectedDeliveryTime;
        @JsonFormat(pattern = "HH:mm:ss", shape = JsonFormat.Shape.STRING)
        private LocalTime deliveryEndTime;

        private List<AggregatedOrderSummary> summaries;


        @Getter
        @Setter
        @AllArgsConstructor
        @Builder
        public static class AggregatedOrderSummary {
            private String couponId;
            private String orderStatus;
            private String orderStatusName;
            private Long payRoomIdx;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING)
            private LocalDateTime confirmDate;
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING)
            private LocalDateTime canceledDate;
            private Long spotIdx;
            private String spotName;
            private Integer totalOrderCount;
            private String receiverName;
            private String deliveryMemo;
            private List<RobotDeliveryOrderDetailResponse.OrderMenu> menus;
        }

    }

    @Getter
    @Setter
    @AllArgsConstructor
    @Builder
    public static class RobotDeliveryScheduleResponse {

        private List<RobotDeliveryScheduleDto> schedules;

        @Getter
        @Setter
        @NoArgsConstructor
        @AllArgsConstructor
        @Builder
        public static class RobotDeliveryScheduleDto {
            private String scheduleId;
            private String robotName;
            @JsonFormat(pattern = "HH:mm:ss", shape = JsonFormat.Shape.STRING)
            private LocalTime deliveryStartTime;
            @JsonFormat(pattern = "HH:mm:ss", shape = JsonFormat.Shape.STRING)
            private LocalTime deliveryEndTime;
            @JsonFormat(pattern = "HH:mm:ss", shape = JsonFormat.Shape.STRING)
            private LocalTime deliveryReturnTime;

        }
    }

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class RobotDeliveryStoreSettingDto {
        private String deliveryInfo;
        private Integer maxDeliveryOrderCount;
        private Boolean isOpened;
        private Boolean isLabelPrintAvailable;
    }

    @Getter
    @Setter
    public static class RobotDeliveryOrderCancelRequest {
        @NotBlank(message = "쿠폰번호는 필수 입니다.")
        private String couponId;

        @NotBlank(message = "취소 사유는 필수 입니다.")
        private String cancelMessage;
    }
}
