package kr.co.vendys.storeapi.persist.robot;

import kr.co.vendys.storeapi.entity.robot.StoreRobotDeliverySpotRelation;
import kr.co.vendys.storeapi.repository.master.robot.StoreRobotDeliverySpotRelationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class StoreRobotDeliverySpotRelationPersist {
    private final StoreRobotDeliverySpotRelationRepository storeRobotDeliverySpotRelationRepository;

    public StoreRobotDeliverySpotRelation findByStoreIdAndRobotDeliverySpotIdxJoinFetch(String storeId, Long robotDeliverySpotIdx) {
        return storeRobotDeliverySpotRelationRepository.findByStoreIdAndRobotDeliverySpotIdxJoinFetch(storeId, robotDeliverySpotIdx);
    }
}
