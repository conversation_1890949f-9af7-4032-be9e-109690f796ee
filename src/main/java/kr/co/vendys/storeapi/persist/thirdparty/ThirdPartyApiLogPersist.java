package kr.co.vendys.storeapi.persist.thirdparty;

import java.time.LocalDateTime;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import kr.co.vendys.storeapi.entity.thirdparty.ThirdPartyApiRequestLog;
import kr.co.vendys.storeapi.entity.thirdparty.ThirdPartyApiRequestLog.ApiOriginType;
import kr.co.vendys.storeapi.entity.thirdparty.ThirdPartyApiRequestLog.ApiTargetType;
import kr.co.vendys.storeapi.entity.thirdparty.ThirdPartyApiResponseLog;
import kr.co.vendys.storeapi.repository.master.thirdparty.ThirdPartyApiRequestLogRepository;
import kr.co.vendys.storeapi.repository.master.thirdparty.ThirdPartyApiResponseLogRepository;
import lombok.RequiredArgsConstructor;

@Service
@Transactional
@RequiredArgsConstructor
public class ThirdPartyApiLogPersist {

    private final ThirdPartyApiRequestLogRepository thirdPartyApiRequestLogRepository;
    private final ThirdPartyApiResponseLogRepository thirdPartyApiResponseLogRepository;

    public ThirdPartyApiRequestLog saveThirdPartyApiRequestLog(ThirdPartyApiRequestLog thirdPartyApiRequestLog) {
        return this.thirdPartyApiRequestLogRepository.save(thirdPartyApiRequestLog);
    }

    public void saveThirdPartyApiResponseLog(ThirdPartyApiResponseLog thirdPartyApiResponseLog) {
        this.thirdPartyApiResponseLogRepository.save(thirdPartyApiResponseLog);
    }
}
