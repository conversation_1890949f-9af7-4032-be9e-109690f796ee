package kr.co.vendys.storeapi.service.store;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import kr.co.vendys.storeapi.constant.Errors;
import kr.co.vendys.storeapi.constant.InitProperties;
import kr.co.vendys.storeapi.dto.MessageDto;
import kr.co.vendys.storeapi.dto.MessageDto.SmsResponse;
import kr.co.vendys.storeapi.dto.MessageDto.SmsUseType;
import kr.co.vendys.storeapi.dto.store.StoreDto.DeleteWorkerRequest;
import kr.co.vendys.storeapi.dto.store.StoreDto.EmployeeApplyListResponse;
import kr.co.vendys.storeapi.dto.store.StoreDto.InviteWorkerRequest;
import kr.co.vendys.storeapi.dto.store.StoreDto.UpdateEmployeeApplyRequest;
import kr.co.vendys.storeapi.dto.store.StoreDto.UpdateEmployeeApplyRequest.EmployeeApply.UpdateApplyStatus;
import kr.co.vendys.storeapi.dto.store.StoreDto.UpdateWorkerRequest;
import kr.co.vendys.storeapi.dto.store.StoreDto.UpdateWorkerRequest.UpdateWorkerStatus;
import kr.co.vendys.storeapi.dto.store.StoreDto.WorkerListResponse;
import kr.co.vendys.storeapi.dto.store.StoreDto.WorkerListResponse.EmployeeApply;
import kr.co.vendys.storeapi.dto.store.StoreSettlementDto.StoreSettlementResponse;
import kr.co.vendys.storeapi.entity.Store;
import kr.co.vendys.storeapi.entity.StoreAccount;
import kr.co.vendys.storeapi.entity.StoreAccountPrivilege;
import kr.co.vendys.storeapi.entity.StoreAccountPrivilege.StoreAccountPrivilegeGrade;
import kr.co.vendys.storeapi.entity.StoreEmployeeApply;
import kr.co.vendys.storeapi.entity.StoreEmployeeApply.EmployeeApplyGrade;
import kr.co.vendys.storeapi.entity.StoreEmployeeApply.EmployeeApplyStatus;
import kr.co.vendys.storeapi.exception.CommonException;
import kr.co.vendys.storeapi.persist.StoreAccountPersist;
import kr.co.vendys.storeapi.persist.StoreAccountPrivilegePersist;
import kr.co.vendys.storeapi.persist.StoreEmployeeApplyPersist;
import kr.co.vendys.storeapi.persist.StorePersist;
import kr.co.vendys.storeapi.service.MessageService;
import kr.co.vendys.storeapi.service.settlement.SettlementService;
import kr.co.vendys.storeapi.util.DateUtil;
import kr.co.vendys.storeapi.vo.AccessTokenInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class WorkerService {

    private final InitProperties initProperties;
    private final StoreEmployeeApplyPersist employeeApplyPersist;
    private final StoreAccountPrivilegePersist privilegePersist;
    private final StoreAccountPersist accountPersist;
    private final MessageService messageService;
    private final StorePersist storePersist;
    private final SettlementService settlementService;

    /**
     * 제휴식당 권한 체크
     */
    private void checkStoreGrade(String said, String storeId) {
        StoreAccountPrivilege account = this.privilegePersist.readBySaidAndStoreIdAndStatus(said, storeId, true);
        if (ObjectUtils.isEmpty(account) || StoreAccountPrivilegeGrade.STAFF.equals(account.getGrade())) {
            throw new CommonException(Errors.STORE_ACCOUNT_PERMISSION_ERROR);
        }
    }

    /**
     * 직원 신청 목록 (사장님)
     */
    public WorkerListResponse getWorkerList(AccessTokenInfo accessTokenInfo, String storeId) {

        // 권한 체크
        this.checkStoreGrade(accessTokenInfo.getSaid(), storeId);

        List<StoreEmployeeApply> employeeList = this.employeeApplyPersist.readByStoreId(storeId);
        List<EmployeeApply> workers = new ArrayList<>();
        for (StoreEmployeeApply data : employeeList) {
            StoreAccount storeAccount = null;
            if (!data.getStatus().equals(EmployeeApplyStatus.PREPARATION)) {
                storeAccount = this.accountPersist.readByCellPhone(data.getPhone(), true);
            }

            EmployeeApply worker = new EmployeeApply();
            worker.setEmployeeApplyIdx(data.getIdx());
            worker.setName(ObjectUtils.isEmpty(storeAccount) ? data.getName() : storeAccount.getName());
            worker.setCellPhone(data.getPhone());
            worker.setInviteDate(DateUtil.setDateByLocalDateTime(data.getCreateDate()).getTime());
            if (!ObjectUtils.isEmpty(data.getApproveDate())) {
                worker.setApproveDate(DateUtil.setDateByLocalDateTime(data.getApproveDate()).getTime());
            }
            worker.setStatus(data.getStatus());
            workers.add(worker);
        }

        return WorkerListResponse.from(workers);
    }

    /**
     * 직원 신청 목록 (직원)
     */
    public EmployeeApplyListResponse getEmployeeApplyList(AccessTokenInfo accessTokenInfo) {
        return EmployeeApplyListResponse.from(this.employeeApplyPersist.readByPhone(accessTokenInfo.getCellPhone()));
    }

    /**
     * 직원 요청 수락 및 거절 (직원)
     */
    public void updateEmployeeApplyList(AccessTokenInfo accessTokenInfo, UpdateEmployeeApplyRequest body) {
        List<Long> idxList = body.getEmployeeApplyList().stream()
                .map(UpdateEmployeeApplyRequest.EmployeeApply::getEmployeeApplyIdx)
                .collect(Collectors.toList());

        List<StoreEmployeeApply> applyList = this.employeeApplyPersist.readByIdxList(idxList);
        for (StoreEmployeeApply apply : applyList) {
            // 본인 정보 인지 체크
            if (!apply.getPhone().equals(accessTokenInfo.getCellPhone())) {
                throw new CommonException(Errors.EMPLOYEE_APPLY_NOT_FOUND);
            }
            // 신청 정보 상태 체크
            if (!EmployeeApplyStatus.PREPARATION.equals(apply.getStatus())) {
                throw new CommonException(Errors.EMPLOYEE_APPLY_INFO_ERROR);
            }

            // 신청 정보 업데이트
            UpdateEmployeeApplyRequest.EmployeeApply req = body.getEmployeeApplyList().stream()
                    .filter(data -> data.getEmployeeApplyIdx().equals(apply.getIdx()))
                    .findFirst().get();
            if (UpdateApplyStatus.WORK.equals(req.getStatus())) {
                apply.updateStatus(EmployeeApplyStatus.WORK, accessTokenInfo.getSaid());

                // 직원 연결
                StoreAccountPrivilegeGrade grade =
                        apply.getGrade().equals(EmployeeApplyGrade.MANAGER) ? StoreAccountPrivilegeGrade.MANAGER :
                                StoreAccountPrivilegeGrade.STAFF;
                this.makeAccountPrivilege(accessTokenInfo.getSaid(), apply.getStoreId(), grade);
            } else {
                apply.updateStatus(EmployeeApplyStatus.REFUSE, accessTokenInfo.getSaid());
            }
        }

        this.employeeApplyPersist.saveAll(applyList);
    }

    /**
     * 직원 수락시 계정 , 제휴점 연결 정보 생성 및 저장
     */
    private void makeAccountPrivilege(String said, String storeId, StoreAccountPrivilegeGrade grade) {
        this.privilegePersist.save(StoreAccountPrivilege.builder()
                .said(said)
                .sid(storeId)
                .grade(grade)
                .status(true)
                .createUser(said)
                .updateUser(said)
                .build());
    }

    /**
     * 직원 신청 로직
     */
    public void inviteWorker(AccessTokenInfo accessTokenInfo, String storeId, InviteWorkerRequest body) {
        // 권한 체크
        this.checkStoreGrade(accessTokenInfo.getSaid(), storeId);

        // 상위 계정 체크
        if (!accessTokenInfo.isAccount()) {
            throw new CommonException(Errors.EMPLOYEE_APPLY_BELOW_STANDARD);
        }

        // 파트너사 정산 정보 및 계좌 정보 체크
        StoreSettlementResponse storeSettleInfo = this.settlementService.getStoreMetaInfo(accessTokenInfo.getSaid(), storeId);
        if (!storeSettleInfo.checkSettleInfo()) {
            throw new CommonException(Errors.EMPLOYEE_APPLY_BELOW_STANDARD);
        }

        // 중복 신청 체크
        List<StoreEmployeeApply> checkDb = this.employeeApplyPersist.readByStoreIdAndPhone(storeId, body.getPhone());
        checkDb.forEach(employee -> {
            if (!EmployeeApplyStatus.CANCEL.equals(employee.getStatus())
                    && !EmployeeApplyStatus.REFUSE.equals(employee.getStatus())) {
                throw new CommonException(Errors.ALREADY_APPLY_EMPLOYEE);
            }
        });

        // 이미 직원인지 체크
        StoreAccount storeAccount = this.accountPersist.readByCellPhone(body.getPhone(), true);
        if (!ObjectUtils.isEmpty(storeAccount)) {
            StoreAccountPrivilege checkPrivilege = this.privilegePersist.readBySaidAndStoreId(storeAccount.getSaid(), storeId);
            if (!ObjectUtils.isEmpty(checkPrivilege)) {
                throw new CommonException(Errors.ALREADY_APPLY_EMPLOYEE);
            }
        }

        // 직원 신청 정보 생성 및 저장
        StoreEmployeeApply apply = StoreEmployeeApply.builder()
                .said(accessTokenInfo.getSaid())
                .storeId(storeId)
                .name(body.getName())
                .phone(body.getPhone())
                .grade(body.getGrade())
                .status(EmployeeApplyStatus.PREPARATION)
                .expireDate(LocalDateTime.now().plusDays(10))
                .createUser(accessTokenInfo.getSaid())
                .updateUser(accessTokenInfo.getSaid())
                .build();
        this.employeeApplyPersist.save(apply);

        // 문자 발송
        String content;
        Store store = this.storePersist.readBySid(storeId);
        content = store.getName() + "에서 " + body.getPhone() + " " + body.getName() + "님께 식권대장 사장님앱 직원 요청을 하셨습니다.\n";
        content += "직원 등록을 수락하시려면 하단 링크를 클릭해 로그인 하시거나,";
        content += " 아직 회원 가입을 하지 않으셨다면 회원 가입 후 로그인 해주세요.";
        content += "회원 가입을 하지 않으면 개인정보 보호 정책에 의해 10일 후 자동으로 요청이 취소됩니다.\n\n";
        content += "식권대장 사장님앱은 취소된 요청에 대해 책임지지 않습니다.\n\n";

        if ("production".equals(this.initProperties.getServerEnv())) {
            content += "https://sikd.ae/storeowner";
        } else {
            content += "https://sikd.ae/storeowner";
        }

        this.sendWorkerSms(body.getPhone(), content);
    }

    /**
     * 직원 상태 업데이트 (사장님)
     */
    public void updateWorkerStatus(AccessTokenInfo accessTokenInfo, String storeId, UpdateWorkerRequest body) {
        // 권한 체크
        this.checkStoreGrade(accessTokenInfo.getSaid(), storeId);

        // 직원 신청 상태 업데이트
        StoreEmployeeApply apply = this.employeeApplyPersist.readByIdx(body.getEmployeeApplyIdx());
        if (UpdateWorkerStatus.CANCEL.equals(body.getStatus()) && EmployeeApplyStatus.PREPARATION.equals(apply.getStatus())) {
            apply.updateStatus(EmployeeApplyStatus.CANCEL, accessTokenInfo.getSaid());
            this.employeeApplyPersist.save(apply);
        } else if (UpdateWorkerStatus.REST.equals(body.getStatus()) && EmployeeApplyStatus.WORK.equals(apply.getStatus())) {
            apply.updateStatus(EmployeeApplyStatus.REST, accessTokenInfo.getSaid());
            this.checkAndUpdatePrivilegeStatus(apply.getPhone(), apply.getStoreId(), accessTokenInfo.getSaid());
            this.employeeApplyPersist.save(apply);
        } else if (UpdateWorkerStatus.DELETE.equals(body.getStatus())) { // 별도 히스토리 처리 안하기로함
            if (EmployeeApplyStatus.REFUSE.equals(apply.getStatus()) && EmployeeApplyStatus.CANCEL.equals(apply.getStatus())) {
                this.employeeApplyPersist.delete(apply);
            } else if (EmployeeApplyStatus.WORK.equals(apply.getStatus())) {
                this.checkAndDeletePrivilege(apply.getPhone(), apply.getStoreId());
                this.employeeApplyPersist.delete(apply);
            } else {
                throw new CommonException(Errors.EMPLOYEE_APPLY_INFO_ERROR);
            }
        } else {
            throw new CommonException(Errors.EMPLOYEE_APPLY_INFO_ERROR);
        }
    }


    /**
     * 만료 기간이 경과된 직원 신청 목록 삭제
     */
    public void expireWorkerDelete() {

        // 만료된 신청 정보를 가져와서 삭제
        List<StoreEmployeeApply> apply = this.employeeApplyPersist.readByExpireEmployeeApply();
        if (!ObjectUtils.isEmpty(apply)) {
            this.employeeApplyPersist.delete(apply);
        }
    }

    private void checkAndUpdatePrivilegeStatus(String phone, String storeId, String said) {
        StoreAccount account = this.accountPersist.readByCellPhone(phone, true);
        if (ObjectUtils.isEmpty(account) || ObjectUtils.isEmpty(account.getPrivilegeList())) {
            throw new CommonException(Errors.PRIVILEGE_INFO_NOT_FOUND);
        }

        StoreAccountPrivilege privilege = account.getPrivilegeList().stream()
                .filter(data -> data.getSid().equals(storeId))
                .findFirst().orElse(StoreAccountPrivilege.builder().build());
        if (ObjectUtils.isEmpty(privilege.getSaid())) {
            throw new CommonException(Errors.PRIVILEGE_INFO_NOT_FOUND);
        }

        privilege.updateStatus(false, said);
        this.privilegePersist.save(privilege);
    }

    /**
     * 제휴점 연결 정보 체크 후 삭제
     */
    private void checkAndDeletePrivilege(String phone, String storeId) {
        StoreAccount account = this.accountPersist.readByCellPhone(phone, true);
        if (ObjectUtils.isEmpty(account) || ObjectUtils.isEmpty(account.getPrivilegeList())) {
            throw new CommonException(Errors.PRIVILEGE_INFO_NOT_FOUND);
        }
        StoreAccountPrivilege privilege = account.getPrivilegeList().stream()
                .filter(data -> data.getSid().equals(storeId))
                .findFirst()
                .orElse(StoreAccountPrivilege.builder().build());
        if (ObjectUtils.isEmpty(privilege.getSaid())) {
            throw new CommonException(Errors.PRIVILEGE_INFO_NOT_FOUND);
        }

        this.privilegePersist.delete(privilege);
    }

    /**
     * 직원 신청 완료 문자 전송
     */
    private void sendWorkerSms(String cellphone, String content) {
        MessageDto.SmsRequest smsRequest = new MessageDto.SmsRequest();
        smsRequest.setTitle("직원 신청 알림");
        smsRequest.setContent(content);
        smsRequest.setUseType(SmsUseType.AS.name());
        smsRequest.setReceiver(cellphone);
        smsRequest.setType("lms");
        smsRequest.setUserId(this.initProperties.getMessageClient());
        smsRequest.setUsecause("직원 신청 알림");
        SmsResponse smsResponse = this.messageService.sendSms(smsRequest);

        if (ObjectUtils.isEmpty(smsResponse) || smsResponse.getResult() != 1) {
            log.error("{}", Errors.WORKER_SMS_SEND_FAIL);
        }
    }
}
