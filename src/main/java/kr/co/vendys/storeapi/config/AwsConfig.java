package kr.co.vendys.storeapi.config;

import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import kr.co.vendys.storeapi.constant.InitProperties;
import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor
public class AwsConfig {

    private final InitProperties initProperties;

    @Bean
    public AmazonS3 initializeAmazon() {
        if ("production".equals(this.initProperties.getServerEnv())) {
            return AmazonS3ClientBuilder.standard()
                    .withRegion(Regions.fromName(this.initProperties.getAwsRegion()))
                    .build();
        } else {
            AWSCredentials credentials = this.getAwsCredentials();
            return AmazonS3ClientBuilder.standard()
                    .withRegion(Regions.fromName(this.initProperties.getAwsRegion()))
                    .withCredentials(new AWSStaticCredentialsProvider(credentials))
                    .build();
        }
    }

    @Bean
    public AmazonSQS initializeAmazonSqs() {

        if ("production".equals(this.initProperties.getServerEnv())) {
            return AmazonSQSClientBuilder.standard()
                    .withRegion(Regions.fromName(this.initProperties.getAwsRegion()))
                    .build();
        } else {
            AWSCredentials credentials = this.getAwsCredentials();
            return AmazonSQSClientBuilder.standard()
                    .withRegion(Regions.fromName(this.initProperties.getAwsRegion()))
                    .withCredentials(new AWSStaticCredentialsProvider(credentials))
                    .build();
        }
    }

    private AWSCredentials getAwsCredentials() {
        return new BasicAWSCredentials(this.initProperties.getAwsAccessKey(), this.initProperties.getAwsSecretKey());
    }

    @Bean
    public TransferManager initS3TransferManger(AmazonS3 s3Client){
        return TransferManagerBuilder.standard().withS3Client(s3Client).build();
    }
}
