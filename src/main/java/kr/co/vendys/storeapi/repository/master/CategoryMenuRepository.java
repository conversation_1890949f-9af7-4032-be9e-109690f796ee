package kr.co.vendys.storeapi.repository.master;

import kr.co.vendys.storeapi.entity.CategoryMenu;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface CategoryMenuRepository extends JpaRepository<CategoryMenu, Long> {

    List<CategoryMenu> findByMCateIdIn(@Param("mCateIds") List<Long> mCateIds);

    @Query(
            "select max(category.priority)" +
            "from CategoryMenu category " +
            "where 1=1 " +
            "and category.sid = :storeId "
    )
    Integer findMaxPriorityByStoreId(@Param("storeId") String storeId);

}
