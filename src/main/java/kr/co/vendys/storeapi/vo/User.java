package kr.co.vendys.storeapi.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Getter
@Setter
@ToString
public class User {
    private String uid;
    private String name;
    private String comid;
    private String comname;
    private Byte level;
    private String position;
    private String rankposition;
    private String division;
    private String cellphone;
    private String email;
    private String password;
    private String comidnum;
    private Boolean sex;
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date birthday;
    private Date joindate;
    private Byte ostype;
    private String appversion;
    private String pushtoken;
    private String accesstoken;
    private Integer mealpoint;
    private Date mpdepositdate;
    private Integer mypoint;
    private String icon;
    private Integer compointright;
    private String passwordreset;
    private Date longdepositdate;
	private String benepiaUserId;
	private String cashReceiptInfo;
    //회원가입 관련 작업 추가 jungsu on 20160923
    private String signId;      //회원가입 ID
    private String migVersion;  //회원가입 여부
    private boolean emailAuth;  //이메일 인증 여부
    //부서정보 jungsu on 2016. 10. 17..
    private Integer divisionIdx;
    //약관정보 jungsu on 2016. 10. 27..
    private Boolean isAgree;
}