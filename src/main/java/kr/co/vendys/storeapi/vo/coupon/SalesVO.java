package kr.co.vendys.storeapi.vo.coupon;

import lombok.Data;

/**
 * Created by shoki on 2016-06-20.
 */
@Data
public class SalesVO {
    private int page;
    private int pageRow;

    private String comid;
    private String date;
    private String endDate;
    private Boolean calcFlag;
    private String couponType;
    private String venderType;
    private int status;

    private long payRoomIdx;
    private String cid;
    private String comname;
    private String leaderid;
    private String leadername;
    private String sid;
    private String storename;
    private String menuname;
    private int tsupplyprice;
    private int tprice;
    private int tsalesPrice;
    private int member;
    private int menunum;
    private String coupontype;
    private String vendertype;
    private long usedate;
    private String calcid;
    private String cancelcause;
    private long canceldate;
    private long capturedate;
    private Boolean isCanceled;

    public int getOffset() {
        int offset = 0;
        if(page > 1) {
            offset = (page - 1) * pageRow;
        }
        return offset;
    }
}
