package kr.co.vendys.storeapi.util;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.springframework.web.bind.annotation.RequestMethod;

import kr.co.vendys.storeapi.constant.Errors;
import kr.co.vendys.storeapi.exception.VlocallyException;
import org.apache.commons.httpclient.Header;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpMethodBase;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.DeleteMethod;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.HeadMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.PutMethod;
import org.apache.commons.httpclient.params.HttpClientParams;


/**
 * Created by ttobii on 2015. 7. 17..
 */
public class UcloudStorageUtil {

    private static int TIME_OUT = 30000;
    private static String CONTENT_CHARSET = "UTF-8";
    private HttpClientParams params;
    private String auth_token = null;
    private String storage_path = null;
    private Header header_auth = null;
    private final String account_id;
    private final String account_key;
    private final String filebox;

    /**
     *
     * @param filebox - 식권대장 Sikdae
     * @param id - ucloud id
     * @param key - ucloud key
     */
    public UcloudStorageUtil(String filebox, String id, String key){
        account_id = id;
        account_key = key;
        this.filebox = filebox;
        params = new HttpClientParams();
        params.setConnectionManagerTimeout(TIME_OUT);
        params.setContentCharset(CONTENT_CHARSET);
    }

    /**
     * 해당 파일 받기
     * @param path 파일 경로 filebox/{path}/{filename}
     * @param filename 파일명
     * @return 파일 바이트 어레이 전송 / 실패시 null 전송
     * @throws VlocallyException
     * @throws IOException
     */
    public byte[] getFile(String path, String filename) throws VlocallyException, IOException {
        for(int i=0;i<2;i++) {
            if (auth_token == null) {
                if (!getAuth()) {
                    throw new VlocallyException(Errors.UCLOUD_STORAGE_AUTH_FAIL);
                }
            }
            String url = storage_path + "/" + path + "/" + filename;

            ArrayList<Header> headers = new ArrayList<Header>(1);
            headers.add(header_auth);

            HttpMethodBase method = getMethod(RequestMethod.GET, url, headers, null, null);
            byte[] body = executeForStream(method);
            switch (method.getStatusCode()) {
                case 200:
                    return body;
                case 400:
                    throw new VlocallyException(Errors.UCLOUD_STORAGE_BAD_REQUEST);
                case 401:
                    auth_token = null;
                    if(i!=0){
                        throw new VlocallyException(Errors.UCLOUD_STORAGE_AUTH_FAIL);
                    }
                    break;
                case 404:
                    throw new VlocallyException(Errors.UCLOUD_STORAGE_NOT_FOUND);
                default:
                    throw new VlocallyException(Errors.UCLOUD_STORAGE_RESPONSE_ERROR.getCode(), method.getStatusCode() + "");
            }
        }
        throw new VlocallyException(Errors.UCLOUD_STORAGE_AUTH_FAIL);
    }

    public HttpMethodBase getMethod(RequestMethod _method, String url, List<Header> headers, NameValuePair[] url_params, NameValuePair[] body_params) {
        HttpMethodBase method;

        switch(_method){
            case POST:
                method = new PostMethod(url);
                if(body_params!=null){
                    ((PostMethod)method).setRequestBody(body_params);
                }
                break;
            case DELETE:
                method = new DeleteMethod(url);
                if(url_params!=null){
                    method.setQueryString(url_params);
                }
                break;
            case PUT:
                method = new PutMethod(url);
                if(url_params!=null){
                    method.setQueryString(url_params);
                }
                break;
            case HEAD:
                method = new HeadMethod(url);
                if(url_params!=null){
                    method.setQueryString(url_params);
                }
                break;
            case GET:
            case OPTIONS:
            case PATCH:
            case TRACE:
            default:
                method = new GetMethod(url);
                if(url_params!=null){
                    method.setQueryString(url_params);
                }
                break;
        }
        if(headers!=null){
            for(Header header : headers){
                method.addRequestHeader(header);
            }
        }
        return method;
    }

    private boolean getAuth() {
        HttpClient client = new HttpClient();
        client.setParams(params);

        ArrayList<Header> headers = new ArrayList<Header>(2);
        headers.add(new Header("X-Storage-User", account_id));
        headers.add(new Header("X-Storage-Pass", account_key));

        HttpMethodBase method = getMethod(RequestMethod.GET, "https://ssproxy.ucloudbiz.olleh.com/auth/v1.0", headers, null, null);
        try {
            String body = executeForString(method);
            if(method.getStatusCode()==200){
                auth_token = method.getResponseHeader("X-Auth-Token").getValue();
                storage_path = method.getResponseHeader("X-Storage-Url").getValue()+"/"+filebox;
                header_auth = new Header("X-Auth-Token", auth_token);
                return true;
            } else {
                return false;
            }
        } catch (Exception e){
            return false;
        }

    }

    @SuppressWarnings("unused")
	private String executeForString(HttpMethodBase method) throws IOException {
        HttpClient client = new HttpClient();
        client.setParams(params);

        int httpcode = -1;
        try {
            System.out.println(method.getURI());
            httpcode = client.executeMethod(method);
            String body = method.getResponseBodyAsString();
            return body;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            method.releaseConnection();
        }
    }

    @SuppressWarnings("unused")
	private byte[] executeForStream(HttpMethodBase method) throws IOException {
        HttpClient client = new HttpClient();
        client.setParams(params);

        int httpcode = -1;
        try {
            System.out.println(method.getURI());
            httpcode = client.executeMethod(method);
            return method.getResponseBody();
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            method.releaseConnection();
        }
    }

}
