package kr.co.vendys.storeapi.entity;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

import kr.co.vendys.storeapi.constant.Errors;
import kr.co.vendys.storeapi.dto.store.MenuDto;
import kr.co.vendys.storeapi.exception.CommonException;
import kr.co.vendys.storeapi.util.MenuUtils;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters.LocalDateTimeConverter;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Created by jungsu on 2018. 11. 20.
 */

@Getter
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Setter(value = AccessLevel.PACKAGE)
@Entity
public class Menu {

    @Id
    private String mid;
    private String menuname;
    private String sid;
    private String storename;
    private Integer categorySeq;
    private Integer seq;
    private String intro;
    private Integer price;
    private Integer sellprice;
    private Integer supplyprice;
    private Date regdate;
    private Integer status;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime statusUpdateDate;
    private Integer categoryId;
    private String category;
    private String productid;
    private Integer mealtype;
    private Integer prodtype;
    private String image;
    private String imageThumbnail;
    private String createUser;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime updateDate;
    private String updateUser;

    private float score;
    private Integer reviewCount;

    @ManyToOne
    @JoinColumn(name = "sid", referencedColumnName = "sid", updatable = false, insertable = false)
    private Store store;

    /**
     * 메뉴 생성
     * @param store
     * @param categoryMenu
     * @param maxPriorityByStoreIdAndCategoryId
     * @param request
     * @param adminUser
     * @return
     */
    public static Menu createMenu(Store store,
                                  CategoryMenu categoryMenu,
                                  Integer maxPriorityByStoreIdAndCategoryId,
                                  MenuDto.CreateMenuRequest request,
                                  AdminUser adminUser) {

        Integer nextPriority = maxPriorityByStoreIdAndCategoryId == null ? 1 : maxPriorityByStoreIdAndCategoryId + 1;

        if(nextPriority >= MenuUtils.MENU_MAX_PRIORITY) nextPriority = MenuUtils.MENU_MAX_PRIORITY;

        return Menu.builder()
                // 메뉴에 대한 설정
                .mid(UUID.randomUUID().toString().toUpperCase())
                .menuname(request.getMenuName())
                .sid(store.getSid())
                .storename(store.getName())
                .seq(nextPriority)
                .intro(request.getIntro())
                .price(request.getPrice())
                .supplyprice(request.getSupplyPrice() == null ? request.getPrice() : request.getSupplyPrice())
                .sellprice(request.getSellPrice() == null ? request.getPrice() : request.getSellPrice())
                .status(request.getStatus())
                .statusUpdateDate(LocalDateTime.now())
                .productid(null)
                .prodtype(null)
                .reviewCount(0)
                .score(0)
                .mealtype(0) // 무조건 로컬상품(0). 해당 컬럼은 아무 영향없는 것으로 판단

                // 카테고리에 관한 설정
                .categorySeq(categoryMenu == null ? MenuUtils.ETC_CATEGORY_PRIORITY : categoryMenu.getPriority())
                .categoryId(categoryMenu == null ? MenuUtils.ETC_CATEGORY_ID.intValue() : categoryMenu.getMCateId().intValue())
                .category(categoryMenu == null ? MenuUtils.ETC_CATEGORY_NAME : categoryMenu.getName())

                // 생성 및 수정에 관한 설정
                .regdate(new Date())
                .createUser(adminUser.getName())
                .updateDate(LocalDateTime.now())
                .updateUser(adminUser.getName())
                .build();
    }

    /**
     * 메뉴 일괄 생성
     * @param store
     * @param categoryMenu
     * @param request
     * @param adminUser
     * @return
     */
    public static Menu createMenuBulk(Store store,
                                  CategoryMenu categoryMenu,
                                  MenuDto.CreateMenuBulkRequest request,
                                  Integer maxPriority,
                                  AdminUser adminUser) {

        Integer priority = request.getPriority() != null ? request.getPriority() : maxPriority + 1;

        if(priority >= MenuUtils.MENU_MAX_PRIORITY) priority = MenuUtils.MENU_MAX_PRIORITY;

        return Menu.builder()
                // 메뉴에 대한 설정
                .mid(UUID.randomUUID().toString().toUpperCase())
                .menuname(request.getMenuName())
                .sid(store.getSid())
                .storename(store.getName())
                .seq(priority)
                .intro(request.getIntro())
                .price(request.getPrice())
                .supplyprice(request.getSupplyPrice() == null ? request.getPrice() : request.getSupplyPrice())
                .sellprice(request.getSellPrice() == null ? request.getPrice() : request.getSellPrice())
                .status(request.getStatus())
                .statusUpdateDate(LocalDateTime.now())
                .productid(null)
                .prodtype(null)
                .reviewCount(0)
                .score(0)
                .mealtype(0) // 무조건 로컬상품(0). 해당 컬럼은 아무 영향없는 것으로 판단

                // 카테고리에 관한 설정
                .categorySeq(categoryMenu == null ? MenuUtils.ETC_CATEGORY_PRIORITY : categoryMenu.getPriority())
                .categoryId(categoryMenu == null ? MenuUtils.ETC_CATEGORY_ID.intValue() : categoryMenu.getMCateId().intValue())
                .category(categoryMenu == null ? MenuUtils.ETC_CATEGORY_NAME : categoryMenu.getName())

                // 생성 및 수정에 관한 설정
                .regdate(new Date())
                .createUser(adminUser.getName())
                .updateDate(LocalDateTime.now())
                .updateUser(adminUser.getName())
                .build();
    }

    public void updateMenu(CategoryMenu categoryMenu, MenuDto.UpdateMenuRequest request, Integer maxPriorityByStoreIdAndCategoryId, AdminUser adminUser) {
        // 메뉴 UPDATE
        this.menuname = request.getMenuName();
        this.intro = request.getIntro();
        this.price = request.getPrice();
        this.supplyprice = request.getSupplyPrice() == null ? request.getPrice() : request.getSupplyPrice();
        this.sellprice = request.getSellPrice() == null ? request.getPrice() : request.getSellPrice();
        this.status = request.getStatus();
        this.statusUpdateDate = LocalDateTime.now();

        // 메뉴 순서 UPDATE
        // 카테고리 변경 시에 해당 카테고리의 맨 끝 우선순위로 설정
        Integer nextPriority = maxPriorityByStoreIdAndCategoryId == null ? 1 : maxPriorityByStoreIdAndCategoryId + 1;

        if(nextPriority >= MenuUtils.MENU_MAX_PRIORITY) nextPriority = MenuUtils.MENU_MAX_PRIORITY;

        if(this.categoryId != request.getCategoryId().intValue()) updatePriority(nextPriority);

        // 카테고리 UPDATE
        this.categorySeq = categoryMenu == null ? MenuUtils.ETC_CATEGORY_PRIORITY : categoryMenu.getPriority();
        this.categoryId = categoryMenu == null ? MenuUtils.ETC_CATEGORY_ID.intValue() : categoryMenu.getMCateId().intValue();
        this.category = categoryMenu == null ? MenuUtils.ETC_CATEGORY_NAME : categoryMenu.getName();

        // 수정 정보 UPDATE
        this.updateDate = LocalDateTime.now();
        this.updateUser = adminUser.getName();
    }

    public void updateMenuBulk(CategoryMenu categoryMenu, MenuDto.UpdateMenuBulkRequest request, Integer maxPriority, AdminUser adminUser) {

        Integer priority = request.getPriority() != null ? request.getPriority() : maxPriority + 1;

        // 메뉴 UPDATE
        this.menuname = request.getMenuName();
        this.intro = request.getIntro();
        this.price = request.getPrice();
        this.seq = priority;
        this.supplyprice = request.getSupplyPrice() == null ? request.getPrice() : request.getSupplyPrice();
        this.sellprice = request.getSellPrice() == null ? request.getPrice() : request.getSellPrice();
        this.status = request.getStatus();
        this.statusUpdateDate = LocalDateTime.now();

        // 카테고리 UPDATE
        this.categorySeq = categoryMenu == null ? MenuUtils.ETC_CATEGORY_PRIORITY : categoryMenu.getPriority();
        this.categoryId = categoryMenu == null ? MenuUtils.ETC_CATEGORY_ID.intValue() : categoryMenu.getMCateId().intValue();
        this.category = categoryMenu == null ? MenuUtils.ETC_CATEGORY_NAME : categoryMenu.getName();

        // 수정 정보 UPDATE
        this.updateDate = LocalDateTime.now();
        this.updateUser = adminUser.getName();
    }

    // 이미지 썸네일 update
    public void updateImageThumbnail(MenuThumbnailImage mainThumbnailImage) {
        this.imageThumbnail = mainThumbnailImage == null ? null : mainThumbnailImage.getImage();
    }

    // 이미지 update
    public void updateImage(MenuImageSplice mainImageSplice) {
        this.image = mainImageSplice == null ? null : mainImageSplice.getImage();
    }

    public void scoreReviewCount(float score, Integer reviewCount) {
        this.score = score;
        this.reviewCount = reviewCount;
    }

    public MenuStatusType getStatusEnum() {
        if (this.status == 0) {
            return MenuStatusType.INACTIVE;
        } else if (this.status == 1) {
            return MenuStatusType.ACTIVE;
        } else {
            return MenuStatusType.SOLD_OUT;
        }
    }

    public void setStatus(MenuStatusType type) {
        this.status = type.getValue();
        this.statusUpdateDate = LocalDateTime.now();
    }

    public void updateUser(String updateUser) {
        this.updateDate = LocalDateTime.now();
        this.updateUser = updateUser;
    }

    // 우선순위 UPDATE
    public void updatePriority(Integer priority) {
        this.seq = priority;
    }

    @Getter
    public enum MenuStatusType {
        INACTIVE(0, "판매종료"),
        ACTIVE(1, "판매중"),
        SOLD_OUT(2, "일시품절");

        private final int value;
        private final String name;

        MenuStatusType(int value, String name) {
            this.value = value;
            this.name = name;
        }
    }
}