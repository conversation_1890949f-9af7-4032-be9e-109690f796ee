package kr.co.vendys.storeapi.entity.robot;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Getter
@Setter
@Entity
public class RobotDeliverySchedule {
    @Id
    private String scheduleId;

    private Long storeRobotDeliverySpotRelationIdx;

    private String robotName;
    @Convert(converter = Jsr310JpaConverters.LocalTimeConverter.class)
    private LocalTime deliveryStartTime;
    @Convert(converter = Jsr310JpaConverters.LocalTimeConverter.class)
    private LocalTime arrivalAfterMinutes;
    @Convert(converter = Jsr310JpaConverters.LocalTimeConverter.class)
    private LocalTime returnAfterMinutes;

    private Boolean isActive;
    @Convert(converter = Jsr310JpaConverters.LocalDateTimeConverter.class)
    private LocalDateTime createDate;
}
