package kr.co.vendys.storeapi.entity;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters.LocalDateTimeConverter;

import javax.persistence.*;
import java.time.LocalDateTime;

@Getter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
@Table(name = "MarketStoreCategory")
@DynamicInsert
public class MarketStoreCategory {
    @Id
    @Column(name = "categoryId", nullable = false)
    private Long categoryId;

    @Column(name = "categoryName", length = 20)
    private String categoryName;

    @Column(name = "mainImage", length = 500)
    private String mainImage;

    @Convert(converter = LocalDateTimeConverter.class)
    @Column(name = "createDate", nullable = false, updatable = false)
    private LocalDateTime createDate;

    @Column(name = "isDelete", nullable = false)
    private Boolean isDelete = false;

    @Builder
    public MarketStoreCategory(Long categoryId, String categoryName, String mainImage, LocalDateTime createDate, Boolean isDelete) {
        this.categoryId = categoryId;
        this.categoryName = categoryName;
        this.mainImage = mainImage;
        this.createDate = createDate;
        this.isDelete = isDelete;
    }
}
