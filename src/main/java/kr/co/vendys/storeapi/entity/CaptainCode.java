package kr.co.vendys.storeapi.entity;

import java.io.Serializable;
import java.time.LocalDateTime;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters.LocalDateTimeConverter;

import lombok.Data;
import org.hibernate.annotations.NotFound;

@Data
@Entity
public class CaptainCode implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idx;
    private String captainCode;
    private String encryptCaptainCode;
    private String encodedCaptainCode;
    private String userId;
    private Long payRoomIdx;
    private String couponId;
    private String requestStoreId;
    private String requestShopCode;
    private String requestTerminalCode;
    private String procStoreId;
    private String procShopCode;
    private String procTerminalCode;
    private String orderNo;
    private Integer orderAmount;
    private String orderMenu;
    @Enumerated(EnumType.STRING)
    private Status status;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime issued;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime issueCanceled;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime prepared;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime used;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime useCanceled;
    private String cancelCause;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime expireDate;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime updated;

    @NotFound
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", referencedColumnName = "uid", insertable = false, updatable = false)
    private User user;

    /**
     * ISSUED: 발급
     * ISSUE_CANCELED: 발급 취소
     * PREPARED : 사용 준비
     * USED: 사용
     * USE_CANCELED: 사용 취소
     */
    public enum Status {
        ISSUED, ISSUE_CANCELED, PREPARED, USED, USE_CANCELED
    }
}
