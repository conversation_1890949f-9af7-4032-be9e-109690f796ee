package kr.co.vendys.batch.controller.entity;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class RequestSendWebHookMessage {
    private String channel;

    private String icon;

    private String pretext;

    private String title;

    private String content;

    private String color;

    @Builder(builderMethodName = "webHook")
    public RequestSendWebHookMessage(String channel, String icon, String pretext, String title, String content, String color) {
        this.channel = channel;
        this.icon = icon;
        this.pretext = pretext;
        this.title = title;
        this.content = content;
        this.color = color;
    }
}
