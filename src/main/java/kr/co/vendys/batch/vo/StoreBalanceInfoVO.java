package kr.co.vendys.batch.vo;

import lombok.Data;

import java.util.Date;

/**
 * Created by jangjungsu on 2017. 1. 5..
 */
public class StoreBalanceInfoVO {

    @Data
    public static class StoreBalanceInfo {
        private String storeId;
        private String region;
        private String bizSerial;
        private String bizSubSerial;
        private String bizName;
        private String bizPresidentName;
        private String bizConditions;
        private String bizType;
        private String bizAddress;
        private String balanceEmail;
        private Date contractDate;
        private Date contractExpireDate;
        private String taxGrade;
        private String receiveMethod;
        private String bankName;
        private String bankOwner;
        private String bankAccount;
        private String niceId;
        private String memo;
    }

}
