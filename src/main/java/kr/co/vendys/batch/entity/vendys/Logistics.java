package kr.co.vendys.batch.entity.vendys;

import java.time.LocalDateTime;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters.LocalDateTimeConverter;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@NoArgsConstructor(access = AccessLevel.PACKAGE)
@Setter(value = AccessLevel.PACKAGE)
@Entity
public class Logistics {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idx;
    private String code;
    private String naverLogisticsCode;
    private String name;
    private String tel;
    private String trackingUrl;
    private Boolean isUseTrackingNumber;
    private String createUser;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime createDate;
    private String updateUser;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime updateDate;

}
