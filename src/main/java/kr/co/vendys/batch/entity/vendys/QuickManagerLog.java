package kr.co.vendys.batch.entity.vendys;

import java.time.LocalDateTime;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters.LocalDateTimeConverter;

import kr.co.vendys.batch.entity.vendys.QuickManager.GoodsType;
import kr.co.vendys.batch.entity.vendys.QuickManager.Pay;
import kr.co.vendys.batch.entity.vendys.QuickManager.Status;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PROTECTED)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
public class QuickManagerLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idx;
    private Long orderId;
    private Long quickManagerIdx;
    private String comId;
    private String userId;
    private String userName;
    @Enumerated(EnumType.STRING)
    private Status status;
    private String vehicle;
    @Enumerated(EnumType.STRING)
    private Pay pay;
    @Enumerated(EnumType.STRING)
    private GoodsType goodsType;
    private String goodsInfo;
    private Integer goodsCnt;
    private Integer amount;
    private Integer bonusAmount;
    private String bonusAmountReason;
    private String remark;
    private Boolean isExpress;
    private Boolean isRain;
    private Boolean isSnow;
    private Boolean isNeedCarry;
    private Boolean isRidePassenger;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime appointmentDate;
    private String requestBody;
    private String updateUserId;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime regDate;

    public void of(QuickManager quick, String body) {
        this.quickManagerIdx = quick.getQuickManagerIdx();
        this.comId = quick.getComId();
        this.userId = quick.getUserId();
        this.userName = quick.getUserName();
        this.orderId = quick.getOrderId();
        this.status = quick.getStatus();
        this.vehicle = quick.getVehicle();
        this.pay = quick.getPay();
        this.goodsType = quick.getGoodsType();
        this.goodsInfo = quick.getGoodsInfo();
        this.goodsCnt = quick.getGoodsCnt();
        this.amount = quick.getAmount();
        this.bonusAmount = quick.getBonusAmount();
        this.bonusAmountReason = quick.getBonusAmountReason();
        this.remark = quick.getRemark();
        this.isExpress = quick.getIsExpress();
        this.isRain = quick.getIsRain();
        this.isSnow = quick.getIsSnow();
        this.isNeedCarry = quick.getIsNeedCarry();
        this.isRidePassenger = quick.getIsRidePassenger();
        this.appointmentDate = quick.getAppointmentDate();
        this.requestBody = body;
        this.updateUserId = "batchService";
        this.regDate = LocalDateTime.now();
    }
}
