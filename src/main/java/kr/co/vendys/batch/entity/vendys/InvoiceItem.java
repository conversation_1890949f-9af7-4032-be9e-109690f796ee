package kr.co.vendys.batch.entity.vendys;

import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import lombok.Data;

/**
 * Created by jinwoo on 2018. 6. 7.
 */
@Data
@Entity
@EntityListeners(value = {AuditingEntityListener.class})
public class InvoiceItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long invoiceItemIdx;
    private Long invoiceIdx;
    private Date date;
    private Integer price;
    private Integer salesPrice;
    private Integer companyPrice;
    private Integer supplyPrice;
    @CreatedDate
    @Temporal(TemporalType.TIMESTAMP)
    private Date created;
    @LastModifiedDate
    @Temporal(TemporalType.TIMESTAMP)
    private Date updated;
}
