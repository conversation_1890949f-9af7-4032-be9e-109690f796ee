package kr.co.vendys.batch.entity.vendys;

import java.time.LocalDateTime;
import java.util.Date;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

import org.springframework.data.jpa.convert.threeten.Jsr310JpaConverters.LocalDateTimeConverter;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Setter(value = AccessLevel.PACKAGE)
@Entity
public class CouponGs {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idx;
    private Long payRoomIdx;
    private String coupon;
    private String startDate;
    private String endDate;
    @Enumerated(EnumType.STRING)
    private Status status;
    private String couponId;
    private String approvalNo;
    private String cancelNo;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime created;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime createCanceled;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime used;
    @Convert(converter = LocalDateTimeConverter.class)
    private LocalDateTime useCanceled;
    private String cancelCause;

    /**
     * ISSUED: 발급
     * ISSUE_CANCELED: 발급취소
     * USED: 사용
     * USE_CANCELED: 사용취소
     * NET_CANCELED: 사용망취소
     */
    public enum Status {
        ISSUED, ISSUE_CANCELED, USED, USE_CANCELED, NET_CANCELED
    }
}
