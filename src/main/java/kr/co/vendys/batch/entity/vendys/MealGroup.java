package kr.co.vendys.batch.entity.vendys;

import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

import lombok.Data;

/**
 * Created by jungsu on 2019-07-11
 */
@Data
@Entity
public class MealGroup {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long groupIdx;

    @Enumerated(EnumType.STRING)
    private GroupType groupType;
    private String comId;
    private String orgCode;
    private String name;
    private String memo;
    private Integer limitAmount;
    private Boolean isDefault;
    private Boolean isActive;
    private Date regDate;

    public enum GroupType {
        MEAL, WELFARE
    }
}
