package kr.co.vendys.batch.entity.vendys;

import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

import lombok.Data;

/**
 * Created by jin<PERSON><PERSON> on 2018. 8. 3.
 */
@Data
@Entity
public class MealPolicy {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long policyIdx;
    private Long groupIdx;
    private String name;
    @Enumerated(EnumType.STRING)
    private Type type;
    private String offset;
    private String expireRange;
    private Date startTime;
    private Date expireTime;
    private String day;
    @Enumerated(EnumType.STRING)
    private HolidayUseOption holidayUseOption;
    private Integer amount;
    private Boolean isActive;
    private Boolean isGroupPay;
    private Boolean isPresent;
    private Integer expireCount;
    private String expireNotice;
    private Integer limitAmount;
    private Integer maxHoldLimitAmount;
    private Integer priority;
    private Date regDate;

    /**
     * DAY: 일일 정책
     * LONGTERM: 장기 정책
     * INFINITE: 무제한 정책
     * DEMAND: 식권 신청 정책
     */
    public enum Type {
        DAY, LONGTERM, INFINITE, DEMAND
    }

    /**
     * DAY_AND_HOLIDAY - 선택한 요일에 사용 가능. 단, 공휴일인 경우에도 사용 가능
     * DAY - 선택한 요일에 사용 가능. 단, 공휴일인 경우에 사용 불가
     * DAY_OR_HOLIDAY - 선택한 요일과 모든 공휴일에 사용 가능
     */
    public enum HolidayUseOption {
        DAY_AND_HOLIDAY, DAY, DAY_OR_HOLIDAY
    }
}
