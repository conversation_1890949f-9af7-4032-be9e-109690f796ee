package kr.co.vendys.batch.entity.vendys;

import java.util.Date;
import java.util.List;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

/**
 * Created by jinwoo on 2018. 6. 7.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@EntityListeners(value = {AuditingEntityListener.class})
public class Invoice {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long invoiceIdx;
    private Integer billingAccountIdx;
    private Date invoiced;
    private Date startDate;
    private Date endDate;
    @Enumerated(EnumType.STRING)
    private Status status;
    @Enumerated(EnumType.STRING)
    private ApprovalStatus approvalStatus;
    @CreatedDate
    @Temporal(TemporalType.TIMESTAMP)
    private Date created;
    @LastModifiedDate
    @Temporal(TemporalType.TIMESTAMP)
    private Date updated;

    @OneToMany(mappedBy = "invoiceIdx")
    private List<InvoiceApprovalActivity> invoiceApprovalActivity;

    @OneToMany(mappedBy = "invoiceIdx")
    private List<InvoiceRelation> invoiceParentRelations;

    @OneToMany(mappedBy = "parent")
    private List<InvoiceRelation> invoiceNodeRelations;
    @Fetch(FetchMode.JOIN)
    @ManyToOne
    @JoinColumn(name = "billingAccountIdx", insertable = false, updatable=false)
    private BillingAccount billingAccount;

    public enum Status {
        INITIATED, DRAFT, COMMITTED
    }

    public enum ApprovalStatus {
        PENDING, PROCESSING, ACCEPTED, REJECTED
    }

    @Override
    public String toString() {
        return ToStringBuilder
            .reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }
}
