package kr.co.vendys.batch.entity.vendys.statistics;

import java.util.Arrays;
import java.util.Date;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

import kr.co.vendys.batch.constant.Errors;
import kr.co.vendys.batch.exception.CommonException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Entity
public class DaejangPoint {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long idx;
    private String sdate;
    private String guid;
    private String behaviorType;
    @Convert(converter = PmTypeConverter.class)
    private String pmType;
    @Enumerated(EnumType.STRING)
    private Type type;
    private Long changePoint;
    private String description;
    private Date updateDate;
    private String vender;

    public enum PmType {
        PLUS("+"),
        MINUS("-")
        ;

        private final String symbol;

        PmType(String symbol) {
            this.symbol = symbol;
        }

        public String getSymbol() {
            return symbol;
        }

        public static PmType find(String name) {
            return Arrays.stream(PmType.values())
                    .filter(type -> type.getSymbol().equals(name))
                    .findFirst()
                    .orElseThrow(() -> new CommonException(Errors.GENERAL_UNKNOWN));
        }
    }

    public enum Type {
        COMPANY, USER, ALL
        ;

        public static Type find(String name) {
            return Arrays.stream(Type.values())
                        .filter(type -> type.name().equals(name))
                        .findFirst()
                        .orElseThrow(() -> new CommonException(Errors.GENERAL_UNKNOWN));
        }
    }


}
