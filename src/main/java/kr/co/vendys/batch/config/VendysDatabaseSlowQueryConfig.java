package kr.co.vendys.batch.config;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

@Configuration
public class VendysDatabaseSlowQueryConfig {

    /**
     * HikariConfig Bean 등록
     */
    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.hikari.vendys.slow-query")
    public HikariConfig slowQueryHikariConfig() {
        return new HikariConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.hikari.vendys.slow-query-ro")
    public HikariConfig slowQueryRoHikariConfig() {
        return new HikariConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.hikari.vendys.slow-query-bigven")
    public HikariConfig slowQueryBigvenHikariConfig() {
        return new HikariConfig();
    }

    /**
     * HikariDataSource 생성
     */
    @Bean(name = "slowQueryDataSource")
    public DataSource slowQueryDataSource(@Qualifier("slowQueryHikariConfig") HikariConfig config) {
        return new HikariDataSource(config);
    }

    @Bean(name = "slowQueryRoDataSource")
    public DataSource slowQueryRoDataSource(@Qualifier("slowQueryRoHikariConfig") HikariConfig config) {
        return new HikariDataSource(config);
    }

    @Bean(name = "slowQueryBigvenDataSource")
    public DataSource slowQueryBigvenDataSource(@Qualifier("slowQueryBigvenHikariConfig") HikariConfig config) {
        return new HikariDataSource(config);
    }

    /**
     * JdbcTemplate 생성
     */
    @Bean(name = "slowQueryJdbcTemplate")
    public JdbcTemplate slowQueryJdbcTemplate(@Qualifier("slowQueryDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Bean(name = "slowQueryRoJdbcTemplate")
    public JdbcTemplate slowQueryRoJdbcTemplate(@Qualifier("slowQueryRoDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    @Bean(name = "slowQueryBigvenJdbcTemplate")
    public JdbcTemplate slowQueryBigvenJdbcTemplate(@Qualifier("slowQueryBigvenDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    /**
     * TransactionManager 생성
     */
    @Bean(name = "slowQueryTransactionManager")
    public PlatformTransactionManager slowQueryTransactionManager(@Qualifier("slowQueryDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "slowQueryRoTransactionManager")
    public PlatformTransactionManager slowQueryRoTransactionManager(@Qualifier("slowQueryRoDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "slowQueryBigvenTransactionManager")
    public PlatformTransactionManager slowQueryBigvenTransactionManager(@Qualifier("slowQueryBigvenDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
