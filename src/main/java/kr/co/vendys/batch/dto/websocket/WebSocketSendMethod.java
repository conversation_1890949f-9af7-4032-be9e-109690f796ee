package kr.co.vendys.batch.dto.websocket;

import lombok.Getter;

@Getter
public enum WebSocketSendMethod {

    SIKDAE_V1_POINT_ENROLL("SIKDAE_V1_POINT", 0, "sikdae.v1.point.enroll", "식대가 지급되었습니다."),
    ;

    private String code;
    private Integer status;
    private String method;
    private String desc;

    WebSocketSendMethod(String code, Integer status, String method, String desc) {
        this.code = code;
        this.status = status;
        this.method = method;
        this.desc = desc;
    }
}
