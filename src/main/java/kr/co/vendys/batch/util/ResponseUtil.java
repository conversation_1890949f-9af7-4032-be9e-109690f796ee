package kr.co.vendys.batch.util;

import kr.co.vendys.batch.exception.VlocallyException;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

/**
 * Created by jin<PERSON><PERSON> on 2015. 7. 15..
 */
public class ResponseUtil {
	private final static String ATTR_STATUS = "status";
	private final static String ATTR_MESSAGE = "message";

	private static HashMap<String, Object> defaultMap() {
		HashMap<String, Object> result = new HashMap<>();
		result.put(ATTR_STATUS, 0);
		result.put(ATTR_MESSAGE, "성공");
		return result;
	}

	public static HashMap<String, Object> make(HashMap<String, Object> content) {
		HashMap<String, Object> result = defaultMap();
		result.put("content", content);
		return result;
	}

	public static HashMap<String, Object> make(List<HashMap<String, Object>> content) {
		HashMap<String, Object> result = defaultMap();
		result.put("content", content);
		return result;
	}

	public static HashMap<String, Object> make(HttpServletResponse response) {
		HashMap<String, Object> result = new HashMap<>();
		result.put(ATTR_STATUS, response.getStatus());
		result.put(ATTR_MESSAGE, HttpStatus.valueOf(response.getStatus()));
		return result;
	}

	public static HashMap<String, Object> make(VlocallyException e) {
		HashMap<String, Object> result = new HashMap<>();
		result.put(ATTR_STATUS, e.getErrorNum());
		result.put(ATTR_MESSAGE, e.getMessage());
		return result;
	}
}
