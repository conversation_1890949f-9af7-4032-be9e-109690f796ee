package kr.co.vendys.batch.util;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import kr.co.vendys.batch.constant.StoreSupplyTypeV2;
import kr.co.vendys.batch.entity.vendys.Store;
import kr.co.vendys.batch.entity.vendys.StoreBlacklist;

@Component
public class StoreUtil {
    /**
     * 리뷰 기능 사용 여부 체크
     * - supplytype: 큐피콘(1), 쿠폰(8), 구내식당 (일회용(4), 고정형(5))
     * 의 경우 사용 X
     * */
    public static Boolean checkIsReview(Store store) {
        return Arrays.stream(StoreSupplyTypeV2.values())
                .filter(vo -> vo.type == store.getSupplytype()) // supplytype 찾고
                .anyMatch(vo -> vo.isReview || store.getIsMultiplePayment()); // isReview 여부 값
    }

    /**
     * 현제 사용제한 제휴식당의 제한시간에 해당하는지 여부
     * true : 사용가능, false : 사용제한
     */
    public static Boolean checkStoreBlackListTime(StoreBlacklist storeBlacklist) {

        if (ObjectUtils.isEmpty(storeBlacklist)) {
            return true;
        }

        if (ObjectUtils.isEmpty(storeBlacklist.getStartTime()) || ObjectUtils.isEmpty(storeBlacklist.getEndTime())) {
            return false;
        }

        return StoreUtil.checkStoreBlackListTimeCommon(storeBlacklist.getStartTime(), storeBlacklist.getEndTime());
    }

    public static Boolean checkStoreBlackListTimeCommon(Date startTime, Date endTime) {

        Date nowDate = DateUtil.clearDate(new Date());
        long nowLong = nowDate.getTime();

        Calendar startCal = Calendar.getInstance();
        startCal.setTime(startTime);
        long startLong = startCal.getTimeInMillis();

        Calendar endCal = Calendar.getInstance();
        endCal.setTime(endTime);
        long endLong = endCal.getTimeInMillis();

        // 당일에 범위가 지정된 경우
        if (startLong < endLong) {
            return startLong <= nowLong && nowLong < endLong ? false : true;
            // 익일까지 걸쳐 있는 경우
        } else if (startLong > endLong) {
            return startLong <= nowLong || nowLong < endLong ? false : true;
            // 시작 시간과 종료시간이 같으면 항상 사용 불가
        } else {
            return false;
        }
    }
}
