package kr.co.vendys.batch.util;

/**
 * Created by vendys-polaris on 2016. 8. 31..
 */
public class NumericUtil {

    private static final long SYSTEM_62 = 62;

    /**
     * long형태의 index를 62진수 id로 변환.
     *
     * @param num
     * @param length
     * @return
     */
    public static String intTo62(long num, int length) {
        long modofresult = 0;
        StringBuilder result62 = new StringBuilder();

        do {
            modofresult = num % SYSTEM_62;
            if (modofresult <= 9) {
                result62.insert(0, modofresult);
            } else if (modofresult <= 35) {
                result62.insert(0, (char) (65 + (modofresult - 10)));
            } else if (modofresult <= 61) {
                result62.insert(0, (char) (97 + (modofresult - 36)));
            }
            num /= SYSTEM_62;
        } while (num > 0);

        for (int i = result62.length(); i < length; i++) {
            result62.insert(0, "0");
        }

        return result62.toString();
    }

}
