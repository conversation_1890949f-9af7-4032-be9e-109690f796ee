package kr.co.vendys.batch.util;


import java.io.StringReader;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.Random;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.apache.xmlrpc.XmlRpcException;
import org.apache.xmlrpc.client.XmlRpcClient;
import org.apache.xmlrpc.client.XmlRpcClientConfigImpl;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import kr.co.vendys.batch.constant.db.SMSResultCode;
import kr.co.vendys.batch.vo.Smslog;

@Component
public class SMSGabiaSender implements SMSSender{

	/**
	 * 현재 사용자 번호 인증요으로만 사용 
	 * 쓰는곳 없음
	 * @param receiver
	 * @param text
	 * @return
	 */
	public boolean sendSMS(String receiver, String text){
		return true;
//		return send(new String[]{smsId, apiKey,"sms", DateUtil.getCurTimeMils()+receiver,"title",text,"025691128",receiver});
		// {smsid, apikey, "전송형태", 일련번호?, 제목, 내용, 보낸이, 받는이}
	}
	
	private String smsId = "vendyslms";
//	private String smspw = "tnaanswk";
	private String apiKey = "def441765ebd81a87ae88d5bcff7efa0";
//	private String apiKey = "763d322afc69795fc68f8767c0ebeed6";
	private final static String apiUrl = "http://sms.gabia.com/api";
	private final static String methodName = "gabiasms";

	private String refXmlFormat = "<request>" + "<sms-id>%s</sms-id>"
			+ "<access-token>%s</access-token>"
			+ "<response-format>xml</response-format>"
			+ "<method>SMS.getStatusByRef</method>" + "<params>"
			+ "<ref_key>%s</ref_key>" + "</params>" + "</request>";
	
	private String reqXmlFormat = "<request>" + "<sms-id>%s</sms-id>"
			+ "<access-token>%s</access-token>"
			+ "<response-format>xml</response-format>"
			+ "<method>SMS.send</method>" + "<params>"
			+ "<send_type>%s</send_type>" + "<ref_key>%s</ref_key>"
			+ "<subject>%s</subject>" + "<message>%s</message>"
			+ "<callback>%s</callback>" + "<phone>%s</phone>" + "</params>"
			+ "</request>";

	public Smslog[] sendSMS(Smslog[] smss){
		for(Smslog sms:smss){
			sendSMS(sms);
		}
		return smss;
	}
	
	public synchronized Smslog sendSMS(Smslog sms){
		String sendid;
		if(sms.getSendid()==null){
			sendid = System.currentTimeMillis()+"";
			sms.setSendid(sendid);
		} else {
			sendid = sms.getSendid();
		}
		
		String[] args = new String[8];
		args[0] = smsId;
		args[1] = apiKey;
		args[2] = sms.getType();
		args[3] = sendid;
		args[4] = sms.getTitle();
		args[5] = sms.getContent();
		args[6] = sms.getSender();
		args[7] = sms.getReceiver();
		
		try {
			XmlRpcClientConfigImpl config = new XmlRpcClientConfigImpl();
			config.setServerURL(new URL(apiUrl));
			XmlRpcClient client = new XmlRpcClient();
			client.setConfig(config);

			String nonce = getNonce();
			String md5_access_token = nonce + getMD5(nonce + this.apiKey);
			String s;

			if ("status_by_ref".equals(args[2])) {
				s = String.format(refXmlFormat, this.smsId, md5_access_token,
						args[3]);
			} else {
				s = String.format(reqXmlFormat, this.smsId, md5_access_token,
						args[2], args[3], args[4], args[5], args[6], args[7]);
			}
			s = s.replace("&","&amp;");
//			System.out.println(s);
			Object[] params = new Object[] { new String(s) };
			String response = (String) client.execute(methodName, params);

//			System.out.println("Response:" + getResultXml(response));
			try{
				ApiResult result = getResult(response);
				String code = result.getCode();
				String msg = result.getMesg();
				if(code.compareTo("0000")==0||code.compareTo("0")==0){
					sms.setResultmessage("code:"+code+" msg:"+msg);
					sms.setResult((byte)SMSResultCode.Success.code);
				} else {
					sms.setResultmessage("code:"+code+" msg:"+msg);
					sms.setResult((byte)SMSResultCode.Fail.code);
				}
			} catch(Exception e){
				if(response.length()>200){
					sms.setResultmessage("code:결과파싱오류 msg:"+e.getMessage()+" c"+response.substring(0, 200));
				} else {
					sms.setResultmessage("code:결과파싱오류 msg:"+e.getMessage()+" c"+response);
				}
				sms.setResult((byte)SMSResultCode.Fail.code);
			}
			
		} catch (XmlRpcException e) {
//			System.err.println("JavaClient: XML-RPC Fault #"
//					+ Integer.toString(exception.code) + ": "
//					+ exception.toString());
			if(e.getMessage().length()>200){
				sms.setResultmessage("통신JavaClient: XML-RPC Fault #"+e.code+" "+e.getMessage().substring(0, 200));
			} else {
				sms.setResultmessage("통신JavaClient: XML-RPC Fault #"+e.code+" "+e.getMessage());
			}
			sms.setResult((byte)SMSResultCode.Fail.code);
		} catch (MalformedURLException e) {
			if(e.getMessage().length()>200){
				sms.setResultmessage("MalformedURLException "+e.getMessage().substring(0, 200));
			} else {
				sms.setResultmessage("MalformedURLException "+e.getMessage());
			}
			sms.setResult((byte)SMSResultCode.Fail.code);
		} catch (NoSuchAlgorithmException e) {
			if(e.getMessage().length()>200){
				sms.setResultmessage("NoSuchAlgorithmException "+e.getMessage().substring(0, 200));
			} else {
				sms.setResultmessage("NoSuchAlgorithmException "+e.getMessage());
			}
			sms.setResult((byte)SMSResultCode.Fail.code);
		}
		
		sms.setSenddate(new Date());
		sms.setCompany("gabia");
		return sms;
	}
	
	public String getNonce() {
		StringBuffer nonce = new StringBuffer();
		Random random = new Random();

		String chars[] = "a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,0,1,2,3,4,5,6,7,8,9"
				.split(",");

		for (int i = 0; i < 8; i++) {
			nonce.append(chars[random.nextInt(chars.length)]);
		}
		// System.out.println("nonce:" + nonce.toString());
		return nonce.toString();
	}

	public String getMD5(String str) throws NoSuchAlgorithmException {
		MessageDigest di = MessageDigest.getInstance("MD5");
		di.update(new String(str).getBytes());
		byte[] md5Code = di.digest();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < md5Code.length; i++) {
			String md5Char = String.format("%02x", 0xff & (char) md5Code[i]);
			sb.append(md5Char);
		}

		return sb.toString();
	}

	public ApiResult getResult( String xmlStr ) throws Exception{
		String code = "";
		String mesg = "";
		
//		try {
			// first of all we request out
			// DOM-implementation:
			DocumentBuilderFactory factory = DocumentBuilderFactory
					.newInstance();
			// then we have to create document-loader:
			DocumentBuilder loader = factory.newDocumentBuilder();

			// loading a DOM-tree...
			Document document = loader.parse(new InputSource(new StringReader(
					xmlStr)));
			// at last, we get a root element:
			Element tree = document.getDocumentElement();
			// ... do something with document element ...
			NodeList items = tree.getElementsByTagName("code");
			code = items.item(0).getFirstChild().getNodeValue();
			
			items = tree.getElementsByTagName("mesg");
			mesg = items.item(0).getFirstChild().getNodeValue();

//		} catch (IOException ex) {
//			// any IO errors occur:
//			handleError(ex);
//		} catch (SAXException ex) {
//			// parse errors occur:
//			handleError(ex);
//		} catch (ParserConfigurationException ex) {
//			// document-loader cannot be created which,
//			// satisfies the configuration requested
//			handleError(ex);
//		} catch (FactoryConfigurationError ex) {
//			// DOM-implementation is not available
//			// or cannot be instantiated:
//			handleError(ex);
//		} catch (NullPointerException ex) {
//			// DOM-implementation is not available
//			// or cannot be instantiated:
//			handleError(ex);
//		}
		
		ApiResult res = new ApiResult(code,mesg);
		return res;
	}
	
//	public String getResultXml(String xmlStr) {
//		try {
//			// first of all we request out
//			// DOM-implementation:
//			DocumentBuilderFactory factory = DocumentBuilderFactory
//					.newInstance();
//			// then we have to create document-loader:
//			DocumentBuilder loader = factory.newDocumentBuilder();
//
//			// loading a DOM-tree...
//			Document document = loader.parse(new InputSource(new StringReader(
//					xmlStr)));
//			// at last, we get a root element:
//			Element tree = document.getDocumentElement();
//			// ... do something with document element ...
//			NodeList items = tree.getElementsByTagName("result");
//			String nodeValue = items.item(0).getFirstChild().getNodeValue();
//
//			byte[] bytDecoded = Base64Utils.decode(nodeValue);
//			String result = new String(bytDecoded);
////			System.out.println("DecodedString=" + result);
//
//			return result.toString();
//			
//		} catch (IOException ex) {
//			// any IO errors occur:
//			handleError(ex);
//		} catch (SAXException ex) {
//			// parse errors occur:
//			handleError(ex);
//		} catch (ParserConfigurationException ex) {
//			// document-loader cannot be created which,
//			// satisfies the configuration requested
//			handleError(ex);
//		} catch (FactoryConfigurationError ex) {
//			// DOM-implementation is not available
//			// or cannot be instantiated:
//			handleError(ex);
//		} catch (NullPointerException ex) {
//			// DOM-implementation is not available
//			// or cannot be instantiated:
//			handleError(ex);
//		}
//		
//		return "";
//	}

//	private static final void handleError(Throwable ex) {
//		// ... handle error here...
//		System.out.println("Error Handler: " + ex.toString());
//	}
	
	public class ApiResult {
		private String code = "";
		private String mesg = "";
		
		public ApiResult( String code, String mesg ) {
			
			this.code = code;
			this.mesg = mesg;
			
		}
		
		public String getCode()
		{
			return this.code;
		}
		
		public String getMesg()
		{
			return this.mesg;
		}
		
		
	}
}
