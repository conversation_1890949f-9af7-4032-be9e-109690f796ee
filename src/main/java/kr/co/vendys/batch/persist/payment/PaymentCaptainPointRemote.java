package kr.co.vendys.batch.persist.payment;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import kr.co.vendys.batch.persist.common.RemoteHeader;

@Service
public class PaymentCaptainPointRemote {

    @Autowired
    private RestTemplate customRestTemplate;
    @Autowired
    private RemoteHeader remoteHeader;
    @Value(value = "${sikdae.payment-service.api.host}")
    private String paymentHost;

    /**
     * 사용자 포인트 초기화 및 충전
     */
    public void captainPointUserTargetProcessing(String userId, String captainPointUserTargetId) {

        HttpEntity<Object> httpEntity = this.getPaymentHeader(userId);
        Map<String, String> pathParams = new HashMap<>();
        pathParams.put("userId", userId);
        pathParams.put("captainPointUserTargetId", captainPointUserTargetId);
        URI uri = UriComponentsBuilder
                .fromUriString(this.paymentHost + "/captainpoint/v1/{userId}/process/{captainPointUserTargetId}/admin")
                .buildAndExpand(pathParams)
                    .toUri();
        this.customRestTemplate.exchange(uri, HttpMethod.PUT, httpEntity, String.class);
    }

    public void captainPointUserTargetExtinction(String userId, String captainPointUserTargetId) {

        HttpEntity<Object> httpEntity = this.getPaymentHeader(userId);
        Map<String, String> pathParams = new HashMap<>();
        pathParams.put("userId", userId);
        pathParams.put("captainPointUserTargetId", captainPointUserTargetId);
        URI uri = UriComponentsBuilder
                .fromUriString(this.paymentHost + "/captainpoint/v1/{userId}/extinction/{captainPointUserTargetId}/system")
                .buildAndExpand(pathParams)
                .toUri();
        this.customRestTemplate.exchange(uri, HttpMethod.DELETE, httpEntity, String.class);
    }

    public void captainPointUserTargetExtinctionBeforePush(String userId, String captainPointUserTargetId) {

        HttpEntity<Object> httpEntity = this.getPaymentHeader(userId);
        Map<String, String> pathParams = new HashMap<>();
        pathParams.put("userId", userId);
        pathParams.put("captainPointUserTargetId", captainPointUserTargetId);
        URI uri = UriComponentsBuilder
                .fromUriString(this.paymentHost + "/captainpoint/v1/{userId}/extinction/push/{captainPointUserTargetId}/system")
                .buildAndExpand(pathParams)
                .toUri();
        this.customRestTemplate.exchange(uri, HttpMethod.PUT, httpEntity, String.class);
    }

    private HttpEntity<Object> getPaymentHeader(String userId) {
        HttpHeaders httpHeaders = this.remoteHeader.getPaymentHeaders(userId);
        HttpEntity<Object> httpEntity = new HttpEntity<>(null, httpHeaders);
        return httpEntity;
    }
}
