package kr.co.vendys.batch.repository.vendys.master;

import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import kr.co.vendys.batch.entity.vendys.LatestTradingDate;
import lombok.RequiredArgsConstructor;

@Repository
public class LatestTradingDateBulkRepository {

    @Qualifier("sikdaeJdbcTemplate")
    private final JdbcTemplate jdbcTemplate;

    public LatestTradingDateBulkRepository(@Qualifier("sikdaeJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 식권대장 최근 거래일 merge insert 쿼리
     */
    final String mergeSikdaeTradingDateQuery =
            "INSERT INTO LatestTradingDate (uid, commonCode, commonCodeCategory, latestSikdaeTradingDate, latestWelfareTradingDate) " +
                    "VALUES (?, ?, ?, ?, ?) " +
                    "ON DUPLICATE KEY UPDATE latestSikdaeTradingDate = ? ";

    /**
     * 복지대장 최근 거래일 merge insert 쿼리
     */
    final String mergeWelfareTradingDateQuery =
            "INSERT INTO LatestTradingDate (uid, commonCode, commonCodeCategory, latestSikdaeTradingDate, latestWelfareTradingDate) " +
                    "VALUES (?, ?, ?, ?, ?) " +
                    "ON DUPLICATE KEY UPDATE latestWelfareTradingDate = ? ";

    public void mergeSikdaeTradingDate(List<LatestTradingDate> entities) {
        jdbcTemplate.batchUpdate(
                mergeSikdaeTradingDateQuery,
                new BatchPreparedStatementSetter() {
                    @Override
                    public void setValues(PreparedStatement preparedStatement, int i) throws SQLException {
                        LatestTradingDate entity = entities.get(i);

                        // INSERT Parameter
                        preparedStatement.setString(1, entity.getId());
                        preparedStatement.setString(2, entity.getCommonCode());
                        preparedStatement.setString(3, entity.getCommonCodeCategory());
                        preparedStatement.setDate(4, Date.valueOf(entity.getLatestSikdaeTradingDate()));
                        preparedStatement.setDate(5, null);

                        // UPDATE Parameter
                        preparedStatement.setDate(6, Date.valueOf(entity.getLatestSikdaeTradingDate()));
                    }

                    @Override
                    public int getBatchSize() {
                        return entities.size();
                    }
                });
    }// mergeSikdaeTrandingDate

    public void mergeWelfareTradingDate(List<LatestTradingDate> entities) {
        jdbcTemplate.batchUpdate(
                mergeWelfareTradingDateQuery,
                new BatchPreparedStatementSetter() {
                    @Override
                    public void setValues(PreparedStatement preparedStatement, int i) throws SQLException {
                        LatestTradingDate entity = entities.get(i);

                        // INSERT Parameter
                        preparedStatement.setString(1, entity.getId());
                        preparedStatement.setString(2, entity.getCommonCode());
                        preparedStatement.setString(3, entity.getCommonCodeCategory());
                        preparedStatement.setDate(4, null);
                        preparedStatement.setDate(5, Date.valueOf(entity.getLatestWelfareTradingDate()));

                        // UPDATE Parameter
                        preparedStatement.setDate(6, Date.valueOf(entity.getLatestWelfareTradingDate()));
                    }

                    @Override
                    public int getBatchSize() {
                        return entities.size();
                    }
                });
    }// mergeWelfareTradingDate

}
