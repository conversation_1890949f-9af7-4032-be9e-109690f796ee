package kr.co.vendys.batch.repository.vendys.master;

import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

import kr.co.vendys.batch.entity.vendys.Invoice;
import kr.co.vendys.batch.entity.vendys.InvoiceRelation;

/**
 * Created by leedo on 2018. 6. 29.
 */
public interface InvoiceRelationRepository extends JpaRepository<InvoiceRelation, Long> {

    Optional<InvoiceRelation> findByInvoiceIdxAndParent(Invoice invoiceIdx, Invoice parentInvoiceIdx);

}
