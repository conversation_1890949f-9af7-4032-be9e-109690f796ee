package kr.co.vendys.batch.mapper.master;

import java.util.HashMap;
import java.util.List;

import kr.co.vendys.batch.vo.Menu;
import kr.co.vendys.batch.vo.MenuExample;
import kr.co.vendys.batch.vo.MenuVo;
import org.apache.ibatis.session.RowBounds;

public interface MenuMapper {
    void insertSelective(Menu menu);

    int updateByPrimaryKeySelective(Menu menu);

    Menu selectByPrimaryKey(String mid);

    int countByExample(MenuExample ex);

    List<Menu> selectByExampleWithBLOBs(MenuExample ex, RowBounds rb);

    List<String> selectIdByMealType(int type);

    int updateByExampleSelective(HashMap<String, Object> map);

    // ################### NEW ################### //
    // jungsu on 2017. 01. 05..
    List<MenuVo.Menu> selectMenu(MenuVo.Menu vo);
    void updateMenu(MenuVo.Menu vo);
    void insertMenu(MenuVo.Menu vo);
}
