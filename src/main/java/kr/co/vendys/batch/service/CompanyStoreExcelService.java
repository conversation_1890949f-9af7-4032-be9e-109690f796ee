package kr.co.vendys.batch.service;

import kr.co.vendys.batch.persist.captainPayment.CaptainPaymentRemote;
import kr.co.vendys.batch.service.MailService;
import kr.co.vendys.batch.vo.MailDto;
import kr.co.vendys.batch.vo.StoreCouponVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class CompanyStoreExcelService {

    private final CaptainPaymentRemote captainPaymentRemote;
    private final MailService mailService;
    
    @Value("${server.sikdae.mode}")
    private String serverMode;
    
    private static final Map<String, CompanyConfig> PRODUCTION_CONFIGS = new HashMap<>();
    private static final Map<String, CompanyConfig> TEST_CONFIGS = new HashMap<>();
    
    static {
        // 고정 참조 메일 주소 (운영환경)
        List<String> prodFixedEmails = Arrays.asList(
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        );
        
        // 삼성E&A 메일 발송 매장 설정 (운영환경)
        List<StoreInfo> fosecaStores = Arrays.asList(
            new StoreInfo("05F0565D-5A2C-07C5-A36C-746BBF1538D3", Arrays.asList("<EMAIL>")),
            new StoreInfo("0FE61722-7453-58B8-4841-45ED07E4EB88", Arrays.asList("<EMAIL>")),
            new StoreInfo("185C802C-F510-9D23-4CEF-CFC991CCDCFA", Arrays.asList("<EMAIL>")),
            new StoreInfo("1A281409-BEF7-A4DF-1588-7B0F51B93B57", Arrays.asList("<EMAIL>")),
            new StoreInfo("220CCDCE-6561-6736-3638-BB0C52491306", Arrays.asList("<EMAIL>")),
            new StoreInfo("583AEB27-5D92-4E2F-71E9-5CB2978FDA8A", Arrays.asList("<EMAIL>")),
            new StoreInfo("6CC2F142-C5DC-4E43-1509-E93A893E48E9", Arrays.asList("<EMAIL>")),
            new StoreInfo("72B6F0E4-F7F1-AE21-E6EC-08DB53C3F704", Arrays.asList("<EMAIL>")),
            new StoreInfo("7573F207-0F01-1DFB-2F7F-E0D6E16AAC62", Arrays.asList("<EMAIL>")),
            new StoreInfo("75780A1B-E132-1464-C438-F1D9DC4E191A", Arrays.asList("<EMAIL>")),
            new StoreInfo("A364D5A0-58D3-83E4-7DC1-4918C2211C8F", Arrays.asList("<EMAIL>", "<EMAIL>")),
            new StoreInfo("B35E53E1-47AF-BB93-D23A-DDC695733218", Arrays.asList("<EMAIL>", "<EMAIL>")),
            new StoreInfo("D0508CC0-34B4-6BBE-138D-5A5657767F8E", Arrays.asList("<EMAIL>")),
            new StoreInfo("D6B4C631-4938-305C-2DB9-5B3485C3890C", Arrays.asList("<EMAIL>")),
            new StoreInfo("FD714D79-7646-849E-D6A4-165563E7A6A4", Arrays.asList("<EMAIL>", "<EMAIL>")),
            new StoreInfo("02899E29-2DCC-D3B5-2427-E283B029F0FE", Arrays.asList("<EMAIL>")),
            new StoreInfo("6441D79C-1ED8-D4CF-55C1-F273BD769EE7", Arrays.asList("<EMAIL>")),
            new StoreInfo("71457C70-F477-58E0-E673-F522D1630BB4", Arrays.asList("<EMAIL>"))
        );
        PRODUCTION_CONFIGS.put("F0C2775D-D815-458B-A2AD-71FFDDC24C2D", 
                               new CompanyConfig("삼성E&A", prodFixedEmails, fosecaStores));
        
        // 테스트 환경 설정
        List<String> testFixedEmails = Arrays.asList(
            "<EMAIL>"
        );
        
        // 테스트용 매장 설정 (로컬 환경)
        List<StoreInfo> testStores = Arrays.asList(
            new StoreInfo("889700DE-F887-6A72-90A6-801F45072B63", Arrays.asList("<EMAIL>")),
            new StoreInfo("F0BFF7E5-A825-D51A-BFCC-A25A7BE869B0", Arrays.asList("<EMAIL>")),
            new StoreInfo("B7B61BEB-4787-AD23-94BF-2AF410AEE917", Arrays.asList("<EMAIL>")),
            new StoreInfo("E2801FAF-6AA3-7F5D-9EF3-FA3547AA3D27", Arrays.asList("<EMAIL>")),
            new StoreInfo("3885516B-C1EE-4E2F-D538-0A1942A06799", Arrays.asList("<EMAIL>")),
            new StoreInfo("16D7D3BD-C25F-942C-ACDC-FD8C3BC2339E", Arrays.asList("<EMAIL>"))
        );
        TEST_CONFIGS.put("13CB2F3C-F28E-40A4-82E4-C904656D1AE1", 
                         new CompanyConfig("테스트회사", testFixedEmails, testStores));
    }

    @Data
    @AllArgsConstructor
    private static class CompanyConfig {
        private String companyName;
        private List<String> fixedEmails;  // 고정 참조 메일 주소
        private List<StoreInfo> storeInfos;
    }

    @Data
    @AllArgsConstructor
    private static class StoreInfo {
        private String sid;
        private List<String> emails;  // 매장별 메일 주소
    }

    public void sendEmailBeforeMonth() {
        // 전월 데이터 조회 날짜 설정 (6월 1일이면 5월 1일~31일)
        LocalDate endDate = LocalDate.now().withDayOfMonth(1).minusDays(1);
        LocalDate startDate = endDate.withDayOfMonth(1);
        
        log.info("고객사<>매장간 월별 식대내역 데이터 처리 시작: {} ~ {}", startDate, endDate);
        
        // 모든 등록된 고객사에 대해 처리
        Map<String, CompanyConfig> configs = getCompanyConfigs();
        for (Map.Entry<String, CompanyConfig> entry : configs.entrySet()) {
            String comId = entry.getKey();
            CompanyConfig companyConfig = entry.getValue();
            
            log.info("{} 고객사 매장별 월별 식대내역 처리 시작 (comId: {})",
                    companyConfig.getCompanyName(), comId);
            
            processCompanyStores(comId, companyConfig, startDate, endDate);
            
            log.info("{} 고객사 월별 식대내역 처리 완료", companyConfig.getCompanyName());
        }
        
        log.info("전체 고객사<>매장간 월별 식대내역 데이터 처리 완료");
    }

    private void processCompanyStores(String comId, CompanyConfig companyConfig, 
                                    LocalDate startDate, LocalDate endDate) {
        for (StoreInfo storeInfo : companyConfig.getStoreInfos()) {
            try {
                StoreCouponVO.StoreRequest request = new StoreCouponVO.StoreRequest();
                request.setStartDate(startDate);
                request.setEndDate(endDate);
                request.setSid(storeInfo.getSid());
                
                StoreCouponVO.StoreCouponListResponse response = 
                    captainPaymentRemote.getStoreCoupons(comId, request);
                
                if (response != null) {
                    processStoreData(companyConfig, storeInfo, response, startDate, endDate);
                } else {
                    log.error("매장 쿠폰 데이터 조회 실패 - SID: {}", storeInfo.getSid());
                }
                
            } catch (Exception e) {
                log.error("매장 쿠폰 데이터 처리 중 오류 발생 - SID: {}, 오류: {}", 
                         storeInfo.getSid(), e.getMessage(), e);
            }
        }
    }

    private void processStoreData(CompanyConfig companyConfig, StoreInfo storeInfo, 
                                 StoreCouponVO.StoreCouponListResponse response, 
                                 LocalDate startDate, LocalDate endDate) {
        String storeName = response.getStoreName();
        List<String> storeEmails = storeInfo.getEmails();
        List<String> fixedEmails = companyConfig.getFixedEmails();
        
        log.info("매장 데이터 처리: {} (SID: {}), 쿠폰 수: {}, TO: {}, CC: {}", 
                storeName, storeInfo.getSid(), response.getTotalCount(), storeEmails, fixedEmails);
        
        try {
            // Excel 파일 생성
            byte[] excelData = createExcelFile(companyConfig.getCompanyName(), storeName, response, startDate);
            
            // 파일명 생성
            String fileName = createFileName(companyConfig.getCompanyName(), storeName, startDate);
            
            // 메일 발송 (TO: 매장별 이메일, CC: 고정 참조 이메일)
            sendStoreDataEmail(companyConfig.getCompanyName(), storeName, storeEmails, fixedEmails,
                             fileName, excelData, startDate);
            
            log.info("매장 {} Excel 파일 생성 및 메일 발송 완료 - 파일명: {}, TO: {}명, CC: {}명", 
                    storeName, fileName, storeEmails.size(), fixedEmails.size());
            
        } catch (Exception e) {
            log.error("매장 {} Excel 파일 생성 또는 메일 발송 실패: {}", storeName, e.getMessage(), e);
        }
    }

    private byte[] createExcelFile(String companyName, String storeName, 
                                  StoreCouponVO.StoreCouponListResponse response, 
                                  LocalDate startDate) throws IOException {

        try (Workbook workbook = new XSSFWorkbook()) {
            // 시트1: 결제 건 데이터
            createPaymentDataSheet(workbook, response.getStoreCoupons());

            // 시트2: 부서별 매출 통계
            createDepartmentStatsSheet(workbook, response.getDepartmentStats());

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }

    private void createPaymentDataSheet(Workbook workbook, List<StoreCouponVO.StoreCouponData> paymentData) {
        Sheet sheet = workbook.createSheet("결제 건 데이터");
        
        // 헤더 스타일 생성
        CellStyle headerStyle = createHeaderStyle(workbook);
        
        // 헤더 행 생성
        Row headerRow = sheet.createRow(0);
        String[] headers = {"결제번호", "원결제번호", "결제시간", "회사명", "부서", "결제자", "메뉴명", "식대금액", "간편결제", "총 결제금액"};
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // 데이터 행 생성
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        int rowNum = 1;
        
        if (paymentData != null) {
            for (StoreCouponVO.StoreCouponData data : paymentData) {
                Row row = sheet.createRow(rowNum++);
                
                row.createCell(0).setCellValue(data.getPayRoomIdx() != null ? data.getPayRoomIdx() : 0);
                row.createCell(1).setCellValue(data.getOriginPayRoomIdx() != null ? data.getOriginPayRoomIdx() : 0);
                row.createCell(2).setCellValue(data.getUseDate() != null ? dateFormat.format(data.getUseDate()) : "");
                row.createCell(3).setCellValue(data.getComName() != null ? data.getComName() : "");
                row.createCell(4).setCellValue(data.getDepartment() != null ? data.getDepartment() : "");
                row.createCell(5).setCellValue(data.getUsername() != null ? data.getUsername() : "");
                row.createCell(6).setCellValue(data.getMenuName() != null ? data.getMenuName() : "");
                row.createCell(7).setCellValue(data.getCompanyPrice() != null ? data.getCompanyPrice() : 0);
                row.createCell(8).setCellValue(data.getInstantPayPrice() != null ? data.getInstantPayPrice() : 0);
                row.createCell(9).setCellValue(data.getPrice() != null ? data.getPrice() : 0);
            }
        }
        
        // 합계 행 추가
        if (paymentData != null && !paymentData.isEmpty()) {
            CellStyle totalStyle = createTotalStyle(workbook);
            
            // 빈 행 추가
            rowNum++;
            
            // 합계 행 생성
            Row totalRow = sheet.createRow(rowNum);
            Cell totalLabelCell = totalRow.createCell(0);
            totalLabelCell.setCellValue("합계");
            totalLabelCell.setCellStyle(totalStyle);
            
            // 식대금액 합계 (컬럼 인덱스 7)
            Cell companyPriceTotal = totalRow.createCell(7);
            companyPriceTotal.setCellFormula("SUM(" + CellReference.convertNumToColString(7) + "2:" + 
                                           CellReference.convertNumToColString(7) + (rowNum - 1) + ")");
            companyPriceTotal.setCellStyle(totalStyle);
            
            // 간편결제 합계 (컬럼 인덱스 8)
            Cell instantPayPriceTotal = totalRow.createCell(8);
            instantPayPriceTotal.setCellFormula("SUM(" + CellReference.convertNumToColString(8) + "2:" + 
                                              CellReference.convertNumToColString(8) + (rowNum - 1) + ")");
            instantPayPriceTotal.setCellStyle(totalStyle);
            
            // 총 결제금액 합계 (컬럼 인덱스 9)
            Cell priceTotal = totalRow.createCell(9);
            priceTotal.setCellFormula("SUM(" + CellReference.convertNumToColString(9) + "2:" + 
                                    CellReference.convertNumToColString(9) + (rowNum - 1) + ")");
            priceTotal.setCellStyle(totalStyle);
        }
        
        // 컬럼 너비 자동 조정
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    private void createDepartmentStatsSheet(Workbook workbook, List<StoreCouponVO.DepartmentSalesStats> departmentStats) {
        Sheet sheet = workbook.createSheet("부서별 매출 통계");
        
        // 헤더 스타일 생성
        CellStyle headerStyle = createHeaderStyle(workbook);
        
        // 헤더 행 생성
        Row headerRow = sheet.createRow(0);
        String[] headers = {"고객사명", "부서", "식대금액", "간편결제", "총 결제금액"};
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // 데이터 행 생성
        int rowNum = 1;
        
        if (departmentStats != null) {
            for (StoreCouponVO.DepartmentSalesStats stats : departmentStats) {
                Row row = sheet.createRow(rowNum++);
                
                row.createCell(0).setCellValue(stats.getComName() != null ? stats.getComName() : "");
                row.createCell(1).setCellValue(stats.getDepartment() != null ? stats.getDepartment() : "");
                row.createCell(2).setCellValue(stats.getCompanyPrice() != null ? stats.getCompanyPrice() : 0);
                row.createCell(3).setCellValue(stats.getInstantPayPrice() != null ? stats.getInstantPayPrice() : 0);
                row.createCell(4).setCellValue(stats.getTotalPrice() != null ? stats.getTotalPrice() : 0);
            }
        }
        
        // 합계 행 추가
        if (departmentStats != null && !departmentStats.isEmpty()) {
            CellStyle totalStyle = createTotalStyle(workbook);
            
            // 빈 행 추가
            rowNum++;
            
            // 합계 행 생성
            Row totalRow = sheet.createRow(rowNum);
            Cell totalLabelCell = totalRow.createCell(0);
            totalLabelCell.setCellValue("합계");
            totalLabelCell.setCellStyle(totalStyle);
            
            // 식대금액 합계 (컬럼 인덱스 2)
            Cell companyPriceTotal = totalRow.createCell(2);
            companyPriceTotal.setCellFormula("SUM(" + CellReference.convertNumToColString(2) + "2:" + 
                                           CellReference.convertNumToColString(2) + (rowNum - 1) + ")");
            companyPriceTotal.setCellStyle(totalStyle);
            
            // 간편결제 합계 (컬럼 인덱스 3)
            Cell instantPayPriceTotal = totalRow.createCell(3);
            instantPayPriceTotal.setCellFormula("SUM(" + CellReference.convertNumToColString(3) + "2:" + 
                                              CellReference.convertNumToColString(3) + (rowNum - 1) + ")");
            instantPayPriceTotal.setCellStyle(totalStyle);
            
            // 총 결제금액 합계 (컬럼 인덱스 4)
            Cell totalPriceTotal = totalRow.createCell(4);
            totalPriceTotal.setCellFormula("SUM(" + CellReference.convertNumToColString(4) + "2:" + 
                                         CellReference.convertNumToColString(4) + (rowNum - 1) + ")");
            totalPriceTotal.setCellStyle(totalStyle);
        }
        
        // 컬럼 너비 자동 조정
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        
        // 폰트 설정
        Font font = workbook.createFont();
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(font);
        
        // 배경색 설정
        style.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        // 정렬 설정
        style.setAlignment(HorizontalAlignment.CENTER);
        
        // 테두리 설정
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        
        return style;
    }

    private CellStyle createTotalStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        
        // 폰트 설정
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        
        // 정렬 설정
        style.setAlignment(HorizontalAlignment.CENTER);
        
        // 테두리 설정
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        
        return style;
    }

    private String createFileName(String companyName, String storeName, LocalDate startDate) {
        String yearMonth = startDate.format(DateTimeFormatter.ofPattern("yyyyMM"));
        return String.format("%s_%s %s 월 매출 데이터.xlsx", yearMonth, companyName, storeName);
    }

    private void sendStoreDataEmail(String companyName, String storeName, List<String> toRecipients, 
                                   List<String> ccRecipients, String fileName, byte[] excelData, LocalDate startDate) {
        try {
            // 메일 제목 생성
            String yearMonth = startDate.format(DateTimeFormatter.ofPattern("yyyyMM"));
            String subject = String.format("%s_%s %s 월 매출 데이터 발송의 건", yearMonth, companyName, storeName);
            
            // 메일 본문 생성
            String emailBody = createEmailBody(companyName, storeName, startDate);
            
            // MailDto 생성
            MailDto.MailWithAttachmentDto mailDto = new MailDto.MailWithAttachmentDto();
            mailDto.setFrom("<EMAIL>");
            mailDto.setTo(String.join(",", toRecipients));
            if (!CollectionUtils.isEmpty(ccRecipients)) {
                mailDto.setCc(String.join(",", ccRecipients));
            }
            mailDto.setSubject(subject);
            mailDto.setBodyHtml(emailBody);
            
            // 첨부파일 설정
            MailDto.AttachmentInfo attachment = new MailDto.AttachmentInfo(
                    fileName, 
                    excelData, 
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            );
            mailDto.setAttachments(Arrays.asList(attachment));
            
            // 메일 발송
            mailService.sendHtmlMailWithAttachments(mailDto);
            
            log.info("매장 {} 데이터 메일 발송 완료 - TO: {}명, CC: {}명", 
                    storeName, toRecipients.size(), ccRecipients != null ? ccRecipients.size() : 0);
            
        } catch (Exception e) {
            log.error("매장 {} 데이터 메일 발송 실패: {}", storeName, e.getMessage(), e);
            throw e;
        }
    }

    private String createEmailBody(String companyName, String storeName, LocalDate startDate) {
        String year = String.valueOf(startDate.getYear());
        String month = String.format("%02d", startDate.getMonthValue());
        
        return String.format(
                "안녕하세요<br/>" +
                "현대벤디스입니다.<br/><br/>" +
                "%s년 %s월 %s와 %s 간 월 매출 데이터를 발송해드립니다.<br/>" +
                "발송해드린 데이터 상 문제가 있을 경우 서비스운영파트 <EMAIL> 메일로 문의 부탁드립니다.<br/><br/>" +
                "감사합니다.",
                year, month, companyName, storeName
        );
    }

    private Map<String, CompanyConfig> getCompanyConfigs() {
        if ("RELEASE".equals(serverMode)) {
            return PRODUCTION_CONFIGS;
        } else {
            return TEST_CONFIGS;
        }
    }
}
