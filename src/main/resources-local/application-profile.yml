vendys:
  env: local
  app:
    install-url: https://sikd.ae/sign
  pageRequest:
    page: 0
    size: 1000

server:
  port: 9090
  sikdae:
    mode: LOCAL

spring:
  application:
    name: sikdae_batch
  batch:
    job:
      enabled: false
  mvc:
    view:
      prefix: /WEB-INF/jsp/
      suffix: .jsp
  data:
    mongodb:
      database: sikdae-batch
      host: localhost
      port: 27017
  jpa:
    hibernate:
      format_sql: true
      use_sql_comments: true
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
  datasource:
    hikari:
      vendys:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          #          jdbc-url: *************************************************************************************************************************************************
          jdbc-url: ****************************************************************************************************************************************
          username: ENC(FkZtc+Q7xiWv9eeAWTB2Xm0U5pSbqmk8)
          password: ENC(AjDeRTjieYKuA+bRRk59fb3yQkCI6fp0AzTp0dJt32A=)
          pool-name: SIKDAE-MASTER-POOL
          maximum-pool-size: 20
          connection-init-sql: SELECT 1
          connection-test-query: SELECT 1
          connection-timeout: 120000
          validation-timeout: 120000
        slave:
          driver-class-name: com.mysql.cj.jdbc.Driver
          #          jdbc-url: ***************************************************************************************************************************************************
          jdbc-url: ****************************************************************************************************************************************
          username: ENC(FkZtc+Q7xiWv9eeAWTB2Xm0U5pSbqmk8)
          password: ENC(AjDeRTjieYKuA+bRRk59fb3yQkCI6fp0AzTp0dJt32A=)
          pool-name: SIKDAE-SLAVE-POOL
          maximum-pool-size: 20
          connection-init-sql: SELECT 1
          connection-test-query: SELECT 1
          connection-timeout: 120000
          validation-timeout: 120000
          read-only: true
        statistics:
          driver-class-name: com.mysql.cj.jdbc.Driver
          #          jdbc-url: *************************************************************************************************************************************************
          jdbc-url: ****************************************************************************************************************************************
          username: ENC(FkZtc+Q7xiWv9eeAWTB2Xm0U5pSbqmk8)
          password: ENC(AjDeRTjieYKuA+bRRk59fb3yQkCI6fp0AzTp0dJt32A=)
          pool-name: STATISTICS-POOL
          maximum-pool-size: 20
          connection-init-sql: SELECT 1
          connection-test-query: SELECT 1
          connection-timeout: 120000
          validation-timeout: 120000
        slow-query:
          driver-class-name: com.mysql.cj.jdbc.Driver
          jdbc-url: ************************************************************************************************************************************************
          username: ENC(Dp2DXbFd8I+d3ev9yHE8UoqvwSyQl5iH)
          password: ENC(lq3syzbUaHTz2cFE/q9MTipEGTVEhmGIpDvptLDzpYo=)
          pool-name: SIKDAE-SLOW-QUERY-POOL
          maximum-pool-size: 4
          connection-init-sql: SELECT 1
          connection-test-query: SELECT 1
          read-only: true
        slow-query-ro:
          driver-class-name: com.mysql.cj.jdbc.Driver
          jdbc-url: ************************************************************************************************************************************************
          username: ENC(Dp2DXbFd8I+d3ev9yHE8UoqvwSyQl5iH)
          password: ENC(lq3syzbUaHTz2cFE/q9MTipEGTVEhmGIpDvptLDzpYo=)
          pool-name: SIKDAE-SLOW-QUERY-RO-POOL
          maximum-pool-size: 4
          connection-init-sql: SELECT 1
          connection-test-query: SELECT 1
          read-only: true
        slow-query-bigven:
          driver-class-name: com.mysql.cj.jdbc.Driver
          jdbc-url: ************************************************************************************************************************************************
          username: ENC(Dp2DXbFd8I+d3ev9yHE8UoqvwSyQl5iH)
          password: ENC(lq3syzbUaHTz2cFE/q9MTipEGTVEhmGIpDvptLDzpYo=)
          pool-name: SIKDAE-SLOW-QUERY-BIGVEN-POOL
          maximum-pool-size: 4
          connection-init-sql: SELECT 1
          connection-test-query: SELECT 1
          read-only: true
      settlement:
        driver-class-name: org.mariadb.jdbc.Driver
        jdbc-url: ************************************
        username: ENC(ziZ0iGJbdRXisTVUL+xDPg==)
        password: ENC(Dolj4F/W3lox+lJ1ZXHOKs2gBWm+2UdB)
        pool-name: SETTLE-MASTER-POOL
        maximum-pool-size: 5
        connection-init-sql: SELECT 1
        connection-test-query: SELECT 1
        connection-timeout: 120000
        validation-timeout: 120000
ucloud:
  filebox: Test-Sikdae
  id: <EMAIL>
  key: ********************************

aws:
  s3:
    access: ********************
    bucket: vendys.develop/attachments/store/{storeId}/menu
    region: ap-northeast-2
    secret: 5jQC2CDcY2A9RxLByMA4glyJhEXJDVu0/TXU8FFh
    url: https://s3.ap-northeast-2.amazonaws.com
  sqs:
    host:
      payment: https://sqs.ap-northeast-2.amazonaws.com/064020620436/develop-sikdae-payment
      sikdae: https://sqs.ap-northeast-2.amazonaws.com/064020620436/develop-sikdae-payment-daily-point.fifo

custom:
  rest:
    connection:
      connect-timeout: 20000
      connection-request-timeout: 20000
      read-timeout: 20000

file:
  conimgpath: /Users/<USER>/Downloads/con_images
  snackimgpath: /Users/<USER>/Downloads/snack_images

invoice:
  link: http://dev-corp.mealc.co.kr/main/bill
  mainLink: https://dev-corp.mealc.co.kr/

signup:
  url: https://dev-signup.mealc.co.kr

oauth:
  host: https://dev-auth.mealc.co.kr
  xUserAgent: Vendys/1.0 {"client":"Batch"}

payment:
  service:
    host: https://dev-payment.mealc.co.kr
    #host: http://localhost:18010

captainPayment:
  service:
#    host: https://dev-corp-api.mealc.co.kr
    host: http://localhost:18089

store-api:
  service:
    host: https://dev-store-api.mealc.co.kr
    secretKey: 06AEBBA6-C5C1-11EC-BDC6-02B6D2317B36
    client-id: E4B84769-1254-11ED-BDC6-02B6D2317B36

customer-api:
  service:
    #host: http://localhost:51101
    host: https://dev-organization.mealc.co.kr
    header:
      key: X-Customer
      userId: SYSTEM
      value:
        client: Batch
        version: 1.0

settlement-api:
  service:
    #    host: https://dev-settlement.mealc.co.kr
    host: http://localhost:18080
    header:
      key: X-Settlement
      value:
        client: Batch
        version: 1.0

message-api:
  service:
    host: https://dev-message.mealc.co.kr
    sms:
      path: /sms
    header:
      key: X-Message
      client: Batch
      version: 1.0

sikdae-api:
  service:
    host: https://dev-mobile-api.mealc.co.kr
    #host: http://localhost:8081

qpcon:
  catelist: /qpcon/api/cateList.do
  cmd: cateList
  host: http://qpcon.dev.mealc.co.kr
  key: 1f77b506607c11e3b5ae00304860c864
  proddetail: /qpcon/api/prodDetail.do
  prodlist: /qpcon/api/prodList.do
  sendlist: /qpcon/api/sendList.do


slack:
  settlement:
    url: *******************************************************************************
  dev-msg:
    url: *******************************************************************************
  slow-query:
    url: *******************************************************************************

freshcode:
  storeid: CC08A583-5BF6-90B4-8869-69B1EFF61B55
  host: https://dev3-api.freshcode.me
  key: NDEzNDE6MTQxMjM1NzYyYjI0NGY5NjliMjBiMzMwMWVhZGQ5MmQ

sikdae:
  mail:
    api: https://api.mailgun.net/v3/mail.sikdae.com/messages
    id: <EMAIL>
    key: ************************************
    password: vnVw&73fej*#ejUEHVwlfj)_wf3
    excelpassword: qpseltm123!
    quick:
      to: '<EMAIL>'
      cc: '참조 <<EMAIL>>'
    settlement:
      to:
        all: '전체 <<EMAIL>>'
        group1:
          type: GAON
          target:
        group2:
          type: HANSOL, SUNGWONADPIA
          target: 'group2 <<EMAIL>>'
        group3:
          type: SMILE_GATE
          target: 'group3 <<EMAIL>>'
      bcc: '숨은참조 <<EMAIL>>'
      from: '발신 <<EMAIL>>'
    iljin:
      to: '박소현 <<EMAIL>>'
      bcc: '숨은참조 <<EMAIL>>'
      from: '배치서비스 <<EMAIL>>'
    hanmi:
      to: '강민기 <<EMAIL>>'
      cc: '참조 <<EMAIL>>'
      bcc: '숨은참조 <<EMAIL>>'
      from: '배치서비스 <<EMAIL>>'
    hansol-point:
      to: '강민기 <<EMAIL>>'
      cc: '참조 <<EMAIL>>'
      from: '배치서비스 <<EMAIL>>'
    bighit:
      to: '<EMAIL>, <EMAIL>'
      cc: '<EMAIL>'
      bcc: '<EMAIL>'
      from: '식권대장 <<EMAIL>>'
  message:
    api:
      client: batchServer
      host: https://dev-message.mealc.co.kr
  organization:
    api:
      host: https://dev-organization.mealc.co.kr
      header:
        key: X-Customer
        value:
          client: Batch
          version: 1.0
  payment-service:
    api:
      host: https://dev-payment.mealc.co.kr/
      header:
        key: X-Payment
        value:
          client: Batch
          version: 1.0
  corp:
    api:
      host: https://dev-corp.mealc.co.kr
      header:
        key: X-Corp
        value:
          client: Batch
          version: 1.0
          key: zuycOZ5vxpMVLxR61K8D/AbsrH0S18/WzIs2XYEV/W8=
  webSocket:
    api:
      host: http://localhost:7777
      header:
        key: X-WebSocket
        value:
          client: Batch
          version: 1.0

gogoX:
  host: https://business-staging.gogox.co.kr
  header:
    a2b: false
    code: 3001042
    apikey: 47D335D09FC9BA00CF21A4EE8E35A76DB85BFC8C2B4CC1F2F0B47AAEC3D7F844
    userCode: *********

sso-use-company-id: 4ADFE4C9-579F-4F0B-A9D6-7849F8133FFE


web-hook-channel:
  settlement-comparison: dev_debug # 정산 대사 확인
  settlement-created-send-mail: dev_debug # 특정 고객사 정산정보 생성 및 메일 발송 알림
  settlement-info-sync: dev_debug # 정산 정보 동기화
  settlement-pay-sync: dev_debug # 결제 정보 동기화
  settlement-store-sync: dev_debug # 제휴점 정보 동기화
  settlement-division-sync: dev_debug # 부서 동기화 처리 및 검증
  settlement-company-store-sync: dev_debug # 고객사 제휴점 관계 검증






smailgate-group-company-id:
  smailgate: 7FE23F38-54CD-4FEE-AAA8-F62416751802
  super-creative: 77A0461B-7906-4BB7-8FAC-1981A4640295