import { COMPANY_API } from 'apis';

const PREFIX = '/captain-payment/benefit';

export const getBeltBanner = async () => {
  const { data } = await COMPANY_API.get(`${PREFIX}/pass/v1/belt-banner`);
  return data.beltBanners;
};

export const getRollingBanner = async () => {
  const { data } = await COMPANY_API.get(`${PREFIX}/pass/v1/rolling-banner`);
  return data.rollingBanner;
};

export const getBenefitBanner = async () => {
  const result = await COMPANY_API.get(`${PREFIX}/pass/v1/benefit-banner`);
  return result.data;
};

export const getBenefitService = async (params) => {
  const result = await COMPANY_API.get(`${PREFIX}/pass/v1/benefit-service`, { params });
  return result.data;
};

export const getBenefitServiceDetail = async (id) => {
  const { data } = await COMPANY_API.get(`${PREFIX}/pass/v1/benefit-service/${id}`);
  return data;
};

export const postBenefitInquiry = async (params) => {
  await COMPANY_API.post(`${PREFIX}/pass/v1/benefit-inquiry`, params);
};

export const applyBenefitService = async (params) => {
  await COMPANY_API.post(`${PREFIX}/pass/v1/benefit-service-apply`, params);
};
