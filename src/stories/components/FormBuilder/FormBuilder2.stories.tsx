import React from 'react';
import { Form, Input, Button } from 'antd';
import FormBuilder, { FormBuilderProps } from 'vcomponents/FormBuilder/index2';
import FormBuilderMin from 'vcomponents/FormBuilder/FormBuilderMin';
import { useForm } from 'antd/lib/form/Form';
import { action } from '@storybook/addon-actions';

import { requireRule } from 'decorators/validators/addressPost'

export default {
  title: 'vendys-components/Forms/FormBuilder-multi',
  component: FormBuilder,
  argTypes: {
    layout: {control: { type: "select", options: ["vertical", "horizontal"] } },
    onValuesChange: { action: 'clicked' },
  },
  parameters: { actions: { argTypesRegex: '^on.*' } },

};

const columns = [
  {id: 'test2', label: 'id', type: 'string', viewProps: {disabled: true}, name: 'test',
    rules: [{ required: true, message: 'Please input your username!' }]
  },
  {id: 'enum2', label: 'enum 테스트', type: 'enum', meta: {enumId: 'DeliveryStatus'}, name: 'enum',
    rules: [{ required: true, message: 'Please input your username!' }]
  },
  {id: 'email2', label: 'email', type: 'email', name: 'email'},
  {id: 'address', label: 'address', type: 'address', name: 'address',
    rules: [...requireRule]
  },
  {id: 'a2', label: 'sender.name', type: 'EnumSelector', name: 'a', viewProps: {enumId: 'ProductSizeType', disabled: true},
  rules: [{ required: true, message: 'Please input your username!' }] }
]

const Template: Story<FormBuilderProps> = (args) => {

  const [form] = Form.useForm();

  const validate = async ()=>{
    try{
      var valid = await form.validateFields();
    }
    catch(e){
      action(e)();
      console.log("### valid" , e)
    }

  }

  return <Form form={form} >
      {/* <FormBuilder {...args}>
        <div id="test">custom view </div>
      </FormBuilder> */}
      <FormBuilderMin {...args}>
        <div id="test">custom view </div>
      </FormBuilderMin>
      <FormBuilderMin {...args} columns={columns}>
        <div id="test">custom view </div>
      </FormBuilderMin>
      <Button onClick={validate} >validate</Button>
    </Form>
};

export const Common = Template.bind({});
Common.args = {
  colInRow:2,
  layout: 'horizontal',
  onValuesChange: (a,b,c)=>{
    console.log(a,b,c);
  },
  onFieldsChange: (a,b,c)=>{
    console.log(a,b,c);
  },
  data: {
    'test': 'asldfkjalskdfj'
  },
  columns:[
    {id: 'test', label: 'id', type: 'string', viewProps: {disabled: true}, name: 'test',
      rules: [{ required: true, message: 'Please input your username!' }]
    },
    {id: 'enum', label: 'enum 테스트', type: 'enum', meta: {enumId: 'DeliveryStatus'}, name: 'enum',
      rules: [{ required: true, message: 'Please input your username!' }]
    },
    {id: 'email2', label: 'email', type: 'email', name: 'email2'},
    {id: 'a', label: 'sender.name', type: 'EnumSelector', name: 'a', viewProps: {enumId: 'ProductSizeType', disabled: true},
    rules: [{ required: true, message: 'Please input your username!' }] }
  ]
};
