// Quick
export enum OUICK_ORDER_STATUS {
  ORDER_RECEIVED = '배송 접수',
  DRIVER_ASSIGNED = '배차 완료',
  PICKUP_COMPLETE = '픽업 완료',
  DELIVERY_COMPLETE = '배송 완료',
  ORDER_CANCELLED = '접수 취소'
}

// Quick
export enum OUICK_SEARCH_TYPE {
  ORDERID = '예약번호',
  NAME = '발/수신자명'
}

export enum ENUM_VEHICLE {
  MOTORCYCLE = '오토바이'
}

export enum ENUM_ORDER_TYPE {
  QUICK_EXPRESS = `퀵 급송(가장 빠른 배송)`,
  QUICK = '퀵(합리적인 요금)'
}

export enum DeliveryStatus {
  PENDING = '배송 접수',
  ACTIVE = '배차 완료',
  CANCELLED = '접수 취소',
  INCOMPLETED = '부분 완료',
  COMPLETED = '배송 완료',
  RETURN = '회차'
}

export enum SearchQuickType {
  ORDERID = '예약번호',
  NAME = '발/수신자명'
}

export enum ProductSizeType {
  DOCUMENTS = '소량의 서류',
  SMALLBOX = '소형',
  BIGBOX = '대형',
  OTHERS = '기타'
}

export enum CargoWeight {
  DAMAS = '최대 크기 : 110 * 160 * 110 / 최대 중량 : 450kg 이하 / 용도 : 오토바이로 적재가 위험한 취급 주의 상품 및 다령의 박스품',
  LABO = '최대 크기 : 120 * 180 * 180 / 최대 중량 : 500kg 이하 / 용도 : 세워서 운반해야하는 물품 (지붕 X)',
  VAN = '최대 크기 : 120 * 200 * 130 / 최대 중량 : 800kg 이하 / 용도 : 매우 높은 화물 안정성, 고가 물품에 적합',
  TON1_CARGO = '최대 크기 : 160 * 280 * 160 / 최대 중량 : 1100kg 이하 / 용도 : 적재칸에 지붕 없음',
  TON1_TAPCHA = '최대 크기 : 160 * 280 * 160 / 최대 중량 : 1100kg 이하 / 용도 : 적재칸에 지붕 있음 '
}

export enum WeatherType {
  NONE = '해당 없음',
  RAIN = '우천 시, 추가 운임 (오토바이만 해당)',
  SNOW = '강설 시, 추가운임 (오토바이만 해당)'
}

export enum UserStatus {
  ACTIVE = '활성',
  INACTIVE = '일시중지'
}

export enum UserStatusChanged {
  WITHDRAW = '계정 탈퇴',
  INACTIVE = '계정 일시중지',
  ACTIVE = '계정 일시정지 해제'
}

export enum DormantUserStatus {
  ACTIVE = '활성 사용자',
  INACTIVE = '일시중지',
  WITHDRAW = '탈퇴'
}

export enum UserPermission {
  USER = '사용자',
  ADMIN = '관리자',
  SUPER = '최고 관리자'
}

export enum UserSex {
  F = '여자',
  M = '남자'
  // O = '기타'
}

export enum ServiceType {
  SIKDAE = 'SIKDAE',
  QUICK = 'QUICK',
  FLOWER = 'FLOWER'
}

export enum StatDate {
  DAY = 'DAY',
  MONTH = 'MONTH'
}

export enum PaymentMethod {
  null = '지급 방식을 선택해주세요',
  YEAR = '연 단위 지급',
  MONTH = '월 단위 지급',
  EVENT = '1회성 이벤트'
}

export enum StatusType {
  DEAL = '거래',
  DEDUCTION = '차감',
  EXTINCTION = '소멸',
  PROVISION = '지급',
  CANCELDEAL = '취소거래'
}

export enum DateSearchType {
  REG = '신청일자로 검색',
  EXECUTE = '지급/차감일자로 검색'
}
