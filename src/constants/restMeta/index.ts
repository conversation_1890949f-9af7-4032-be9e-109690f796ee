import { getWelfareTaskStatus } from 'apis/captainpayment/welfare';
import { getWelfare } from 'apis/captainpayment/member';

export default {
  welfare: {
    taskStatus: {
      restApi: getWelfareTaskStatus,
      translate: (data) => data && data.status.reduce((a, c) => ({ ...a, [String(c.value)]: c.text }), {})
    },
    group: {
      restApi: getWelfare,
      translate: (data) => data && data.group.reduce((a, c) => ({ ...a, [String(c.idx)]: c.name }), {})
    }
  }
};
