@import (multiple) '../company.config';


/* 식권신청 승인/내역 */

.maf .approve-btns {
  .rem-px(margin-bottom, 20);
  position: relative;
}

.maf .approve-btns .member-count {
  .rem-px(font-size, @vendysH6);
  .foo(0, 15);
  margin: @return;
  font-weight: bold;
}

.maf .approve-btns > button{
  .rem-px(margin-left, @marginDefault8);
}

.select-btn {
  position: absolute !important;
  right: 0;
}

.maf .ReactTable .rt-tbody .rt-td
, .maf .ReactTable .rt-thead .rt-td, .ReactTable .rt-thead .rt-th {
  border-right: 0 !important;
  padding-left: 0 !important;
  text-align: left;
}

.maf .ReactTable .rt-table{
  overflow-x: auto;
  overflow-y: hidden;
}

.maf .ReactTable .rt-thead .rt-td, .maf .ReactTable .rt-thead .rt-th:first-child {
  text-align: center;
  .rem-px(padding-left, 16) !important;
  .rem-px(padding-top, 11) !important;

}

.maf .ReactTable .rt-tbody .rt-td:first-child {
  .rem-px(padding-left, 16) !important;
  .rem-px(padding-top, 10) !important;
  .rem-px(padding-bottom, 8) !important;
}

/* 식권신청 내역 - 반려*/

.reject-modal textarea {
  .rem-px(min-height, 126) !important;
}

.reject-modal .row {
  height: auto;
  overflow: hidden;
}

.reject-modal .row .left {
  .rem-px(width, 100);
  float: left;
  font-weight: bold;
}

.reject-modal .row .left span {
  .rem-px(height, 24);
  line-height: 2;
}

.reject-modal .row .right {
  .rem-px(width, 640);
  float: right;
}

/* 식대내역 */

.sikdae-list-detail .table-box {
  .rem-px(margin-top, @marginDefault16);
}

.sikdae-list-detail .header .excel {
  margin-left: auto;
}

.sikdae-list-detail .header .reserve {
  .rem-px(margin-left, @marginDefault16);
}

/* 식대 지급/차감 > 엑셀파일로 지급/차감하기 */


.deduct-excel .ReactTable.react-table {
  border-left : 0;
  border-right : 0;
}

.deduct-excel .ReactTable .rt-thead .rt-th:first-child {
  text-align: left;
  .rem-px(padding-left, 16) !important;
  .rem-px(padding-top, 14) !important;

}

.deduct-excel .ReactTable .rt-tbody .rt-td:first-child {
  .rem-px(padding-left, 16) !important;
  .rem-px(padding-top, 10) !important;
  .rem-px(padding-bottom, 8) !important;
}

.deduct-excel .ReactTable .rt-tbody .rt-td
, .sm .ReactTable .rt-thead .rt-td
, .ReactTable .rt-thead .rt-th {
  border-right: 0!important;
  padding-left: 0!important;
  // text-align: left;
}


.deduct-excel .download-excel {
  .rem-px(padding-bottom, 60);
  border-bottom: 1px solid @frameOutlineColor;
}
.deduct-excel .download-excel > button {
  .rem-px(width, 305);
  .rem-px(height, 48);
  .rem-px(font-size, @vendysH4);
  float: left;
}
.deduct-excel .download-excel > div {
  .rem-px(margin-left, @marginDefault16);
  .rem-px(margin-top, 4);
  float: left;
}
.deduct-excel .download-excel > div p {
  .rem-px(font-size, @vendysH5);
  margin-bottom: 0;
}

.deduct-excel .body-content {
  border-bottom: 4px solid @deepBlue;
}

.deduct-excel .deduct-excel-form {
  .rem-px(padding-bottom, @paddingDefault24);
  height: auto;
  overflow: hidden;
}
.deduct-excel .deduct-excel-form {
  width: 100%;
}
.deduct-excel .deduct-excel-form > div {
  .rem-px(margin-top, @marginDefault16);
  height: auto;
  overflow: hidden;
}
.deduct-excel .deduct-excel-form .left-txt {
  .rem-px(width, 150);
  font-weight: bold;
  float: left;
}
.deduct-excel .deduct-excel-form .deduct-text {
  .rem-px(margin-left, 150);
  .rem-px(width, 600);
  word-break: break-all;
  word-wrap: break-word;
}
.deduct-excel .deduct-excel-form .right-content {
  width: 100%;
}
.deduct-excel .deduct-excel-form .right-content .portion {
  float: left;
}
.deduct-excel .deduct-excel-form .right-content .portion p {
  .rem-px(height, 32);
  margin-bottom: 0 !important;
}
.deduct-excel .deduct-excel-form .right-content .portion .cm-time {
  .rem-px(margin-left, (@marginDefault8)-1);
}
.deduct-excel .deduct-excel-form .right-content .portion button {
  .rem-px(height, 32);
  .rem-px(margin-left, @marginDefault8);
  line-height: 0.5;
}
.deduct-excel .deduct-excel-form .right-content .show-time {
  .rem-px(font-size, @vendysH5);
  padding-bottom: 6px;
  text-align: left;
  font-weight: bold;
  clear: left;
}

.deduct-excel .deduct-excel-form .timepicker-bar {
  .rem-px(font-size, @vendysH7);
  .rem-px(width, 28);
  &{
    .foo(@paddingDefault8);
    padding-left: @return;
    padding-right: @return;
  }
  margin-right: 0;
}

.deduct-excel .bottom-btns {
  .rem-px(padding-top, @paddingDefault16);
}
.deduct-excel .bottom-btns > .cancel {
  .rem-px(width, 120);
}
.deduct-excel .bottom-btns > .complete {
  .rem-px(width, 200);
  float: right;
}

.deduct-excel .deduct-excel-form .dim.startDate
, .deduct-excel .deduct-excel-form .dim.endDate {
  pointer-events: none;
  opacity: 0.4;
}

/* 식대 지급/차감 */

.sgd .ui.tabular.menu.vendysFullTab a:nth-child(2):hover {
  border-top: 3px solid #d0021b;
}

.sgd .ui.tabular.menu.vendysFullTab .item:hover > span {

}

.sgd .top-menu .ui.steps .step .content {
  position: absolute;
  left: 18px;
}

.sgd .vendysFullTab.menu > .active.item.deduct {
  border-top-color: @red;
  color : @red;
}

.sgd .ui.steps:first-child {
  .rem-px(margin-top, @marginDefault8);
}
.sgd .ui.steps:first-child > .step.active > .icon {
  color : #ffffff;
}

.sgd .search-form {
  .foo(24, 18);
  padding : @return;
}

.sgd .search-form .select-btn {
  .rem-px(width, 200) !important;
  button {
    width: 100%;
  }
}

.sgd .search-form .ui.equal.width.grid .row>  .two.wide.column {
  .rem-px(width, 136)!important;
  line-height: 2.5;
  padding-right: 0;
}

.sgd .search-form .division-form {
  padding-bottom: 0;
}

.sgd .search-form .ui.equal.width.grid .row > .three.wide.column {
  .rem-px(width, 200) !important;
  padding-right: 0;

  .ui.selection.dropdown {
    width: 100%;
  }
}

.sgd .search-form {
  .ui.input.search-input > input
  , .ui.input.search-input > button {
    .rem-px(height, 32) !important;
  }

  .division-form {
    padding-top : 0;
  }
}

.sgd .info-form {
  border-bottom : 1px solid @standardGray;
  border-left : 1px solid @standardGray;
  border-right : 1px solid @standardGray;
}


.sgd .ReactTable.react-table {
  border-left : 0;
  border-right : 0;
}

.sgd .ReactTable .rt-table {
  // overflow: hidden;

}

.sgd .ReactTable .rt-tbody .rt-td:first-child {
  .rem-px(padding-left, 16) !important;
  .rem-px(padding-top, 10) !important;
  .rem-px(padding-bottom, 8) !important;
}

.sgd .ReactTable .rt-tbody .rt-td:last-child {
  // display: contents;
}
.sgd .ReactTable .rt-tbody .rt-td:last-child .inline.field {
  // .rem-px(margin-top, 6);
}

.sgd .ReactTable .rt-tbody .rt-td
, .sm .ReactTable .rt-thead .rt-td
, .ReactTable .rt-thead .rt-th {
  border-right: 0!important;
  padding-left: 0!important;
  // text-align: left;
}

.sgd .ReactTable .rt-table .rt-thead .header-input-box {
  bottom: 7px;
}

.sgd .ReactTable .rt-table .rt-thead .header-input-box .ui.selection.dropdown {
  .rem-px(width, 160);
  float: left;
}

.sgd .ReactTable .rt-thead .rt-th:first-child {
  text-align: left;
  .rem-px(padding-left, 16) !important;
  .rem-px(padding-top, 11) !important;
}

.sgd .ReactTable .red.warning.circle.icon {
  .rem-px(font-size, 24);
  line-height: 1.5;
}

.sgd .bottom-form {

  .rem-px(margin-top, 16);

  .ui.grid > .left.floated.five.wide.column {
    text-align: left;

    button {
      .rem-px(width, 224);
    }
  }
  .ui.grid > .right.floated.five.wide.column {
    text-align: right;

    button {
      .rem-px(width, 224);
    }
  }

  .ui.grid .total-text {
    font-weight: bold;
    .rem-px(margin-right, 8);
    .rem-px(font-size, 16);
  }
}

.sgd button
, .sgd input
, .sgd .ui.basic.label{
  .rem-px(height, 36) !important;
}

.sgd #allMoney {
  .rem-px(padding-top, 4);
  width : 50%
}

.sgd #allReason {
  .rem-px(padding-top, 4);
  width : 70%
}

.sgd .header-input-box > button {
  .rem-px(height, 36) !important;
}

.sgd .ui.basic.label {
  .rem-px(padding-top, 2);
}

.sgd .info-form .group-box {

  .three.wide.column.group {
    padding-right: 0;

    .vendysLeftMenu.group {
      border : none;
    }
  }

  .thirteen.wide.column.staff {
    padding-left: 0;

    .vendysLeftMenu.staff {
      border : none;
      margin-bottom: 0;
    }

  }
  .step-two-staff .infinite-scroll {
    // border-left: 1px solid @standardGray;
  }

  .step-two-staff .default-sel {
    .rem-px(width, 180);
    .rem-px(height, 36);
    .rem-px(margin-right, 10);
  }
}
.sgd .info-form {

  .title.policy-title .red.warning.circle.icon {
    vertical-align: top;
    .rem-px(font-size, 27);
  }

  .title.policy-title .checkmark.icon {
    .rem-px(font-size, 20);
  }

  .active.title.policy-title
  , .title.policy-title
  , .title.policy-title:hover {

    background-color: #eeeeee;

    .the-last-text {
      // position: absolute;
      // right: 40px;
    }
  }

  .step-three-staff .title1 > span {
    float : left;
    .rem-px(padding-top, 5);
    .rem-px(font-size, 16);
  }

  .step-three-staff .title2 {
    text-align: right;
  }

  .step-three-staff .title2 > span {
    .rem-px(font-size, 12);
  }

  .step-three-staff .value-null {
    border:1px solid #ff0000;
  }

  .step-three-staff .rt-tr input {
    width: 70%;
  }

  .step-three-staff .rt-tr .rt-td .money {
    width: 50%;
  }

  .step-three-staff .time-setting {
    display: block;
    text-align: left;
    margin : 0;
    .rem-px(padding-bottom, 20);
  }
  .step-three-staff .time-setting > span
  , .step-three-staff .money-setting > span{
    font-weight: bold;
    .rem-px(margin-left, @marginDefault16);
  }

  .step-three-staff .time-setting > button{
    .rem-px(width, 200);
  }
  .step-three-staff .money-setting > button  {
    .rem-px(width, 200);
  }

  .step-three-staff .money-setting {
    display: block;
    text-align: left;
    .rem-px(margin-top, 25);
  }

  .step-three-staff .calendar.disabled.icon {
    line-height: 2.5;
  }

  .cm-time i {
    .rem-px(height, 36) !important;
    line-height: 1.7 !important;
  }

  .step-three-staff .date-setting {
    .rem-px(padding-top, @paddingDefault16);

    .checkbox.pure {
      .rem-px(margin-right, @paddingDefault8);
    }
  }

  .step-three-staff .dim {
    pointer-events: none;
    opacity: 0.4;
  }

  .step-three-staff .react-datepicker__input-container {
    .rem-px(margin-right, @paddingDefault8);
    border-right: 1px solid #f0f0f0;
  }

  .step-three-staff .test-stick {
    .foo(0, 8);
    margin : @return;
  }

  .step-three-staff .date-btn1 {
    .foo(0, 8);
    margin : @return;
    .rem-px(width, 140);
    .rem-px(height, 32);
  }
  .step-three-staff .date-btn2 {
    .rem-px(width, 130);
    .rem-px(height, 32);
  }

  .step-three-staff .deduct-date-text {
    font-size: 14px;
    font-weight: bold;
    .rem-px(padding-top, 30);
    .rem-px(padding-bottom, @paddingDefault8);
    display: block;
  }

  .step-three-staff .count-line {
    padding: 0;
    margin: 0;
  }

  .step-three-staff .user-cnt{
    // .rem-px(width, 200);
    .rem-px(font-size, 16);
    font-weight: bold;
    color: #2f323b;
    padding: 16px 5px;
    display: block;
    float: left;
  }

  .step-three-staff .search-box {
    margin: 8px 35px;
    text-align: right;
  }
}

.icons.cm-time.dim {
  pointer-events: none;
  opacity: 0.4;
}

.sgd .step i.icon {
  height: 0;
}
.sgd .ui.steps .step>.icon {
  line-height: 0;
}

.rs .ReactTable .rt-tbody .rt-tr-group {
  border-bottom: 0px;
}

.rs .ReactTable .rt-tbody .rt-td {
  border-right: 0px;
}

.rs .ReactTable .rt-thead {
  padding-right: 10px;
}
.rs .ReactTable .rt-thead .rt-th:first-child {
  .rem-px(padding-left, 16) !important;
  .rem-px(padding-top, 10) !important;
  .rem-px(padding-bottom, 8) !important;
}

.rs .ReactTable .rt-tbody .rt-td:first-child {
  .rem-px(padding-left, 16) !important;
  .rem-px(padding-top, 10) !important;
  .rem-px(padding-bottom, 8) !important;
}

.rs .ReactTable .rt-tbody .rt-td.right, .rs .ReactTable .rt-thead .rt-th.right {
  text-align: right;
}

.rs .ReactTable .rt-thead .rt-th, .rs .ReactTable .rt-tbody .rt-td {
  padding-left: 0!important;
}

.sikdae-policy-mod .rc-time-picker-input {
  border-radius: 0 !important;
}

.reject-modal .ReactTable.react-table {
  border-left : 0;
  border-right : 0;
  border: 1px solid @outlineColor;
}

.reject-modal .ReactTable .rt-table {
  // overflow: hidden;

}

.reject-modal .ReactTable .rt-tbody .rt-td:first-child {
  .rem-px(padding-left, 16) !important;
  .rem-px(padding-top, 10) !important;
  .rem-px(padding-bottom, 8) !important;
}

.reject-modal .ReactTable .rt-tbody .rt-tr-group {
  cursor: default;
}
.reject-modal .ReactTable .rt-tbody .rt-td:last-child .inline.field {
  // .rem-px(margin-top, 6);
}

.reject-modal .ReactTable .rt-tbody .rt-td
, .ReactTable .rt-thead .rt-th {
  border-right: 0!important;
  padding-left: 0!important;
  // text-align: left;
}

.reject-modal .ReactTable .rt-tbody .rt-td.right,
.reject-modal .ReactTable .rt-thead .rt-th.right {
  text-align: right;
}

.reject-modal .ReactTable .rt-table .rt-thead .header-input-box {
  bottom: 7px;
}

.reject-modal .ReactTable .rt-table .rt-thead .header-input-box .ui.selection.dropdown {
  .rem-px(width, 160);
  float: left;
}

.reject-modal .ReactTable .rt-thead .rt-th:first-child {
  .rem-px(padding-left, 16) !important;
  .rem-px(padding-top, 11) !important;
}

.reject-modal .ReactTable .rt-tbody .rt-td:last-child {
  .rem-px(padding-right, 20) !important;
}

.reject-modal .ReactTable .rt-tbody .rt-tr-group .totaltr {
  background: #243346;
  color: #ffffff;
}


.reject-modal .ReactTable .red.warning.circle.icon {
  .rem-px(font-size, 24);
  line-height: 1.5;
}