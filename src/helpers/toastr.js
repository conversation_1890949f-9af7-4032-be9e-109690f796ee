import toastr from 'config/toastr'


/*
  pos :
    top-right, top-left, top-full-width, top-center
    , bottom-right, bottom-left, bottom-full-width, bottom-center

  theme :
    success,
    info,
    warning, - 사용자 error
    error - api,통신 error

  message : toastr 메세지

  ex)

    import toastr from 'helpers/toastr';
    toastr("top-right", "warning", "메세지 입력");
*/
function notify(pos, theme, message) {
    toastr.options.positionClass = 'toast-' + pos;
    toastr.options.extendedTimeOut = 0;
    toastr.options.timeOut = 5000;
    toastr.options.closeButton = true;
    toastr.options.iconClass = 'toast-' + theme;
    toastr['custom'](message);
}

export default notify
