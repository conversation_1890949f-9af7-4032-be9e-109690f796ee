// import moment from 'moment-timezone';
import moment from 'moment-timezone';
import storage from 'helpers/storage';
import { S_IFBLK } from 'constants';

moment.tz.setDefault('Asia/Seoul');
const commons = {};

commons.tableHeaderColumns = (column, idx, divisionText, width, customProps = {}) => {
  const depth = storage.get('depth');
  const columns = [...column];
  const w = width ? width : 100;
  for (let i = 0, length = depth; i < length; i++) {
    const item = {
      Header: `${divisionText}${i + 1}`,
      title: `${divisionText}${i + 1}`,
      accessor: `division${i + 1}`,
      dataIndex: `division${i + 1}`,
      key: `division${i + 1}`,
      width: w,
      ...customProps
    };
    columns.splice(idx + i, 0, item);
  }
  return columns;
};

commons.valueCheck = (params) => {
  if (params === null || typeof params === 'undefined' || params === '') {
    return true;
  }
  return false;
};

commons.emptyText = (params) => {
  if (params === null || typeof params === 'undefined' || params === '' || params === 'null') {
    return '';
  }
  return params;
};

commons.selectDataForMat = (params, defaultOpt = '선택하세요', type) => {
  const forMat = [];
  if (params.length > 0) {
    forMat.push({
      value: 0,
      text: defaultOpt
    });

    params.forEach((data) => {
      let idx = '';

      if (data.idx) {
        idx = data.idx;
      } else if (type === 'position') {
        idx = data.name;
      } else if (type === 'preset') {
        idx = data.presetIdx;
      } else if (type === 'status') {
        idx = data.value;
      } else if (data.id) {
        idx = data.id;
      }

      if (type === 'longterm') {
        if (data.type === 'LONGTERM') {
          forMat.push({
            value: idx,
            text: data.name
          });
        }
      } else if (type === 'status') {
        forMat.push({
          value: idx,
          text: data.text
        });
      } else if (type === 'rankposition') {
        forMat.push({
          value: data.name,
          text: data.name
        });
      } else if (type === 'company') {
        const isDisabled = data.version !== 'v1';
        const addText = data.version === 'v1' ? '(3.0)' : '(2.0)';
        forMat.push({
          value: idx,
          text: `${data.name} ${addText}`,
          disabled: isDisabled
        });
      } else {
        forMat.push({
          value: idx,
          text: data.name
        });
      }
    });

    return forMat;
  }
  return null;
};

// 날짜 형식 yyyy-MM-dd
commons.yyyyMMdd = (time) => {
  let result = '';

  if (time) {
    result = moment(time)
      .tz('Asia/Seoul')
      .format('YYYY-MM-DD');
  }

  return result;
};

// 날짜 형식 yyyy-MM
commons.yyyyMM = (time) => {
  let result = '';

  if (time) {
    const yyyy = time.getFullYear().toString();
    if (yyyy === '1970') {
      return '';
    }
    result = moment(time)
      .tz('Asia/Seoul')
      .format('YYYY-MM');
  }

  return result;
};

// 날짜 형식 MM-dd
commons.mmDD = (time) => {
  let result = '';

  if (time) {
    const yyyy = time.getFullYear().toString();
    if (yyyy === '1970') {
      return '';
    }
    result = moment(time)
      .tz('Asia/Seoul')
      .format('MM-DD');
  }

  return result;
};

// 시간 hh:mm:ss
commons.hhmmss = (time) => {
  const yyyy = time.getFullYear().toString();
  if (yyyy === '1970') {
    return '';
  }
  return moment(time)
    .tz('Asia/Seoul')
    .format('HH:mm:ss');
};

// 시간 hh:mm
commons.hhmm = (time, type) =>
  moment(time)
    .tz('Asia/Seoul')
    .format('HH:mm');

// 날짜 형식 yyyy-MM-dd
commons.yyyyMMddhhmm = (time) => {
  let result = '';

  if (time) {
    const yyyyMMdd = commons.yyyyMMdd(time);
    const hhmm = commons.hhmm(time);
    result = `${yyyyMMdd} ${hhmm}`;
  }

  return result;
};

// 금액 콤마
commons.numberComma = (num) => {
  if (num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
  return '0';
};

commons.isValidNumber = (value) => {
  return typeof value === 'number' && !Number.isNaN(value);
};

/**
 * 원,장 표시
 * @param  {[number]} num [통계 데이터]
 * @return {[string]}     [원을 쓰는 회사: 만단위 표시, 장 쓰는 회사: 장 단위 표시]
 */
commons.monetaryUnit = (num) => {
  const { company } = storage.get('company').user;
  let result = num;
  const unit = '만';

  if (num >= 10000) {
    result = `${commons.numberComma(num / 10000)}만`;
  } else {
    result = commons.numberComma(num);
  }
  // if(company.isTicketFormat) {
  //   result = result + '장';
  // }

  return result;
};

/**
 * [글자 ... 표시]
 * @param  {[string]} text    [ 변경 텍스트 ]
 * @param  {[int]}    length  [ 최대 글자 수 ]
 * @param  {[int]}    start   [ 노출 글자 수 ]
 * @return {[string]}         [ 완료 된 글자 ]
 */
commons.textNowrap = (text, length, start) => {
  let result = text;

  if (length < text.length) {
    result = text.substring(0, start);
    result += '...';
  }

  return result;
};
commons.unit = () => {
  const { company } = storage.get('company').user;
  let result = '원';

  if (company.isTicketFormat) {
    result = '장';
  }

  return result;
};

// 시간 비교
commons.timeComparison = (sDate, eDate) => {
  const time = new Date(eDate);
  const hh = Number(time.getHours());
  const MM = Number(time.getMinutes());

  let result = true;
  if (!(new Date(sDate) < new Date(eDate))) {
    // 00:00은 익일이 아님.
    if (hh === 0 && MM === 0) {
      result = true;
    } else {
      result = false;
    }
  }

  return result;
};

commons.timeComparisonV2 = (sTime, eTime) => {
  if (!sTime || !eTime) {
    return false;
  }
  try {
    const sHH = parseInt(sTime.substring(0, 2));
    const eHH = parseInt(eTime.substring(0, 2));
    if (sHH >= eHH) {
      return true;
    }
  } catch (e) {
    console.error(e);
    return false;
  }
};

commons.policyUsedTimeHourSplit = (startTime) => {
  try {
    return parseInt(startTime.substring(0, 2));
  } catch (e) {
    return;
  }
};
commons.policyUsedTimeMinSplit = (startTime) => {
  try {
    return parseInt(startTime.substring(3, 5));
  } catch (e) {
    return;
  }
};

/**
 * [parm_sDate description]
 * @type {[type]}
 */

commons.dataNextMonth = (addMonth) => {
  const month = new Date().getMonth() + addMonth;

  if (month === 13) {
    return 1;
  }
  return month;
};

/*
	날짜 조건 체크
	parm_sDate : 시작날짜
	parm_eDate : 종료날짜

	종료날짜가 시작날짜 보다 크면 false
	시작날짜와 종료날짜 사이가 90일보다 크면 false (3달까지 조회 가능)
*/
commons.dateCheck = (parm_sDate, parm_eDate, isDiffDay, maxDays = 92) => {
  const sDate = new Date(parm_sDate);
  const eDate = new Date(parm_eDate);
  const diffDay = parseInt((eDate - sDate) / (24 * 60 * 60 * 1000));

  const result = {
    isState: true,
    message: ''
  };

  if (sDate > eDate) {
    result.isState = false;
    result.message = '시작 날짜를 종료 날짜 이전으로 선택해주세요.';
  } else if (diffDay > maxDays && isDiffDay) {
    result.isState = false;
    result.message = `날짜 검색 조건 최대 범위는 ${maxDays}일까지 입니다.`;
  }
  return result;
};

// 숫자 -> 전화번호 형태
commons.phoneFormat = (num) => {
  let format;

  if (!num) {
    return '-';
  }

  if (num.length === 11) {
    format = num.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3');
  } else if (num.length === 10) {
    format = num.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3');
  } else if (num.length === 8) {
    format = num.replace(/(\d{4})(\d{4})/, '$1-$2');
  } else if (num.length === 7) {
    format = num.replace(/(\d{3})(\d{4})/, '$1-$2');
  } else if (num.indexOf('02') == 0) {
    format = num.replace(/(\d{2})(\d{4})(\d{4})/, '$1-$2-$3');
  } else {
    format = num.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3');
  }
  return format;
};

/**
 * [ 문자열을 받아 integer로 변환한다. ]
 * @param  {[string]} value [ 변수 ]
 * @return {[type]}       [description]
 */
commons.toInt = (value) => {
  const num = parseInt(value.replace(/,/gi, ''));

  if (isNaN(num)) {
    return 0;
  }
  return num;
};

// 이메일 validation check
commons.validateEmail = (email) => {
  //   var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(email);
};

// 핸드폰 validation check
commons.validatePhone = (phone) => {
  const re = /^0[0-9]{1,2}-[0-9]{3,4}-[0-9]{4}$/;
  return re.test(phone);
};
commons.validatePhone2 = (phone) => {
  const re = /^0[0-9]{1,2}[0-9]{3,4}[0-9]{4}$/;
  return re.test(phone);
};

// 핸드폰, 이메일 배열 유효성 검사
commons.validCheck = (arr) => {
  const checkArr = {
    phone: [],
    email: [],
    except: []
  };

  arr.forEach((value) => {
    if (commons.validatePhone(value)) {
      checkArr.phone.push(value);
    } else if (commons.validateEmail(value)) {
      checkArr.email.push(value);
    } else {
      checkArr.except.push(value);
    }
  });

  return checkArr;
};

commons.scrollLastEvent = (id) => {
  // 현재문서의 높이
  const scrollHeight = Math.max(document.documentElement.scrollHeight, document.getElementById(id).scrollHeight);

  // 현재 스크롤탑의 값
  const scrollTop = Math.max(document.documentElement.scrollTop, document.getElementById(id).scrollTop);

  // 현재 화면 높이 값
  const { clientHeight } = document.getElementById(id);

  if (scrollTop + clientHeight == scrollHeight) {
    // 스크롤이 마지막일때
    return true;
  }
  return false;
};

// 상단 타이틀
commons.mainTitle = (title) => {
  document.getElementsByClassName('main-title')[0].textContent = title;
};

// json을 QueryString으로 변환
commons.jsonToQueryString = (json) =>
  Object.keys(json)
    .filter((key) => json[key] != null && json[key] != '')
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(json[key])}`)
    .join('&');

// Object map형식에 데이터 추가
commons.arrayPush = (obj, pushData) => {
  Object.keys(pushData).forEach((key) => {
    if (pushData[key]) {
      obj[key] = pushData[key];
    }
  });

  return obj;
};

// division checked
commons.divisionCheck = (depth, data) => {
  if (data) {
    switch (depth) {
      case 1:
        if (data && data.name) {
          return data.name;
        }
        return '';

        break;
      case 2:
        if (data.division && data.division.name) {
          return data.division.name;
        }
        return '';

        break;
      case 3:
        if (data.division && data.division.division && data.division.division.name) {
          return data.division.division.name;
        }
        return '';

        break;
      default:
        return '';
        break;
    }
  } else {
    return '';
  }
};

commons.convertMinusNumber = (number) => {
  let result = commons.numberComma(number);
  if (number < 0) {
    const temp = -number;
    result = `(-) ${commons.numberComma(temp)}`;
  }

  return result;
};

commons.remConverter = (num) => {
  const basicFontSize = 14;
  let result = 0;

  if (num > 0) {
    result = num / basicFontSize;
  }

  return result;
};

// props 초기화
commons.propsInit = (props) => {
  if (props.date) {
    props.date.sDate = moment()
      .startOf('month')
      .format('LLL');
    props.date.eDate = moment().format('LLL');
  }

  if (props.depth) {
    props.depth.value = null;
  }

  if (props.orgIdx) {
    props.orgIdx.value = null;
  }

  if (props.dateType) {
    props.dateType.value = 'MM';
  }

  if (props.store) {
    props.store.value = null;
  }

  if (props.statusStr) {
    props.statusStr.value = null;
    props.statusStr.type = null;
    props.statusStr.status = null;
  }

  if (props.text) {
    props.text.value = null;
  }

  if (props.groupIdx) {
    props.groupIdx.value = null;
  }

  if (props.policyIdx) {
    props.policyIdx.value = null;
  }

  if (props.preset) {
    props.preset.value = null;
  }

  if (props.preset) {
    props.preset.value = null;
  }
};

commons.removeDup = (originalArray, detArray, prop) => {
  const newArray = [];
  const lookupObject = {};
  detArray.forEach((item) => {
    originalArray.some((sItem, idx) => {
      if (item.id === sItem.id) {
        originalArray.splice(idx, 1);
        return true;
      }
    });
  });
  return originalArray;
};
// 사용자 checked 데이터 반환
commons.staffCheckedData = (e, staffData, isChecked, type, allData) => {
  const data = JSON.parse(JSON.stringify(staffData));
  let resultData = [];
  switch (type) {
    case 'all':
      if (isChecked) {
        data.forEach((item) => {
          const status = typeof item.status === 'string' ? item.status : item.status.code;
          resultData.push({
            id: item.id,
            name: item.name,
            signid: item.signid || item.signId,
            status
          });
        });
      } else {
        resultData = [];
      }

      break;

    case 'all-filter':
      const uniqueArray = Object.values(data.reduce((acc, cur) => Object.assign(acc, { [cur.id]: cur }), {}));
      if (isChecked) {
        uniqueArray.forEach((item) => {
          let isDisabled = false;
          const policyIdx = 0; // 장기 idx

          // 장기가 있으면 true
          isDisabled = item.policy.some((data) => {
            item.sPolicy = data.idx;
            return data.type === 'LONGTERM';
          });

          // 그룹이 있으면 true
          if (isDisabled && item.group) {
            isDisabled = true;
          } else {
            isDisabled = false;
          }

          if (isDisabled) {
            resultData.push(item);
          }
        });
      } else {
        const detData = commons.removeDup(uniqueArray, allData, 'id');
        $('.staff-checkbox').prop('checked', isChecked);
        resultData = detData;
      }
      break;

    case 'each':
      let item2 = e;

      if (isChecked) {
        const isAdd = data.filter((dataItem) => dataItem.id === item2.id);
        if (isAdd.length === 0) {
          const signid = item2.className.split(/\s+/)[1];

          data.push({
            id: item2.id,
            name: item2.name,
            signid,
            status: item2.dataset.status
          });
        }
        resultData = data;
      } else {
        data.some((item2Data, idx) => {
          if (item2Data.id === item2.id) {
            data.splice(idx, 1);
          }
        });
        resultData = data;
      }
      break;

    case 'each-filter':
      var item = e;

      if (isChecked) {
        let temp = null;
        const isAdd = data.filter((dataItem) => {
          if (dataItem.id === item.id) {
            temp = data;
          }
        });

        if (isAdd.length === 0) {
          allData.some((dataItem) => {
            // 장기가 있으면 true

            const isDisabled = dataItem.policy.some((item) => {
              dataItem.sPolicy = item.idx;
              return item.type === 'LONGTERM';
            });

            if (dataItem.id === item.id) {
              data.push(dataItem);
            }
          });
        }
        resultData = data;
      } else {
        data.some((dataItem, idx) => {
          if (dataItem.id === item.id) {
            data.splice(idx, 1);
          }
        });
        resultData = data;
      }
      break;

    default:
      return resultData;
  }
  return resultData;
};

// 선택 된 부서 네이게이션
/**
 * [ 부서 navi 생성 (재귀호출)]
 * @param  {[array]} division   [ 회서 부서 정보 ]
 * @param  {[String]} parentName  [ 상위 부서 navi ]
 * @return {[array]}             [ 회사 부서 navi ]
 */
commons.divisionNavigation = (division, parentName) => {
  const navi = [];
  let name = null;

  if (division) {
    for (let i = 0, { length } = division; i < length; i++) {
      const temp = division[i];

      if (parentName) {
        name = `${parentName}>${temp.name}`;
      } else {
        name = temp.name;
      }

      if (temp.division) {
        const depth = commons.divisionNavigation(temp.division, name);

        if (depth) {
          depth.forEach((data) => {
            navi.push(data);
          });
        }
      }

      navi.push({
        orgIdx: temp.orgIdx,
        orgCode: temp.orgCode,
        name,
        depth: name.split('>').length
      });
    }
  } else {
    console.log('##### end');
  }

  return navi;
};

/**
 * [사용자별 부서 조회]
 * @param  {[string]} orgCode  [ 사용자 부서 orgCode ]
 * @return {[string]}         [ 사용자 부서 ]
 */
commons.staffDivision = (orgCode) => {
  const navi = storage.get('navi');
  let result = null;

  navi.some((data) => {
    if (data.orgCode === orgCode) {
      const divisionArr = data.name.split('>');
      result = divisionArr[data.depth - 1];
    }
  });
  return result;
};

// 부서별 네비게이션 생성
commons.staffDivisionNavigation = (depth, navi) => {
  storage.set('pastDepth', depth);
  storage.set('pastNavi', navi);
  storage.set('navi', navi);
  storage.set('depth', depth);
};

/**
 * [해당 orgCode의 navi]
 * @param  {[String]} orgCode [ 선택 된 부서 orgCode ]
 * @return {[String]}         [ full navi ]
 */
commons.divisionNavi = (orgCode, isPast) => {
  const navi = isPast ? storage.get('pastNavi') : storage.get('navi');
  let result = null;

  navi.some((data) => {
    if (data.orgCode === orgCode) {
      result = data.name;
    }
  });

  return result;
};

// 부서 header list
commons.tableHeaderList = (header, index, isChangeColor, type, pDepth, isPast) => {
  const depth = pDepth || (isPast ? storage.get('pastDepth') : storage.get('depth'));
  const result = JSON.parse(JSON.stringify(header));

  if (type === 'default') {
    for (let i = 0, length = depth; i < length; i++) {
      result.splice(index + i, 0, {
        data: `부서${i + 1}`,
        textAlign: 'left'
      });
    }
  } else if (type === 'stat') {
    for (let i = 0, length = depth; i < length; i++) {
      result.splice(index + i, 0, {
        content: `부서${i + 1}`,
        textAlign: 'left',
        maxWidth: 150
      });
    }
  } else if (type === 'notMaxWidth') {
    for (let i = 0, length = depth; i < length; i++) {
      result.splice(index + i, 0, {
        header: `부서${i + 1}`,
        width: 128,
        notMaxWidth: true
      });
    }
  } else if (isChangeColor) {
    for (let i = 0, length = depth; i < length; i++) {
      result.splice(index + i, 0, {
        header: `부서${i + 1}`,
        width: 128,
        changeColor: true
      });
    }
  } else {
    for (let i = 0, length = depth; i < length; i++) {
      result.splice(index + i, 0, { header: `부서${i + 1}`, width: 128 });
    }
  }

  return result;
};

commons.colWidth = (colwidth, index) => {
  const depth = storage.get('depth');

  for (let i = 0, length = depth; i < length; i++) {
    colwidth.splice(index + i, 0, 128);
  }

  return colwidth;
};

// default 부서 사용자별 데이터 set
/**
 * 부서 사용자별 데이터 set
 * @param  {[object]}   item         [ 테이블 row ]
 * @param  {[intarger]} index        [ 부서 시작 지점 ]
 * @param  {[string]}   code         [ orgcode 또는 orgidx ]
 * @param  {[string]}   type         [ type 또는 depthIndex ]
 * @param  {[array]}    fulldivision [ fulldivision ]
 * @param  {[boolean]}    isPast       [ 과거 division 정보 조회 ]
 * @return {[object]}                [ 부서가 추가 된 테이블 row ]
 */
commons.defalutTableStaffDivisionSet = (item, index, code, type, fulldivision, isPast) => {
  const temp = item;
  const result = {};
  let depth = isPast ? storage.get('pastDepth') : storage.get('depth');
  const navi = isPast ? storage.get('pastNavi') : storage.get('navi');

  let staffDivision = null;
  let division = null;
  if (!navi) {
    result.row = item;
    return result;
  }
  if (code) {
    if (type === 'stat') {
      if (code) {
        navi.some((data) => {
          if (data.orgIdx === code) {
            staffDivision = data.name;
          }
        });
      }
    } else if (code) {
      navi.some((data) => {
        if (data.orgCode === code) {
          staffDivision = data.name;
        }
      });
    }

    if (staffDivision) {
      division = staffDivision.split('>');
    }
  } else if (fulldivision) {
    division = fulldivision;
    depth = type;
  }

  for (let i = 0, length = depth; i < length; i++) {
    const data = { data: division ? division[i] : '', textAlign: 'left' };
    temp.splice(index + i, 0, data);
  }

  result.row = temp;

  return result;
};

commons.notErrorTrTdDivision = (divisionName) => {
  const depth = storage.get('depth');
  const division = [];
  for (let i = 0, length = depth; i < length; i++) {
    if (i === 0) division.push(divisionName);
    else division.push('');
  }

  return division;
};

commons.trTdDivision = (orgCode, isPast) => {
  const depth = isPast ? storage.get('pastDepth') : storage.get('depth');
  const navi = isPast ? storage.get('pastNavi') : storage.get('navi');
  let staffDivision = null;
  let division = 0;
  if (!navi) return;

  if (orgCode) {
    navi.some((data) => {
      if (data.orgCode === orgCode) {
        staffDivision = data.name;
      }
    });
    if (staffDivision) {
      division = staffDivision.split('>');
    }
    const divisionLength = division.length;
    if (divisionLength !== depth) {
      for (let i = divisionLength, length = depth; i < length; i++) {
        division.push('');
      }
    }
  }
  if (!division) {
    const temp = [];
    for (let i = 0, length = depth; i < length; i++) {
      temp.push('-');
    }
    division = temp;
  }
  return division;
};

// 무한 스크롤 부서 사용자별 데이터 set

commons.tableStaffDivisionSet = (item, index, orgCode, orgColor, divisionArr) => {
  const temp = [item];
  const result = {};
  const depth = storage.get('depth');
  const navi = storage.get('navi');

  let staffDivision = null;
  let division = null;

  if (orgCode) {
    navi.some((data) => {
      if (data.orgCode === orgCode) {
        staffDivision = data.name;
      }
    });

    if (staffDivision) {
      division = staffDivision.split('>');
    }
  } else {
    division = divisionArr;
  }

  for (let i = 0, length = depth; i < length; i++) {
    const key = i;
    const tooltipDivision = division ? (division[i] ? { tooltip: division[i] } : '') : '';
    temp[0].splice(index + i, 0, tooltipDivision);
    // temp[0].splice(index+i, 0, division ? division[i] : "" );
  }
  temp[0].forEach((data, idx) => {
    // if(orgColor && (idx>=index && idx<(index+depth)) ) {
    //   result[idx] = { data: data, color: orgColor };
    // } else {
    result[idx] = data;

    // }
  });
  return result;
};

commons.tooltipHeader = (startNum, isNotTooltipItem) => {
  const depth = storage.get('depth');
  const tooltip = [];

  for (let i = 0; i < depth; i += 1) {
    tooltip.push(startNum + i);
  }
  if (isNotTooltipItem) {
    for (let i = 0; i < isNotTooltipItem.length; i += 1) {
      tooltip.push(isNotTooltipItem[i] + depth);
    }
  }
  return tooltip;
};

commons.divisionOrgIdx = (orgCode) => {
  const navi = storage.get('navi');
  let result = null;

  navi.some((data) => {
    if (data.orgCode === orgCode) {
      result = data.orgIdx;
    }
  });

  return result;
};

/**
 * [사용자 권한]
 * @param  {[string]} parm  [ 사용자별 권한 코드 ]
 * @return {[string]}         [ 권한 정보 ]
 */
commons.typeName = (parm) => {
  const num = String(parm);

  const name = [
    { value: '0', text: '권한변경' },
    { value: '30', text: '회사관리자' },
    { value: '1', text: '일반사용자' },
    { value: '99', text: '최고관리자' },
    { value: '-1', text: '일시정지' }
  ];

  let result = '';
  name.some((data) => {
    if (data.value === num) {
      result = data.text;
      return true;
    }
  });

  return result;
};

commons.newTypeName = (parm) => {
  const num = String(parm);

  const code = [
    { value: 'USER', text: '일반사용자' },
    { value: 'COM_ADMIN', text: '회사관리자' },
    { value: 'SUPER', text: '전체관리자' }
  ];

  let result = '';
  code.some((data) => {
    if (data.value === num) {
      result = data.text;
      return true;
    }
  });

  return result;
};

commons.gradeTypeName = (parm) => {
  const num = String(parm);

  const code = [
    { value: 'ACTIVE', text: '활성 사용자' },
    { value: 'INACTIVE', text: '일시정지' },
    { value: 'WITHDRAW', text: '탈퇴' }
  ];

  let result = '';
  code.some((data) => {
    if (data.value === num) {
      result = data.text;
      return true;
    }
  });

  return result;
};

/**
 * 청구서용으로 만든 공통함수 (권한)
 * */
commons.gradeTypeNameV2 = (parm) => {
  const values = String(parm);
  const code = [
    { value: 'USER', text: '일반사용자', fontColor: '#e20b44' },
    { value: 'COM_ADMIN', text: '회사관리자', fontColor: '#000000' },
    { value: 'SUPER', text: '전체관리자', fontColor: '#000000' }
  ];

  let result = {};
  code.some((data) => {
    if (data.value === values) {
      result = data;
      return true;
    }
  });

  return result;
};

/**
 * 청구서용으로 만든 공통함수 (상태)
 *  휴면 포함
 * */
commons.statusTypeNameV2 = (parm, isDormant) => {
  const values = isDormant ? 'DORMANT' : String(parm);
  const code = [
    { value: 'ACTIVE', text: '활성', fontColor: '#000000' },
    { value: 'INACTIVE', text: '일시정지', fontColor: '#e20b44' },
    { value: 'WITHDRAW', text: '탈퇴', fontColor: '#e20b44' },
    { value: 'DORMANT', text: '휴면', fontColor: '#e20b44' }
  ];

  let result = {};
  code.some((data) => {
    if (data.value === values) {
      result = data;
      return true;
    }
  });

  return result;
};

/**
 * 청구서용 결재자 에러 체크 함수
 * */
commons.invoicedApprovalStatus = (status, grade, isDormant) => {
  let result = {
    msg: '',
    status: true,
    msgType: 0
  };
  if (status === 'WITHDRAW' || grade === 'USER') {
    result.msg = '결재 라인에 탈퇴 사용자와 일반 사용자가 포함되어 있습니다. 해당 사용자를 제거해주시길 바랍니다.';
    result.status = false;
    result.msgType = 1;
  } else if (status !== 'ACTIVE' || isDormant) {
    result.msg =
      '결재 라인에 비정상 상태(일시정지/휴면)의 관리자가 포함되어 있습니다. 이 경우 승인/반려 처리를 할 수없습니다. 현재 상태로 저장하시겠습니까?';
    result.status = false;
    result.msgType = 2;
  }
  return result;
};

/**
 * 권한별 LNB 노출 여부
 * @param  {[object]}   data [ lnb depth1 data ]
 * @param  {[array]}    auth [ 사용자 권한 data ]
 * @return {string}          [ 첫 권한 lnb rul ]
 */
commons.isMenuView = (data, auth) => {
  let result = null;

  // 전체 권할 찾기
  if (data.code === '010' && auth) {
    auth.some((item) => {
      //전체 권한
      if (data.authCode[0] === item.code || item.code === '*:*') {
        data.isLink = true;
        result = '/main/dashBoard';
        return true;
      }
    });

    return result;
  }

  // if(data.isShow) {
  data.depth2.forEach((depth2) => {
    const { authCode } = depth2; // 하위 lnb(depht2) 권한 코드
    const comInfo = storage.get('company').info;

    if (auth) {
      auth.some((item) => {
        // 전체 권한 일 떄 LNB 전부 노출
        if (item.code === '*:*') {
          depth2.isShow = true;
          data.isShow = true;
          result = '/main/dashBoard';
          return result;
        }
        if (authCode && authCode.length > 1) {
          authCode.some((code) => {
            if (code === item.code) {
              depth2.isShow = true;
              data.isShow = true;
              result = depth2.url;

              return true;
            }
          });
        } else if (authCode[0] === item.code) {
          depth2.isShow = true;
          data.isShow = true;
          result = depth2.url;
          if (result === '/main/bill' && !comInfo.isInvoice) return (result = '/main');
          return true;
        }
      });
    }
  });
  return result;
};

/**
 * [ 페이지별 권한 정보 ]
 * @param  {[string]} groupname [ 권한 그룹 정보 ex) employee, meal-group ]
 * @return {[array]}            [ 사용자가 가진 권한그룹 정보 ]
 */
commons.authGroupSet = (groupname) => {
  const { auth } = storage.get('info').auth; // 사용자 권한 리스트
  const groupAuth = {}; // 해당되는 권한 그룹

  auth.some((data, i) => {
    // 전체 권한 일 때
    if (data === '*:*') {
      groupAuth.all = true;
      return true;
      // 그룹별 권한 찾기
    }
    if (data.indexOf(groupname) > -1) {
      const sidx = data.indexOf(':');
      const eidx = data.length;
      const act = data.substring(sidx + 1, eidx); // 권한별 action(ex: update, create...)

      groupAuth[act] = true;
    }
  });

  return groupAuth;
};

/**
 * [ 부서권한을 가질 때 부서 수정 권한 체크]
 * @param  {[array]} auth          	  [ 권한 ]
 * @param  {[String]} choiceOrgCode  	[ 부서코드 ]
 * @return {[boolean]}                [ 권한 여부 ]
 */
commons.authDivisionCheck = (auth, choiceOrgCode) => {
  const authType = auth.type; // 권한 타입
  const authDepth = auth.depth; // 권한 부서뎁스
  const authOrgCode = auth.organization; // 권한 부서코드
  const orgCode = choiceOrgCode;
  const navi = storage.get('navi');
  let result = false;

  navi.some((data) => {
    // 선택 된 orgCode와 navi의 orgCode가 같을 때
    if (data.orgCode === orgCode) {
      // 뎁스 권한이 없을 시
      if (data.depth < authDepth) {
        result = true;
        return true;
      }
    }
  });

  return result;
};

commons.arryOverlap = (array) => {
  const data = array;

  const result = data.reduce((a, b) => {
    if (a.indexOf(b) < 0) {
      a.push(b);
    }
    return a;
  }, []);

  return result;
};

// url 파라미터 값 읽어오기
commons.getQuerystring = (name) => {
  name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
  const regex = new RegExp(`[\\?&]${name}=([^&#]*)`);
  const results = regex.exec(location.search);
  return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
};

/**
 * [ object형 user정보를 받아 map으로 변환 ]
 * @param  {[object]} user [ 제외 대상 유저 정보 ]
 * @return {[map]}         [ 제외된 대상 정보 ]
 */
commons.excludeUseMap = (user) => {
  const object = {};
  try {
    user.forEach((item) => {
      const { id } = item;
      const signId = item.signId ? item.signId : '-';
      object[id] = signId;
    });
    const map = new Map(Object.entries(object));
    return map;
  } catch (error) {
    return null;
  }
};

/**
 * 배열에서 데이터 index 찾기
 * @param {[object]} data     [ 데이터 배열]
 * @param {[begin]} intarger  [ 시작 지점 ]
 * @param {[end]} intarger    [ 종료 지점 ]
 * @param {[target]} string   [ 찾을 데이터 ]
 * @return {{intarger}}       [ 찾은 배열 index ]
 */

commons.searchIdx = (data, begin, end, target) => {
  try {
    if (begin > end) {
      return -1;
    }
    if (target === data[begin].id) {
      return begin;
    }
    return commons.searchIdx(data, begin + 1, end, target);
  } catch (error) {
    console.log('##### error: ', error);
  }
};

/**
 * array object 중복 제거
 * @param {[array]} originalArray   [ 중복제거 할 배열 ]
 * @param {[string]} prop           [ object key ]
 * @returns {[array]}               [ 중복 제거 된 배열 ]
 */
commons.removeDuplicates = (originalArray, prop) => {
  const newArray = [];
  const lookupObject = {};

  for (const i in originalArray) {
    lookupObject[originalArray[i][prop]] = originalArray[i];
  }

  for (const i in lookupObject) {
    newArray.push(lookupObject[i]);
  }
  return newArray;
};

/**
 * object is empty
 * @param {[object]} object   [ check object ]
 * @return {[boolean]}        [ 비여있으면 true 아니면 false ]
 */
commons.isEmpty = (obj) => {
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) return false;
  }
  return true;
};

export default commons;
