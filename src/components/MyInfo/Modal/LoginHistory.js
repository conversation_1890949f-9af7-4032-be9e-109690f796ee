import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import * as compnayActions from 'actions/company';
import { But<PERSON>, Modal } from 'semantic-ui-react';
import { TableForm, LoadingBar, Pagination } from 'components/Commons';
import toastr from 'helpers/toastr';
import moment from 'moment';

const propTypes = {
  companyAPI: PropTypes.objectOf(PropTypes.func).isRequired,
  open: PropTypes.bool.isRequired,
  closeFn: PropTypes.func.isRequired,
  size: PropTypes.string
};

const defaultProps = {
  size: 'large'
};

class LoginHistory extends Component {
  constructor(props) {
    super(props);
    this.state = {
      contents: null,
      page: 1,
      pageRow: 10,
      totalcount: 0
    };
  }

  componentDidMount() {
    console.log('didmount');
    this.getLoginHistory(1);
  }

  getLoginHistory = async (page) => {
    const { companyAPI } = this.props;
    const params = {
      page,
      pageRow: 10,
      start: moment()
        .subtract('year', 1)
        .format('YYYY-MM-DD'),
      end: moment().format('YYYY-MM-DD')
    };

    try {
      const {
        value: { data }
      } = await companyAPI.loginHistory(params);
      this.setState({
        page,
        totalcount: data.total,
        contents: data.content
      });
    } catch (error) {
      const { response } = error;
      if (response) {
        toastr('top-right', 'error', response.data.message);
      }
    }
  };

  render() {
    const { open, size, closeFn } = this.props;
    const { page, pageRow, totalcount, contents } = this.state;
    // const { content: contents, page, pageRow, total } = data;
    let loading = <LoadingBar />;
    if (contents) {
      loading = null;
    }

    const header = [
      { data: '로그인 일시', textAlign: 'left' },
      { data: 'IP', textAlign: 'left' },
      { data: 'User Agent', textAlign: 'left' }
    ];
    const tabledata = {
      header: {
        main: header
      },
      body: {
        message: '식대 상세조회 내역이 존재하지 않습니다.',
        main: null
      }
    };

    if (contents && contents.length) {
      tabledata.body.main = contents.map((item) => {
        const result = {
          row: [
            {
              data: moment(item.created).format('YYYY-MM-DD HH:mm:ss'),
              textAlign: 'left'
            },
            {
              data: item.ip,
              textAlign: 'left'
            },
            {
              data: item.userAgent,
              textAlign: 'left'
            }
          ]
        };
        return result;
      });
    }

    const columnWidth = [160, 120, 750];

    return (
      <Modal className="login-history" size={size} open={open} onClose={() => closeFn(false, 'large')}>
        <Modal.Header>로그인 이력</Modal.Header>
        <Modal.Content>
          <div>
            {loading}
            <div className="">
              <TableForm tbcommon="true" columnWidth={columnWidth} tabledata={tabledata} />
            </div>
            <div className="table-paging">
              <Pagination
                page={page}
                pageRow={pageRow}
                pageBlock={10}
                totalCount={totalcount}
                pageClick={this.getLoginHistory}
              />
            </div>
            <div className="table-paging" style={{ textAlign: 'left' }}>
              <p>
                최근 1년간의 로그인 이력을 확인하실 수 있습니다. 1년 이상의 과거 데이터가 필요하실 경우, 식권대장에 문의
                바랍니다.
              </p>
            </div>
          </div>
        </Modal.Content>
        <Modal.Actions>
          <Button className="cancel" inverted color="green" onClick={() => closeFn(false, 'large')}>
            닫기
          </Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

LoginHistory.propTypes = propTypes;
LoginHistory.defaultProps = defaultProps;

export default connect(
  (state) => ({
    loginHistory: state.company.loginHistory
  }),
  (dispatch) => ({
    companyAPI: bindActionCreators(
      {
        loginHistory: compnayActions.loginHistory
      },
      dispatch
    )
  })
)(LoginHistory);
