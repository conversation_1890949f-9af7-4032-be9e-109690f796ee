import React, { Component } from 'react';
import { connect } from 'react-redux';
import { Modal, Button, List, Input, Grid, Form } from 'semantic-ui-react';
import cm from 'helpers/commons';
import toastr from 'helpers/toastr';
import storage from 'helpers/storage';
import { Commons } from 'components';
import moment from 'moment-timezone';
import { bindActionCreators } from 'redux';
import StaffStop from './StaffStop';
import StaffPosition from './StaffPosition';
import ChangeInformation from './ChangeInformation';
import * as commons from 'services/commons';
import * as cpAction from 'actions/company';
// import * as company from 'services/company';

moment.tz.setDefault('Asia/Seoul');
const propTypes = {};

const defaultProps = {};

class CommonModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      inputValue: null,
      stopType: 'im',
      staffInfo: {}
    };
  }

  infoContent = (type) => {
    switch (type) {
      case '식대그룹 변경':
      case '부서 이동':
      case '계정 일시정지':
      case '계정 탈퇴':
      case '일시정지 해제':
        // const { data } = this.props;
        return (
          <Grid reversed="tablet vertically">
            <Grid.Row className="row1">
              <Grid.Column>
                <Form>
                  <Form.Field inline>
                    <label>변경 인원</label>
                    <span>{this.props.data.staffCheckedData.length} 명</span>
                  </Form.Field>
                </Form>
              </Grid.Column>
            </Grid.Row>
            <Grid.Row className="row2">
              <Grid.Column>
                <Form>
                  <Form.Field inline>
                    <List horizontal className="name_list">
                      {this.props.data.staffCheckedData.map((data) => {
                        return (
                          <List.Item key={data.id}>
                            {data.name + (data.signid != 'null' ? `${'(' + ''}${data.signid})` : '')},
                          </List.Item>
                        );
                      })}
                    </List>
                  </Form.Field>
                </Form>
              </Grid.Column>
            </Grid.Row>
          </Grid>
        );

        break;
      case '부서명 변경':
        const { orgCode } = this.props;

        const navi = cm.divisionNavi(orgCode);

        return (
          <Grid reversed="tablet vertically">
            <Grid.Row className="row1">
              <Grid.Column>
                <Form>
                  <Form.Field inline>
                    <label>부서명</label>
                    <span>{navi}</span>
                  </Form.Field>
                </Form>
              </Grid.Column>
            </Grid.Row>
          </Grid>
        );
        break;
      default:
    }
  };

  actionContent = (type) => {
    const { fn, historyList } = this.props;
    const { stopType } = this.state;
    switch (type) {
      case '부서 이동':
        const { staffDivisionList } = this.props;

        return (
          <Grid reversed="tablet vertically">
            <Grid.Row className="">
              <Grid.Column>
                <Form>
                  <Commons.Division staffDivisionList={staffDivisionList} isLabel />
                </Form>
              </Grid.Column>
            </Grid.Row>
          </Grid>
        );
        break;

      case '식대그룹 변경':
        const { sikdaeGroupList } = this.props;
        return (
          <Grid reversed="tablet vertically">
            <Grid.Row>
              <Grid.Column>
                <Form>
                  <Commons.SikdaeGroup sikdaeGroup={sikdaeGroupList} isLabel />
                  <div className="inline field">
                    <label className="one wide field" />
                    <span>
                      * 식대 그룹을 변경하면 해당 <span style={{ color: 'red' }}>사용자 식대가 초기화</span> 됩니다
                    </span>
                  </div>
                </Form>
              </Grid.Column>
            </Grid.Row>
          </Grid>
        );
        break;

      case '직위 관리':
        const { positionList } = this.props;

        return <StaffPosition positionList={positionList} fn={fn} />;
        break;

      case '계정 일시정지':
        const { status } = this.props;
        return <StaffStop stopType={stopType} modalType="stop" status={status} changeStopParm={this.changeStopParm} />;
        break;
      case '일시정지 해제':
        return (
          <Grid className="content">
            <Grid.Row style={{ paddingTop: 10 }}>
              <Grid.Column width={1} />
              <Grid.Column width={15}>
                <div>
                  <span>* 일시정지를 해제하면 바로 식권대장을 사용할 수 있습니다.</span>
                  <br />
                  <span>* 식대가 소멸되어 잔여식대가 없을 수 있으니 확인 부탁드립니다.</span>
                  <br />
                </div>
              </Grid.Column>
            </Grid.Row>
          </Grid>
        );
        break;
      case '계정 탈퇴':
        return <StaffStop stopType={stopType} modalType="leave" changeStopParm={this.changeStopParm} />;
        break;
      case '부서명 변경':
      case '부서 추가':
        return (
          <Grid reversed="tablet vertically">
            <Grid.Row className="row1">
              <Grid.Column>
                <Form>
                  <Form.Field inline style={{ marginBottom: '10px' }}>
                    <label>{type}</label>
                    <Input
                      placeholder="50자 이하로 입력하세요."
                      style={{ width: 360 }}
                      onChange={this.changeValue}
                      maxLength={50}
                    />
                  </Form.Field>
                </Form>
              </Grid.Column>
            </Grid.Row>
          </Grid>
        );
        break;

      case '부서 삭제':
        return (
          <div className="content">
            <span>정말 부서를 삭제 하시겠습니까?</span>
          </div>
        );
        break;
      case '정보 변경내역':
        return <ChangeInformation fn={fn} />;
      case '오름차순':
        return (
          <div className="content">
            <span>부서를 가나다 순으로 변경하시겠습니까?</span>
          </div>
        );
        break;
      default:
        break;
    }
  };

  // input setting
  changeValue = (e, data) => {
    const { value } = data;
    if (value && value.length > 50) {
      // toastr("top-right", "error", "40 이하로 입력하세요.");
      return;
    }

    this.setState({
      inputValue: data.value
    });
  };

  // 일지정시 parameter setting
  changeStopParm = (params) => {
    if (!params) return;
    this.setState({
      stopType: params.stopType,
      startDate: params.sDate
    });
  };

  // 공통 팝업 닫기
  modalClose = () => {
    const { modal, time } = this.props;
    if (time) {
      time.sTime = null;
    }
    this.setState({ stopType: 'im' });

    modal.close();
  };

  //사용자 정보 조회
  staffSel = async () => {
    const { data } = this.props;
    const staff = data.staffCheckedData[0];
    const params = { id: staff.id };
    const sel = await commons.StaffSel(params);
    this.setState({ staffInfo: sel.data.user.historyList[0] });
  };

  // 완료
  success = async (type) => {
    await this.staffSel();
    const { fn } = this.props;
    let params = null;
    // 계정 탈퇴, 계정 일시정지 일때.
    if (type === 'withdraw' || type === 'pause') {
      const { stopType, startDate } = this.state;
      const { time } = this.props;
      // 예약일 때
      if (stopType === 're') {
        if (!startDate) {
          toastr('top-right', 'error', '날짜를 선택해주세요.');
          return;
        }
        if (!time || !time.sTime) {
          toastr('top-right', 'error', '시간을 선택해주세요.');
          return;
        }
        params = `${startDate.format('YYYY-MM-DD')} ${time.sTime}:00`;
        if (new Date(params) < new Date()) {
          toastr('top-right', 'error', '예약 시각을 지금 이후로 설정해주세요.');
          return;
        }
      }
    }
    this.setState({ stopType: 'im' });
    const { time } = this.props;
    if (time) {
      time.sTime = null;
    }
    const { staffInfo } = this.state;
    if (staffInfo && staffInfo.status && staffInfo.status === 'READY') {
      fn.cancelReserve(staffInfo.infoHistoryIdx);
    }
    fn.bulkCompletionBtn(type, params);
  };

  render() {
    const { modal, fn, divisionFunc, data, orgCode, status, depth, groupIdx } = this.props;
    const { inputValue, staffInfo } = this.state;
    const info = this.infoContent(modal.header);
    const action = this.actionContent(modal.header);

    let btn = (
      <Button color="green" onClick={modal.close}>
        완료
      </Button>
    );
    let bulk = null;
    let divisionAct = null;
    let params = null;
    let classname = '';
    let disabled = false;

    switch (modal.header) {
      case '직위 관리':
        classname = 'position-mod';
        break;
      case '부서 이동':
        bulk = 'division';
        disabled = !depth || !depth.value;
        break;
      case '식대그룹 변경':
        bulk = 'group';
        disabled = !groupIdx || !groupIdx.value;
        break;
      case '계정 일시정지':
        bulk = 'pause';
        break;
      case '일시정지 해제':
        bulk = 'pauseCancel';
        break;
      case '계정 탈퇴':
        bulk = 'withdraw';
        classname = 'withdraw';
        break;
      case '부서명 변경':
        divisionAct = 'mod';
        break;
      case '부서 삭제':
        divisionAct = 'del';
        break;
      case '부서 추가':
        divisionAct = 'add';
        break;
      case '오름차순':
        divisionAct = 'ascending';
        break;
      case '정보 변경내역':
        bulk = 'changeInfo';
        classname = 'change-info';
        break;
      default:
    }

    if (bulk) {
      const bulkType = bulk === 'pause' || bulk === 'withdraw';
      const name = '완료';

      btn = (
        <Button disabled={disabled} color={bulkType ? 'red' : 'green'} onClick={(e) => this.success(bulk)}>
          {bulk === 'pause' || bulk === 'clear' || bulk === 'withdraw' ? modal.header : '완료'}
        </Button>
      );

      if (bulk === 'changeInfo') btn = null;
    } else if (divisionAct) {
      const comcid = storage.get('company').user.company.id;
      switch (divisionAct) {
        case 'mod':
          params = {
            name: inputValue,
            sort: null
          };
          btn = (
            <Button color="green" onClick={(e) => divisionFunc(params, 'mod')}>
              완료
            </Button>
          );
          break;
        case 'del':
          btn = (
            <Button color="green" onClick={(e) => divisionFunc(null, 'del')}>
              완료
            </Button>
          );
          break;
        case 'add':
          const id = orgCode;
          const parentId = id;
          params = {
            name: inputValue,
            parentOrgCode: parentId || null
          };
          btn = (
            <Button color="green" onClick={(e) => divisionFunc(params, 'add')}>
              완료
            </Button>
          );
          break;
        case 'ascending':
          btn = (
            <Button color="green" onClick={(e) => divisionFunc(params, 'ascending')}>
              완료
            </Button>
          );
          break;
        default:
          break;
      }
    }
    return (
      <Modal className="sf-modal" size={modal.size} dimmer={modal.dimmer} open={modal.open} onClose={this.modalClose}>
        <Modal.Header>{modal.header}</Modal.Header>
        <Modal.Content className={classname}>
          {info}
          {action}
        </Modal.Content>
        <Modal.Actions>
          {btn ? (
            <Button inverted color="grey" onClick={this.modalClose}>
              취소
            </Button>
          ) : (
            <Button inverted color="green" onClick={this.modalClose}>
              닫기
            </Button>
          )}
          {btn}
        </Modal.Actions>
      </Modal>
    );
  }
}

CommonModal.propTypes = propTypes;
CommonModal.defaultProps = defaultProps;

CommonModal = connect(
  (state) => ({
    staffDivisionList: state.commons.staffDivision,
    sikdaeGroupList: state.commons.sikdaeGroup,
    positionList: state.company.position,
    time: state.commons.time,
    depth: state.commons.depth,
    groupIdx: state.commons.group
  }),
  (dispatch) => ({
    cpAPI: bindActionCreators(
      {
        positionSel: cpAction.PositionSel,
        staffInfoAct: cpAction.StaffAct,
        staffDel: cpAction.StaffDel,
        staffPwdChange: cpAction.StaffPwdChange,
        logOut: cpAction.Logout,
        logOutStepTwo: cpAction.LogoutStepTwo,
        staffBulkMod: cpAction.StaffBulkMod,
        cancelReserveDel: cpAction.CancelReserve
      },
      dispatch
    )
  })
)(CommonModal);

export default CommonModal;
