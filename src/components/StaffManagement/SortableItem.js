import React, { Component } from 'react';
import { SortableElement, SortableHandle } from 'react-sortable-hoc';
import { Icon } from 'semantic-ui-react';
import styled from 'styled-components';

import { SortableDivision } from '../StaffManagement';
//style={{ position: 'absolute', right: '10px' }}
const DragHandle = SortableHandle(() => <ItemIcon disabled name='sidebar' style={{ position: 'absolute', right: '20px', top: '10px' }}/>);
const SortableItem = SortableElement(({
  value,
  childUserCnts,
  divisionClick,
  divisionStaffSel,
  divisionMod,
  divisionAdd,
  divisionDel,
  isLoadingBarChange,
  divisionUserCntSel,
  userCnt,
  modal,
  orgCodeChange,
  orgCodePut,
  orgCodes,
  selectedOrgCode,
  itemWidth
}) => {
  const styleds = {
    depth: value.depth,
    isTopLine: value.division && value.isOpen ? true : false,
    activity: orgCodes[orgCodes.length-1] === value.orgCode ? ['#0bb656', '#ffffff'] : [null, null]
  }
  return (
    <div>
      <ItemDiv2 className="line-item-sort" onClick={(e) => divisionClick(value, 'click')} styleItems={styleds} itemWidth={itemWidth}>
        {
          value.isOpen
          ? <ItemIcon disabled name='minus' />
          : <ItemIcon disabled name='plus' />
        }
        {/* <Icon disabled name='plus' /> */}
        <OverSpan
          isOver={value.name.length > 18}
          itemWidth={itemWidth}
          styleItems={styleds}
          depth={value.depth > 1 ? value.depth - 1 : 0}
        >
          {`${value.name}`}
        </OverSpan>
        <span>{`(${userCnt ? userCnt : 0})`}</span>
        <DragHandle />
      </ItemDiv2>
      {
        value.division && value.isOpen
        ? (
          <SortableDivision
            items={value.division}
            userCnt={userCnt}
            childUserCnts={childUserCnts}
            divisionStaffSel={divisionStaffSel}
            parentOrgCode={value.orgCode}
            divisionMod={divisionMod}
            divisionAdd={divisionAdd}
            divisionDel={divisionDel}
            isLoadingBarChange={isLoadingBarChange}
            divisionUserCntSel={divisionUserCntSel}
            modal={modal}
            orgCodeChange={orgCodeChange}
            orgCodePut={orgCodePut}
            orgCodes={orgCodes}
          />
        ) : null
      }
    </div>
  )
});

const OverSpan = styled.span`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-left: 8px;
  width: ${ props => props.isOver ? (220 - (props.styleItems.depth * 16)) + 'px' : null}
`

const ItemIcon = styled(Icon)`
  font-size: 24px !important;
  margin: 0 0px -5px 5px !important;
`

const ItemDiv2 = styled.div`
  height: 40px;
  display: flex;
  width: ${ props => (props.itemWidth - (props.styleItems.depth * 16)) + "px" };
  margin-left: ${ props => (props.styleItems.depth * 16)+"px" };
  padding-right: ${ props => (props.styleItems.depth > 1) ? "20px" : null };
  border-bottom: ${
    props => props.styleItems.isTopLine ? '1px solid #efefef' : 'none'
  };
  border-top: 2px solid #efefef;
  border-left: 1px solid #efefef;
  align-items: center;
  z-index: 999;
  cursor: pointer;
  background: ${ props => props.styleItems.activity[0] };
  color: ${ props => props.styleItems.activity[1] };
  position: relative;
`

const ItemDiv = styled.div`
  height: 59px;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  display: -ms-flexbox;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  padding: 0 20px;
  border-bottom: 1px solid #efefef;
  background-color: #fff;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: #333;
  font-weight: 400;
  position: relative;
  border-bottom: 1px solid #999;
`

export default SortableItem;
