import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import {SortableContainer, SortableElement, SortableHandle, arrayMove} from 'react-sortable-hoc';
import { List, Icon } from 'semantic-ui-react';
import storage from 'helpers/storage';


const propTypes = {
};

const defaultProps = {
};

const DragHandle = SortableHandle(() => <Icon disabled name='plus' />);
const DragHandle2 = SortableHandle(() => <Icon disabled name='minus' />);

const SortableItem = SortableElement(({ idx, value, fn, thid, type }) => {

  const name = `${value.orgCode}`;
  const lowRank = type ? " "+type : "";

  let dom = null

  return (
    <List.Item
      className={"division-items " + name + lowRank}
      id={name}
      onClick={(e) => fn.divisionClick(e, name, value, idx, thid)}>
      <div className="line">
        <DragHandle />
        <span>{value.name}</span>
      </div>
      {
        idx === thid.state.idx
        ? thid.state.dom
        : null
      }
    </List.Item>
  )
});

const SortableList = SortableContainer(({items, fn, thid, type, counts}) => {

  return (
    <List>

      {
        !type
        ? (
          <List.Item
            className={"division-items all"}
            style={{"background" : "#0bb656"}}
            onClick={(e) => fn.divisionClick(e, "all")}
            id="all">
            <div className="line">
              <DragHandle2 />
              <span style={{"color" : "#ffffff"}}>{"전체"}</span>
            </div>
          </List.Item>
        )
        : null
      }

      {
        items
        ? (
          items.map((value, index) => (
            <SortableItem
              key={`item-${index}`}
              index={index}
              value={value}
              idx={index}
              thid={thid}
              counts={counts}
              fn={fn}
              type={type}
            />
          ))
        )
        : null

      }
    </List>
  );

});

class SortTableDivision extends Component {

  constructor(props) {
    super(props);
    this.state = {
      items : null
      , counts : null
    }
  }

  componentWillMount() {

    const { items } = this.props;

    this.setState({
      items : this.props.items
    })

  }

  shouldComponentUpdate(nextProps, nextState) {

    const nItems = JSON.stringify(nextState);
    const tItems = JSON.stringify(this.state);
    let result = false;

    if(nItems !== tItems) {

      result = true;
    }

    return result;
  }

  componentWillReceiveProps(nextProps, nextState) {

    this.setState({
      items : nextProps.items
      , counts : nextProps.counts
    })

  }


  onSortStart = async(e, {node, index, collection}) => {

    const { fn } = this.props;
    let id = e.node.id;

    fn.dimAction(id, "move");

  }

  shouldCancelStart = (e) => {
    return false;
  }

  onSortEnd = ({oldIndex, newIndex, collection}, e) => {

    const { divisionMod } = this.props;
    const { items } = this.state;

    let sort = [];
    let comid = storage.get("company").user.company.id;
    let parentOrgCode = e.path[1].id ? e.path[1].id : null;

    items.forEach((data) =>{

      sort.push(data.orgCode);

    });

    let id = sort[oldIndex];
    let temp = sort[oldIndex];
    sort[oldIndex] = sort[newIndex];
    sort[newIndex] = temp;

    const params = {
      id : id
      , sort : {
        parentOrgCode : parentOrgCode
        , orgCode : sort
      }
    }
    divisionMod(params);

    setTimeout(() => {
      this.setState({
        items: arrayMove(items, oldIndex, newIndex)
        , dom : null
        , idx : null
      });
    }, 1200)

  };

  render() {


    const { items } = this.state;
    const { fn, dom, type } = this.props;

    return(
      <SortableList
        pressDelay={200}
        items={this.state.items}
        onSortEnd={this.onSortEnd}
        onSortStart={this.onSortStart}
        shouldCancelStart={this.shouldCancelStart}
        getHelperDimensions={this.getHelperDimensions}
        thid={this}
        fn={fn}
        type={type}
      />
    );
  }
}

SortTableDivision.propTypes = propTypes;
SortTableDivision.defaultProps = defaultProps;

SortTableDivision = connect(

  state => ({
    divisionUserCnt : state.company.divisionUserCnt
  })

)(SortTableDivision);

export default SortTableDivision;
