import React, { Component } from 'react';
import { connect } from 'react-redux';
import ReactTable from 'react-table';

import scroll from 'helpers/scroll';
import toastr from 'helpers/toastr';
import cm from 'helpers/commons';
import Infinite from 'react-infinite';
import { Popup } from 'semantic-ui-react';

import { Commons } from 'components';
import storage from "../../helpers/storage";

const propTypes = {};

const defaultProps = {};

class StaffList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      elements: [],
      headers: [
        { header: '', width: 56, isChecked: true, className: 'allCheckbox' },
        { header: 'ID', width: 120 },
        { header: '이름', width: 128 },
        { header: '식대그룹', width: 128 },
        { header: '이메일', width: 208 },
        { header: '휴대전화번호', width: 200 },
        { header: '직위', width: 88 },
        { header: '직책', width: 88 },
        { header: '사원번호', width: 112 },
        { header: '권한', width: 88 },
        { header: '사용자 상태', width: 80 },
        { header: '가입일', width: 90 }
      ],
      heightIdx: 0,
      containerHeight: 620,
      employeeAuth: cm.authGroupSet('employee'),
      isInfiniteLoading: true,
    };
  }

  componentDidMount() {
    const leftAreaHeight = document.querySelector('.left-area').offsetHeight;
    this.setState({
      containerHeight: leftAreaHeight - 160
    });
  }

  componentWillReceiveProps(nextProps) {
    const { staffCheckedData: nCheckData } = nextProps;
    const { staffCheckedData } = this.props;

    if (nCheckData === staffCheckedData) {
      this.scrollEvent(nextProps.staffList);
    }
  }

  componentDidUpdate(nextProps) {
    this.scrollHandler();
  }

  scrollEvent = (staffList) => {
    if (staffList && staffList.data.user) {
      const { user } = staffList.data;
      this.setState({
        elements: this.buildElements(0, 100, user, false),
        isInfiniteLoading: false
      });
    } else {
      this.setState({ elements: [] });
    }
  };

  handleInfiniteLoad = async () => {
    const { staffList } = this.props;
    const { elements, heightIdx, containerHeight } = this.state;
    const item = staffList ? staffList.data.paging : null;

    const that = this;
    const elemLength = elements.length;
    const itemLength = item ? item.totalcount : 0;
    const infiniteParm = scroll.event(item, elements, containerHeight, heightIdx, elemLength);
    if (elemLength && elemLength < itemLength) {
      const params = {
        orgCode: this.props.params.orgCode,
        groupid: this.props.params.groupid
      };

      if (this.props.params) {
        params.keyword = this.props.params.keyword;
      }

      this.setState({
        isInfiniteLoading: true,
        heightIdx: heightIdx + containerHeight
      });

      const data = this.buildElements(elemLength, elemLength + 100, staffList.data.user, false);

      setTimeout(() => {
        that.setState({
          elements: that.state.elements.concat(data),
          heightIdx: infiniteParm.heightIdx,
          isInfiniteLoading: false
        });
      }, 500);
    } else {
      this.setState({
        // isInfiniteLoading : false
      });
    }
  };

  elementInfiniteLoad = () => {
    const { staffList } = this.props;
    let dom = <Commons.LoadingBar />;
    if (!staffList) {
      dom = null;
    } else if (!staffList.data.user) {
      dom = null;
    }
    return dom;
  };

  buildElements = (start, end, item) => {
    try {
      const elements = [];
      if (item) {
        const user = item;
        for (let i = start; i < end; i++) {
          const temp = this.listItem(user[i]);
          if (temp) {
            elements.push(temp);
          }
        }
      }
      return elements;
    } catch (e) {
      console.log('###### e : ', e);
    }
  };

  listItem = (item) => {
    const { staffChecked } = this.props;
    const { headers } = this.state;

    let result = null;
    if (item) {
      const isOurHome = storage.get('companyInfo').useOurHomePoint;
      const division = cm.trTdDivision(item.orgCode, true);
      const styles = [];
      headers.forEach((headerItem) => {
        styles.push({
          flex: `${headerItem.width} 0 auto`,
          width: headerItem.width,
          maxWidth: headerItem.width,
          padding: '10px 0'
        });
      });
      result = (
        <div className="rt-tr -odd" key={item.id} onClick={(e) => this.detClick(e, item)}>
          <div className="rt-td" style={styles[0]}>
            <div className="checkbox pure">
              <label>
                <input
                  type="checkbox"
                  id={item.id}
                  name={item.name}
                  data-status={item.status.code}
                  className={`staff-checkbox ${item.signid}`}
                  value="on"
                  onClick={(e) => staffChecked(e, item, null)}
                />
                <span />
              </label>
            </div>
          </div>
          <div className="rt-td" style={styles[1]}>
            {item.signid}
          </div>
          <div className="rt-td" style={styles[2]}>
            {item.name}
          </div>
          <div className="rt-td" style={styles[3]}>
            {item.group.name}
          </div>
          {division && division.length > 0
            ? division.map((divi, i) => (
                <div
                  key={`divi${i}`}
                  className="rt-td"
                  style={{ flex: '128 0 auto', width: 128, maxWidth: 128, padding: '10px 0' }}
                >
                  <Popup className="td-tooltip" inverted trigger={<span>{divi}</span>} content={divi} />
                </div>
              ))
            : null}
          <div className="rt-td" style={styles[4]}>
            {item.email.value}
          </div>
          <div className="rt-td" style={styles[5]}>
            {cm.phoneFormat(item.phone.value)}
          </div>
          <div className="rt-td" style={styles[6]}>
            {item.rankposition}
          </div>
          <div className="rt-td" style={styles[7]}>
            {item.position}
          </div>
          <div className="rt-td" style={styles[8]}>
            {item.comidnum}
          </div>
          <div className="rt-td" style={styles[9]}>
            {item.grade.text}
          </div>
          <div className="rt-td" style={styles[10]}>
            {item.status.text}
          </div>
          <div className="rt-td" style={styles[11]}>
            {cm.yyyyMMdd(new Date(item.regdate))}
          </div>
          {isOurHome ? (
            <div className="rt-td" style={{ ...styles[12], paddingRight: 30, textAlign: 'right' }}>
              {cm.numberComma(item.group.maxSubtractPoint)}
            </div>
          ) : null}
        </div>
      );
      return result;
    }
    return null;
  };

  detClick = (e, item) => {
    // e.preventDefault();
    const { employeeAuth } = this.state;
    const { className } = e.target;

    if (className !== 'rt-td') return;

    if (employeeAuth.all || employeeAuth.update) {
      location.href = `/main/staffInfo/det/${item.id}`;
    } else {
      toastr('top-right', 'warning', '사용자 수정권한이 없습니다.');
    }
  };

  scrollHandler = (e) => {
    const { staffCheckedData } = this.props;
    staffCheckedData.forEach((data) => {
      $(`#${data.id}`).prop('checked', true);
    });
  };

  tBodyFunc = (state) => {
    const { elements, containerHeight, isInfiniteLoading } = this.state;
    return (
      <div className="rt-tbody" style={state.style}>
        <div className="rt-tr-group">
          <Infinite
            className="infinite-scroll"
            elementHeight={43}
            containerHeight={containerHeight}
            infiniteLoadBeginEdgeOffset={containerHeight - 50}
            onInfiniteLoad={this.handleInfiniteLoad}
            loadingSpinnerDelegate={this.elementInfiniteLoad()}
            isInfiniteLoading={isInfiniteLoading}
            handleScroll={this.scrollHandler}
          >
            {elements}
          </Infinite>
        </div>
      </div>
    );
  };

  noDataProps = (state) => {
    const { staffList } = this.props;
    let dom = (
      <div className="rt-noData">
        검색 결과가 없습니다.
      </div>
    );
    if (staffList && staffList.data.user) {
      dom = <div className="rt-noData" />;
    }

    return dom;
  };

  render() {
    const { staffChecked } = this.props;
    const { headers, noDataText } = this.state;
    const isOurHome = storage.get('companyInfo').useOurHomePoint;
    const isHeader = headers.filter(({ header }) => header === '아워홈 최대차감금액')[0];

    if (isOurHome && !isHeader) {
      headers.push({ header: '아워홈 최대차감금액', width: 150 });
    }
    const header = cm.tableHeaderList(headers, 4, null, null, null, true);
    const fn = { fnChecked: staffChecked, move: null };

    // let tooltipArray = cm.tooltipHeader(4, null);
    const columns = scroll.header(header, fn);

    return (
      <ReactTable
        className="react-table"
        noDataText={
          noDataText || '검색 결과가 없습니다. (휴면 사용자인 경우, 휴면 사용자 관리로 이동 후 검색해주세요.)'
        }
        columns={columns}
        sortable={false}
        resizable={false}
        showPagination={false}
        TbodyComponent={this.tBodyFunc}
        NoDataComponent={this.noDataProps}
        style={{ height: 'calc(100% - 40px)' }}
      />
    );
  }
}

StaffList.propTypes = propTypes;
StaffList.defaultProps = defaultProps;

StaffList = connect((state) => ({
  staffList: state.commons.staff,
  loading: state.commons.staffPending
}))(StaffList);

export default StaffList;
