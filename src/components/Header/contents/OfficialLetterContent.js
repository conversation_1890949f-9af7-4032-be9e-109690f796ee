import React from 'react';
import styled from 'styled-components';
import { Button } from 'semantic-ui-react';

const Wrapper = styled.div`
  padding: 36px 60px;
  display: flex;
  gap: 16px;
  font-size: 18px;
  flex-direction: column;
  border-bottom: 1px solid rgba(34, 36, 38, 0.15);
  .bold {
    font-weight: 700;
  }
  .decoration {
    text-decoration: underline;
  }
  .buttonGroup {
    display: flex;
    gap: 16px;
    justify-content: center;
  }
  h2 {
    text-align: center;
  }
  .align-end {
    text-align: end;
  }
`;

const OfficialLetterContent = () => {
  const handleClick = () => {
    window.open('https://sikd.ae/999835');
  };
  const handleGuideClick = () => {
    window.open('https://sikd.ae/a7a683');
  };
  return (
    <Wrapper>
      <h2>안녕하십니까, 주식회사 벤디스입니다</h2>
      <div>벤디스가 2022년 11월 22일 자로 현대백화점그룹 주식회사 현대이지웰과 한 가족이 되었습니다.</div>
      <div>
        지금까지의 성원에 진심으로 감사드리며, <br />
        앞으로도 신뢰받는 서비스가 될 수 있도록 고객사의 목소리에 늘 귀 기울이겠습니다.
      </div>
      <div>
        원활한 정산 업무 진행을 위해{' '}
        <span className="bold decoration">고객사 청구서 발행 관련 운영정책이 개선될 예정</span>입니다. <br />
        기존 방식은 고객사 확인 없이 세금계산서가 먼저 발행되어 <br />
        세금계산서 수정 발행으로 인한 비효율적인 중복 업무가 발생되고 있었습니다. <br />
      </div>
      <div>
        이런 문제점을 개선하고자 <span className="bold decoration">세금계산서 발급 전에 수정/확인하는 절차</span>를{' '}
        만들고 <br />
        <span className="bold decoration">수정사항을 일괄 반영하는 프로세스</span>를 만들었습니다.
      </div>
      <div>
        앞으로는 기본적인 <span className="bold decoration">수정사항 발생 시, 청구서를 직접 수정</span>할 수 있으며
        <br />
        <span className="bold decoration">실시간으로 수정사항 진행 여부를 확인</span>하실 수 있게 됩니다.
      </div>
      <div>
        자세한 내용은 12월 14일 <span className="bold"><EMAIL></span> 보내드린 이메일과 아래 첨부 문서를{' '}
        참고해 주시기 바랍니다.
      </div>
      <div className="align-end">
        <div>감사합니다.</div>
        <div>벤디스 드림.</div>
      </div>
      <div className="buttonGroup">
        <Button onClick={handleClick}>협조 공문 다운로드</Button>
        <Button onClick={handleGuideClick}>정산 가이드 다운로드</Button>
      </div>
    </Wrapper>
  );
};

export default OfficialLetterContent;
