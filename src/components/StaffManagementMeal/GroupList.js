import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { List, Icon } from 'semantic-ui-react';
import styled from 'styled-components';

const propTypes = {
};

const defaultProps = {
};

class GroupList extends Component {

  constructor(props) {
    super(props);
    this.state = {
      selectedGroupIdx : null
    }
  }

  groupList = () => {

    const { sikdaeGroupList } = this.props;
    const { selectedGroupIdx } = this.state;

    let dom = null;

    if(sikdaeGroupList && sikdaeGroupList.data) {

      const { group } = sikdaeGroupList.data;


      dom = group.map((data) => {

        const styleds = {
          isActive: data.idx === selectedGroupIdx ? true : false
        }


        return (
          <ItemDiv
            key={data.idx+1}
            id={"group-" + data.idx}
            className="division-items"
            styleItems={styleds}
            onClick={(e) => this.groupClick(e, data)}>
            <ItemSpan isOver={data.name.length > 18} >{data.name}</ItemSpan>
            <span>{" (" + data.mcount + ")"}</span>
          </ItemDiv>
        )
      })

      return dom;
    }

  }

  groupClick = (e, group) => {

    e.stopPropagation();
    this.setState({
      selectedGroupIdx: group ? group.idx : null
    });

    const { groupStaffList } = this.props;
    groupStaffList(group);
  }

  render() {

    const { userTotal } = this.props;
    const { selectedGroupIdx } = this.state;
    const dom = this.groupList();

    const styleds = {
      isActive: selectedGroupIdx ? false : true
    }

    return(

      <List>
        <ItemDiv
          key={0}
          id={"group-0"}
          className="division-items"
          styleItems={styleds}
          onClick={(e) => this.groupClick(e, null)}>
          {"전체 (" + userTotal + ")" }
        </ItemDiv>
        {dom}
      </List>
    );
  }
}

const ItemDiv = styled.div`
  padding-left : 10px !important;
  display: flex;
  background: ${ props => props.styleItems.isActive ? '#0bb656' : '#ffffff' };
  color: ${ props => props.styleItems.isActive ? '#ffffff' : null };
  cursor: pointer;
`

const ItemSpan = styled.span`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-left: 8px;
  width: ${ props => props.isOver ? '245px' : null};
`

GroupList.propTypes = propTypes;
GroupList.defaultProps = defaultProps;

GroupList = connect(
  state => ({
    sikdaeGroupList : state.commons.sikdaeGroup
  })
)(GroupList);

export default GroupList;
