import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Button, Grid } from 'semantic-ui-react';

import CommonModal from './Modal/CommonModal';
import toastr from 'helpers/toastr';
import cm from 'helpers/commons';

const propTypes = {};

const defaultProps = {};

class BottomBtnArea extends Component {
  constructor(props) {
    super(props);
    this.state = {
      open: false,
      employeeAuth: cm.authGroupSet('employee'),
      mealcGorupAuth: cm.authGroupSet('meal-group')
    };
  }

  componentWillMount() {
    const { employeeAuth } = this.state;
  }

  ShowModalMod = (dimmer, header, size) => {
    const { data } = this.props;

    if (header !== '직위 관리' && header !== '정보 변경내역' && data.staffCheckedData.length === 0) {
      toastr('top-right', 'warning', '사용자을 선택하세요.');
      return;
    } else {
      this.setState({
        dimmer,
        data,
        size: size ? size : 'small',
        open: true,
        header: header
      });
    }
  };

  closeModal = () => {
    this.setState({
      open: false,
      dimmer: false
    });
  };

  changeInfoModal = () => {
    const { fn } = this.props;
    const params = {
      page: 1,
      pageRow: 10,
      type: 'ALL'
    };
    try {
      fn.changeInfoSel(params);
    } catch (error) {
      console.log('##### error: ', error);
    }

    this.ShowModalMod(true, '정보 변경내역', 'large');
  };

  render() {
    const { data, fn } = this.props;
    const { open, dimmer, size, header, employeeAuth, create, update, mealcGorupAuth } = this.state;
    const modal = {
      open: open,
      size: size,
      dimmer: dimmer,
      header: header,
      close: this.closeModal
    };

    return (
      <Grid style={{ width: '100%' }}>
        {employeeAuth.all || employeeAuth.create ? (
          <div className="btn-group-one" style={{ float: 'left' }}>
            <Button className="staff-management-btn" color="black" onClick={(e) => this.changeInfoModal()} inverted>
              정보 변경내역
            </Button>
          </div>
        ) : null}

        <div className="btn-group-two" style={{ position: 'absolute', right: '0px' }}>
          <Button className="position-btn" color="black" inverted onClick={(e) => this.ShowModalMod(true, '직위 관리')}>
            직위 관리
          </Button>
          {1 || employeeAuth.all || employeeAuth.update ? (
            <Button
              className="division-move-btn"
              color="black"
              inverted
              onClick={(e) => this.ShowModalMod(true, '부서 이동')}
            >
              부서 이동
            </Button>
          ) : null}
          {1 || mealcGorupAuth.all || mealcGorupAuth.assign ? (
            <Button
              className="sikdaeGroup-btn"
              color="black"
              inverted
              onClick={(e) => this.ShowModalMod(true, '식대그룹 변경')}
            >
              식대그룹 변경
            </Button>
          ) : null}
          {1 || employeeAuth.all ? (
            <Button
              className="staffPause-btn"
              color="black"
              inverted
              onClick={(e) => this.ShowModalMod(true, '일시정지 해제')}
            >
              일시정지 해제
            </Button>
          ) : null}
          {1 || employeeAuth.all ? (
            <Button
              className="staffPause-btn"
              color="red"
              inverted
              onClick={(e) => this.ShowModalMod(true, '계정 일시정지')}
            >
              계정 일시정지
            </Button>
          ) : null}

          {1 || employeeAuth.all || employeeAuth.delete ? (
            <Button className="resign-btn" color="red" inverted onClick={(e) => this.ShowModalMod(true, '계정 탈퇴')}>
              계정 탈퇴
            </Button>
          ) : null}
        </div>
        <CommonModal data={data} fn={fn} modal={modal} />
      </Grid>
    );
  }
}

BottomBtnArea.propTypes = propTypes;
BottomBtnArea.defaultProps = defaultProps;

BottomBtnArea.contextTypes = {
  router: PropTypes.object.isRequired
};

export default BottomBtnArea;
