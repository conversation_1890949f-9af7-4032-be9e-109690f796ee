import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import ReactTable from 'react-table';
import StaffMealUseage from './StaffMealUseage';
import scroll from 'helpers/scroll';
import toastr from 'helpers/toastr';
import cm from 'helpers/commons';
import Infinite from 'react-infinite';
const numeral = require('numeral');
const moment = require('moment');

import { Popup, Input, Table, Grid, Header, Button, Form, Modal, Icon } from 'semantic-ui-react';

import { Commons } from 'components';

import * as cp_action from 'actions/company';
import * as cm_action from 'actions/commons';

import { DateForm, DateRangeForm } from '../Commons';

const propTypes = {
  divisionBudget: PropTypes.array,
  budgetFormAdd: PropTypes.number
};

const defaultProps = {};

const ModalScrollingContent = ({ open, onClose, devisionObj, divisionCode }) => {
  // return (<Modal open={open} style={{marginTop:'50% !important', marginLeft: 'calc(50% - 475px)'}}>
  return (
    <Modal open={open}>
      <Modal.Header>예산사용내역</Modal.Header>
      <Modal.Content image className="useage-modal-list">
        <StaffMealUseage divisionCode={divisionCode} />
      </Modal.Content>
      <Modal.Actions>
        <Button primary onClick={onClose}>
          닫기 <Icon name="chevron right" />
        </Button>
      </Modal.Actions>
    </Modal>
  );
};

class StaffMealList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      divisionCode: props.params.orgCode || null,
      divisionBudget: [],
      budgetFormAdd: 0,
      loadDivisionBudget: {}
    };
    this.divisionDateChange = this.divisionDateChange.bind(this);
    this.showUseage = this.showUseage.bind(this);
  }

  componentWillReceiveProps(nextProps) {
    const { params: nexPrams } = nextProps;
    const { params } = this.props;
    const divisioncode1 = (params && params.orgCode) || null;
    const divisioncode2 = (nexPrams && nexPrams.orgCode) || null;
    if (divisioncode1 !== divisioncode2) {
      // console.log(divisioncode1,divisioncode2)
      this.setState(
        {
          budgetFormAdd: 0,
          useageModal: false,
          divisionCode: divisioncode2,
          divisionBudget: [],
          budgetFormStart: moment().startOf('month'),
          budgetFormEnd: moment().endOf('month'),
          loadDivisionBudget: {}
        },
        () => {
          this.loadDivisionBuget();
        }
      );
    }
  }

  componentDidMount() {
    this.loadDivisionBuget();
  }

  loadDivisionBuget = async () => {
    const { cmAPI } = this.props;
    const { divisionCode } = this.state;
    const division_buget = await cmAPI.divisionBudgetApi(divisionCode);
    const response_data = division_buget && division_buget.value.data;
    console.log(divisionCode, response_data);

    this.setState({
      budgetFormAdd: 0,
      divisionBudget: response_data || []
    });
  };

  showUseage(code) {
    this.setState({
      modalDivisionCode: code,
      useageModal: !this.state.useageModal
    });
  }

  budgetFormAddChange = (e) => {
    let value = e.target.value;
    this.setState({
      ...this.state,
      budgetFormAdd: value
    });
  };

  divisionDateChange = (e) => {
    this.setState(
      {
        ...this.state,
        budgetFormStart: e.startDate,
        budgetFormEnd: e.endDate
      },
      () => {
        console.log(this.state);
      }
    );
  };

  showPricesText = (numberPrice) => {
    let number = Number(numberPrice);
  };

  divisionBugetMod(budgetData) {
    this.setState(
      {
        budgetFormAdd: budgetData.guarantee,
        budgetFormStart: moment(budgetData.start),
        budgetFormEnd: moment(budgetData.end),
        loadDivisionBudget: budgetData
      },
      () => {
        console.log(this.state.loadDivisionBudget);
      }
    );
  }

  noDataProps = (state) => {
    const { staffList } = this.props;
    let dom = <div className="rt-noData">검색 결과가 없습니다.</div>;
    if (staffList && staffList.data.user) {
      dom = <div className="rt-noData"></div>;
    }

    return dom;
  };

  divisionBudgetModFnc() {
    const { cmAPI } = this.props;
    const { budgetFormAdd, budgetFormStart, budgetFormEnd, divisionCode } = this.state;
    let params = {
      orgCode: divisionCode
    };

    if (
      this.state.loadDivisionBudget.usageAmount <= numeral(budgetFormAdd).value() &&
      this.state.loadDivisionBudget.guarantee !== budgetFormAdd
    ) {
      params['guarantee'] = numeral(budgetFormAdd).value();
    }

    if (
      this.state.loadDivisionBudget.usageAmount === 0 &&
      this.state.loadDivisionBudget.start !== moment(budgetFormStart.format('YYYY-MM-DD')).valueOf()
    ) {
      params['start'] = moment(budgetFormStart.format('YYYY-MM-DD')).valueOf();
    }

    if (this.state.loadDivisionBudget.end !== moment(budgetFormEnd.format('YYYY-MM-DD')).valueOf()) {
      params['end'] = moment(budgetFormEnd.format('YYYY-MM-DD')).valueOf();
    }

    console.log(budgetFormAdd);
    console.log(this.state.budgetFormStart);
    console.log(this.state.budgetFormEnd);
    console.log(this.state.loadDivisionBudget, params);
    if (Object.values(params).length === 1) {
      if (this.state.loadDivisionBudget.usageAmount === 0) window.alert('변경된 내역이 없습니다.');
      else window.alert('사용이된 예산설정은 종료일과 금액(사용금액 이상)만 수정이 가능합니다.');
      return;
    }

    if (window.confirm('예산을 수정 하시겠습니까?')) {
      const budgetDel = cmAPI.divisionBudgetMod(this.state.loadDivisionBudget.idx, params);
      budgetDel
        .then(() => {
          alert('예산이 수정 되었습니다.');
          this.loadDivisionBuget();
          this.divisionFormRest();
        })
        .catch((err) => alert(`${err.response.data.message}`));
    }
  }

  divisionBudgetDelFnc = async () => {
    const { cmAPI } = this.props;
    if (window.confirm('예산을 삭제 하시겠습니까?')) {
      const budgetDel = cmAPI.divisionBudgetDel(this.state.loadDivisionBudget.idx);
      budgetDel
        .then(() => {
          alert('예산이 삭제 되었습니다.');
          this.loadDivisionBuget();
          this.divisionFormRest();
        })
        .catch((err) => alert(`${err.response.data.message}`));
    }
  };

  divisionFormRest() {
    this.setState(
      {
        budgetFormAdd: 0,
        budgetFormStart: moment().startOf('month'),
        budgetFormEnd: moment().endOf('month'),
        loadDivisionBudget: {}
      },
      () => {
        console.log(this.state);
      }
    );
  }

  divisionBudgetSubmit = async () => {
    const { cmAPI } = this.props;
    const { budgetFormAdd, budgetFormStart, budgetFormEnd, divisionCode, loadDivisionBudget } = this.state;
    const updateCheck = Object.keys(loadDivisionBudget).length > 0;
    let params = {
      orgCode: divisionCode,
      start: moment(budgetFormStart.format('YYYY-MM-DD')).valueOf(),
      end: moment(budgetFormEnd.format('YYYY-MM-DD')).valueOf(),
      guarantee: numeral(budgetFormAdd).value()
    };

    if (params.guarantee === 0) {
      alert('금액을 입력해 주세요');
      return;
    }

    const division_buget = cmAPI.divisionBudgetAdd(params);
    division_buget
      .then(() => {
        alert('정상적으로 등록 되었습니다');
        this.divisionFormRest();
        this.loadDivisionBuget();
      })
      .catch((err) => {
        alert(`${err.response.data.message}`);
      });
  };

  render() {
    const { loadDivisionBudget } = this.state;
    const modStatus = loadDivisionBudget.hasOwnProperty('usageAmount');
    return (
      <div>
        <Form onSubmit={this.divisionBudgetSubmit}>
          <Header as="h4">예산 설정</Header>
          <Grid.Row>
            <Table celled structured>
              <Table.Body>
                <Table.Row>
                  <Table.HeaderCell width={2} textAlign="center">
                    예산할당 부서
                  </Table.HeaderCell>
                  <Table.Cell width={14} colSpan="3">
                    {this.props.navi || '전체'}
                  </Table.Cell>
                </Table.Row>
                <Table.Row>
                  <Table.HeaderCell width={2} textAlign="center">
                    예산할당 금액
                  </Table.HeaderCell>
                  <Table.Cell width={3}>
                    <Input
                      style={{ width: '100%' }}
                      className="budget-input"
                      maxLength="11"
                      onChange={(e) => this.budgetFormAddChange(e)}
                      value={numeral(this.state.budgetFormAdd).format('0,0')}
                    />
                  </Table.Cell>
                  <Table.HeaderCell width={2} textAlign="center">
                    예산할당 기간
                  </Table.HeaderCell>
                  <Table.Cell width={9}>
                    <DateRangeForm
                      periodType="month"
                      changeData={this.divisionDateChange}
                      loadData={loadDivisionBudget}
                    />
                  </Table.Cell>
                </Table.Row>
              </Table.Body>
            </Table>
          </Grid.Row>
          <Grid.Row>
            {modStatus ? (
              <Button.Group style={{ float: 'right', marginTop: '10px' }}>
                <Button
                  content="예산 수정"
                  type="button"
                  className="budget-btn"
                  color="green"
                  inverted
                  onClick={() => this.divisionBudgetModFnc()}
                />
                <Button
                  disabled={loadDivisionBudget.usageAmount > 0}
                  content="예산 삭제"
                  inverted
                  className="budget-btn"
                  onClick={() => this.divisionBudgetDelFnc()}
                  color="red"
                  type="button"
                />
                <Button
                  content="취소"
                  color="black"
                  type="button"
                  inverted
                  onClick={() => {
                    if (window.confirm('초기화 하시 겠습니까?')) {
                      this.divisionFormRest();
                    }
                  }}
                />
              </Button.Group>
            ) : (
              <Button
                style={{ float: 'right', marginTop: '10px' }}
                content="추가"
                color="green"
                type="button"
                inverted
                onClick={() => this.divisionBudgetSubmit()}
              />
            )}
          </Grid.Row>
        </Form>
        <Header as="h4">예산 사용 내역</Header>
        <Grid.Row>
          <Table celled structured>
            <Table.Header>
              <Table.Row>
                <Table.HeaderCell width={4} textAlign="center">
                  유효기간
                </Table.HeaderCell>
                <Table.HeaderCell textAlign="center">지급예산</Table.HeaderCell>
                <Table.HeaderCell textAlign="center">적용그룹</Table.HeaderCell>
                <Table.HeaderCell textAlign="center">사용금액</Table.HeaderCell>
                <Table.HeaderCell textAlign="center">잔여금액</Table.HeaderCell>
                <Table.HeaderCell width={2} textAlign="center">
                  사용내역
                </Table.HeaderCell>
                <Table.HeaderCell width={2} textAlign="center">
                  관리
                </Table.HeaderCell>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {this.state.divisionBudget.length === 0 && (
                <Table.Row>
                  <Table.Cell colSpan={7} style={{ textAlign: 'center' }}>
                    예산 내역이 없습니다
                  </Table.Cell>
                </Table.Row>
              )}
              {this.state.divisionBudget.map((item, idx) => {
                return (
                  <Table.Row key={idx}>
                    <Table.Cell textAlign="center">
                      {moment(item.start).format('YYYY.MM.DD')} ~ {moment(item.end).format('YYYY.MM.DD')}
                    </Table.Cell>
                    <Table.Cell textAlign="center">{numeral(item.guarantee).format('0,0')}</Table.Cell>
                    <Table.Cell textAlign="center">{this.props.navi}</Table.Cell>
                    <Table.Cell textAlign="center">{numeral(item.usageAmount).format('0,0')}</Table.Cell>
                    <Table.Cell textAlign="center">{numeral(item.amount).format('0,0')}</Table.Cell>
                    <Table.Cell textAlign="center">
                      <Button content="사용내역" color="black" inverted onClick={() => this.showUseage(item.idx)} />
                    </Table.Cell>
                    <Table.Cell textAlign="center">
                      <Button content="설정" color="black" inverted onClick={() => this.divisionBugetMod(item)} />
                    </Table.Cell>
                  </Table.Row>
                );
              })}
            </Table.Body>
          </Table>
        </Grid.Row>
        <ModalScrollingContent
          open={this.state.useageModal}
          onClose={this.showUseage}
          divisionCode={this.state.modalDivisionCode}
        />
      </div>
    );
  }
}

StaffMealList.propTypes = propTypes;
StaffMealList.defaultProps = defaultProps;

StaffMealList = connect(
  (state) => ({
    staffList: state.commons.staff
  }),
  (dispatch) => ({
    cmAPI: bindActionCreators(
      {
        divisionBudgetApi: cp_action.DivisionBudget,
        divisionBudgetAdd: cp_action.DivisionBudgetAdd,
        divisionBudgetMod: cp_action.DivisionBudgetMod,
        divisionBudgetDel: cp_action.DivisionBudgetDel
      },
      dispatch
    )
  })
)(StaffMealList);

export default StaffMealList;
