import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Icon, Menu, Segment, Button, Grid } from 'semantic-ui-react';
import update from 'react-addons-update';

import styled from 'styled-components';
import SortableDivision from './SortableDivision';
import GroupList from './GroupList';
import CommonModal from './Modal/CommonModal';

import toastr from 'helpers/toastr';
import cm from 'helpers/commons';

const propTypes = {};

const defaultProps = {};

class LeftArea extends Component {
  constructor(props) {
    super(props);
    this.state = {
      open: false,
      orgCodes: []
    };
  }

  componentWillMount() {}

  shouldComponentUpdate(nextProps, nextState) {
    const { staffDivisionList } = this.props;
    const { open, orgCodes } = this.state;
    const divisionChange = staffDivisionList !== nextProps.staffDivisionList;
    const openChange = open !== nextState.open;
    const orgCodesChange = orgCodes !== nextState.orgCodes;

    return !divisionChange || !staffDivisionList || openChange || !orgCodesChange || !userCntChange;
  }

  /**
   * [인사 및 부서권한을 가진 사용자 부서에 대한 권한 체크]
   * @return {[boolean]} [권한 유무]
   */
  authCheck = () => {
    const { data, company } = this.props;
    let result = false;

    if (company && company.data) {
      const { auth } = company.data;
      const orgCode = data.param.orgCode; // 선택 된 부서
      const authType = auth.type; // 권한 타입=
      const authCheck = auth.auth.map((item) => item.code).indexOf('employee:create');

      if (authType === 'DIVISION') {
        //인사 및 부서권한을 가진 사용자 체크
        if (!data.param.orgCode && authCheck === -1) {
          return true;
        }

        result = cm.authDivisionCheck(auth, orgCode);
      }
    }
    return result;
  };

  ShowModalMod = (dimmer, header) => {
    const { data, fn } = this.props;

    let result = this.authCheck();
    if (header === '부서명 변경' && !data.param.orgCode) {
      toastr('top-right', 'warning', '부서를 선택하세요.');
      return;
    }

    //권한 체크 유무
    if (result) {
      toastr('top-right', 'warning', '해당 부서권한이 없습니다.');
      return;
    }

    this.setState({
      dimmer,
      data,
      size: 'small',
      open: true,
      header: header,
      openModalOrgCode: data.param.orgCode
    });
  };

  divisionFunc = (params, type) => {
    const { fn, data } = this.props;
    let result = false;

    if (type === 'mod') {
      result = fn.divisionMod(params);
    } else if (type === 'del') {
      result = fn.divisionDel();
    } else if (type === 'add') {
      this.setState({ open: false });
      result = fn.divisionAdd(params);
    }

    // this.context.router.history.go();
    const _this = this.context.router;
    result.then(function(value, _this) {
      result = value;
      if (result === true) {
        // _this.history.go()
        location.href = '/main/staffManagement';
      }
    });
  };

  closeModal = () => {
    this.setState({
      open: false,
      dimmer: false,
      openModalOrgCode: null
    });
  };

  orgCodePut = async (orgCode, depth) => {
    const { orgCodes } = this.state;
    let idx = null;
    let size = orgCodes.length;
    await this.setState({
      orgCodes: update(this.state.orgCodes, { $splice: [[depth - 1, size]] })
    });

    if (depth == 0) {
      await this.setState({
        orgCodes: []
      });
    } else if (depth == 1) {
      await this.setState({
        orgCodes: [orgCode]
      });
    } else {
      this.setState({
        orgCodes: update(this.state.orgCodes, { $push: [orgCode] })
      });
    }
  };

  tabMove = (type) => {
    const { fn } = this.props;
    if (type === 'group') {
      this.setState({ orgCodes: [] });
    }
    fn.tabMove(type);
  };

  scrollMove = (e) => {
    $('.actions-btn').css('position', 'sticky');
  };

  render() {
    const { fn, data, staffDivisionList } = this.props;
    const { open, dimmer, size, header, openModalOrgCode, orgCodes } = this.state;
    const modal = {
      open: open,
      size: size,
      dimmer: dimmer,
      header: header,
      close: this.closeModal,
      openModalOrgCode: openModalOrgCode
    };
    const items = staffDivisionList && staffDivisionList.data ? staffDivisionList.data.division : null;

    return (
      <Grid.Column width={4} style={{ paddingLeft: '0px', paddingRight: '0px' }}>
        <Menu widths={2} tabular className="vendysFullTab" attached="top">
          <Menu.Item active={data.tabType === 'division'} onClick={(e) => this.tabMove('division')}>
            <span>부서</span>
          </Menu.Item>
          <Menu.Item />
        </Menu>
        <Segment
          attached="bottom"
          onScroll={this.scrollMove}
          style={{
            overflowY: 'scroll',
            padding: '0px',
            overflowX: 'hidden',
            marginLeft: '-1px',
            marginBottom: 0,
            height: 'calc(100vh - 190px)'
          }}
        >
          {data.tabType === 'division' ? (
            items ? (
              <SortableDivision
                items={items}
                divisionStaffSel={fn.divisionStaffSel}
                divisionMod={fn.divisionMod}
                divisionAdd={fn.divisionAdd}
                divisionDel={fn.divisionDel}
                isLoadingBarChange={fn.isLoadingBarChange}
                divisionUserCntSel={fn.divisionUserCntSel}
                modal={modal}
                userTotal={data.userTotal}
                orgCodePut={this.orgCodePut}
                orgCodes={orgCodes}
                isLoadingBar={data.isLoadingBar}
              />
            ) : null
          ) : (
            <GroupList groupStaffList={fn.groupStaffList} userTotal={data.userTotal} />
          )}
        </Segment>
        {/* <CommonModal
          data={data}
          fn={fn}
          modal={modal}
          divisionFunc={this.divisionFunc}
        /> */}
      </Grid.Column>
    );
  }
}

const ContentWrapper2 = styled.div`
  display: table;
  width: 100%;
  & > div > em {
    display: block;
  }
  border-bottom: ${(props) => (props.depth ? '1px solid #efefef' : null)};
`;

const ItemDiv2 = styled.div`
  height: 40px;
  display: flex;
  padding-left: 5px;
  border-top: 1px solid #efefef;
  border-bottom: 1px solid #efefef;
  align-items: center;
  border-bottom: 1px solid #efefef;
  z-index: 999;
  cursor: pointer;
  width: ${(props) => props.itemWidth + 'px'};
  background: ${(props) => (props.isAllClick && !props.selectedOrgCode ? '#0bb656' : '#ffffff')};
  color: ${(props) => (props.isAllClick && !props.selectedOrgCode ? '#ffffff' : null)};
`;

LeftArea.propTypes = propTypes;
LeftArea.defaultProps = defaultProps;

LeftArea.contextTypes = {
  router: PropTypes.object.isRequired
};

LeftArea = connect((state) => ({
  staffDivisionList: state.commons.staffDivision,
  company: state.auth.company
}))(LeftArea);

export default LeftArea;
