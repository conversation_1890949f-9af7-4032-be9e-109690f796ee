import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';

import { Popup, Button } from 'semantic-ui-react';

import cm from 'helpers/commons';
import moment from 'moment-timezone';

moment.tz.setDefault('Asia/Seoul');

const DormantCount = ({ dormantTargetCount, history }) => {
  const {
    data: { count }
  } = dormantTargetCount;

  const goDormantTargets = () => {
    history.push({
      pathname: '/main/staffManagement',
      search: 'dormant=true'
    });
  };

  const trigger = (
    <span className="dormant-info">
      <p>
        {cm.yyyyMMdd(new Date(moment().add(1, 'day')))}(내일) - {cm.yyyyMMdd(new Date(moment().add(30, 'day')))}(내일 +
        30일)
      </p>
      <p className="info-title">위 기간동안 휴면처리되는 사용자 {count}명</p>
    </span>
  );

  const content = '지난 1년동안 식권대장을 사용하지않은 사용자는 휴면됩니다';
  return (
    <div className="dormant-count">
      <Popup trigger={trigger} content={content} position="right center" />
      <Button className="dormant-go-btn" basic onClick={goDormantTargets}>
        {'자세히보기 >'}
      </Button>
    </div>
  );
};

DormantCount.propTypes = {
  dormantTargetCount: PropTypes.shape({
    data: PropTypes.any
  }),
  history: PropTypes.shape({
    push: PropTypes.func
  })
};

DormantCount.defaultProps = {
  dormantTargetCount: {
    data: {}
  },
  history: {}
};

export default connect((state) => ({
  dormantTargetCount: state.dashboard.dormantRevCnt
}))(DormantCount);
