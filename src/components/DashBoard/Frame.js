import React, { Component } from 'react';
import classNames from 'classnames';

class Frame extends Component {
  render() {
    const { width, title, type, date, periodSel, children } = this.props;
    const frameStyle = {
      width: width ? `${width}px` : ''
    };

    let periodBtn = '';
    if (['T', '1W', '1M', '6M'].indexOf(type) > -1) {
      const t = type === 'T' ? 'active' : '';
      const w_1 = type === '1W' ? 'active' : '';
      const m_1 = type === '1M' ? 'active' : '';
      const m_6 = type === '6M' ? 'active' : '';
      periodBtn = (
        <div className="tit-period">
          <div className={`period-btn ${t}`} onClick={() => periodSel('hourlyList', 'T')}>
            오늘
          </div>
          <div className={`period-btn ${w_1}`} onClick={() => periodSel('hourlyList', '1W')}>
            일주일간
          </div>
          <div className={`period-btn ${m_1}`} onClick={() => periodSel('hourlyList', '1M')}>
            1개월간
          </div>
          <div className={`period-btn ${m_6}`} onClick={() => periodSel('hourlyList', '6M')}>
            6개월간
          </div>
        </div>
      );
    }

    return (
      <div className="frame" style={frameStyle}>
        <div className="frame-tit">
          <div className="tit-left">{title}</div>
          <div className="tit-right">
            {periodBtn}

            <div className="tit-date">{date}</div>
          </div>
        </div>

        <div className="frame-cont" style={{ padding: '0 10px' }}>
          {children}
        </div>
      </div>
    );
  }
}

export default Frame;
