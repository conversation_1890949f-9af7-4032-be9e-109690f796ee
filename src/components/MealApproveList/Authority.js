import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Dropdown, Form, Button } from 'semantic-ui-react';

import * as cm_action from 'actions/commons';

class Authority extends Component {
  constructor(props) {
    super(props);
    this.state = {
      defaultOptions: [
        { value: null, text: '권한 전체' },
        { value: 'USER', text: '일반관리자' },
        { value: 'COM_ADMIN', text: '회사관리자' },
        { value: 'SUPER', text: '전체관리자' }
      ]
    };
  }

  componentWillMount() {
    const { cmAPI } = this.props;

    cmAPI.authChange({
      type: 'auth',
      name: 'value',
      value: null
    });
  }

  //권한 변경
  authorityChange = async (e, data) => {
    try {
      const { cmAPI, fnOnChange } = this.props;

      await cmAPI.authChange({
        type: 'auth',
        name: 'value',
        value: data.value
      });

      if (fnOnChange) {
        fnOnChange();
      }
    } catch (e) {
      console.log('###### authorityChange Error : ', e);
    } finally {
    }
  };

  render() {
    const { label, input, defaultValue, auth, isLabel, options } = this.props;
    const { defaultOptions } = this.state;

    let def = 0;

    //지정 된 디폴트 있을 시
    if (defaultValue) {
      def = defaultValue;
    }

    return (
      <Form.Field inline>
        {isLabel ? <label style={label}>사용자 권한 *</label> : null}

        <Dropdown
          selection
          style={input}
          className="authority"
          placeholder="권한 전체"
          value={def ? def : auth ? auth.value : null}
          options={options ? options : defaultOptions}
          onChange={this.authorityChange}
        />
      </Form.Field>
    );
  }
}

Authority = connect(
  (state) => ({
    auth: state.commons.auth
  }),
  (dispatch) => ({
    cmAPI: bindActionCreators(
      {
        authChange: cm_action.ReduxDataSet
      },
      dispatch
    )
  })
)(Authority);

export default Authority;
