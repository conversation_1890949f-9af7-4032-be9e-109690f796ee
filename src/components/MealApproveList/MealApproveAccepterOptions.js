import React, { Component, Fragment } from 'react';
import { Grid, Button, Form, Radio, Icon } from 'semantic-ui-react';
import { connect } from 'react-redux';
import styled from 'styled-components';
import { SearchInputBox } from 'components/Commons';

class MealApproveAccepterOptions extends Component {
  submitForm = async () => {
    const { handleSubmit, keyword } = this.props;
    const word = keyword ? keyword.value : '';
    await handleSubmit(word);
  };
  handleEntSubmit = e => {
    if (e.key === 'Enter') {
      this.submitForm();
    }
  };

  render() {
    const {
      authApproveInfo,
      accepterTemp,
      staffList,
      accepterRemove,
      modalFn,
      accepter,
      handleChange,
      checkedId
    } = this.props;
    const { open, openModal, closeModal } = modalFn;
    const styles = {
      marginRight: 5,
      padding: 0,
      bacckgroundColor: '#eee',
      color: '#222'
    };

    let checkAccepterCnt = 0;
    if (checkedId) {
      checkAccepterCnt = checkedId.length;
    } else {
      checkAccepterCnt = 0;
    }

    let allAccepterCnt = 0;
    if (accepterTemp && accepterTemp.data) {
      allAccepterCnt = accepterTemp.data.accepters
        ? accepterTemp.data.accepters.length
        : 0;
    }

    return (
      <StyledGrid divided="vertically">
        {authApproveInfo && (
          <Fragment>
            <StyledGridRow columns={2}>
              <StyledGridColumn width={2}>식대정책</StyledGridColumn>
              <StyledGridColumn width={14}>
                {`${authApproveInfo.groupName} > ${authApproveInfo.policyName}`}
              </StyledGridColumn>
            </StyledGridRow>

            <StyledGridRow columns={2}>
              <StyledGridColumn width={2}>신청종류</StyledGridColumn>
              <StyledGridColumn width={14}>결재라인 식권신청</StyledGridColumn>
            </StyledGridRow>

            <StyledGridRow columns={3}>
              <StyledGridColumn width={2}>선택</StyledGridColumn>
              <StyledGridColumn width={14}>
                <Radio
                  label="사용자 전체"
                  name="accepterType"
                  value="ALL"
                  checked={accepter === 'ALL'}
                  onChange={() => handleChange('ALL')}
                  style={{ marginRight: 20 }}
                />
                <Radio
                  label="지정 사용자"
                  name="accepterType"
                  value="ADMIN"
                  checked={accepter === 'ADMIN'}
                  onChange={() => handleChange('ADMIN')}
                  style={{ marginRight: 20 }}
                />
                <Radio
                  label="부서 사용자"
                  name="accepterType"
                  value="DIVISION"
                  checked={accepter === 'DIVISION'}
                  onChange={() => handleChange('DIVISION')}
                />
              </StyledGridColumn>
            </StyledGridRow>

            {accepter !== 'ALL' ? (
              <Fragment>
                <StyledGridRow columns={3}>
                  <StyledGridColumn width={2}>사용자 추가</StyledGridColumn>
                  <StyledGridColumn width={14}>
                    <Button inverted color="grey" onClick={openModal}>
                      <Icon disabled name="plus" style={styles} />
                      사용자
                    </Button>
                  </StyledGridColumn>
                </StyledGridRow>

                <StyledGridRow columns={3}>
                  <StyledGridColumn width={2}>
                    {checkAccepterCnt}명 /{allAccepterCnt}명
                  </StyledGridColumn>
                  <StyledGridColumn width={14}>
                    <Grid className="ui form">
                      <Form.Group>
                        <SearchInputBox
                          handleSubmit={this.submitForm}
                          handleEntSubmit={e => this.handleEntSubmit(e)}
                          placeHolder={'ID, 이름'}
                        />
                        <Button
                          inverted
                          color="red"
                          onClick={accepterRemove}
                          content="삭제"
                          style={{ padding: '0.5em 1.5em' }}
                        />
                      </Form.Group>
                    </Grid>
                  </StyledGridColumn>
                </StyledGridRow>
              </Fragment>
            ) : null}
          </Fragment>
        )}
      </StyledGrid>
    );
  }
}

const StyledGrid = styled(Grid)`
  padding: 16px 0 !important;
`;

const StyledGridRow = styled(Grid.Row)`
  &&&& {
    font-weight: bold;
    padding: 0;

    &::before {
      display: none;
    }
    &.active {
      background-color: #1db53a;
      color: #fff;
    }
    &.disabled {
      background-color: #eee;
      color: rgba(0, 0, 0, 0.6);
      cursor: not-allowed;
    }
  }
`;

const StyledGridColumn = styled(Grid.Column)`
  &&&& {
    padding: 0 16px;
    margin: 0;
  }
`;

MealApproveAccepterOptions = connect(state => ({
  staffList: state.commons.staff,
  keyword: state.commons.text
}))(MealApproveAccepterOptions);

export default MealApproveAccepterOptions;
