import React, { Component } from 'react';
import PropTypes from 'prop-types';

import { Loader, Segment, Dimmer } from 'semantic-ui-react';

class LoadingBar extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loader: <div></div>,
      isView: false
    };
  }

  componentWillMount() {
    const { style, children } = this.props;
    let styles = {};
    if (style) {
      styles = style;
    } else {
      // styles['opacity'] = '0.5';
      styles['backgroundColor'] = 'rgb(0, 0, 0, 0.5)';
      styles['zIndex'] = '10000';
    }
    this.setState({
      loader: (
        <Dimmer active inverted style={styles}>
          <Loader inverted size="massive" style={{ color: '#E0E0E0' }}>
            <div>Loading</div>
            <div>{children}</div>
          </Loader>
        </Dimmer>
      )
    });
  }

  componentDidMount() {
    // setTimeout(() => {
    //   this.setState({
    //     loader : <div>Error!</div>
    //   })
    // }, 50000)
  }

  render() {
    const { loader } = this.state;
    const { style } = this.props;

    return <div className="loader-content">{loader}</div>;
  }
}

export default LoadingBar;
