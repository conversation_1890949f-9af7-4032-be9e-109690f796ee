import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { Form, Dropdown } from 'semantic-ui-react';

import * as cmAPI_Action from 'actions/commons';

class StatusList extends Component {
  constructor() {
    super();
  }

  componentWillMount() {
    this.statusSearch(null, { value: 0 });
  }

  // 상태 선택
  statusSearch = async (e, data) => {
    const { name, commonFn, onChangeFn } = this.props;
    await commonFn.changeStatus({
      type: 'statusStr',
      name: name || 'value',
      value: data.value === 0 ? null : data.value
    });

    if (onChangeFn) {
      onChangeFn();
    }
  };

  render() {
    const { defaultOption, status } = this.props;

    const placeHolder = defaultOption || '상태 전체';
    const defaultOpt = [{ text: placeHolder, value: 0 }];
    let options;

    if (status) {
      options = defaultOpt.concat(status.data.status);
    } else {
      options = defaultOpt;
    }

    return (
      <Form.Field inline className="status-list">
        <Dropdown selection defaultValue={0} options={options} onChange={this.statusSearch} />
      </Form.Field>
    );
  }
}

StatusList = connect(
  (state) => ({
    statusStr: state.commons.statusStr
  }),
  (dispatch) => ({
    commonFn: bindActionCreators(
      {
        changeStatus: cmAPI_Action.ReduxDataSet
      },
      dispatch
    )
  })
)(StatusList);

export default StatusList;
