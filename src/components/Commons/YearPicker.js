import React, { Component } from 'react';
import PropTypes from 'prop-types';

import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import moment from 'moment-timezone';
moment.tz.setDefault('Asia/Seoul');

import { Dropdown, Form } from 'semantic-ui-react';

import * as cm_action from 'actions/commons';
import cm from 'helpers/commons';

class YearPicker extends Component {
  constructor(props) {
    super(props);
  }

  render() {
    let group = null;
    const { input, startYear, style } = this.props,
      defaultOptions = [{ value: 0, text: '식대그룹 전체' }];

    let options = [
      { value: 2019, text: '2019' },
      { value: 2018, text: '2018' }
    ];

    return (
      <Form.Field inline style={style} className="com-sikdae-group">
        <Dropdown selection style={input} placeholder="식대그룹 전체" value={startYear} options={options} />
      </Form.Field>
    );
  }
}

export default YearPicker;
