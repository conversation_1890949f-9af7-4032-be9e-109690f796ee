import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';

import { TableForm, InfiniteScroll, LoadingBar } from 'components/Commons';
import { Popup } from 'semantic-ui-react';
import update from 'react-addons-update';
import ReactTable from 'react-table';
import Infinite from 'react-infinite';
import scroll from 'helpers/scroll';
import cm from 'helpers/commons';
import storage from 'helpers/storage';
import UserSikdaeGivenModal from './Modal/UserSikdaeGivenModal';

class UserSikdaeGivenResult extends Component {
  constructor() {
    super();
    this.state = {
      id: 'desc',
      page: 1,
      name: 'asc',
      elements: [],
      heightIdx: 0,
      containerHeight: 620,
      headers: [
        { header: 'ID', width: 120 },
        { header: '이름', width: 128 },
        { header: '사번', width: 128 },
        { header: '직위', width: 88 },
        { header: '직책', width: 88 },
        { header: '식대그룹', width: 128 },
        { header: '식대지급/차감', width: 100, className: 'right', headerClassName: 'right' },
        { header: '식대사용', width: 100, className: 'right', headerClassName: 'right' },
        { header: '식사횟수', width: 100, className: 'right', headerClassName: 'right' }
      ]
    };
  }

  componentWillMount() {}

  componentDidMount() {
    const windowH = $(window).height();
    const searchFormH = $('.ui.form').outerHeight();

    const listH = windowH - (searchFormH + 150);

    this.setState({
      containerHeight: update(this.state.param, { $set: listH })
    });
  }

  shouldComponentUpdate(nextProps, nextState) {
    const { sikList, resultFn } = this.props;
    const { isInfiniteLoading } = this.state;
    const loadingChange = isInfiniteLoading !== nextState.isInfiniteLoading;
    // return loadingChange || resultFn.modal;
    return loadingChange || !isInfiniteLoading || resultFn.modal;
  }

  async componentWillReceiveProps(nextProps) {
    // if (params !== nextProps.params) {
    this.scrollEvent(nextProps.sikList);
    // }
  }

  scrollEvent = (sikList) => {
    if (sikList && sikList.data.user) {
      const { user } = sikList.data;
      // const elements = scroll.buildElements(0, 100, user);
      this.setState({
        elements: this.buildElements(0, 100, user),
        isInfiniteLoading: false
      });
    } else {
      this.setState({ elements: [] });
    }
  };

  handleInfiniteLoad = () => {
    const { sikList } = this.props;
    const { elements, heightIdx, containerHeight } = this.state;
    const item = sikList ? sikList.data.paging : null;

    const that = this;
    const elemLength = elements.length;
    const itemLength = item ? item.totalcount : 0;
    if (elemLength < itemLength) {
      this.setState({
        isInfiniteLoading: true,
        heightIdx: heightIdx + containerHeight
      });

      const data = this.buildElements(elemLength, elemLength + 100, sikList.data.user, false);
      const infiniteParm = scroll.event(item, elements, containerHeight, heightIdx, elemLength);

      setTimeout(function() {
        that.setState({
          elements: that.state.elements.concat(data),
          heightIdx: infiniteParm.heightIdx,
          isInfiniteLoading: false
        });
      }, 500);
    } else {
      this.setState({
        isInfiniteLoading: false
      });
    }
  };

  buildElements = (st, en, item) => {
    try {
      const { headers } = this.state;
      const elements = [];
      if (st === 0) {
        const styles = [];
        const depth = storage.get('depth');
        headers.forEach((headerItem) => {
          styles.push({
            flex: `${headerItem.width} 0 auto`,
            width: headerItem.width,
            maxWidth: headerItem.width,
            padding: '10px 0'
          });
        });
        const divisionDom = [];
        for (let i = 0; i < depth; i++) {
          divisionDom.push(
            <div key={`${item.id}divisionDom-${i}`} className="rt-td" style={{ flex: `128 0 auto`, width: 128 }} />
          );
        }

        let totalDom = null;
        // if(idx === 0) {
        const { sikTotal } = this.props;
        const totalData = { outamount: 0, inamount: 0, couponcount: 0 };

        if (sikTotal && sikTotal.data) {
          const { total } = sikTotal.data;
          totalData.outamount = total.outamount;
          totalData.inamount = total.inamount;
          totalData.couponcount = total.couponcount;
        }
        totalDom = (
          <div className="rt-tr -odd totaltr" style={{ width: '100%' }} key="totaltd">
            <div className="rt-td" style={styles[0]}>
              합계
            </div>
            <div className="rt-td" style={styles[1]} />
            {divisionDom.map((e) => e)}
            <div className="rt-td" style={styles[2]} />
            <div className="rt-td" style={styles[3]} />
            <div className="rt-td" style={styles[4]} />
            <div className="rt-td right" style={styles[5]}>
              {cm.numberComma(totalData.inamount)}
            </div>
            <div className="rt-td right" style={styles[6]}>
              {cm.convertMinusNumber(totalData.outamount)}
            </div>
            <div className="rt-td right" style={styles[7]}>
              {cm.numberComma(totalData.couponcount)}
            </div>
          </div>
        );
        // }

        elements.push(totalDom);
      }

      if (item) {
        const user = item;

        for (let i = st; i < en; i++) {
          const temp = this.listItem(user[i], i);

          if (temp) {
            elements.push(temp);
          }
        }
      }

      return elements;
    } catch (e) {
      console.log('###### e : ', e);
    }
  };

  listItem = (item, idx) => {
    const { headers } = this.state;
    let result = null;
    if (item) {
      const division = cm.trTdDivision(item.orgCode);
      const styles = [];
      headers.forEach((headerItem, index) => {
        styles.push({
          flex: `${headerItem.width} 0 auto`,
          width: headerItem.width,
          maxWidth: headerItem.width,
          padding: '10px 0'
        });
      });

      result = (
        <div className="rt-tr -odd" style={{ width: '100%' }} key={item.id} onClick={(e) => this.detClick(e, item)}>
          <div className="rt-td" style={styles[0]}>
            {item.signid ? item.signid : '-'}
          </div>
          <div className="rt-td" style={styles[1]}>
            {item.name}
          </div>
          <div className="rt-td" style={styles[2]}>
            {item.comidnum}
          </div>
          {division.map((e, i) => {
            return (
              <div
                key={`${item.id}division-${i}`}
                className="rt-td"
                style={{ flex: `128 0 auto`, width: 128, padding: '10px 0' }}
              >
                <Popup className="td-tooltip" inverted trigger={<span>{e}</span>} content={e} />
              </div>
            );
          })}
          <div className="rt-td" style={styles[3]}>
            {item.rankposition ? item.rankposition : '-'}
          </div>
          <div className="rt-td" style={styles[4]}>
            {item.position ? item.position : '-'}
          </div>
          <div className="rt-td" style={styles[5]}>
            {item.group.name ? item.group.name : '-'}
          </div>
          <div className="rt-td right" style={styles[6]}>
            {item.coupon.inamount ? cm.convertMinusNumber(item.coupon.inamount) : '-'}
          </div>
          <div className="rt-td right" style={styles[7]}>
            {item.coupon.outamount ? cm.convertMinusNumber(item.coupon.outamount) : '-'}
          </div>
          <div className="rt-td right" style={styles[8]}>
            {item.coupon.count}
          </div>
        </div>
      );

      return result;
    }
    return null;
  };

  elementInfiniteLoad = () => {
    const { sikList } = this.props;
    let dom = <LoadingBar />;
    if (!sikList) {
      dom = null;
    }
    return dom;
  };

  tBodyFunc = (state) => {
    const { elements, containerHeight, isInfiniteLoading } = this.state;
    return (
      <div className="rt-tbody" style={state.style}>
        <div className="rt-tr-group">
          <Infinite
            className="infinite-scroll"
            elementHeight={40}
            containerHeight={containerHeight}
            infiniteLoadBeginEdgeOffset={containerHeight - 50}
            onInfiniteLoad={this.handleInfiniteLoad}
            loadingSpinnerDelegate={this.elementInfiniteLoad()}
            isInfiniteLoading={isInfiniteLoading}
            handleScroll={this.scrollHandler}
          >
            {elements}
          </Infinite>
        </div>
      </div>
    );
  };

  noDataProps = () => {
    const { sikList } = this.props;
    let dom = <div className="rt-noData">검색 결과가 없습니다.</div>;

    if (sikList && sikList.data.user) {
      dom = <div className="rt-noData" />;
    }

    return dom;
  };

  detClick = (d, item) => {
    const { openModal } = this.props.resultFn;
    const userId = item.id;
    if (!userId) return;
    openModal(userId);
  };

  changeOrdering = (name, value) => {
    this.setState({ [name]: value });
  };

  render() {
    const { headers } = this.state;
    const { resultFn } = this.props;
    const { modal, closeModal, pageClickModal, downloadExcel, isLoading } = resultFn;
    const modalFn = {
      modal,
      closeModal,
      pageClickModal,
      downloadExcel,
      isLoading
    };
    const header = cm.tableHeaderList(headers, 3, null, 'notMaxWidth');
    const fn = { fnChecked: null, move: null };
    const columns = scroll.header(header, fn);

    return (
      <div>
        <ReactTable
          className="react-table"
          noDataText="검색 결과가 없습니다."
          columns={columns}
          sortable={false}
          resizable={false}
          showPagination={false}
          getLoadingProps={this.test}
          TbodyComponent={this.tBodyFunc}
          NoDataComponent={this.noDataProps}
        />

        <UserSikdaeGivenModal modalFn={modalFn} />
      </div>
    );
  }
}

UserSikdaeGivenResult = connect((state) => ({
  sikList: state.usage.userSikdaeList,
  sikTotal: state.usage.userSikdaeTotal
}))(UserSikdaeGivenResult);

export default UserSikdaeGivenResult;
