import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Grid, Container } from 'semantic-ui-react';

import StepTwoGroup from './StepTwoGroup';
import StepTwoStaff from './StepTwoStaff';

const propTypes = {};

const defaultProps = {};

class GiveDeductList extends Component {
  constructor(props) {
    super(props);
  }

  groupStaffList = () => {
    const { data, staffCheckedData } = this.props;

    return;
  };

  render() {
    const { data, fn, sikdaeGroup } = this.props;

    let groupStaffList = [];
    let groupNum = data.groupNum;

    sikdaeGroup.data.group.some((item, i) => {
      if (!groupNum) {
        data.staffCheckedData.some((data, idx) => {
          if (data.group.idx === item.idx) {
            groupNum = item.idx;
            return;
          }
        });
      }
    });

    data.staffCheckedData.forEach((item) => {
      let idx = item.group.idx;

      if (idx === groupNum) {
        groupStaffList.push(item);
      }
    });

    return (
      <div>
        <Container fluid>
          <Grid className="group-box">
            <Grid.Column width={3} className="group">
              <StepTwoGroup data={data} fn={fn} groupNum={groupNum} />
            </Grid.Column>
            <Grid.Column width={13} className="staff">
              <StepTwoStaff data={data} fn={fn} groupNum={groupNum} groupStaffList={groupStaffList} />
            </Grid.Column>
          </Grid>
        </Container>
      </div>
    );
  }
}

GiveDeductList.propTypes = propTypes;
GiveDeductList.defaultProps = defaultProps;

GiveDeductList = connect((state) => ({
  sikdaeGroup: state.commons.sikdaeGroup
}))(GiveDeductList);

export default GiveDeductList;
