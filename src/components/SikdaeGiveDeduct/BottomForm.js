import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Button, Grid } from 'semantic-ui-react';

import cm from 'helpers/commons';

const propTypes = {};

const defaultProps = {};

class BottomForm extends Component {
  constructor(props) {
    super(props);
  }

  render() {
    const { fn, data } = this.props;

    let totalMoney = 0;
    let totalUser = 0;

    //총 금액과 총 인원 데이터 합계
    if (data.actForm.dataSet) {
      data.actForm.dataSet.forEach((data) => {
        // 총 금액
        if (data.money) {
          totalMoney += data.money;
        }
        // 총 인원
        if (data.count) {
          totalUser += data.count;
        }
      });
    }

    let cName = data.topMenu.tabType === 'deduct' ? 'deduct' : 'give';
    let isMinus = data.topMenu.tabType === 'deduct' ? '(-)' : '';
    return (
      <Grid>
        <Grid.Column floated="left" width={5}>
          {data.topMenu.nowStep === 'step2' ? (
            <Button color="black" inverted onClick={(e) => fn.stepMove('step1')}>
              {'< 이전으로'}
            </Button>
          ) : null}
          {data.topMenu.nowStep === 'step3' ? (
            <Button color="black" inverted onClick={(e) => fn.stepMove('step2')}>
              {'< 이전으로'}
            </Button>
          ) : null}
        </Grid.Column>
        <Grid.Column floated="right" width={5}>
          {data.topMenu.nowStep === 'step1' ? (
            <Button color={cName === 'give' ? 'green' : 'red'} onClick={(e) => fn.stepMove('step2')} className={cName}>
              {'정책 선택 >'}
            </Button>
          ) : null}
          {data.topMenu.nowStep === 'step2' ? (
            <Button color={cName === 'give' ? 'green' : 'red'} onClick={(e) => fn.stepMove('step3')} className={cName}>
              {cName === 'give' ? '날짜/금액 설정 >' : '금액 설정 >'}
            </Button>
          ) : null}
          {data.topMenu.nowStep === 'step3' ? (
            <div>
              <span className="total-text">
                {totalUser + '명 | 총 ' + isMinus + cm.numberComma(totalMoney) + ' 원(장)'}
              </span>
              <Button
                color={cName === 'give' ? 'green' : 'red'}
                onClick={(e) => fn.stepMove('step4-1')}
                className={cName}
              >
                {cName === 'give' ? '지급 완료 >' : '차감 완료 >'}
              </Button>
            </div>
          ) : null}
        </Grid.Column>
      </Grid>
    );
  }
}

BottomForm.propTypes = propTypes;
BottomForm.defaultProps = defaultProps;

export default BottomForm;
