import React, { Component } from 'react';
import { Container, Divider, Header, Grid } from 'semantic-ui-react';
import InfoGridBox from './InfoGridBox';

class Info extends Component {
  constructor(props) {
    super(props);
  }

  render() {
    const { companyInfoObj } = this.props;
    const { Info } = companyInfoObj || {};
    const {
      companyName,
      bizSerial,
      chargeName,
      address,
      bizCondition,
      bizType,
      phone,
      bizSubSerial,
      managerList,
      settleType,
      settlePeriodType,
      settleStartDay,
      settleEndDay,
      taxFormatType,
      taxInvoiceDate,
      bankName,
      accountNo,
      depositor,
      depositDay,
      taxInvoiceEmail1,
      taxInvoiceEmail2,
      depositType,
      depositHolidayType,
      storePayDay
    } = Info || {};
    const managerListInfo = managerList || [];
    const { value: settleTypeVal = '' } = settleType || {};
    const { value: settlePeriodTypeVal = '' } = settlePeriodType || {};
    const { value: taxFormatTypeVal = '' } = taxFormatType || {};
    const { value: taxInvoiceDateVal = '' } = taxInvoiceDate || {};
    const { value: depositTypeVal = '' } = depositType || {};
    const { value: depositHolidayTypeVal = '' } = depositHolidayType || {};

    return (
      <div className="container-box company">
        <Container fluid className="info-form">
          <Header as="h2">고객회사 기준 정보</Header>
          <Divider />

          <InfoGridBox title="법인명" info={companyName} />
          <InfoGridBox title="사업자 번호" info={bizSerial} />
          <InfoGridBox title="대표자명" info={chargeName} />
          <InfoGridBox title="주소" info={address} />
          <InfoGridBox title="업태" info={bizCondition} />
          <InfoGridBox title="종목" info={bizType} />
          <InfoGridBox title="연락처" info={phone} />
          <InfoGridBox title="종사업자 번호" info={bizSubSerial} />
          {managerList ? (
            <Grid>
              <Grid.Column width={3}>
                <b>고객회사 담당자</b>
              </Grid.Column>
              <Grid.Column width={11}>
                {managerListInfo.map((el, idx) => {
                  const { name, email, phoneNumber, mobileNumber } = el || {};
                  return (
                    <div key={`key_${idx}`}>
                      {name ? <p>{name}</p> : null}
                      {email ? <p>{email}</p> : null}
                      {phoneNumber ? <p>{phoneNumber}</p> : null}
                      {mobileNumber ? <p>{mobileNumber}</p> : null}
                    </div>
                  );
                })}
              </Grid.Column>
            </Grid>
          ) : null}
        </Container>

        <Container fluid className="low-rank info-form">
          <Header as="h2">정산 기준 정보</Header>
          <Divider />

          <InfoGridBox title="정산 방식" info={settleTypeVal} />
          <InfoGridBox title="정산 주기" info={settlePeriodTypeVal} />
          <InfoGridBox title="첫번째 시작일" info={settleStartDay} />
          <InfoGridBox title="첫번째 종료일" info={settleEndDay} />
          <InfoGridBox title="세금계산서 포맷" info={taxFormatTypeVal} />
          <InfoGridBox title="세금신고일자 유형" info={taxInvoiceDateVal} />
        </Container>

        <Container fluid className="low-rank info-form">
          <Header as="h2">입금 기준 정보</Header>
          <Divider />

          <InfoGridBox title="은행 정보" info={bankName} />
          <InfoGridBox title="계좌번호" info={accountNo} />
          <InfoGridBox title="입금자" info={depositor} />
          <InfoGridBox title="입금일" info={depositDay} />
          <InfoGridBox title="세금계산서 전송 메일1" info={taxInvoiceEmail1} />
          <InfoGridBox title="세금계산서 전송 메일2" info={taxInvoiceEmail2} />
          <InfoGridBox title="입금 유형" info={depositTypeVal} />
          <InfoGridBox title="휴일시" info={depositHolidayTypeVal} />
          <InfoGridBox title="식당 식대 지급일" info={storePayDay} />
        </Container>
      </div>
    );
  }
}
export default Info;
