import React, { Component } from 'react';
import PropTypes from 'prop-types';

import { Grid, Container, Image, Form, Checkbox, Icon, Header, Message } from 'semantic-ui-react';
import { Button } from 'antd';
import { loadScript } from 'helpers/cloudgate';
import loginImage from 'images/login-image.png';
import logo from 'images/loginLogo.png';
import footerLogo from 'images/footerLogo.svg';
import captainPayments from 'images/captainPayments.png';
import Dpm_logo from 'images/payments/logo_black.svg';

import channelTalkService from 'helpers/channeltalk';
import storage from 'helpers/storage';
import api from 'config';

const propTypes = {
  handleChange: PropTypes.func,
  loginAction: PropTypes.func
};

const defaultProps = {};

class LoginForm extends Component {
  constructor(props) {
    super(props);
  }

  async componentDidMount() {
    channelTalkService.boot({
      pluginKey: api.config.channel_tolk_plugin_key
    });
  }

  async componentWillUnmount() {
    // channelTalkService.shutdown();
  }

  handleEntSubmit = (e) => {
    if (e.key === 'Enter') {
      this.props.loginAction();
    }
  };

  // _onClick = () => {
  //   channelTalkService.boot();
  // }

  popOpen = () => {
    const uri = api.config.authAPI + '/account/find/password';
    const name = 'changePWD';
    const option = 'top=10, left=10, width=500, height=600, status=no, menubar=no, toolbar=no, resizable=no';
    const win = window.open(uri, name, option);
    return win;
  };

  render() {
    return (
      <Grid className="login-form" verticalAlign="middle">
        <Grid.Column className="login-form-col">
          <Header>
            <img src={Dpm_logo} alt="logo" />
            <p>기업비용 통합관리를 위한 식권대장의 새로운 관리자페이지</p>
          </Header>
          <Form size="large">
            <Form.Input
              className="form-input"
              label="아이디"
              placeholder=""
              value={this.props.id}
              autoFocus={!this.props.idSave}
              onChange={(e) => {
                this.props.handleChange(e.target.value, 'userID');
              }}
            />
            <Form.Input
              className="form-input"
              label="비밀번호"
              placeholder=""
              autoFocus={this.props.idSave}
              type="password"
              onChange={(e) => {
                this.props.handleChange(e.target.value, 'userPWD');
              }}
              onKeyPress={this.handleEntSubmit}
            />
            <Grid className="form-group">
              <Form.Checkbox
                className="form-checkbox"
                label="아이디 기억하기"
                checked={this.props.idSave}
                onChange={this.props.idSaveCheckbox}
              />
              <Button className="login-form-button-channel" onClick={this.popOpen}>
                비밀번호 찾기
              </Button>
            </Grid>

            <Button
              style={{ width: '100%', textShadow: 'none' }}
              type="primary"
              color="green"
              size="large"
              loading={this.props.isDim}
              onClick={this.props.loginAction}
            >
              로그인하기
            </Button>
            <p>고객센터 1644-5047</p>
          </Form>
          <div className="login-form-footer">
            <div className="login-form-footer-img">
              <Image src={footerLogo} />
            </div>
          </div>
        </Grid.Column>
      </Grid>
    );
  }
}

LoginForm.propTypes = propTypes;
LoginForm.defaultProps = defaultProps;

export default LoginForm;
