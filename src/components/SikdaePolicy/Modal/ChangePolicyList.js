import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON>, <PERSON>dal, Grid, Container, Table } from 'semantic-ui-react';

import cm from 'helpers/commons';
import { Commons } from 'components';

const propTypes = {};

const defaultProps = {};

class ChangePolicyList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      page: 1,
      isactive: true
    };
  }

  pageClick = async (page) => {
    const { fn } = this.props.modal;
    this.setState({ page: page });
    fn.changePolicyHistorySel(page);
  };

  render() {
    const { history } = this.props.modal;
    const { open, dimmer, close, pagerow } = this.props.modal;
    const columnWidth = [50, 150, 100, 100, 500, 100];

    let item = {
      page: 1,
      pagerow: pagerow,
      pageblock: 10,
      totalcount: 0,
      pageClick: this.pageClick
    };

    let data = '';
    if (history && history.data.history) {
      const { paging, history } = this.props.modal.history.data;
      const { page } = this.state;

      item = {
        page: paging.page,
        pagerow: paging.pageRow,
        pageblock: 10,
        totalcount: paging.totalCount,
        pageClick: this.pageClick
      };

      data =
        history.length > 0
          ? history.map((data, i) => {
              return (
                <Table.Row key={i}>
                  <Table.Cell verticalAlign="top" className="wNumber">
                    {paging.totalCount - (page - 1) * pagerow - i}
                  </Table.Cell>
                  <Table.Cell verticalAlign="top">{cm.yyyyMMddhhmm(new Date(data.regdate))}</Table.Cell>
                  <Table.Cell verticalAlign="top">{data.groupname}</Table.Cell>
                  <Table.Cell verticalAlign="top">{data.policyname}</Table.Cell>
                  <Table.Cell className="log">
                    {data.log
                      ? data.log.map((data, j) => {
                          return (
                            <div key={j}>
                              {data.content}
                              <br></br>
                            </div>
                          );
                        })
                      : '-'}
                  </Table.Cell>
                  <Table.Cell verticalAlign="top">{data.username}</Table.Cell>
                </Table.Row>
              );
            })
          : '';
    }

    return (
      <Modal size="large" dimmer={dimmer} open={open} className="change-policy-list-modal">
        <Modal.Header>식대정책 변경내역</Modal.Header>
        <Modal.Content>
          {history ? (
            <Container fluid style={{ textAlign: 'center' }}>
              <Table unstackable>
                <Table.Header>
                  <Table.Row>
                    <Table.HeaderCell className="wNumber">#</Table.HeaderCell>
                    <Table.HeaderCell className="wDate">날짜</Table.HeaderCell>
                    <Table.HeaderCell className="wSikdaeGroup">식대그룹</Table.HeaderCell>
                    <Table.HeaderCell className="wSikdaePolicy">식대정책</Table.HeaderCell>
                    <Table.HeaderCell style={{ width: '516px' }}>상세내역</Table.HeaderCell>
                    <Table.HeaderCell className="wUser">변경한 사람</Table.HeaderCell>
                  </Table.Row>
                </Table.Header>
                <Table.Body>
                  {item.totalcount === 0 ? (
                    <Table.Row textAlign="center">
                      <Table.Cell colSpan="6">조회 결과가 없습니다.</Table.Cell>
                    </Table.Row>
                  ) : null}
                  {data}
                </Table.Body>
              </Table>
              <Commons.Pagination
                page={item.page}
                pageRow={item.pagerow}
                pageBlock={item.pageblock}
                totalCount={item.totalcount}
                pageClick={item.pageClick}
              />
            </Container>
          ) : (
            <div style={{ height: '632px' }}>
              <Commons.LoadingBar />
            </div>
          )}
        </Modal.Content>
        <Modal.Actions>
          <Button
            positive
            color="green"
            content="닫기"
            onClick={(e) => close('close', false)}
            style={{ width: '128px' }}
          />
        </Modal.Actions>
      </Modal>
    );
  }
}

ChangePolicyList.propTypes = propTypes;
ChangePolicyList.defaultProps = defaultProps;

export default ChangePolicyList;
