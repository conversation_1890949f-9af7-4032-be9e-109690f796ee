import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Menu, Grid, Segment, List } from 'semantic-ui-react'
import { Commons } from 'components';

const propTypes = {
};

const defaultProps = {
};

class Policy extends Component {

  constructor(props) {
    super(props);
    this.state = {
      choicePolicy : 0
    }
  }

  componentWillReceiveProps() {
    this.setState({
      choicePolicy : 0
    })
  }


  click = (data) => {

    const { limitStoreSel } = this.props;
    this.setState({
      choicePolicy : data.idx
    })
    limitStoreSel(data.idx);
  }

  render() {

    const { choicePolicy } = this.state;
    const { sikdaePolicy } = this.props;

    return(
      <Menu vertical className="vendysLeftMenu" fluid>
        <Menu.Item className="top" name="title">
          식대정책
        </Menu.Item>
        {
          sikdaePolicy
          ? (
            sikdaePolicy.data.policy.map((data, i) => {
              let policyIdx = choicePolicy;

              if(policyIdx === 0) {
                policyIdx = sikdaePolicy.data.policy[0].idx;
              }

              return (
                <Menu.Item
                  name={data.name}
                  active={policyIdx === data.idx}
                  key={i}
                  onClick={(e) => this.click(data)}
                >
                  {data.name}
                </Menu.Item>
              )
            })
          )
          : (
            <Menu.Item>
              <Commons.LoadingBar style={{ margin: "100px 0"}}/>
            </Menu.Item>
          )
        }
      </Menu>
    );
  }
}

Policy.propTypes = propTypes;
Policy.defaultProps = defaultProps;

Policy = connect(

  state => ({
    sikdaePolicy    : state.commons.sikdaePolicy
  })

)(Policy);

export default Policy;
