import React, { Component } from 'react';
import styled from 'styled-components';
import { List, Icon } from 'semantic-ui-react';

class BillAccount extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }
  render() {
    const { children, billRowClick, choiceIdx } = this.props;
    return (
      <StyledList>
        {children.map((item) => {
          let icon = null;
          switch (item.approvalStatus) {
            case 'PROCESSING':
            case 'PENDING':
              icon = <ItemIcon name="ellipsis horizontal" />;
              break;
            case 'ACCEPTED':
              icon = <ItemIcon name="check" />;
              break;
            case 'REJECTED':
              icon = <ItemIcon name="ban" style={{ color: 'red' }} />;
              break;
            default:
              icon = null;
              break;
          }
          return (
            <StyledGroupItem key={item.billingAccountIdx}>
              <StyledBillItem
                isActivity={item.billingAccountIdx === choiceIdx}
                onClick={(e) => billRowClick(item.billingAccountIdx, item.name)}
                style={{ color: !item.userStatus ? '#e20b44' : '' }}
              >
                {item.name}
                {icon}
              </StyledBillItem>
            </StyledGroupItem>
          );
        })}
      </StyledList>
    );
  }
}

const StyledList = styled(List.List)`
  &&&& {
    padding: 0;
    margin-left: 20px;
  }
`;
const StyledGroupItem = styled(List.Item)`
  &&&& {
    padding: 0;
    border-top: solid 1px #dcdcdc;
  }
`;
const StyledBillItem = styled.div`
  &&&& {
    height: 40px;
    border-left: solid 1px #dcdcdc;
    border-bottom: solid 1px #dcdcdc;
    font-size: 14px;
    font-weight: bold;
    color: #ffffff;
    padding: 11px;
    position: relative;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    width: 100%;
    padding-right: 50px;
    cursor: pointer;
    background-color: ${(props) => (props.isActivity ? '#0bb656' : '#ffffff')};
    color: ${(props) => (props.isActivity ? '#ffffff' : '#000000')};
  }
  &&&&:hover {
    background-color: ${(props) => (props.isActivity ? '#0bb656' : '#eeeeee')};
  }
`;

const ItemIcon = styled(Icon)`
  &&&& {
    font-size: 24px;
    position: absolute;
    right: 10px;
    color: #000000;
  }
`;

export default BillAccount;
