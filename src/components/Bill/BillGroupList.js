import React, { Component } from 'react';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { Header, Segment, List, Icon } from 'semantic-ui-react';
import styled from 'styled-components';
import BillAccount from './BillAccount';
import * as usg_action from 'actions/usage';

class BillGroupList extends Component {
  constructor(props) {
    super(props);
    const { isLink, params, invoiceIdx } = props;
    const idx = isLink ? params.account : invoiceIdx ? invoiceIdx : props.billGroupList[0].billingAccountIdx;
    this.state = {
      choiceIdx: idx
    };
  }

  componentWillMount() {
    const { activeItem, billApprovalStepSel } = this.props;
    if (activeItem === 'bill') return;
    const { choiceIdx } = this.state;
    billApprovalStepSel(choiceIdx);
  }

  billRowClick = async (idx, name) => {
    const { billDtl, params, activeItem, ApprovalStepGroupIdx, billApprovalStepSel } = this.props;
    if (!idx) return;
    this.setState({ choiceIdx: idx });
    if (activeItem === 'bill') {
      billDtl(idx, params, false, name);
    } else {
      ApprovalStepGroupIdx(idx, name);
      billApprovalStepSel(idx, name);
    }
    $('.corp-wrapper').scrollTop(0);
  };

  render() {
    const { billGroupList } = this.props;
    const { choiceIdx } = this.state;
    return (
      <div>
        <StyledHeader as="h2" attached="top">
          청구목록
        </StyledHeader>
        <StyledSegment attached>
          <List>
            {billGroupList
              ? billGroupList.map((item, idx) => {
                  let icon = null;
                  switch (item.approvalStatus) {
                    case 'PROCESSING':
                    case 'PENDING':
                      icon = <ItemIcon name="ellipsis horizontal" />;
                      break;
                    case 'ACCEPTED':
                      icon = <ItemIcon name="check" />;
                      break;
                    case 'REJECTED':
                      icon = <ItemIcon name="ban" style={{ color: 'red' }} />;
                      break;
                    default:
                      icon = null;
                      break;
                  }

                  return (
                    <StyledGroupItem key={item.billingAccountIdx} className={`header-${idx}`}>
                      <StyledBillItem
                        isActivity={item.billingAccountIdx === choiceIdx}
                        onClick={(e) => this.billRowClick(item.billingAccountIdx, item.name)}
                        style={{ color: !item.userStatus ? '#e20b44' : '' }}
                      >
                        {item.name}
                        {icon}
                      </StyledBillItem>
                      {item.accountChildrens && item.accountChildrens.length > 0 ? (
                        <BillAccount
                          choiceIdx={choiceIdx}
                          children={item.accountChildrens}
                          billRowClick={this.billRowClick}
                        />
                      ) : null}
                    </StyledGroupItem>
                  );
                })
              : null}
          </List>
        </StyledSegment>
      </div>
    );
  }
}

const StyledHeader = styled(Header)`
  &&&& {
    margin-top: 0;
    background-color: #f8f8f8;
    height: 40px;
    border: solid 1px #dcdcdc;
    font-size: 14px;
    border-radius: 0;
  }
`;
const StyledSegment = styled(Segment)`
  &&&& {
    height: 100vh;
    overflow: scroll;
    min-height: 561px;
    max-width: none;
    padding: 0;
  }
`;
const StyledGroupItem = styled(List.Item)`
  &&&& {
    padding: 0;
    border-top: ${(props) => (props.className === 'header-0' ? '0' : 'solid 1px #dcdcdc')};
  }
`;

const StyledBillItem = styled.div`
  &&&&& {
    height: 40px;
    /* border-left: solid 1px #dcdcdc; */
    border-bottom: solid 1px #dcdcdc;
    font-size: 14px;
    font-weight: bold;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    padding: 11px;
    padding-right: 50px;
    position: relative;
    cursor: pointer;
    background-color: ${(props) => (props.isActivity ? '#0bb656' : '#ffffff')};
    color: ${(props) => (props.isActivity ? '#ffffff' : '#000000')};
  }
  &&&&:hover {
    background-color: ${(props) => (props.isActivity ? '#0bb656' : '#eeeeee')};
  }
`;

const ItemIcon = styled(Icon)`
  &&&& {
    font-size: 24px;
    position: absolute;
    right: 10px;
    color: #000000;
  }
`;
BillGroupList = connect(
  (state) => ({}),
  (dispatch) => ({
    usageAPI: bindActionCreators(
      {
        approvalStepSel: usg_action.BillApprovalStepList
      },
      dispatch
    )
  })
)(BillGroupList);

export default BillGroupList;
