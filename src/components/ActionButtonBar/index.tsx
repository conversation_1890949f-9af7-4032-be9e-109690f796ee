import React from 'react';

import { ConfirmArea } from 'styles/global'
import * as Title from 'vcomponents/Forms/Title';
import { Container, TitleWrapper } from './styles'

interface ActionButtonBarProps {
  titleId?: string
  title?: string
  children?: any
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
function ActionButtonBar(props: ActionButtonBarProps) {
  const { titleId, title } = props;
  return (
  <Container >
    <TitleWrapper>{titleId ? <Title.H2 msgId={titleId} /> : title ? <div>{title}</div> : undefined}</TitleWrapper>
    <ConfirmArea>
      {React.Children.map(props.children, child=>{
      return <child.type {...child.props} ></child.type>
      })}
    </ConfirmArea>
  </Container>
  );
};

export default ActionButtonBar;
