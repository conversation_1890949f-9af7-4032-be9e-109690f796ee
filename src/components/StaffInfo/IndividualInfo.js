import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import DatePicker from 'react-datepicker';
import moment from 'moment-timezone';
import { Form, Input, Button, Checkbox } from 'semantic-ui-react';
import 'react-datepicker/dist/react-datepicker.css';
import { Commons } from 'components';

moment.tz.setDefault('Asia/Seoul');

const birthday = moment().startOf('month');

const Certificate = ({ phone, phoneAuth, isCertification }) => {
  if (phone) {
    if (isCertification) {
      return (
        <div className="accredit col-green">
          <p>본인인증완료</p>
        </div>
      );
    }
    if (!phoneAuth && !isCertification) {
      return (
        <div className="accredit">
          <p>인증필요</p>
        </div>
      );
    }
    if (phoneAuth && !isCertification) {
      return (
        <div className="accredit col-green">
          <p>점유인증완료</p>
        </div>
      );
    }
    return null;
  }
  return null;
};

Certificate.propTypes = {
  phone: PropTypes.string.isRequired,
  phoneAuth: PropTypes.bool.isRequired,
  isCertification: PropTypes.bool.isRequired
};

const IndividualInfo = ({ handleEvent, info, type }, context) => {
  const style = {
    checkboxInput: { width: '500px' }
  };

  const handle = (e, data, handleType) => {
    handleEvent(handleType, data.value);
  };

  const dateHandle = (dateType, data) => {
    if (dateType === 'birthday') {
      handleEvent('birthday', data);
    }
  };

  return (
    <Form className="form-info">
      <Form.Field inline>
        <label>아이디 *</label>
        <Input
          placeholder="6자 이상 40자 이하로 입력해주세요."
          value={info.signid}
          disabled={type === 'dormant'}
          onChange={(e, data) => handle(e, data, 'signid')}
        />
        {type === 'join' ? (
          <div className="surely-text">
            <p>* 표시 항목은 필수 입력입니다.</p>
          </div>
        ) : null}
      </Form.Field>
      {type === 'join' ? (
        <span>
          <Form.Field inline>
            <label>비밀번호 *</label>
            <Input
              type="password"
              placeholder="4자 이상 입력해주세요."
              maxLength={20}
              onChange={(e, data) => handle(e, data, 'password')}
            />
          </Form.Field>
          <Form.Field inline>
            <label>비밀번호 재입력*</label>
            <Input
              type="password"
              placeholder="동일한 비빌번호를 입력해주세요."
              maxLength={20}
              onChange={(e, data) => handle(e, data, 'password2')}
            />
          </Form.Field>
          <Form.Field inline style={{ marginBottom: '13px' }}>
            <label />
            생성된 사용자는 최초 로그인시, 반드시 비밀번호 재설정 과정을 거치게 됩니다.
          </Form.Field>
        </span>
      ) : null}
      <Form.Field inline>
        <label>이름 *</label>
        <Input
          placeholder="이름을 입력해주세요."
          value={info.name}
          disabled={type === 'dormant'}
          onChange={(e, data) => handle(e, data, 'name')}
        />
      </Form.Field>
      {type !== 'dormant' ? (
        <Fragment>
          <Form.Field inline>
            <label>이메일</label>
            <Input
              placeholder="@포함 50자 이하로 입력해주세요."
              value={info.email}
              onChange={(e, data) => handle(e, data, 'email')}
            />
            {type === 'det' && info.email ? (
              !info.emailAuth ? (
                <div className="accredit">
                  <p>인증필요</p>
                </div>
              ) : (
                <div className="accredit col-green">
                  <p>인증완료</p>
                </div>
              )
            ) : null}
          </Form.Field>
          <Form.Field inline>
            <label>휴대전화번호</label>
            <Input
              placeholder="- 구분 없이 입력해주세요."
              value={info.phone}
              onChange={(e, data) => handle(e, data, 'phone')}
            />
            {type === 'det' ? (
              <Certificate phone={info.phone} phoneAuth={info.phoneAuth} isCertification={info.isCertification} />
            ) : null}
          </Form.Field>
          <Form.Field inline>
            <label>생년월일</label>
            <DatePicker
              className="birthday"
              selected={info.birthday}
              onChange={(date) => dateHandle('birthday', date)}
              dateFormat="YYYY-MM-DD"
              peekNextMonth
              showMonthDropdown
              showYearDropdown
              dropdownMode="select"
              placeholderText="YYYY-MM-DD"
              disabled={info.isCertification}
            />
          </Form.Field>
        </Fragment>
      ) : null}
    </Form>
  );
};

export default IndividualInfo;
