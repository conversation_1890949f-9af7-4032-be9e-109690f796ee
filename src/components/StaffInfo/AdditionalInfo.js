import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import { Form, Input, Button, Divider } from 'semantic-ui-react';
import { Commons } from 'components';

import cm from 'helpers/commons';

const propTypes = {};

const defaultProps = {};

class AdditionalInfo extends Component {
  constructor(props) {
    super(props);
    this.state = {
      mealcGorupAuth: cm.authGroupSet('meal-group')
    };
  }

  handle = async (e, data, type) => {
    const { handleEvent } = this.props;
    await handleEvent(type, data.value);

    // const { info } = this.props;
    // this.history(info);
  };

  status = (info) => {
    const { type } = this.props;
    if (info && info.grade) {
      const { status } = type === 'dormant' ? info : info.grade;
      switch (status) {
        case 'ACTIVE':
          return '활성 사용자';
        case 'INACTIVE':
          return '일시정지';
        case 'WITHDRAW':
          return '탈퇴';
        default:
          break;
      }
    }
  };

  render() {
    const style = {
      label: {
        width: '100px'
      },
      input: {
        width: '300px',
        margin: '0'
      }
    };
    const { mealcGorupAuth } = this.state;
    const { accountModal, data, info, type, barcodeModal, isPrintBarcode } = this.props;
    const { btns } = data;

    const { dormant: { target, expectedDate } = { target: undefined, expectedDate: {} } } = info;
    const status = this.status(info);

    const dormantText = type !== 'dormant' ? ['휴면 예정', '예정일'] : ['휴면 처리', '처리일'];

    return (
      <Form className="form-info">
        {(mealcGorupAuth.all || mealcGorupAuth.assign) && type !== 'dormant' ? (
          <Commons.SikdaeGroup isLabel defaultValue={info.group} />
        ) : null}

        {(mealcGorupAuth.all || mealcGorupAuth.assign) && type !== 'dormant' ? (
          <Form.Field inline>
            <label />
            <span style={{ color: 'red' }}>* 식대그룹 변경 시 사용자의 식대가 초기화 됩니다.</span>
          </Form.Field>
        ) : null}
        {type !== 'dormant' ? (
          <Fragment>
            <Commons.Division
              label={{ float: 'left' }}
              isLabel
              defaultValue={info.orgcodelist}
              staffDivisionList={data.staffDivision}
            />
            <Commons.Position value={info.rankposition} onChange={(e, data) => this.handle(e, data, 'rankposition')} />
            <Form.Field inline>
              <label>직책</label>
              <Input
                placeholder="20자 이하로 입력해주세요."
                value={info.position}
                onChange={(e, data) => this.handle(e, data, 'position')}
              />
            </Form.Field>
            <Form.Field inline>
              <label>사원번호</label>
              <Input
                placeholder="15이하로 입력해주세요."
                value={info.comidnum}
                onChange={(e, data) => this.handle(e, data, 'comidnum')}
              />
            </Form.Field>
          </Fragment>
        ) : null}
        <Form.Group inline className="field">
          <label>사용자 상태</label>
          <Form.Field style={{ marginRight: 20 }}>
            <span>{status}</span>
          </Form.Field>
          {status === '일시정지' ? (
            <Form.Field>
              <Button
                className="pwd-init-btn"
                inverted
                color="black"
                onClick={() => accountModal(true, '일시정지 변경')}
              >
                일시정지 변경
              </Button>
            </Form.Field>
          ) : (
            <Form.Field>
              {btns.sBtn.length > 0 ? btns.sBtn[0] : null}
              {btns.wBtn.length > 0 ? btns.wBtn[0] : null}
            </Form.Field>
          )}
        </Form.Group>
        {isPrintBarcode && type !== 'dormant' && (
          <Form.Group inline className="field">
            <label>바코드</label>
            <Form.Field style={{ marginRight: 20 }}>
              <Button color="green" content="바코드 출력" onClick={barcodeModal} />
            </Form.Field>
          </Form.Group>
        )}
        {status !== '일시정지' ? (
          <div>
            {btns.sBtn.length > 0 ? btns.sBtn[1] : null}
            {btns.wBtn.length > 0 ? btns.wBtn[1] : null}
          </div>
        ) : null}
        {/* <Commons.Vitality
          isLabel={true}
          defaultValue={info.level}
        /> */}
        {info.auth ? (
          <div>
            <Divider />
            <Form.Field inline>
              <div style={{ display: 'inline-table', width: '100%' }}>
                {info.auth.type === 'COMPANY' ? (
                  <p>
                    <li>{info.auth.name}</li>
                  </p>
                ) : (
                  <p>
                    <li>{`'${cm.divisionNavi(info.auth.orgcode)}' 범위의 '${info.auth.name}'`}</li>
                  </p>
                )}
              </div>
            </Form.Field>
          </div>
        ) : null}
      </Form>
    );
  }
}

AdditionalInfo.propTypes = propTypes;
AdditionalInfo.defaultProps = defaultProps;

export default AdditionalInfo;
