import React, { ReactElement, useEffect, useMemo, useRef, useState } from 'react';
import { Button, ConfigProvider, Layout } from 'antd';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import locale from 'antd/lib/locale/ko_KR';
import { serviceMenusAPI } from 'state';
import api from 'config';
import channelTalkService from 'helpers/channeltalk';
import { myEmployeeInfoAPI } from 'containers/Service/Management/UserDetail/state';
import storage from 'helpers/storage';
import { useQuery } from 'react-query';
import { FormattedMessage } from 'vendys/decorators/views';
import { tag } from 'images/companyEvent';
import { generateHash } from 'utils';
import { Content, ContentLayout } from './styles';
import MenuView from '../components/LNB/MenuView';

const { Header, Sider, Footer: AntdFooter } = Layout;

interface Props {
  children: ReactElement;
  location: any;
  serviceMenuList: Object;
}

function MainLayout(props: Props): ReactElement {
  const company = storage.get('company') || {};
  const isExistCompany = Object.keys(company).length > 1;
  const [paddingV, setPaddingV] = useState(20);
  const [menuSnapshot, setMenuSnapshot] = useState(null);
  const { location, children, history, serviceMenuList } = props;
  // const serviceMenus = useRecoilValue(serviceMenusState);
  const { data: serviceMenus, isLoading, isError } = useQuery('serviceMenusAPI', () => serviceMenusAPI());
  const { data: myInfo } = useQuery('myEmployeeInfoAPI', () => myEmployeeInfoAPI());

  const serviceName = useMemo(() => {
    try {
      return location.pathname.split('/')[2];
      // eslint-disable-next-line no-empty
    } catch (e) {}
    return null;
  }, [location]);

  const menus = useMemo(() => {
    const result = serviceMenus && serviceMenus[serviceName];

    if (result?.length > 0 && serviceName === 'welfare') {
      const isEvent = result.filter(({ name }) => name === '이벤트')[0];
      if (!isEvent) {
        result.unshift({
          idx: 50,
          name: '이벤트',
          title: (
            <div>
              이벤트
              <img
                src={tag}
                alt="new-badge"
                style={{ width: '35px', verticalAlign: 'middle', marginLeft: 7, marginBottom: 3 }}
              />
            </div>
          ),
          sort: 0,
          url: '',
          to: '/service/welfare/event'
        });
      }
    }
    setMenuSnapshot(result);

    if (serviceName === 'myInfo') {
      return menuSnapshot;
    }
    return result;
  }, [serviceMenus, serviceName]);

  useEffect(() => {
    if (location.pathname === `/service/${serviceName}/landing`) {
      setPaddingV(0);
    } else {
      setPaddingV(20);
    }
  }, [location]);

  return (
    <Layout className="site-layout">
      <ConfigProvider locale={locale}>
        <Sider width={240} className="site-layout-background" style={{ background: '#243346' }}>
          <MenuView data={menus} location={location} />
        </Sider>
        {location.pathname.indexOf('/main') < 0 ? (
          <ContentLayout className="vendys-contents">
            <Content style={{ padding: paddingV }}>{children}</Content>
          </ContentLayout>
        ) : null}
        {isExistCompany && location.pathname.indexOf('/benefits') < 0 ? (
          <ChannelTalk key={myInfo} location={location} myInfo={myInfo} />
        ) : null}
        {/* <Footer style={{ textAlign: 'center' }}>Ant Design ©2018 Created by Ant UED</Footer> */}
      </ConfigProvider>
    </Layout>
  );
}

const ChannelTalk = ({ location, myInfo }) => {
  const [isCustomChannelTalk, setIsCustomChannelTalk] = useState<boolean | undefined>(undefined);
  const bootChannelTalk = async (isCustomButton) => {
    const company = storage.get('company') || {};
    const { user = {} } = company;
    const memberHash = await generateHash(`corp_${company.userID}`, api.config.channel_tolk_plugin_key);

    const defaultBootOptions = {
      pluginKey: api.config.channel_tolk_plugin_key,
      userId: user?.id,
      memberId: `corp_${company.userID}`,
      memberHash,
      profile: {
        channel: '대장마켓플레이스',
        name: user?.name,
        username: user?.name,
        company: user?.company && user?.company?.name,
        mobileNumber: myInfo && myInfo.cellphone?.value,
        email: myInfo && myInfo.email?.value,
        companyAddress: '',
        userId: company.user?.id,
        companyId: company.id
      }
    };

    const customBootOptions = {
      customLauncherSelector: '.login-form-button-channel',
      hideChannelButtonOnBoot: true
    };

    channelTalkService.boot(isCustomButton ? { ...defaultBootOptions, ...customBootOptions } : defaultBootOptions);
  };

  useEffect(() => {
    if (isCustomChannelTalk !== undefined) {
      bootChannelTalk(isCustomChannelTalk);
    }
  }, [isCustomChannelTalk]);

  useEffect(() => {
    channelTalkService.hideMessenger();

    const islandingPath = location.pathname.indexOf('landing') > -1;
    setIsCustomChannelTalk(!islandingPath);
  }, [location.pathname]);

  return isCustomChannelTalk ? (
    <div
      style={{
        position: 'fixed',
        height: '100px',
        bottom: '0px',
        width: '239px',
        zIndex: 1,
        background: 'linear-gradient(0deg, #243346 83.1%, rgba(36, 51, 70, 0) 103.76%)'
      }}
    >
      <div style={{ width: '208px', left: '16px', position: 'fixed', bottom: '26px', zIndex: 99 }}>
        <Button className="login-form-button-channel" type="primary" style={{ height: '40px', width: '100%' }}>
          <FormattedMessage id="1:1" />
        </Button>
      </div>
    </div>
  ) : null;
};

export default withRouter(
  connect((state) => ({
    serviceMenuList: state.auth && state.auth.captainPayment && state.auth.captainPayment.serviceMenuList
  }))(MainLayout)
);
