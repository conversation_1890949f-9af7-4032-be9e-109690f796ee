import request from 'helpers/request';
// eslint-disable-next-line import/no-unresolved
import api from 'config';

export const getCaptainPaymentMe = async () => {
  const { data } = await request({
    url: `${api.config.companyAPI}/captain-payment/auth/v1/me`
  });
  return data;
};

export const getPermission = async (menuIdx) => {
  const { data } = request({
    url: `${api.config.companyAPI}/captain-payment/auth/v1/menu/${menuIdx}`
  });
  return data;
};

const authFuncFake = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, 1000);
  });
};

export const getSikdaePermission = async (params) => {
  const { data } = request({
    url: `${api.config.companyAPI}/captain-payment/pass/v1/service/application`,
    params
  });
  return data;
};
