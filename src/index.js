import 'core-js/stable';
import 'regenerator-runtime/runtime';
// for core-js v3:
import 'core-js/features/array/flat-map';
// IE11 브라우저 지원
import 'react-app-polyfill/ie11';
import 'react-app-polyfill/stable';
import 'abortcontroller-polyfill/dist/polyfill-patch-fetch';

/* eslint-disable import/no-unresolved */
import React from 'react';
import ReactDOM from 'react-dom';
import { BrowserRouter as Router } from 'react-router-dom';
import ReactGA from 'react-ga4';
import { Provider } from 'react-redux';
import { GlobalStyle } from 'styles/global';
import { ThemeProvider, theme } from 'styles/themes';

import { ModalContextProvider } from 'context/ModalContext';
import LanguageProvider from 'vendys/translations/LanguageProvider';

import store, { gaId } from './helpers/loggers';
import App from './containers/App';
import 'styles/themes/antd.less';

const rootElement = document.getElementById('root');
const customContext = React.createContext(null);

ReactGA.initialize([{ trackingId: gaId.DMP_TRACKING_ID }, { trackingId: gaId.MARKETING_TRACKING_ID }]);

const render = () => {
  ReactDOM.render(
    <Provider store={store} className="compnay-manager" context={customContext}>
      <LanguageProvider>
        <ThemeProvider theme={theme.default}>
          <ModalContextProvider>
            <GlobalStyle />
            <Router>
              <App store={store} />
            </Router>
          </ModalContextProvider>
        </ThemeProvider>
      </LanguageProvider>
    </Provider>,
    rootElement
  );
};
render();
