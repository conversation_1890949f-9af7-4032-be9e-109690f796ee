package com.vendys.payment.service;

import java.util.Calendar;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import org.junit.Test;
import org.junit.runner.RunWith;

import com.vendys.payment.entity.MealAccount;
import com.vendys.payment.entity.User;
import com.vendys.payment.persist.PointPersist;
import com.vendys.payment.persist.UserPersist;

/**
 * Created by jinwoo on 2018. 8. 3.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PointPersistTest {
    @Autowired
    private PointPersist pointPersist;
    @Autowired
    private UserPersist userPersist;

    @Test
    public void test() {
        String userId = "575CCA5F-6AFC-2406-BA95-5EE9C867648C";
        long policyIdx = 279;

        User user = this.userPersist.readUser(userId);
        List<MealAccount> result = this.pointPersist.getMealAccount(user.getUid(), user.getGroupIdx(), policyIdx);
        for (MealAccount account : result) {
            System.out.println(account);
        }

    }

    @Test
    public void test2() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 17);
        cal.set(Calendar.MINUTE, 48);
        cal.set(Calendar.SECOND, 40);
        System.out.println(cal.getTime());

        String userId = "60A5B0C3-C881-032D-922A-95E11A4F356C";

        com.vendys.payment.entity.MealPointLog r2 = this.pointPersist.getMealPointLogOffset(userId, 1206, cal.getTime());
        System.out.println(r2);
    }
}
