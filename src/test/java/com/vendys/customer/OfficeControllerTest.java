package com.vendys.customer;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

/**
 * Created by jinwo<PERSON> on 2017. 10. 18.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class OfficeControllerTest {

	@Autowired
	private WebApplicationContext wac;
	private MockMvc mockMvc;

	final static String xCustomer = "{\"client\":\"Test\",\"userId\":\"7AAC05DF-8DFB-D67F-9FB1-2C3D8125017D\",\"version\":\"1.0\"}";

	@Before
	public void setUp() {
		this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
	}

	@Test
	public void officeList() throws Exception {
		String comId = "B068366F-5EB8-1C7E-E79C-D438C8E32F9D";
		ResultActions result = this.mockMvc.perform(get("/company/v1/" + comId + "/office")
				.header("X-Customer", xCustomer));

		result.andDo(print());
		result.andExpect(status().isOk());
	}

	@Test
	public void storeList() throws Exception {
		String comId = "B068366F-5EB8-1C7E-E79C-D438C8E32F9D";
		long officeIdx = 2;
		ResultActions result = this.mockMvc.perform(get("/company/v1/" + comId + "/office/" + officeIdx + "/store")
				.header("X-Customer", xCustomer));

		result.andDo(print());
		result.andExpect(status().isOk());
	}
}
