package com.vendys.customer.util;

import java.io.Serializable;
import java.lang.reflect.Method;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.assertEquals;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import com.vendys.customer.thirdparty.naver.NaverCrypto;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {NaverCrypto.class, NaverCrypto.class, ConverterUtil.class, ObjectMapper.class})
public class NaverCryptoTest {

    @Autowired
    NaverCrypto naverCrypto;

    Method encryptStrToStrMethod;
    Method decryptStrToStrMethod;

    @Before
    public void getMethod() throws Exception {
        this.encryptStrToStrMethod = this.naverCrypto.getClass().getDeclaredMethod("encrypt", String.class);
        this.encryptStrToStrMethod.setAccessible(true);
        this.decryptStrToStrMethod = this.naverCrypto.getClass().getDeclaredMethod("decrypt", String.class);
        this.decryptStrToStrMethod.setAccessible(true);
    }

    @Test
    public void encryptDecryptTest() throws Exception {
        String testText = "강민기아맨야dsakldas";
        String encrypt = (String) this.encryptStrToStrMethod.invoke(this.naverCrypto, testText);
        String decrypt = (String) this.decryptStrToStrMethod.invoke(this.naverCrypto, encrypt);
        assertEquals(testText, decrypt);
    }

    @Test
    public void encryptNaverTest() throws Exception {
        String str = "naverpay";
        String expect = "ntAaxxZLgSxRwWvhNxH97g==";

        String encrypt = this.naverCrypto.encrypt(str);
        assertEquals(expect, encrypt);
    }

    @Test
    public void encryptDecryptObjectTest() throws Exception {
        TestObj obj = new TestObj();
        String encrypt = this.naverCrypto.encryptObject(obj);
        TestObj decrypt = this.naverCrypto.decrypt(encrypt, TestObj.class);
        assertEquals(obj, decrypt);
    }

    @Test
    public void mini() throws Exception {
        String str = this.naverCrypto.decrypt("0qOMzdQwai2Lq24RO5jCCr0KwseuZCeR6wfWvopBKSketswAIOqdvNWe0FVDdpHcWj4jBrlHHEswP2fnBbjNWtqF4gM3pRMs/fCP7w6y2nSa8h/jAm0yctrN5FLfyROy6lIqlEUUfgzkebDgXN/QE0B6p+ZpsnKc2Fcm2YElwt1nMYk9B2djBL+/3noKTavA2YPYB/tpHslBsz3VqejM2je6RcAQbl3pwgnU6oOGKEeU7NGZgb6d05n4VhkUQ0HHzQuo3gCqEmeAyKXy5oKFXA==");
        System.out.println(str);
        // http://dev.apis.naver.com/i23G8tvVyi7uNnEgHMR9RBwnHZuQGbO9CvqUoHeHwIs5aJrSH6Es6JeVMqP0tE7d/naverpay_user_point/cardpoint_checkauth

        // ?msgpad=1637234110955
        //         &md=nlImUX2srcgictqHf0x4x93dHQU%253D
        //         &partnerCode=TEST
        //         &data=0qOMzdQwai2Lq24RO5jCCr0KwseuZCeR6wfWvopBKSketswAIOqdvNWe0FVDdpHcWj4jBrlHHEswP2fnBbjNWtqF4gM3pRMs/fCP7w6y2nSa8h/jAm0yctrN5FLfyROy6lIqlEUUfgzkebDgXN/QE0B6p+ZpsnKc2Fcm2YElwt1nMYk9B2djBL+/3noKTavA2YPYB/tpHslBsz3VqejM2je6RcAQbl3pwgnU6oOGKEeU7NGZgb6d05n4VhkUQ0HHzQuo3gCqEmeAyKXy5oKFXA==

    }


    @EqualsAndHashCode
    @Data
    public static class TestObj implements Serializable {

        private String str = "asddsadsa****";
        private int in = 1231;
        private Integer wrapperInt = 1231;
        private String han = "한글입니다.";
        private double d = 1.0;
        private String nil = null;
    }


}