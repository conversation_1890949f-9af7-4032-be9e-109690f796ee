package com.vendys.customer.controller.user;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import com.vendys.customer.util.ConverterUtil;

/**
 * Created by jinwoo on 07/10/2019
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class OneTimePasswordControllerTest {
    final static String xCustomer = "{\"client\":\"Test\",\"userId\":\"********-39D2-4B4D-8521-96716E65FE8A\",\"version\":\"1.0\"}";

    @Autowired
    private WebApplicationContext wac;
    private MockMvc mockMvc;
    @Autowired
    private ConverterUtil converterUtil;

    @Before
    public void setUp() {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void create() throws Exception {
        String userId = "********-39D2-4B4D-8521-96716E65FE8A";
        ResultActions result = mockMvc.perform(post("/user/v1/" + userId + "/otp")
            .header("X-Customer", xCustomer)
            .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isCreated());
    }

    @Test
    public void getOtp() throws Exception {
        String userId = "********-39D2-4B4D-8521-96716E65FE8A";
        String code = "123456789012";
        ResultActions result = mockMvc.perform(get("/user/v1/" + userId + "/otp/" + code)
            .header("X-Customer", xCustomer)
            .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }
}
