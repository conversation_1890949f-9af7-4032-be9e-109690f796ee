package com.vendys.customer.service.elasticSearch;

import java.io.IOException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import org.junit.Test;
import org.junit.runner.RunWith;

import com.vendys.customer.CustomerServerApplication;
import com.vendys.customer.controller.user.entity.ElasticUserDto.Request;


@RunWith(SpringRunner.class)
@ActiveProfiles(profiles = "local")
@SpringBootTest(classes = CustomerServerApplication.class)
public class ElasticUserServiceTest {

    @Autowired
    private ElasticUserService service;

    @Test
    public void findAllByComIdAndNameLikeUsers() throws IOException {
        Request request = new Request();
        request.setCompanyId("C8486D30-195F-A602-7704-4AE7E3CB1681");
        request.setSearchName("TEST");
        this.service.findAllByComIdAndNameLikeUsers(request).forEach(r-> System.out.println(r.getName()));
    }
}