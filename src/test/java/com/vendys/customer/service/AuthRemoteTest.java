package com.vendys.customer.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.Assert.*;

import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class AuthRemoteTest {

    @Autowired
    private AuthRemote authRemote;

    private String expectCi ="7569a9ee5c7dcaa15206da382f1ded2b2e0fa8ca6d1acaab63c03dd37f5284992c46f80f33b1744f4c239f70de170f61fa928bb2e36a317d47c1e626e3cce99c29f39dbeb626b7d2a7ac2a6c7cf2725686552dad0245d4e9e2b86d282e75243c";
    private String givenUserId = "32BBEC0F-9C57-4919-8B44-636959FA69B9";

    @Test
    public void canGetAuth() {
        String ci = this.authRemote.getCi(givenUserId);
        assertEquals(expectCi, ci);

    }
}