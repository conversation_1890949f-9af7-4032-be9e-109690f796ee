package kr.co.vendys.storeapi.controller.store;

import junit.framework.TestCase;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class ExcelControllerTest extends TestCase {
    @Autowired
    private WebApplicationContext wac;
    private MockMvc mockMvc;
    private HttpHeaders httpHeaders;

    @Before
    public void setUp() throws Exception {
        // header
        this.httpHeaders = new HttpHeaders();
        this.httpHeaders.add("Authorization", "Bearer hiARzIrVO5SR3IFsV218Li7aQ4pEQgGalY3DWKjZTMTiTo32OkfF9cCz9GEDnpZ5");
        this.httpHeaders.add("X-Store-gsid", "0920CF82-27D7-1F8D-EF77-416B89C82DDA");
        this.httpHeaders.add("Content-Type", "application/json");
        this.httpHeaders.add("X-User-Agent", "Vendys/1.0 {\"client\": \"SikdaeUser\", \"os\": \"web\"}");

        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void testExcelExport() throws Exception {
        //given

        //expected
        this.mockMvc.perform(MockMvcRequestBuilders.post("/v1/store/excel/export")
                        .param("sid", "0920CF82-27D7-1F8D-EF77-416B89C82DDA")
                        .param("startDate", "2022-07-01")
                        .param("endDate", "2022-12-31"))
                .andDo(MockMvcResultHandlers.print());
    }
}