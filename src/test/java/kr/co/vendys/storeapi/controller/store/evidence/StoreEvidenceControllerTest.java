package kr.co.vendys.storeapi.controller.store.evidence;

import junit.framework.TestCase;
import kr.co.vendys.storeapi.controller.store.evidence.dto.StoreEvidenceSearchCondition;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class StoreEvidenceControllerTest {
    @Autowired
    private WebApplicationContext wac;
    private MockMvc mockMvc;
    private HttpHeaders httpHeaders;

    @Before
    public void setUp() {
        // header
        this.httpHeaders = new HttpHeaders();
        this.httpHeaders.add("Authorization", "Bearer Tud2iOFCpyFyl45NzXk0wPg7xJAvWA8H9Ctb0Z830QBtB484wkh1NVme8Hps5RyQ");
        this.httpHeaders.add("X-Store-gsid", "E7C3A386-A93F-E505-9551-150639D999FC");
        this.httpHeaders.add("Content-Type", "application/json");
        this.httpHeaders.add("X-User-Agent", "Vendys/1.0 {\"client\": \"SikdaeUser\", \"os\": \"web\"}");

        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void testFindEvidences() throws Exception {
        //given
        StoreEvidenceSearchCondition condition = new StoreEvidenceSearchCondition("2023", 0, "PAPER");
        String storeId = "3AB3B03C-D210-FBCD-273D-7D333C1B91C5";

        //expected
        this.mockMvc.perform(get("/settlement/v1/store/{storeId}/evidences", storeId)
                        .params(condition.toParam().toParams()))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void testFindEvidence() {
    }

    @Test
    public void testModifyIssueState() {
    }

    @Test
    public void testMailEvidence() throws Exception {
        //given
        String storeId = "E7C3A386-A93F-E505-9551-150639D999FC";
        int evidenceSeq = 1279;

        //expected
        this.mockMvc
                .perform(post("/settlement/v1/store/{storeId}/evidences/{evidenceSeq}/mail", storeId, evidenceSeq)
                        .headers(httpHeaders))
                .andDo(print())
                .andExpect(status().isOk());
    }
}