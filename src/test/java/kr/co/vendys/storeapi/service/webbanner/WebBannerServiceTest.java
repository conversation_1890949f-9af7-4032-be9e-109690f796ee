package kr.co.vendys.storeapi.service.webbanner;
import kr.co.vendys.storeapi.entity.Store;
import kr.co.vendys.storeapi.entity.webbanner.WebBanner;
import kr.co.vendys.storeapi.persist.webbanner.WebBannerPersist;
import kr.co.vendys.storeapi.service.store.StoreService;
import kr.co.vendys.storeapi.vo.AccessTokenInfo;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WebBannerServiceTest {

    @InjectMocks
    private WebBannerService webBannerService;

    @Mock
    private WebBannerPersist webBannerPersist;

    @Mock
    private WebBannerFilter webBannerFilter;

    @Mock
    private StoreService storeService;


    @Test
    public void getMainWebBannerListTest() {
        AccessTokenInfo token = new AccessTokenInfo();
        token.setGsid("testGsid");
        Store store = new Store();
        WebBanner webBanner = WebBanner.builder()
                .idx(1L)
                .bannerType("MAIN")
                .name("Test Banner")
                .build();
        List<WebBanner> webBanners = Arrays.asList(webBanner);

        when(storeService.findStore(token.getGsid())).thenReturn(store);
        when(webBannerPersist.findByType("MAIN")).thenReturn(webBanners);
        when(webBannerFilter.filterWebBannerList(token, webBanners)).thenReturn(webBanners);

        List<WebBanner> result = webBannerService.getMainWebBannerList(token);

        assertEquals(1, result.size());
        assertEquals(webBanner, result.get(0));
    }

    @Test
    public void getShortcutWebBannerListTest() {
        AccessTokenInfo token = new AccessTokenInfo();
        token.setGsid("testGsid");
        Store store = new Store();
        WebBanner webBanner = WebBanner.builder()
                .idx(1L)
                .bannerType("SHORTCUT")
                .name("Test Shortcut Banner")
                .build();
        List<WebBanner> webBanners = Arrays.asList(webBanner);

        when(storeService.findStore(token.getGsid())).thenReturn(store);
        when(webBannerPersist.findByType("SHORTCUT")).thenReturn(webBanners);
        when(webBannerFilter.filterWebBannerList(token, webBanners)).thenReturn(webBanners);

        List<WebBanner> result = webBannerService.getShortcutWebBannerList(token);

        assertEquals(1, result.size());
        assertEquals(webBanner, result.get(0));
    }
}

