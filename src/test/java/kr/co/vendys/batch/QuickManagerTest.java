package kr.co.vendys.batch;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import kr.co.vendys.batch.service.QuickManagerService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(SpringRunner.class)
@Slf4j
@SpringBootTest
public class QuickManagerTest {

    @Autowired
    private QuickManagerService quickManagerService;

    @Test
    public void quickManagerSyncTest() throws InterruptedException {
        this.quickManagerService.quickManagerSync();
    }

    @Test
    public void quickSyncV2Test() throws InterruptedException {
        this.quickManagerService.syncQuickOrdersFromCaptainPayment(false);
    }
}
