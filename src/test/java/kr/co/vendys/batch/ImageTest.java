package kr.co.vendys.batch;

import java.io.BufferedInputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;

import kr.co.vendys.batch.constant.Errors;
import kr.co.vendys.batch.exception.CommonException;
import org.apache.tika.Tika;
import org.junit.Test;

public class ImageTest {

    @Test
    public void imageTest() {
        String test;
        try (InputStream is = new BufferedInputStream(
                new URL("https://s3.ap-northeast-2.amazonaws.com/freshcode/menu/origin/857_20220120173631").openStream())) {
            test = URLConnection.guessContentTypeFromStream(is);
        } catch (Exception e) {
            throw new CommonException(Errors.MENU_IMAGE_FAIL);
        }
        String mimeType = null;
        Tika tika = new Tika();
        try(InputStream is = new BufferedInputStream(
                new URL("https://s3.ap-northeast-2.amazonaws.com/freshcode/menu/origin/857_20220120173631").openStream())) {
            mimeType = tika.detect(is);
        }catch(Exception e) {
        }
        System.out.println(test);
        System.out.println(mimeType);
    }

}
