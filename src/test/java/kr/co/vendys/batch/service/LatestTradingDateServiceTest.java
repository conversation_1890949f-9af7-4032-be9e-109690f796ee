package kr.co.vendys.batch.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import kr.co.vendys.batch.InitApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = InitApplication.class)
@Transactional
public class LatestTradingDateServiceTest {

    @Autowired
    private LatestTradingDateService latestTradingDateService;

    @Test
    public void updateStoreLatestTradingDate() throws Exception {
        // given
        latestTradingDateService.updateLatestTradingDate();

        // when

        // then
    }

}