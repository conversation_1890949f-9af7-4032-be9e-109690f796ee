package kr.co.vendys.batch.service;

import kr.co.vendys.batch.persist.captainPayment.CaptainPaymentRemote;
import kr.co.vendys.batch.vo.StoreCouponVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.util.Arrays;

import static org.junit.Assert.assertNotNull;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class CompanyStoreExcelServiceTest {

    @Autowired
    private CompanyStoreExcelService companyStoreExcelService;

    @Autowired
    private CaptainPaymentRemote captainPaymentRemote;

    @Test
    public void testSendEmailBeforeMonth() {
        log.info("=== 전체 CompanyStoreExcelService.sendEmailBeforeMonth() 통합 테스트 시작 ===");
        
        try {
            // When: 실제 서비스 메서드 호출
            companyStoreExcelService.sendEmailBeforeMonth();
            
            log.info("CompanyStoreExcelService.sendEmailBeforeMonth() 실행 완료");
        } catch (Exception e) {
            log.error("CompanyStoreExcelService 테스트 중 오류 발생: {}", e.getMessage(), e);
            throw e;
        }
        
        log.info("=== CompanyStoreExcelService 통합 테스트 완료 ===");
    }

    @Test
    public void testGetStoreCouponsWithSpecificStore() {
        log.info("=== 특정 매장 API 호출 테스트 시작 ===");
        
        // Given: 테스트용 파라미터 설정
        String testComId = "13CB2F3C-F28E-40A4-82E4-C904656D1AE1";
        String testSid = "889700DE-F887-6A72-90A6-801F45072B63";
        
        LocalDate endDate = LocalDate.now().withDayOfMonth(1).minusDays(1);
        LocalDate startDate = endDate.withDayOfMonth(1);
        
        StoreCouponVO.StoreRequest request = new StoreCouponVO.StoreRequest();
        request.setStartDate(startDate);
        request.setEndDate(endDate);
        request.setSid(testSid);
        
        log.info("테스트 파라미터 - comId: {}, sid: {}, 기간: {} ~ {}", 
                testComId, testSid, startDate, endDate);
        
        try {
            // When: 실제 API 호출
            StoreCouponVO.StoreCouponListResponse response = 
                captainPaymentRemote.getStoreCoupons(testComId, request);
            
            // Then: 응답 검증 및 로깅
            if (response != null) {
                log.info("API 호출 성공 - 매장명: {}, 총 건수: {}", 
                        response.getStoreName(), response.getTotalCount());
                
                if (response.getStoreCoupons() != null && !response.getStoreCoupons().isEmpty()) {
                    log.info("쿠폰 데이터 {} 건 확인", response.getStoreCoupons().size());
                    
                    // 첫 번째 쿠폰 데이터 로깅
                    StoreCouponVO.StoreCouponData firstCoupon = response.getStoreCoupons().get(0);
                    log.info("첫 번째 쿠폰 정보 - 결제번호: {}, 회사명: {}, 부서: {}, 결제자: {}, 메뉴: {}, 금액: {}", 
                            firstCoupon.getPayRoomIdx(), firstCoupon.getComName(), 
                            firstCoupon.getDepartment(), firstCoupon.getUsername(),
                            firstCoupon.getMenuName(), firstCoupon.getPrice());
                }
                
                if (response.getDepartmentStats() != null && !response.getDepartmentStats().isEmpty()) {
                    log.info("부서별 통계 {} 건 확인", response.getDepartmentStats().size());
                }
            } else {
                log.warn("API 응답이 null입니다.");
            }
            
        } catch (Exception e) {
            log.error("API 호출 중 오류 발생: {}", e.getMessage(), e);
            throw e;
        }
        
        log.info("=== 특정 매장 API 호출 테스트 완료 ===");
    }
}
