package kr.co.vendys.company.api.controller.billing;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import kr.co.vendys.company.api.controller.billing.entity.ApprovalDto;
import kr.co.vendys.company.api.controller.billing.entity.ApprovalDto.InvoiceApprovalAcceptBody;
import kr.co.vendys.company.api.controller.billing.entity.ApprovalDto.InvoiceApprovalRejectBody;
import kr.co.vendys.company.api.util.ConverterUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ApprovalControllerTest {

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;
    private HttpHeaders httpHeaders;

    @Autowired
    private ConverterUtil converterUtil;

    @Before
    public void setUp() throws Exception {
        // header
        this.httpHeaders = new HttpHeaders();
        this.httpHeaders.add("Authorization", "Bearer DXMfJwN8nLSOhr73pDQ3VFwc21Ilaq9v4aD1NOuMTZ9qBDPiVMAF3XNVUac129an");
        this.httpHeaders.add("Content-Type", "application/json");
        this.httpHeaders.add("x-userid", "56F0931B-7243-2595-719F-65EEBE47884B");
        this.httpHeaders.add("x-comid", "B068366F-5EB8-1C7E-E79C-D438C8E32F9D");
        this.httpHeaders.add("x-corp", "{\"auth\":[\"*:*\"],\"organization\":\"B068366F-5EB8-1C7E-E79C-D438C8E32F9D\",\"type\":\"COMPANY\", \"depth\":null}");
        this.httpHeaders.add("Origin", "http://localhost");

        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void setInvoiceAccept() throws Exception {
        /**
         * E61023A4-CB98-9542-3E3D-243B5159490E
         * 8D1C4B6B-9F7B-2658-2775-FADF3B8AE538
         * 56F0931B-7243-2595-719F-65EEBE47884B
         */
        ApprovalDto.InvoiceApprovalAcceptBody postRequest = new InvoiceApprovalAcceptBody();
        postRequest.setUserId("E61023A4-CB98-9542-3E3D-243B5159490E");

        ResultActions result = this.mockMvc.perform(post("/bill/v1/account/2/invoice/2/approval")
            .headers(this.httpHeaders)
            .content(this.converterUtil.toJsonString(postRequest))
            .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isCreated());
    }

    @Test
    public void setInvoiceReject() throws Exception {
        /**
         * E61023A4-CB98-9542-3E3D-243B5159490E
         * 8D1C4B6B-9F7B-2658-2775-FADF3B8AE538
         * 56F0931B-7243-2595-719F-65EEBE47884B
         */
        ApprovalDto.InvoiceApprovalRejectBody postRequest = new InvoiceApprovalRejectBody();
        postRequest.setUserId("8D1C4B6B-9F7B-2658-2775-FADF3B8AE538");
        postRequest.setCause("반려 단위 테스트 - 로컬에서 내가 한다.");

        ResultActions result = this.mockMvc.perform(put("/bill/v1/account/2/invoice/2/approval")
            .headers(this.httpHeaders)
            .content(this.converterUtil.toJsonString(postRequest))
            .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }
}