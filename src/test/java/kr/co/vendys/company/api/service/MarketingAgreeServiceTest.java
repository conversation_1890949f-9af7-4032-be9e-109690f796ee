package kr.co.vendys.company.api.service;

import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import kr.co.vendys.company.api.controller.ParamDto.RequestData;
import kr.co.vendys.company.api.controller.marketing.dto.MarketingAgreeDto.MarketingAgreeBody;
import kr.co.vendys.company.api.controller.marketing.dto.MarketingAgreeDto.MarketingAgreeBody.MarketingReception;
import kr.co.vendys.company.api.controller.marketing.dto.MarketingAgreeDto.MarketingAgreeBody.MarketingReception.Reception;
import kr.co.vendys.company.api.controller.marketing.dto.MarketingAgreeDto.MarketingAgreeResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class MarketingAgreeServiceTest {

    @Autowired
    private MarketingAgreeService marketingAgreeService;

    @Test
    public void testMarketingAgreeTest() {
        RequestData request = new RequestData();
        request.setUserId("123");
        MarketingAgreeBody marketingAgreeBody = new MarketingAgreeBody();
        marketingAgreeBody.setMarketingId("test");
        List<MarketingReception> list = new ArrayList<>();
        MarketingReception test = new MarketingReception();
        test.setReception(Reception.PUSH);
        list.add(test);
        marketingAgreeBody.setReceptions(list);
        MarketingAgreeResponse response = this.marketingAgreeService
                .postMarketingAgreeInfo(request, marketingAgreeBody);
        Assert.assertNotNull(response);
        MarketingAgreeResponse response2 = this.marketingAgreeService
                .getMarketingAgreeInfo(request, "test");
        Assert.assertNotNull(response2);
        Assert.assertNotNull(response2.getRecommended());

    }

}
