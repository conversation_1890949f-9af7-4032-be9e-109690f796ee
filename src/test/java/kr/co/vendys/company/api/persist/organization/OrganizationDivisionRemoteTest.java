package kr.co.vendys.company.api.persist.organization;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import kr.co.vendys.company.api.controller.ParamDto.RequestData;
import kr.co.vendys.company.api.vo.UserVo;
import kr.co.vendys.company.api.vo.UserVo.User;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(SpringRunner.class)
@SpringBootTest
public class OrganizationDivisionRemoteTest {

    @Autowired OrganizationDivisionRemote organizationDivisionRemote;
    private static final String comId = "0300B12D-2EC5-458D-81E0-BD813BE445E5";
    private List<String> orgCodeList;
    private RequestData requestData;

    private String path = "/Users/<USER>/Downloads/hynix-division.csv";
    private String encoding = "utf-8";

    @Before
    public void setUp() throws Exception {

        UserVo.User user = new User();
        user.setUid("FECD977E-B319-6A63-E67B-B0058F7A0364");
        requestData = new RequestData();
        requestData.setUser(user);

        BufferedReader br = null;
        String line;
        this.orgCodeList = new ArrayList<>();
        try {
            br = new BufferedReader(new InputStreamReader(new FileInputStream(this.path), this.encoding));
            while ((line = br.readLine()) != null) {
                orgCodeList.add(line);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Test
    public void delDivision() {
        System.out.println(orgCodeList);
        for (String orgCode : orgCodeList) {
            try {
                this.organizationDivisionRemote.delDivision(requestData.getUser(), comId, orgCode);
            } catch(Exception e) {
                e.printStackTrace();
            }
        }
    }
}