package kr.co.vendys.company.api.controller.company;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import kr.co.vendys.company.api.controller.company.entity.DivisionDto;
import kr.co.vendys.company.api.util.ConverterUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

/**
 * Created by jangjungsu on 2017. 10. 19.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class DivisionControllerTest {

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;
    private HttpHeaders httpHeaders;

    @Autowired
    private ConverterUtil converterUtil;

    @Before
    public void setUp() throws Exception {
        // header
        this.httpHeaders = new HttpHeaders();
        this.httpHeaders.add("Authorization", "Bearer iWbuUgmd6xZMg62GJFK7Glf7dro3efHXRA2pdykMLbbpKTLWsM8SsSZeDLuG7Ddw");
        this.httpHeaders.add("Content-Type", "application/json");

        /*
        this.httpHeaders.add("x-userid", "42626897-6F3C-8CAD-1295-F41DDDB3D04E");
        this.httpHeaders.add("x-comid", "B068366F-5EB8-1C7E-E79C-D438C8E32F9D");
        this.httpHeaders.add("x-corp", "{\"auth\":[\"*:*\"],\"organization\":\"B068366F-5EB8-1C7E-E79C-D438C8E32F9D\",\"type\":\"COMPANY\",\"depth\":null}");
        */

        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void getDivision() throws Exception {

        ResultActions result = this.mockMvc.perform(get("/company/v1/division")
                .headers(this.httpHeaders)
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }

    @Test
    public void setDivision() throws Exception {

        DivisionDto.RequestBody body = new DivisionDto.RequestBody();
        body.setName("자원봉사자");
        body.setParentOrgCode("B1E04B11-13E4-EF59-CB55-DA6182B39EB1");

        ResultActions result = this.mockMvc.perform(post("/company/v1/division")
                .headers(this.httpHeaders)
                .content(this.converterUtil.toJsonString(body))
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isCreated());

    }

    @Test
    public void getDivisionDetail() throws Exception {
    }

    @Test
    public void delDivision() throws Exception {
    }

    @Test
    public void getUserByDivision() throws Exception {
        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
//        multiValueMap.set("orgCode", "DFBD5515-DA91-45C0-AC81-8CF8F1B90B5B"); // 3
//        multiValueMap.set("orgCode", "41BCED67-3F04-4555-9AC3-16A77DB5E08C"); // 3
//         multiValueMap.set("orgCode", "0A4191C7-1432-49CA-8C16-C0CD5C061ACF"); // 2




        ResultActions result = this.mockMvc.perform(get("/company/v1/division/usercnt")
                .headers(this.httpHeaders)
                .params(multiValueMap)
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }

}