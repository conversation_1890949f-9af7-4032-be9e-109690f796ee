package kr.co.vendys.company.api.controller.history;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

/**
 * Created by <PERSON>ha on 2020. 01. 20.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class LoginHistoryControllerTest {
    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;
    private HttpHeaders httpHeaders;


    @Before
    public void setUp() throws Exception {
        // header
        this.httpHeaders = new HttpHeaders();
        this.httpHeaders.add("Authorization", "Bearer rsh2ojHdPQzw9422SCGCFXPpr7cri5CzvLDkMlUtheQFvoClOpRlez0IKgrv0y9K");
        this.httpHeaders.add("Content-Type", "application/json");

        this.httpHeaders.add("x-userid", "0A86E2CE-50BB-4622-864F-0FC6B0124A1C333");
        this.httpHeaders.add("x-comid", "B068366F-5EB8-1C7E-E79C-D438C8E32F9D");
        this.httpHeaders.add("x-corp", "{\"auth\":[\"*:*\"],\"organization\":\"B068366F-5EB8-1C7E-E79C-D438C8E32F9D\",\"type\":\"COMPANY\",\"depth\":null}");

        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void testGetPolicyHistory() throws Exception {

        String testDefaultUrl = "/history/v1/login";
        testDefaultUrl += "?page=1";
        testDefaultUrl += "&pageRow=10";
        testDefaultUrl += "&start=2020-01-01";
        testDefaultUrl += "&end=2020-01-20";

        ResultActions result = this.mockMvc.perform(
                get(testDefaultUrl)
                        .headers(this.httpHeaders)
                        .contentType(MediaType.APPLICATION_JSON)
        );

        result.andDo(print());
        result.andExpect(status().isOk());
    }
}
