package kr.co.vendys.company.api.controller.coupon;

import kr.co.vendys.company.api.util.ConverterUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.context.WebApplicationContext;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.GregorianCalendar;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Created by jangjungsu on 2017. 6. 26..
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class UserControllerTest {

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;
    private HttpHeaders httpHeaders;

    @Autowired
    private ConverterUtil converterUtil;

    @Before
    public void setUp() throws Exception {
        // header
        this.httpHeaders = new HttpHeaders();
        this.httpHeaders.add("Authorization", "Bearer 9unqdkNNgrm1rlAHcukniqaVqleCiyl2T2LFqQV7jm6nsdYNE0QNUq1jnMIN8SWf");
        this.httpHeaders.add("Content-Type", "application/json");
        this.httpHeaders.add("userid", "02DB14AA-15B9-CCF1-BBBF-F8DAB0793792");
        this.httpHeaders.add("comid", "B068366F-5EB8-1C7E-E79C-D438C8E32F9D");

        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void testGetPointLog() throws Exception {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        calendar.add(GregorianCalendar.DAY_OF_MONTH, -13);
        String startdate = new SimpleDateFormat().format(calendar.getTime());
        String enddate = new SimpleDateFormat().format(new Date());

        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        multiValueMap.set("startdate", startdate);
        multiValueMap.set("enddate", enddate);
        multiValueMap.set("page", String.valueOf(1));
        multiValueMap.set("pagerow", String.valueOf(10));
        multiValueMap.set("orgCode","3D24B711-1AF4-B8B1-9A22-741E49449948");


        ResultActions result = this.mockMvc.perform(get("/coupon/v1/user")
                .headers(this.httpHeaders)
                .params(multiValueMap)
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }

    @Test
    public void testGetPointLogDetail() throws Exception {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        calendar.add(GregorianCalendar.DAY_OF_MONTH, -18);
        String startdate = new SimpleDateFormat().format(calendar.getTime());
        String enddate = new SimpleDateFormat().format(new Date());

        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        multiValueMap.set("startdate", startdate);
        multiValueMap.set("enddate", enddate);
        multiValueMap.set("page", String.valueOf(1));
        multiValueMap.set("pagerow", String.valueOf(10));


        ResultActions result = this.mockMvc.perform(get("/coupon/v1/user/CBECB5D3-0005-43A1-9CA1-DBA135326C02")
                .headers(this.httpHeaders)
                .params(multiValueMap)
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }
}