package kr.co.vendys.company.api.controller.stat;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import kr.co.vendys.company.api.util.ConverterUtil;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;

/**
 * Created by jangjungsu on 2017. 7. 14..
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CompanyStatControllerTest {

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;
    private HttpHeaders httpHeaders;

    @Autowired
    private ConverterUtil converterUtil;

    @Before
    public void setUp() throws Exception {
        // header
        this.httpHeaders = new HttpHeaders();
        this.httpHeaders.add("Authorization", "Bearer LSOzNWYzYFlAC6V1C9BHcKj8vXAbYvYtWfUp2n8pBl6V3801nYqCgM9TxECtxHIo");
        this.httpHeaders.add("Content-Type", "application/json");
        this.httpHeaders.add("x-userid", "C0E13AEC-2AA1-4E6A-B466-B90B18A59492");
        this.httpHeaders.add("x-comid", "8A360BC5-837D-41BF-A8DA-4AD5417F49A6");
        this.httpHeaders.add("x-corp",
                "{\"auth\":[\"*:*\"],\"organization\":\"8A360BC5-837D-41BF-A8DA-4AD5417F49A6\",\"type\":\"COMPANY\",\"depth\":null}");
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void getDivisionStat() throws Exception {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        calendar.add(GregorianCalendar.MONTH, -1);
//        calendar.add(GregorianCalendar.DAY_OF_MONTH, -14);
        String startdate = new SimpleDateFormat().format(calendar.getTime());
        String enddate = new SimpleDateFormat().format(new Date());


        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        multiValueMap.set("startdate", "September 1, 2017 12:00 AM");
        multiValueMap.set("enddate", "September 23, 2017 6:01 PM");
        multiValueMap.set("datetype", "MM");
//        multiValueMap.set("orgidx", "250"); // 3depth
//        multiValueMap.set("orgCode", "6DC013A1-5CAE-FA6B-806A-F13864DC0291"); // 2depth

        ResultActions result = this.mockMvc.perform(get("/stat/v1/company/division")
                .headers(this.httpHeaders)
                .params(multiValueMap)
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }

    @Test
    public void getDivisionStatExcel() throws Exception {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        calendar.add(GregorianCalendar.MONTH, -1);
//        calendar.add(GregorianCalendar.DAY_OF_MONTH, -14);
        String startdate = new SimpleDateFormat().format(calendar.getTime());
        String enddate = new SimpleDateFormat().format(new Date());


        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        multiValueMap.set("startdate", "October 1, 2017 12:00 AM");
        multiValueMap.set("enddate", "October 23, 2017 6:01 PM");
        multiValueMap.set("datetype", "MM");
        multiValueMap.set("comid", "B068366F-5EB8-1C7E-E79C-D438C8E32F9D");
        multiValueMap.set("requestid", "FECD977E-B319-6A63-E67B-B0058F7A0364");
        multiValueMap.set("filetype", "excel");

//        multiValueMap.set("orgidx", "250"); // 3depth
//        multiValueMap.set("orgCode", "6DC013A1-5CAE-FA6B-806A-F13864DC0291"); // 2depth

        ResultActions result = this.mockMvc.perform(get("/stat/v1/company/division/download")
                .headers(this.httpHeaders)
                .params(multiValueMap)
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }

    @Test
    public void getDivisionStatDash() throws Exception {
        ResultActions result = this.mockMvc.perform(get("/stat/v1/company/division/top")
                .headers(this.httpHeaders)
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }

    @Test
    public void getGroupStat() throws Exception {
    }

    @Test
    public void getGroupStatExcel() throws Exception {
    }

    @Test
    public void getCouponStat() throws Exception {
        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        multiValueMap.set("startdate",
                "2022-12-01T00:00:00"); // (테스트용) RequestDto 의 startdate 타입에 @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss") 설정해야됨
        multiValueMap.set("enddate", "2023-02-01T00:00:00");
        multiValueMap.set("datetype", "MM");
        multiValueMap.set("usersort", "true");
        multiValueMap.set("page", "1");
        multiValueMap.set("pagerow", "15");

        ResultActions result = this.mockMvc.perform(get("/stat/v1/company/coupon")
                .headers(this.httpHeaders)
                .params(multiValueMap)
                .contentType(MediaType.APPLICATION_JSON));

        MvcResult response = result.andReturn();

        String body = response.getResponse().getContentAsString();
        DocumentContext parse = JsonPath.parse(body);
        List<String> comIdNumList = parse.read("$.stat[*].comIdNum");
        for (String s : comIdNumList) {
            Assertions.assertThat(s).isEqualTo("5");
        }
    }

    @Test
    public void getCouponStatExcel() throws Exception {
    }

    @Test
    public void testGetStoreStat() throws Exception {

    }

    @Test
    public void testGetStoreStatDash() throws Exception {

    }

    @Test
    public void testGetGroupStat() throws Exception {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
//         calendar.add(GregorianCalendar.MONTH, -3);
        calendar.add(GregorianCalendar.DAY_OF_MONTH, -16);
        String startdate = new SimpleDateFormat().format(calendar.getTime());
        String enddate = new SimpleDateFormat().format(new Date());

        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        multiValueMap.set("startdate", startdate);
        multiValueMap.set("enddate", enddate);
        multiValueMap.set("orgidx", "250");
//        multiValueMap.set("groupidx", String.valueOf(11));
//        multiValueMap.set("policyidx", String.valueOf(11));

        ResultActions result = this.mockMvc.perform(get("/stat/v1/company/group")
                .headers(this.httpHeaders)
                .params(multiValueMap)
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());

    }

    @Test
    public void testGetCouponStat() throws Exception {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.set(2017, 9,01);
        String startdate = new SimpleDateFormat().format(calendar.getTime());
        calendar.set(2017, 9,30);
        String enddate = new SimpleDateFormat().format(calendar.getTime());

        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        multiValueMap.set("startdate", startdate);
        multiValueMap.set("enddate", enddate);
        multiValueMap.set("datetype", "MM");
        multiValueMap.set("page", "1");
        multiValueMap.set("pagerow", "15");
//        multiValueMap.set("orgidx", "250");

//        multiValueMap.set("usersort", "true");
        multiValueMap.set("divisionsort", "true");
//        multiValueMap.set("storesort", "true");
//        multiValueMap.set("groupsort", "true");
//        multiValueMap.set("policysort", "true");

//        multiValueMap.set("groupidx", String.valueOf(11));
//        multiValueMap.set("policyidx", String.valueOf(11));
//        multiValueMap.set("division", "");
//        multiValueMap.set("division2", "");
//        multiValueMap.set("division3", "");
//        multiValueMap.set("uid", "");
//        multiValueMap.set("sid", "");

        ResultActions result = this.mockMvc.perform(get("/stat/v1/company/coupon")
                .headers(this.httpHeaders)
                .params(multiValueMap)
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }
    @Test
    public void testGetCouponStatExcel() throws Exception {
        GregorianCalendar calendar = new GregorianCalendar();
        calendar.set(2017, 8,01);
        String startdate = new SimpleDateFormat().format(calendar.getTime());
        calendar.set(2017, 9,30);
        String enddate = new SimpleDateFormat().format(calendar.getTime());

        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        multiValueMap.set("comid", "B068366F-5EB8-1C7E-E79C-D438C8E32F9D");
        multiValueMap.set("requestid", "FECD977E-B319-6A63-E67B-B0058F7A0364");
        multiValueMap.set("filetype", "excel");
        multiValueMap.set("startdate", startdate);
        multiValueMap.set("enddate", enddate);
        multiValueMap.set("datetype", "SUM");
        multiValueMap.set("page", "1");
        multiValueMap.set("pagerow", "15");
        multiValueMap.set("orgidx", "23150");

//        multiValueMap.set("usersort", "true");
        multiValueMap.set("divisionsort", "true");
//        multiValueMap.set("storesort", "true");
//        multiValueMap.set("groupsort", "true");
//        multiValueMap.set("policysort", "true");

//        multiValueMap.set("groupidx", String.valueOf(11));
//        multiValueMap.set("policyidx", String.valueOf(11));
//        multiValueMap.set("division", "");
//        multiValueMap.set("division2", "");
//        multiValueMap.set("division3", "");
//        multiValueMap.set("uid", "");
//        multiValueMap.set("sid", "");

        ResultActions result = this.mockMvc.perform(get("/stat/v1/company/coupon/download")
                .headers(this.httpHeaders)
                .params(multiValueMap)
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }

    @Test
    @DisplayName("대마플 아워홈 포인트 사용 내역 api 테스트")
    public void testGetSubtractStat() throws Exception {
        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        multiValueMap.set("startdate", "2023-02-01");
        multiValueMap.set("enddate", "2023-02-17");

        ResultActions result = this.mockMvc.perform(get("/stat/v1/company/subtract")
                .headers(this.httpHeaders)
                .params(multiValueMap)
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }

    @Test
    @DisplayName("대마플 아워홈 포인트 사용 내역 엑셀 다운로드 테스트")
    public void testGetSubtractStatExcelDownload() throws Exception {
        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        multiValueMap.set("startdate", "2023-02-01");
        multiValueMap.set("enddate", "2023-02-23");

        ResultActions result = this.mockMvc.perform(get("/stat/v1/company/subtract/download")
                .headers(this.httpHeaders)
                .params(multiValueMap)
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }

}