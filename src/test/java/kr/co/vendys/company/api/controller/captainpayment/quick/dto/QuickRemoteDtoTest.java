package kr.co.vendys.company.api.controller.captainpayment.quick.dto;

import static org.junit.jupiter.api.Assertions.*;

import kr.co.vendys.company.api.controller.captainpayment.quick.dto.GogoXQuickDto.BookingListRequest;
import kr.co.vendys.company.api.controller.captainpayment.quick.dto.GogoXQuickDto.BookingListRequest.SearchType;
import kr.co.vendys.company.api.exception.CommonException;
import org.junit.Test;

public class QuickRemoteDtoTest {

    @Test
    public void searchNameTest() {
        BookingListRequest request = new BookingListRequest();
        request.setSearchType(SearchType.NAME);
        request.setSearchValue("아이아");
        assertEquals(false, request.isOrderIdSearch());
        assertNull(request.orderId());
    }

    @Test
    public void searchOrderIdTest() {
        BookingListRequest request = new BookingListRequest();
        request.setSearchType(SearchType.ORDERID);
        request.setSearchValue("321");
        Long aLong = request.orderId();
        assertEquals(321L, aLong.longValue());

        request.setSearchValue("dsadsa");
        assertThrows(CommonException.class, request::orderId);
        assertNull(request.searchName());
    }
}