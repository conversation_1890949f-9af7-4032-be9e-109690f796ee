package kr.co.vendys.company.api.service.stat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import kr.co.vendys.company.api.controller.stat.entity.StoreDto;
import kr.co.vendys.company.api.controller.stat.entity.StoreDto.StoreResponse;
import kr.co.vendys.company.api.controller.stat.entity.StoreDto.StoreResponse.Stat;
import kr.co.vendys.company.api.entity.master.MealGroup.GroupType;
import kr.co.vendys.company.api.controller.ParamDto;
import kr.co.vendys.company.api.vo.UserVo;

import static org.assertj.core.api.Assertions.assertThat;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;

@RunWith(SpringRunner.class)
@SpringBootTest
public class StoreStatServiceTest {

    @Autowired
    private StoreStatService storeStatService;

    private Date startDate;
    private Date endDate;
    private LocalDate testStartDate;
    private LocalDate testEndDate;
    private String comId;
    private StoreDto.StoreRequest storeRequest;

    @Before
    public void setUp() {
        startDate = Date.from(LocalDateTime.of(2023,11,7,0,0)
                .atZone(ZoneId.systemDefault()).toInstant());
        endDate = Date.from(LocalDateTime.of(2023,11,8,0,0)
                .atZone(ZoneId.systemDefault()).toInstant());
        testStartDate = LocalDate.of(2023, 11, 7);
        testEndDate = LocalDate.of(2023, 11, 8);
        comId = "8A360BC5-837D-41BF-A8DA-4AD5417F49A6";
        storeRequest = StoreDto.StoreRequest.of(startDate, endDate, "MM");
    }

    @Test
    @DisplayName("제휴식당 통계 데이터 확인")
    public void getStoreStat_test() {
        StoreResponse storeStat = this.storeStatService.getStoreStat(comId, storeRequest, GroupType.MEAL);
        Stat stat = storeStat.getStat().get(0);
        stat.getStore().stream()
                .forEach(System.out::println);
    }

    @Test
    @DisplayName("가맹점 상세 결제 내역 조회")
    public void getStoreDetail_test() {
        // Given - User 객체 설정 (organizationDivisionRemote 호출에 필요)
        UserVo.User user = new UserVo.User();
        user.setUid("test-user-id");
        user.setComid("0DF3A576-FDB7-4AB1-A9B5-0C0F385687E6");

        // StoreDetailRequest 설정
        StoreDto.StoreDetailRequest storeDetailRequest = new StoreDto.StoreDetailRequest();
        storeDetailRequest.setStartDate(testStartDate);
        storeDetailRequest.setEndDate(testEndDate);
        storeDetailRequest.setSid("5EE45207-7835-B703-4954-CCA65AE73079"); // 실제 테스트 환경에 맞는 store ID 사용
        storeDetailRequest.setComId("0DF3A576-FDB7-4AB1-A9B5-0C0F385687E6");

        // When
        StoreDto.StoreDetailResponse response = this.storeStatService.getStoreDetail(storeDetailRequest, user);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getDetails()).isNotNull();

        System.out.println("총 결제 내역 수: " + response.getDetails().size());

        // 결과가 있는 경우 상세 검증
        if (!response.getDetails().isEmpty()) {
            StoreDto.StoreDetailResponse.StoreDetail firstDetail = response.getDetails().get(0);

            // 필수 필드 검증
            assertThat(firstDetail.getPayRoomIdx()).isNotNull();
            assertThat(firstDetail.getUseDate()).isNotNull();
            assertThat(firstDetail.getComName()).isNotEmpty();
            assertThat(firstDetail.getUsername()).isNotEmpty();

            // 금액 필드 검증 (null이 아니어야 함)
            assertThat(firstDetail.getCompanyPrice()).isNotNull();
            assertThat(firstDetail.getInstantPayPrice()).isNotNull();
            assertThat(firstDetail.getPrice()).isNotNull();

            System.out.println("첫 번째 결제 내역:");
            System.out.println("  - 결제번호: " + firstDetail.getPayRoomIdx());
            System.out.println("  - 결제시간: " + firstDetail.getUseDate());
            System.out.println("  - 회사명: " + firstDetail.getComName());
            System.out.println("  - 부서: " + firstDetail.getOrgName());
            System.out.println("  - 결제자: " + firstDetail.getUsername());
            System.out.println("  - 메뉴명: " + firstDetail.getMenuName());
            System.out.println("  - 식대금액: " + firstDetail.getCompanyPrice());
            System.out.println("  - 간편결제: " + firstDetail.getInstantPayPrice());
            System.out.println("  - 총 결제금액: " + firstDetail.getPrice());

            // 응답 데이터 구조 검증
            response.getDetails().forEach(detail -> {
                assertThat(detail.getPayRoomIdx()).isNotNull();
                assertThat(detail.getUseDate()).isNotNull();
                assertThat(detail.getComName()).isNotEmpty();
                assertThat(detail.getUsername()).isNotEmpty();
                assertThat(detail.getCompanyPrice()).isNotNull();
                assertThat(detail.getInstantPayPrice()).isNotNull();
                assertThat(detail.getPrice()).isNotNull();
                // orgName과 menuName은 null일 수 있음
            });
        } else {
            System.out.println("해당 기간과 가맹점에 대한 결제 내역이 없습니다.");
        }
    }
}
