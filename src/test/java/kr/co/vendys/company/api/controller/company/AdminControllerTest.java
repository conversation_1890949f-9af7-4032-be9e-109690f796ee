package kr.co.vendys.company.api.controller.company;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import kr.co.vendys.company.api.controller.company.entity.AdminDto;
import kr.co.vendys.company.api.controller.company.entity.AdminDto.BodyDelRequest;
import kr.co.vendys.company.api.controller.company.entity.AdminDto.BodyRequest;
import kr.co.vendys.company.api.controller.company.entity.AdminDto.UpdBodyRequest;
import kr.co.vendys.company.api.controller.company.entity.AdminDto.UpdBodyRequest.Change;
import kr.co.vendys.company.api.controller.company.entity.AdminDto.UpdBodyRequest.Pre;
import kr.co.vendys.company.api.util.ConverterUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(SpringRunner.class)
@SpringBootTest
public class AdminControllerTest {

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;
    private HttpHeaders httpHeaders;

    @Autowired
    private ConverterUtil converterUtil;

    @Before
    public void setUp() throws Exception {
        // header
        this.httpHeaders = new HttpHeaders();
        this.httpHeaders.add("Authorization", "Bearer fsd0pndSnma4xBfaKyFiibFkI6UlI7niTlSulAJF5q6C2sUrVgJuehxLkhBbamML");
        this.httpHeaders.add("Content-Type", "application/json");
        this.httpHeaders.add("x-userid", "FECD977E-B319-6A63-E67B-B0058F7A0364");
        this.httpHeaders.add("x-comid", "B068366F-5EB8-1C7E-E79C-D438C8E32F9D");
        this.httpHeaders.add("x-corp", "{\"auth\":[\"*:*\"],\"organization\":\"B068366F-5EB8-1C7E-E79C-D438C8E32F9D\",\"type\":\"COMPANY\"}");
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void getAmdin() throws Exception {

        MultiValueMap multiValueMap = new LinkedMultiValueMap();
        multiValueMap.add("page", "1");
        multiValueMap.add("pageRow", "10");
        multiValueMap.add("keyword", "iq");

        ResultActions result = this.mockMvc.perform(get("/company/v1/admin")
                .headers(this.httpHeaders)
                .params(multiValueMap)
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }

    @Test
    public void postAdmin() throws Exception {

        String userId = "FECD977E-B319-6A63-E67B-B0058F7A0364";
        String orgCode = "B068366F-5EB8-1C7E-E79C-D438C8E32F9D";
        String type = "COMPANY";
        Long presetIdx = 1L;

        AdminDto.BodyRequest postRequest = new BodyRequest();
        postRequest.setOrgCode(orgCode);
        postRequest.setType(type);
        postRequest.setPresetIdx(presetIdx);


        ResultActions result = this.mockMvc.perform(post("/company/v1/admin/" + userId)
                .headers(this.httpHeaders)
                .content(this.converterUtil.toJsonString(postRequest))
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isCreated());

    }

    @Test
    public void putAdmin() throws Exception {
        String userId = "FECD977E-B319-6A63-E67B-B0058F7A0364";
        String preOrgCode = "D10FADF4-E9FB-4E40-927E-8E47F7E8CC15";

        String type = "COMPANY";
        Long presetIdx = 1L;
        String changeOrgCode = "B068366F-5EB8-1C7E-E79C-D438C8E32F9D";

        AdminDto.UpdBodyRequest putRequest = new UpdBodyRequest();

        AdminDto.UpdBodyRequest.Pre pre = new Pre();
        pre.setOrgCode(preOrgCode);

        AdminDto.UpdBodyRequest.Change change = new Change();
        change.setPresetIdx(presetIdx);
        change.setOrgCode(changeOrgCode);
        change.setType(type);

        putRequest.setPre(pre);
        putRequest.setChange(change);

        ResultActions result = this.mockMvc.perform(put("/company/v1/admin/" + userId)
                .headers(this.httpHeaders)
                .content(this.converterUtil.toJsonString(putRequest))
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }

    @Test
    public void deleteAdmin() throws Exception {
        String userId = "FECD977E-B319-6A63-E67B-B0058F7A0364";
        String orgCode = "B068366F-5EB8-1C7E-E79C-D438C8E32F9D";

        AdminDto.BodyDelRequest delRequest = new BodyDelRequest();
        delRequest.setOrgCode(orgCode);

        ResultActions result = this.mockMvc.perform(delete("/company/v1/admin/" + userId)
                .headers(this.httpHeaders)
                .content(this.converterUtil.toJsonString(delRequest))
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isNoContent());
    }

    @Test
    public void getPreset() throws Exception {

        MultiValueMap multiValueMap = new LinkedMultiValueMap();
        multiValueMap.add("type", "GROUP");

        ResultActions result = this.mockMvc.perform(get("/company/v1/preset")
                .headers(this.httpHeaders)
                .params(multiValueMap)
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }

}