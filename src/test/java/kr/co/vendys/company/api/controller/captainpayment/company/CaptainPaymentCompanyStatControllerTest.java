package kr.co.vendys.company.api.controller.captainpayment.company;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import kr.co.vendys.company.api.controller.CaptainPaymentControllerTestProp;
import org.junit.Test;
import org.junit.runner.RunWith;

@SpringBootTest
@RunWith(SpringRunner.class)
public class CaptainPaymentCompanyStatControllerTest extends CaptainPaymentControllerTestProp {

    @Test
    public void getCompanyInfoTest() throws Exception {
        String url = "/captain-payment/company/v1/info";
        super.mockMvc.perform(
                get(url)
                .contentType(super.mediaType)
                .headers(super.httpHeaders)
        )
                .andExpect(status().isOk())
                .andDo(print());
    }
}