package kr.co.vendys.company.api.controller.captainpayment.member;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MultiValueMap;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import kr.co.vendys.company.api.controller.CaptainPaymentControllerTestProp;
import kr.co.vendys.company.api.controller.history.entity.InfoHistoryDto.InfoHistoryRequest;
import org.junit.Test;
import org.junit.runner.RunWith;

@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class CaptainPaymentHistoryControllerTest extends CaptainPaymentControllerTestProp {

    @Test
    public void getHistory() throws Exception {

        String url = "/captain-payment/member/v1/history/info";
        InfoHistoryRequest request = new InfoHistoryRequest();
        request.setPage(1);
        request.setPageRow(10);
        request.setType("ALL");

        MultiValueMap<String, String> params = super.convertParam(request);

        super.mockMvc.perform(
                get(url)
                .contentType(super.mediaType)
                .headers(super.httpHeaders)
                .params(params)
        )
                .andExpect(status().isOk())
                .andDo(print());
    }
}