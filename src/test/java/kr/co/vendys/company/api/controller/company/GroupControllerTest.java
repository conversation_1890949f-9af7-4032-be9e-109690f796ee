package kr.co.vendys.company.api.controller.company;

import java.text.SimpleDateFormat;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import kr.co.vendys.company.api.controller.company.entity.GroupDto;
import kr.co.vendys.company.api.controller.company.entity.GroupDto.HolidayUseOption;
import kr.co.vendys.company.api.util.ConverterUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

/**
 * Created by jangjungsu on 2017. 7. 11..
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class GroupControllerTest {

    @Autowired
    private WebApplicationContext wac;

    private MockMvc mockMvc;
    private HttpHeaders httpHeaders;

    @Autowired
    private ConverterUtil converterUtil;

    @Before
    public void setUp() throws Exception {
        // header
        this.httpHeaders = new HttpHeaders();
        this.httpHeaders.add("Authorization", "Bearer 5ePp9KERzdmngN1tUJnEa0tnqdBdfdKAUSA3XPMNi4FmMjNBc5ZVdHxjWZ6pazk6");
        this.httpHeaders.add("Content-Type", "application/json");
        this.httpHeaders.add("x-userid", "0A86E2CE-50BB-4622-864F-0FC6B0124A1C");
        this.httpHeaders.add("x-comid", "B068366F-5EB8-1C7E-E79C-D438C8E32F9D");
        this.httpHeaders.add("x-corp", "{\"auth\":[\"*:*\"],\"organization\":\"B068366F-5EB8-1C7E-E79C-D438C8E32F9D\",\"type\":\"COMPANY\"}");
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    public void testGetGroup() throws Exception {

    }

    @Test
    public void testGetPolicy() throws Exception {
        ResultActions result = this.mockMvc.perform(get("/company/v1/group/1/policy")
                .headers(this.httpHeaders)
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }

    @Test
    public void testSetPolicy() throws Exception {
        GroupDto.PolicyBodyRequest policyBodyRequest = new GroupDto.PolicyBodyRequest();
        GroupDto.PolicyBodyRequest.Policy policy = new GroupDto.PolicyBodyRequest.Policy();
        policy.setIdx(83L);
        policy.setDay("0000011");

        SimpleDateFormat transFormat = new SimpleDateFormat("HH:mm:ss");
        String from = "11:11:11";
        Date to = transFormat.parse(from);
        String from2 = "15:15:15";
        Date to2 = transFormat.parse(from2);

        policy.setStartTime(to);
        policy.setEndTime(to2);
        policy.setHolidayUseOption(HolidayUseOption.DAY_AND_HOLIDAY);
        policyBodyRequest.setPolicy(policy);

        ResultActions result = this.mockMvc.perform(put("/company/v1/group/1/policy")
                .headers(this.httpHeaders)
                .content(this.converterUtil.toJsonString(policyBodyRequest))
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }

    @Test
    public void testGetBlackList() throws Exception {
        ResultActions result = this.mockMvc.perform(get("/company/v1/group/85/policy/252/blacklist")
                .headers(this.httpHeaders)
                .contentType(MediaType.APPLICATION_JSON));

        result.andDo(print());
        result.andExpect(status().isOk());
    }

}
