import kr.co.vendys.batch.InitApplication;
import kr.co.vendys.batch.persist.MessagePersist;
import kr.co.vendys.batch.scheduler.PaymentChecksumScheduler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Created by j<PERSON><PERSON><PERSON><PERSON> on 2016. 12. 28..
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = InitApplication.class)
public class EmailTest {

    @Autowired
    private MessagePersist messageService;

    @Autowired
    private PaymentChecksumScheduler paymentChecksumScheduler;


    @Test
    public void sendEmail() throws Exception {
        // this.messageService.sendEmail("<EMAIL>", "장중수", "[메일건SMTP 테스트] " + new Date(),"호이호이");
        // this.mailGunService.send("<EMAIL>", "[메일건API 테스트]", "되랏!!");
        this.paymentChecksumScheduler.paymentChecksumRun();
    }
}
