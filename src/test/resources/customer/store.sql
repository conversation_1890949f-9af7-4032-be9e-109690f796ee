select 'dependeny company3.0.sql';

set @storeId = 'd2e364ef-6b88-4e75-be8b-e49b13d00c5b',
    @menu1Id = '9526ab8c-98bd-4ae4-82ea-5e4017806bdb',
    @menu2Id = 'c4a5f0c5-b408-4c3a-9e10-e5480c09ca7d',
    @menu3Id = '117e000a-c2c2-44b7-a889-8351a0aba1d9';

INSERT INTO Store(sid, id, password, name, intro, phone, cellphone, address, region, category,
  categoryid, categoryorder, regdate, status, email, supplytype, pushcoupon, cancelfunc, cancelpwchange, isMenuless)
  VALUES(@storeId, 'teststoreacid', 'aa', 'teststore', 'i', '343', '343', '3343', '역삼', '한식',
  1, 1, now(), 1, 'emem','0', 0, 0, 0, 0);

INSERT INTO Menu(mid, menuname, sid, storename, categorySeq, seq, intro, price, sellprice, supplyprice, regdate, status,
  categoryId, category, mealtype)
  VALUES(@menu1Id, 'testmenu1name', @storeId, 'testmenuname', 1, 1, 'int', 3000, 2900, 2800, now(), 1,
            1, 'cate', 0),
        (@menu2Id, 'testmenu2name', @storeId, 'testmenuname', 1, 1, 'int', 4000, 3900, 3800, now(), 1,
            1, 'cate', 0),
        (@menu3Id, 'testmenu3name', @storeId, 'testmenuname', 1, 1, 'int', 9000, 8900, 8800, now(), 1,
            1, 'cate', 0);

INSERT INTO OfficeStoreRelation(officeIdx, storeId, priceType, salesPrice, companyPrice, supplyPrice, isActive, regDate)
  VALUES(@officeIdx, @storeId, 'RATE', 1, 0.99, 0.98, 1, now());

SELECT @storeRelationIdx := relationIdx
  FROM OfficeStoreRelation
  WHERE officeIdx = @officeIdx
    AND storeId = @storeId;

INSERT INTO OfficeMenuRelation(
    relationIdx,
    officeIdx,
    storeId,
    menuId,
    salesPrice,
    companyPrice,
    supplyPrice
  )
  VALUES(@storeRelationIdx, @officeIdx, @storeId, @menu1Id, 1000, 1000, 1000),
        (@storeRelationIdx, @officeIdx, @storeId, @menu2Id, 1000, 1000, 1000),
        (@storeRelationIdx, @officeIdx, @storeId, @menu3Id, 1000, 1000, 1000);
