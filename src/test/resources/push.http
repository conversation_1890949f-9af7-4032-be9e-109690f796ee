
POST http://localhost:10080/user/push/send
Content-Type: application/json

{
  "type": "priority",
  "content": "QR코드를 스캔하면 [고정형-비트커피] BEAT커피 자판기 21호 주문이 결제됩니다",
  "param": "33EF6219-A384-6BDB-DE48-9CC0C9502805",
  "param2": null,
  "action": "BookingFinish",
  "title": "식권대장",
  "customScheme": "sikdae://screen/main/stores/detail?q={\"storeId\":\"33EF6219-A384-6BDB-DE48-9CC0C9502805\",\"supplyType\":{\"code\":\"TOTAL\",\"name\":\"전체\"}}",
  "uid": ["955B7581-A4E7-4F58-BA01-A719CBCA6F92"],
  "client": "StoreAPI",
  "requestid": "Beat커피"
}

###
POST http://localhost:10080/company/push/send
Content-Type: application/json

{
  "type": "priority",
  "content": "내용출력",
  "param": "",
  "param2": "",
  "action": "CompanyAnnounce",
  "title": "식권대장",
  "customScheme": "sikdae://screen/main/company-notices",
  "comid": "A8817417-B3B1-43D1-9AE9-1956FD35C4AC",
  "client": "SikdaeCompany",
  "requestid": "955B7581-A4E7-4F58-BA01-A719CBCA6F92"
}

###

POST http://localhost:10080/store/push/send
Content-Type: application/json

{
  "type": "priority",
  "content": "김*호1님 1000원 식권을 사용하였습니다",
  "param": "31F144BB-C4BD-49D2-85BB-D14AB121F126",
  "param2": "",
  "action": "",
  "sid": "8A06A590-0B6D-A763-674F-9F2178EADE87",
  "customScheme": "",
  "client": "SikdaeAPI",
  "requestid": "955B7581-A4E7-4F58-BA01-A719CBCA6F92"
}