
SET @companyId = 'e0f35a20-3028-4ca9-9f07-c51feca5e5b9',
    @rootOrgCode = '1b0ba6a7-9a09-4e4e-99c6-348dbc573521',
    @rootOrgMetaIdx = '',
    @rootOrgIdx = '',
    @companyOrgCode = 'e0f35a20-3028-4ca9-9f07-c51feca5e5b9',
    @companyOrgMetaIdx = '',
    @companyOrgIdx = '',
    @divisionOrgCode = '7b976cd6-b592-4269-988d-f1dd102157a0',
    @divisionOrgMetaIdx = '',
    @divisionOrgIdx = '',
    @officeIdx = '';

INSERT INTO Company(comid, name, bizname, bizserial, address, region, chargename, phone, bankname, bankaccount, bankowner, intro, cash, trustcash, employnum, duesmonth, regdate, paymulti, status, calculatedate, version, isTest)
  values(@companyId, '테스트회사v3', '테스트회사명v3', '**********', '주우소', '지역', '담당자명', '전화번호', '은행', '3827392', '좌주', '소개', 0, 0, 100, 1, now(), 1, 1, 3, 'v1', 0);

INSERT INTO CompanyLimit(comid, meal1, meallimit1, day1, meal2, meallimit2, day2, meal3, meallimit3, day3,
    common_pointtrans, mealc_service, mealc_usemypoint, mealc_multipay, mealc_multientrust, discount_service, discount_daymanlimit, snack_service, snack_present,
    commerce_service, pversion, mealmonthamount, mealmonthforward, mealmonthchargeday, mealpolicy, mealusershare, mealmultimenu,
    mealday, meallong, mealunlimit, mealautomonth, cafeteria, company_notice, isMealGroup, isTicketFormat, isCashReceipt, isTimeDisplayed, isPrintBarcode,
    isQrCodeScan, isPrivacy, isSettlement, isBudget, isSnack, isDemandPos, isMarket)
  VALUES(@companyId, 0, 0, '1111111', 0, 0, '1111111', 0, 0, '1111111',
    0, 1, 1, 1, 1, 0, 0, 0, 0,
    0, 1, 0, 0, 1, 1, 0, 1,
    0, 0, 0, 0, 0, 1, 1, 0, 0, 1, 0,
    1, 1, 1, 1, 1, 1, 1);

INSERT INTO Organization(rootCode, orgCode, type, status, seq, regDate)
  values(@rootOrgCode, @rootOrgCode, 'GROUP', 'ACTIVE', 1, now());

SELECT @rootOrgMetaIdx := orgMetaIdx
  FROM Organization WHERE orgCode = @rootOrgCode;

INSERT INTO OrganizationHistory(orgMetaIdx, parentOrgMetaIdx, depth, name, isActive, regDate)
  VALUES(@rootOrgMetaIdx, -1, 1, 'testComGroupv3name', 1, now());

SELECT @rootOrgIdx := orgIdx
  FROM OrganizationHistory WHERE orgMetaIdx = @rootOrgMetaIdx;

INSERT INTO OrganizationCache(orgIdx, orgMetaIdx, parentOrgMetaIdx, rootCode, orgCode, type, depth, seq, name, isActive, lft, rgt, status, regDate, pathName)
  VALUES(@rootOrgIdx, @rootOrgMetaIdx, -1, @rootOrgCode, @rootOrgCode, 'GROUP', 1, 1, '그룹사', 1, 1, 6, 'ACTIVE', now(), 'test');


INSERT INTO Organization(rootCode, orgCode, type, status, seq, regDate)
  VALUES(@rootOrgCode, @companyOrgCode, 'COMPANY', 'ACTIVE', 1, now());

SELECT @companyOrgMetaIdx := orgMetaIdx FROM Organization WHERE orgCode = @companyOrgCode;

INSERT INTO OrganizationHistory(orgMetaIdx, parentOrgMetaIdx, depth, name, isActive, regDate)
  VALUES(@companyOrgMetaIdx, @rootOrgMetaIdx, 1, 'testCompanyv3name', 1, now());

SELECT @companyOrgIdx := orgIdx
  FROM OrganizationHistory WHERE orgMetaIdx = @companyOrgMetaIdx;

INSERT INTO OrganizationCache(orgIdx, orgMetaIdx, parentOrgMetaIdx, rootCode, orgCode, type, depth, seq, name, isActive, lft, rgt, status, regDate, pathName)
  VALUES(@companyOrgIdx, @companyOrgMetaIdx, @rootOrgMetaIdx, @rootOrgCode, @companyOrgCode, 'COMPANY', 1, 1, '회사', 1, 1, 6, 'ACTIVE', now(), 'test');


INSERT INTO Organization(rootCode, orgCode, type, status, seq, regDate)
values(@rootOrgCode, @divisionOrgCode, 'DIVISION', 'ACTIVE', 1, now());

SELECT @divisionOrgMetaIdx := orgMetaIdx FROM Organization
 WHERE orgCode = @divisionOrgCode;

INSERT INTO OrganizationHistory(orgMetaIdx, parentOrgMetaIdx, depth, name, isActive, regDate)
  VALUES(@divisionOrgMetaIdx, @companyOrgMetaIdx, 1, 'testDivisionv3name', 1, now());

SELECT @divisionOrgIdx := orgIdx
  FROM OrganizationHistory WHERE orgMetaIdx = @divisionOrgMetaIdx;

INSERT INTO OrganizationCache(orgIdx, orgMetaIdx, parentOrgMetaIdx, rootCode, orgCode, type, depth, seq, name, isActive, lft, rgt, status, regDate, pathName)
  VALUES(@divisionOrgIdx, @divisionOrgMetaIdx, @companyOrgMetaIdx, @rootOrgCode, @divisionOrgCode, 'DIVISION', 1, 1, '부서', 1, 1, 6, 'ACTIVE', now(), 'test');


INSERT INTO Office(orgCode, regDate)
values(@companyOrgCode, now());

select @officeIdx := officeIdx
  FROM Office
 WHERE orgCode = @companyOrgCode;

INSERT INTO OfficeHistory(
    officeIdx,
    name,
    officialName,
    region,
    isActive,
    regDate
  )
  VALUES( @officeIdx, 'testOffice', 'testOffice', '강남', 1, now() );