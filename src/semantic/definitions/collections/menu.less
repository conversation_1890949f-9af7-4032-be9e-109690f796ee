
/*******************************
        Vendys Theme
*******************************/

@type    : 'collection';
@element : 'menu';

@import (multiple) '../../theme.config';

.ui.menu {
  border: none;
}

.ui.item.menu.vendysFullTab {
  .rem-px(margin-bottom, @marginDefault16);
}

.ui.item.menu.vendysFullTab .item {
  .rem-px(font-size, @vendysH5);
  font-weight: bold;
}

.ui.tabular.menu.vendysFullTab .active.item {
  & {
    border-left: 1px solid @outlineColor;
    border-right: 1px solid @outlineColor;
  }

}

.ui.tabular.menu.vendysFullTab .active.item {
  background: @white;
  border-radius: 0 !important;
  border-top: 3px solid @tabActiveColor;
  color : @deepGreen;
}

.ui.tabular.menu.vendysFullTab .item {
  background: @menunotOver;
}

.ui.tabular.menu.vendysLeftTab .active.item {
  width : @dividerWidthSize;
  text-align: center;
  border-radius: 0 !important;
  border-top: 3px solid @tabActiveColor;
  color : @deepGreen;
}

.ui.tabular.menu.vendysLeftTab .active.item > span
, .ui.tabular.menu.vendysLeftTab .item > span {
  width : 100%;
  .rem-px(font-size, @vendysH5);

}

.ui.tabular.menu.vendysLeftTab .item {
  .rem-px(font-size, @vendysH5);
  .rem-px(width, @dividerWidthSize);
  text-align: center;
}

.ui.tabular.menu.vendysLeftTab .item:hover
, .ui.tabular.menu.vendysFullTab .item:hover {
  border-radius: 0 !important;
  border-top: 3px solid @tabActiveColor;
  background: @white;
}

.ui.tabular.menu.vendysLeftTab .item:hover > span
, .ui.tabular.menu.vendysFullTab .item:hover > span {
  .rem-px(font-size, @vendysH5);
  // color : @deepGreen;
}

.ui.menu {
  border-radius: 0;
  .rem-px(min-height, @formSize);
}

.ui.menu.vendysLeftMenu {
  border: solid 1px @greySolidBorderColor;
  border-radius: 0;
}

.ui.vertical.menu.vendysLeftMenu {
  .rem-px(font-size, @vendysH5);
  .rem-px(width, @leftMenuWidth);
}

.ui.vertical.menu.vendysLeftMenu .item {
  font-weight: bold;
  .rem-px(padding-top, 11);
  .rem-px(height, 40);
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;

}

.ui.vertical.menu.vendysLeftMenu > .top {
  background: @menunotOver;
}

.ui.vertical.menu.vendysLeftMenu .active.item {
  background : @tabActiveColor;
  color : @white;
}
.ui.vertical.menu.vendysLeftMenu .item:not(.top):not(.active):hover {
  background: @menuActiveColor;
  border-radius: 0;
}

.ui.vertical.menu.vendysLeftMenu > .item:last-child {
  border-radius: 0;
}

.ui.vertical.menu .item>.label {
  background: #d6d6d6;
  color: #4a4a4a;
}

.ui.pagination.menu {
  .rem-px(height, @formSize);
  background-color: @menunotOver;
}

.ui.pagination.menu .active.item {
  background-color: @deepBlue;
  color: @white;
  .foo(13);
  padding-left: @return;
  padding-right: @return;
}

.ui.menu>.item:first-child {
  border-radius: 0;
}

.ui.pagination.menu .item {
  border-left: 1px solid @white;
  .rem-px(min-width, @formSize);
  padding-left: 0;
  padding-right: 0;
}

.ui.pagination.menu .item.icon {
  .rem-px(width, @formSize);
}

.ui.pagination.menu .item:first-child {
  border-left: none;
}
.ui.pagination.menu .item:last-child {
  border-radius: 0;
}

.ui.menu .item:not(.icon) {
  .foo(13);
  padding-left: @return;
  padding-right: @return;
}

.ui.pagination.menu i.icon,
.ui.pagination.menu i.icons {
  .rem-px(font-size, @vendysH6);
}

.ui.menu .item:before {
  background: none;
}

.ui.selection.dropdown .menu>.item {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  .rem-px(height, @formSize);
  .rem-px(line-height, @formSize+(-2));
}

.ui.vertical.menu > .active.item:first-child {
  border-radius: 0;
}

.ui.vertical.menu > .active.item:last-child {
  border-radius: 0;
}
