/**
 * Created by ttobii on 2015. 11. 16..
 */

var menuinfo;
var buttonLock = false;
let excelFile;
$(document).ready(function(){
    /* 달력 피커 */
    $('.datePicker').datepicker({
        dateFormat: 'yy-mm-dd',
        monthNamesShort: ['1월','2월','3월','4월','5월','6월','7월','8월','9월','10월','11월','12월'],
        dayNamesMin: ['일','월','화','수','목','금','토'],
        weekHeader: 'Wk',
        changeMonth: true, //월변경가능
        changeYear: true, //년변경가능
        yearRange:'1900:+0', // 연도 셀렉트 박스 범위(현재와 같으면 1988~현재년)
        showMonthAfterYear: true, //년 뒤에 월 표시
        buttonImageOnly: true, //이미지표시
        buttonText: '날짜를 선택하세요'
    });

    /* nav 선택 */
    /* $('#nav_company').attr("class", "active"); */
    $('#menu_mealc').attr("src", "/mealcoupon/img/mealc_pressed.png");
    $('#submenu_mealc').css("display", "");
    $('#nav_menu_excelinsert').css("color", "white");
    $("#summuryArea").hide();

    $('#menu-exfile').change(function(e){
        excelFile = $('#menu-exfile')[0].files[0];
        if (excelFile !== undefined) {
            $('#excelPreviewButton').css('display','');
        }
    })

});

function removeStore(){
    $('#storeid').val('');
    $('#storename').val('');
    onExcelFileChange();
}

function makePreview(){
    if(buttonLock){
        return;
    } else {
        buttonLock = true;
    }
    $('#insert-summit').css('display','none');
    $('#summury').html('<b>입력결과를 불러오는 중 입니다.</b>');
    $('#summit-saction').css('display','');

    let excelFile = $('#menu-exfile')[0].files[0];
    if (excelFile === undefined) {
        alert("메뉴 엑셀파일을 업로드 해 주세요.");
        return;
    }
    if (!["xls", "xlsx"].includes(excelFile.name.split(".")[1])) {
        alert("유효한 엑셀 파일이 아닙니다. 파일을 다시 확인해주세요. (*.xls, *.xlsx 만 허용)");
        return;
    }

    let data = new FormData();
    data.append('menulist', excelFile);
    //return;

    $.ajax({
        url: '/mealcoupon/admin/menu/excelPreview',
        type: "POST",
        dataType: "text",
        data: data,
         cache: false,
        headers : { 'responseType':'json'},
        processData: false,
        contentType: false,
        success: function(s_response, status, request) {
            var response = JSON.parse(s_response);
            if(response.status!=1){
                alert("오류발생 : "+response.message);
                buttonLock = false;
                return;
            }
            var success = 0, fail = 0;

            var con = response.content;
            menuinfo = con;
            var html = '<tr><th>연변</th><th>분류</th><th>분류우선순위</th><th>메뉴명</th><th>소개</th><th>정가</th><th>판매가</th><th>공급가</th><th>식권판매형태</th><th>예상결과</th></tr>';
            for(var i=0;i<con.length;i++){
                if(con[i].status==1){
                    html += '<tr class="info">';
                } else {
                    html += '<tr class="error">';
                }

                html += '<td>'+(i+1)+'</td>';
                html += '<td>'+con[i].category+'</td>';
                html += '<td>'+con[i].seq+'</td>';
                html += '<td>'+con[i].menuname+'</td>';
                html += '<td>'+con[i].intro+'</td>';
                html += '<td>'+getCurrency(con[i].price)+'</td>';
                html += '<td>'+getCurrency(con[i].sellprice)+'</td>';
                html += '<td>'+getCurrency(con[i].supplyprice)+'</td>';
                html += '<td>'+$('#mealtype option[value='+con[i].mealtype+']').html()+'</td>';
                if(con[i].status==1){
                    html += '<td>정상</td>';
                    success++;
                } else {
                    html += '<td style="color:#ff0000;">'+con[i].resultMsg+'</td>';
                    fail++;
                } // TODO. success를 status의 true, fail을 status의 false으로 바라보도록 되어있는데, 우선은 로직 그대로 둠.
                html += '</tr>';
            }


            $('#menuInsertPreview').html(html);
            var message = '메뉴갯수 : '+con.length+', 성공예상수 : '+getCurrency(success)+', 실패예상수 : '+getCurrency(fail);
            $('#summury').html(message);
            $('#summit-saction').css('display','');
            $('#insert-summit')
                .removeClass('btn-danger')
                .removeClass('btn-success')
                .css('display','');
            if(fail>0){
                $('#insert-summit').addClass('btn-danger');
            } else {
                $('#insert-summit').addClass('btn-success');
            }
            $("#summuryArea").show();
            buttonLock = false;
        }, error: function(request, status, error){
            buttonLock = false;
            handleHttpError(request, status, error);
        }
    });
}

function onExcelFileChange(){
    $('#menuInsertPreview').html('');
    $('#summit-saction').css('display','none');
    $('#insert-summit').css('display','');
    menuinfo = null;
}

function uploadMenus(status){
    if(buttonLock){
        return;
    } else {
        buttonLock = true;
    }
    $('#summury').html('메뉴를 입력중입니다');
    $('#summit-saction').css('display','');
    $('#insert-summit').css('display','none');

    let data = new FormData();
    let storeid = $('#storeid').val();
    let isActive = status;
    if (storeid==null || storeid == '') {
        alert("제휴점을 선택해주세요");
        $('#summury').html('제휴점을 선택해주세요');
        $('#summit-saction').css('display','');
        $('#insert-summit').css('display','');
        buttonLock = false;
        return;
    }
    data.append('storeid', storeid);
    data.append('menulist', $('#menu-exfile')[0].files[0]);
    data.append('status', isActive);
    data.append('isDefaultOpen', $('#isDefaultOpen').is(":checked"));

    $.ajax({
        url: '/mealcoupon/admin/menu/excelinsert',
        type: "POST",
        dataType: "text",
        data: data,
        cache: false,
        headers : { 'responseType':'json'},
        processData: false,
        contentType: false,
        success: function(s_response, status, request) {
            const response = JSON.parse(s_response);
            // alert(response);
            if(response.status!=1){
                alert("오류발생 : "+response.message);
                buttonLock = false;
                return;
            }

            let con = response.content;
            menuinfo = con;
            let html = `<tr><th>연변</th><th>분류</th><th>분류우선순위</th><th>메뉴명</th><th>소개</th><th>정가</th><th>판매가</th><th>공급가</th><th>식권판매형태</th><th>판매 상태</th><th>입력결과</th></tr>`;
            for(let i=0;i<con.length;i++){
                if(con[i].status==1){
                    html += `<tr class="success">`;
                } else if (con[i].status==2){
                    html += `<tr class="warning">`;
                } else {
                    html += `<tr class="error">`;
                }

                html += `<td>${(i+1)}</td>;
                         <td>${con[i].category}</td>;
                         <td>${con[i].seq}</td>;
                         <td>${con[i].menuname}</td>;
                         <td>${con[i].intro}</td>;
                         <td>${getCurrency(con[i].price)}</td>;
                         <td>${getCurrency(con[i].sellprice)}</td>;
                         <td>${getCurrency(con[i].supplyprice)}</td>;
                         <td>${$('#mealtype option[value='+con[i].mealtype+']').html()}</td>;
                         <td>${((con[i].status==1)? '판매중' : (con[i].status==2) ? '일시품절' : '판매종료')}</td>;
                         <td>입력완료</td>;
                         </tr>`;
            }

            $('#menuInsertPreview').html(html);
            const message = `메뉴갯수 : ${con.length}, 성공입력수 : ${getCurrency(con.length)}, 실패입력수 : 0`;
            $('#summury').html(message);
            $('#insert-summit').css('display','none');

            let msg = `메뉴가 `;
            msg += (isActive === 0)? '판매종료 ' : (isActive === 1)? '판매중' : '일시품절';
            msg += `상태로 등록 되었습니다.`;
            alert(msg);
            $("#summuryArea").show();
            $('#summit-saction').css('display','none');
            $('#excelPreviewButton').css('display','none');
            excelFile = null;
            $("#menu-exfile").val("");
            buttonLock = false;
        }, error: function(request, status, error){
            buttonLock = false;
            handleHttpError(request, status, error);
        }
    });
}
