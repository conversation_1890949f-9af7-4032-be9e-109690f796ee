<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://java.sun.com/xml/ns/javaee"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
         id="nthing" version="2.5">
  <filter>
    <filter-name>encodingFilter</filter-name>
    <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
    <init-param>
      <param-name>encoding</param-name>
      <param-value>UTF-8</param-value>
    </init-param>
  </filter>
  <filter>
    <filter-name>cors</filter-name>
    <filter-class>com.vlocally.mealcoupon.interceptor.CORSFilter</filter-class>
  </filter>
  <filter-mapping>
    <filter-name>cors</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>
  <filter-mapping>
    <filter-name>encodingFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>
  <!--<context-param>-->
    <!--<param-name>log4jConfigLocation</param-name>-->
    <!--<param-value>classpath:properties/${env}.log4j.properties</param-value>-->
  <!--</context-param>-->
  <!--<listener>-->
    <!--<listener-class>org.springframework.web.util.Log4jConfigListener</listener-class>-->
  <!--</listener>-->

  <!-- logback 설정 추가 jangjungsu on 2018. 3. 22. -->
  <listener>
    <listener-class>ch.qos.logback.ext.spring.web.LogbackConfigListener</listener-class>
  </listener>
  <context-param>
    <param-name>logbackConfigLocation</param-name>
    <param-value>classpath:properties/${env}.logback.xml</param-value>
  </context-param>

<display-name>sikdae-admin-api</display-name>
  <servlet>
    <servlet-name>sikdae-admin-api</servlet-name>
    <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
    <init-param>
      <param-name>contextConfigLocation</param-name>
      <param-value>
		       classpath:applicationContext-${env}.xml
			</param-value>
    </init-param>
    <load-on-startup>10</load-on-startup>
  </servlet>
  <servlet-mapping>
    <servlet-name>sikdae-admin-api</servlet-name>
    <url-pattern>/</url-pattern>
    <!-- 
    <url-pattern>*.vm</url-pattern>
    <url-pattern>*.json</url-pattern>
     -->
  </servlet-mapping>
  <context-param>
    <param-name>webAppRootKey</param-name>
    <param-value>sikdae-admin-api</param-value>
  </context-param>
  <context-param>
    <param-name/>
    <param-value/>
  </context-param>
  <session-config>
    <session-timeout>60</session-timeout>
  </session-config>
  <welcome-file-list>
    <welcome-file>index.vm</welcome-file>
    <welcome-file>index.html</welcome-file>
    <welcome-file>index.htm</welcome-file>
    <welcome-file>index.jsp</welcome-file>
    <welcome-file>default.html</welcome-file>
    <welcome-file>default.htm</welcome-file>
    <welcome-file>default.jsp</welcome-file>
  </welcome-file-list>

  <error-page>
    <location>/WEB-INF/views/error/404.jsp</location>
  </error-page>
</web-app>