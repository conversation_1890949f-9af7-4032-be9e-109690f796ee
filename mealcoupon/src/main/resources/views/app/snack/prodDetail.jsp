<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>jQuery Mobile page</title>
		<meta name="viewport" content="width=device-width, initial-scale=1 user-scalable=no">
		<link rel="stylesheet" href="${ctx}/css/themes/mealc.css" />
		<link rel="stylesheet" href="${ctx}/css/themes/jquery.mobile.icons.min.css" />
		<link rel="stylesheet" href="${ctx}/css/themes/jquery.mobile.structure-1.4.2.css" /> 
		<script src="http://code.jquery.com/jquery-1.10.2.min.js"></script> 
		<script type="text/javascript" src="${ctx}/js/javascript.js"></script>
		<script src="http://code.jquery.com/mobile/1.4.2/jquery.mobile-1.4.2.min.js"></script>
		<script type="text/javascript">
			$(document).on('pageinit', '#brand', function(){
			});
			
			function readyPay(){
				location.href = "${ctx}/app/snack/buy/ready.vm?snackid=" + "${prod.snackid}";
			}
		</script> 
	</head>
	<body>
		<!-- 	상품 정보 페이지 시작	 -->
		<div data-role="page" data-theme="d" id="brand">
			<div data-role="navbar" data-grid="c">
			    <ul>
			        <li><a href="${ctx }/app/snack/theme.vm" data-ajax="false">홈</a></li>
			        <li><a href="${ctx }/app/snack/brand.vm" data-ajax="false">브랜드샵</a></li>
			        <li><a href="${ctx }/app/snack/search.vm" data-ajax="false">검색</a></li>
			        <li><a href="${ctx }/app/snack/buylog.vm" data-ajax="false">선물함</a></li>
			    </ul>
			</div><!-- /navbar -->
			<div role="main" class="ui-content" data-theme="c">
				<div style="padding:10px; color: rgb(242, 105, 95);">
					${prod.storename}
				</div>
				<div style="padding:10px;">
					<img src="/snack_images/${prod.image}" width="100%">
				</div>
				<div style="text-align: center; font-size: 20px;">
					${prod.prodname}
				</div>
				<div style="text-align: center; color: rgb(129, 128, 128); font-size:17px;">
					<fmt:formatNumber value="${prod.price}" type="currency" currencySymbol="₩" pattern="#,##0"/>원
				</div>
				<!-- <div class="ui-grid-a">
					<div class="ui-block-a" style="padding:5px;"> -->
						<button class="ui-btn ui-btn-a ui-shadow" onclick="javascript:readyPay();">구입하기</button>
					<!-- </div>
					<div class="ui-block-b" style="padding:5px;">
						<button class="ui-btn ui-btn-a">선물하기</button>
					</div>
				</div> -->
				<div data-role="collapsibleset" data-iconpos="right" data-theme="c" data-content-theme="c">
					<div data-role="collapsible">
						<h2>상품 설명</h2>
						<p>${prod.introtext}</br>
							<c:if test="${prod.introurl != null && prod.introurl != ''}">
								<a href="${ctx}/${prod.introurl}" class="ui-btn" data-ajax="false">상세보기</a>
							</c:if>
						
						</p>
					</div>
				</div>
			</div>
		</div>
		<!-- 	상품 정보 페이지 끝	 -->
	</body>
</html>