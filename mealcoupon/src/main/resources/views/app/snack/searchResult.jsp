<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>jQuery Mobile page</title>
		<meta name="viewport" content="width=device-width, initial-scale=1 user-scalable=no">
		<link rel="stylesheet" href="${ctx}/css/themes/mealc.css" />
		<link rel="stylesheet" href="${ctx}/css/themes/jquery.mobile.icons.min.css" />
		<link rel="stylesheet" href="${ctx}/css/themes/jquery.mobile.structure-1.4.2.css" />
		<script src="http://code.jquery.com/jquery-1.10.2.min.js"></script>
		<script type="text/javascript" src="${ctx}/js/javascript.js"></script> 
		<script type="text/javascript">
			var pageRow = 20;
			var page = 1;
			$(document).bind("mobileinit", function(){

			});
			
			function searchByCategory(cateid, brandid){
				var searchtext = $('#search').val();
				var parameter = "page=" + page
								+ "&pageRow=" + pageRow;
				if (cateid != ""){
					parameter += "&cateid=" + cateid;
				}
				if (brandid != ""){
					parameter += "&brandid=" + brandid;
				}
				if (searchtext != ""){
					parameter += "&searchtext=" + searchtext;
				}
				loadAJAX('${ctx}/app/snack/prod/inquiry.json', parameter, handleSearchByCategorySuccess, null, false);
			}
			
			function handleSearchByCategorySuccess(request, status, response){
				/* 검색 성공시 */
				if (response.status == 1){
					console.log(response.content.length);
				}
			}
			
		</script>
		<script src="http://code.jquery.com/mobile/1.4.2/jquery.mobile-1.4.2.min.js"></script>
	</head>
	<body>
		<!-- 	상품 리스트 페이지 시작	 -->
		<div data-role="page" data-theme="d" id="store">
			<div data-role="navbar" data-grid="c">
			    <ul>
			        <li><a href="${ctx }/app/snack/theme.vm" data-ajax="false">홈</a></li>
			        <li><a href="${ctx }/app/snack/brand.vm" data-ajax="false">브랜드샵</a></li>
			        <li><a href="${ctx }/app/snack/search.vm" class="ui-btn-active" data-ajax="false">검색</a></li>
			        <li><a href="${ctx }/app/snack/buylog.vm" data-ajax="false">선물함</a></li>
			    </ul>
			</div><!-- /navbar -->
			<div role="main" class="ui-content">
				<div>
					<div class="ui-block-a" style="width:80%;">
						<input type="search" id="search">
					</div>
					<div class="ui-block-b" style="width:10%; padding-left: 10px;" >
					    <div>
							<button class="ui-btn ui-corner-all ui-btn-a ui-btn-inline" onclick="javascript:searchByCategory('','');">검색</button>
					    </div>
					</div>
				</div>
				<div>
					<c:forEach var="var" items="${category}" varStatus="i">
				    	<img src="/snack_images/${var.icon }" width="30%" onclick="javascript:searchByCategory('${var.cateid}', '');">
				    </c:forEach>
				</div>
			</div>
		</div>
		<!-- 	상품 리스트 페이지 끝	 -->
	</body>
</html>