
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>식권대장 관리자 페이지</title>
		<link rel="stylesheet" type="text/css" href="${ctx}/css/bootstrap.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/style.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/jquery-ui.css">
		<script type="text/javascript" src="${ctx}/js/jquery-1.9.1.js"></script>
		<script type="text/javascript" src="${ctx}/js/jquery-ui.js"></script>
		<script type="text/javascript" src="${ctx}/js/bootstrap.js"></script>
		<script type="text/javascript" src="${ctx}/js/javascript.js"></script>
		<script type="text/javascript">
			var useData;
			var page = 1;
			var pageRow = 20;
		
			$(document).ready(function(){
				/* 달력 피커 */
				$('.datePicker').datepicker({
					dateFormat: 'yy-mm-dd',
				    monthNamesShort: ['1월','2월','3월','4월','5월','6월','7월','8월','9월','10월','11월','12월'],
				    dayNamesMin: ['일','월','화','수','목','금','토'],
				    weekHeader: 'Wk',
				    changeMonth: true, //월변경가능
				    changeYear: true, //년변경가능
				    yearRange:'1900:+0', // 연도 셀렉트 박스 범위(현재와 같으면 1988~현재년)
			    	showMonthAfterYear: true, //년 뒤에 월 표시
			    	buttonImageOnly: true, //이미지표시  
			    	buttonText: '날짜를 선택하세요'
				});
				
				/* nav 선택 */
				//$('#nav_gifticon').attr("class", "active");
				$('#menu_statistics').attr("src", "${ctx }/img/statistics_pressed.png");
				$('#submenu_statistics').css("display", "");
				$('#nav_gifticon').css("color", "white");
				
				/* selector 이벤트 설정*/
				$("#calcflag").change(function(){
					searchGifticonlog(1);
				});
				
				$("#type").change(function(){
					searchUselog(1);
				});
				
				$("#status").change(function(){
					searchUselog(1);
				});
				
				$("#issueservice").change(function(){
					searchUselog(1);
				});
				
				/* 페이지 호출하기 */
				searchGifticonlog(1);
			});
			
			function selectOption(){
				searchGifticonlog(1);
			}
			
			function searchGifticonlog(input){
				page = input;
				/* 회사명으로 검색 */
				var storeid = $('#storeid').val();
				var menuid = $('#menuid').val();
				var userid = $('#userid').val();
				var startdate = $('#startdate').val();
				var enddate = $('#enddate').val();
				var calcflag = $('#calcflag').val();
				var pin = $('#pin').val();
				var status = $('#status').val();
				var type = $('#type').val();
				var issueservice = $('#issueservice').val();
				
				var parameter = "storeid=" + storeid
							+ "&menuid=" + menuid
							+ "&userid=" + userid
							+ "&startdate=" + startdate
							+ "&enddate=" + enddate
							+ "&calcflag=" + calcflag
							+ "&pin=" + pin
							+ "&status=" + status
							+ "&type=" + type
							+ "&issueservice=" + issueservice 
							+ "&page=" + page
							+ "&pageRow=" + pageRow;
				loadAJAX("${ctx}/admin/gifticon/inquiry.json", parameter, handleSearchGifticonlogSuccess, null);
			}
			
			function handleSearchGifticonlogSuccess(request, status, response){
				/* 회사명 검색 성공시 */
				/* 결과 테이블로 보여주기 시작 */
				useData = response.content;
				if (response.status == 1){
					
					pageController('gifticonlogPageNum', response.page, response.pageRow, response.total, 'searchGifticonlog');
					
					var table = $('#gifticonlogTable');
					var html = "<tr><th>상품명</th><th>PIN</th><th>유효기간</th><th>정가</th><th>판매가</th><th>공급가</th><th>상태</th><th>사용일</th><th>사용처</th><th>정산</th></tr>";
					for (var i = 0; i < useData.length; i++){
						html += "<tr>";
						html += "<td>"+useData[i].storename+'-'+useData[i].menuname+"</td>";
						html += "<td>"+useData[i].pin+"</td>";
						html += "<td>"+transDateTime(useData[i].issuedate)+" ~ "+transDate(useData[i].expiredate)+"</td>";
						
						
						html += "<td>"+getCurrency(useData[i].price)+"</td>";
						html += "<td>"+getCurrency(useData[i].salesprice)+"</td>";
						html += "<td>"+getCurrency(useData[i].supplyprice)+"</td>";
						
						if(useData[i].status=="000"){
							html += "<td>기간 만료</td>";
						} else if(useData[i].status=="001"){
							html += "<td>부분 만료</td>";
						} else if(useData[i].status=="100"){
							html += "<td>미사용</td>";
						} else if(useData[i].status=="400"){
							html += "<td>사용 완료</td>";
						} else if(useData[i].status=="800"){
							html += "<td>발행취소</td>";
						} else {
							html += "<td>불명</td>";
						}
						
						if(useData[i].usedate==null){
							html += "<td></td>";
						} else {
							html += "<td>"+transDateTime(useData[i].usedate)+"</td>";
						}
						
						html += "<td>"+useData[i].usestore+"</td>";
						if(useData[i].calcid==null){
							html += "<td>대기중</td>";
						} else if(useData[i].storecalcid.length==36){
							html += '<td><a href="#" >완료</a></td>';
						} else {
							html += '<td>불명</td>';
						}
						html += "</tr>";
						
					}
					table.html(html);
					
					/* 결과 테이블로 보여주기 끝 */
				} else {
					alert("오류가 발생하였습니다.");
				}
			}
			
			function removeSelectedStore(){
				$('#storename').val('');
				$('#storeid').val('');
				searchGifticonlog(1);
			}
			
			function removeSelectedMenu(){
				$('#menuname').val('');
				$('#menuid').val('');
				searchGifticonlog(1);
			}
			
			function removeSelectedUser(){
				$('#username').val('');
				$('#userid').val('');
				searchGifticonlog(1);
			}
			
			// excel export
			
			function exportExcel(){
				var type = $('#type').val();
				var storeid = $('#storeid').val();
				var menuid = $('#menuid').val();
				var userid = $('#userid').val();
				var startdate = $('#startdate').val();
				var enddate = $('#enddate').val();
				
				if(startdate==""){
					alert("기간 시작을 입력해주세요");
					return;
				}
				if(enddate==""){
					alert("기간 종료을 입력해주세요");
					return;
				}
				
				var columns = "";
				var name = $('#storename').val();
				if(name!=""){columns += name+",";}
				name = $('#username').val();
				if(name!=""){columns += name+",";}
				name = $('#menuname').val();
				if(name!=""){columns += name+",";}
				if(type=="1"){columns += "큐피콘발급,";}
				
				if(columns==""){columns="없음";}
				
				if(!confirm("아래와 같은 조건으로 엑셀출력하시겠습니까?\n결과가 너무 많은경우 생성되지 않을 수 있습니다\n조건 : "+columns+"\n일시 : "+startdate+" ~ "+enddate)){
					return;
				}
				
				var parameter = "contenttype=G"
							+ "&type=" + type
							+ "&sid=" + storeid
							+ "&mid=" + menuid
							+ "&uid=" + userid
							+ "&startdate=" + startdate
							+ "&enddate=" + enddate
							+ "&columns=" + columns;
				
				loadAJAX("${ctx}/admin/excel/export.json", parameter, handleExcelExportSuccess, null);
			}
			
			function handleExcelExportSuccess(request, status, response){
				if(response.status==1){
					alert("생성 성공\n엑셀 출력 메뉴에서 확인하실수 있습니다");
				} else {
					alert("오류!\n"+response.message+" : "+response.status);
				}
			}
		</script>
	</head>
	<body>
		<%@ include file="../topmenu.jsp" %>
		<div class="span16">
		<div class="row">
			<!-- sidebar nav 시작 -->
			<%@ include file="../menu.jsp" %>
			<!-- sidebar nav 끝 -->
			<div class="span13 contents-section">
				<!-- alert 영역 시작 -->
				<div id="message">
				</div>
				<!-- alert 영역 끝 -->
				<div class="form-inline">
					<label>제휴점 : </label>
					<input type="text" readonly="readonly" class="input-small" placeholder="제휴점명" id="storename">
					<input type="hidden" id="storeid">
					<a href="javascript:removeSelectedStore()"><i class="icon-remove"></i></a>
					<button type="button" class="btn" onclick="javascript:showStoreSelecterCallback('storename', 'storeid',selectOption)">찾기</button>
					
					&nbsp					
					<label>메뉴 : </label>
					<input type="text" readonly="readonly" class="input-small" placeholder="메뉴명" id="menuname">
					<input type="hidden" id="menuid">
					<a href="javascript:removeSelectedMenu()"><i class="icon-remove"></i></a>
					<button type="button" class="btn" onclick="javascript:showMenuSelecter('menuname', 'menuid')">찾기</button>
					
					&nbsp					
					<label>사용자 : </label>
					<input type="text" readonly="readonly" class="input-small" placeholder="이름" id="username">
					<input type="hidden" id="userid">
					<a href="javascript:removeSelectedUser()"><i class="icon-remove"></i></a>
					<button type="button" class="btn" onclick="javascript:showUserSelecter('username', 'userid')">찾기</button>
				</div>
				<div class ="form-inline" style="margin-top: 10px;">
					<label>pin : </label>
					<input type="text" class="input-big" placeholder="pin" id="pin">
					&nbsp&nbsp
					<label>상품권상태 : </label>
					<select id="status" class="searchInput">
			    		<option value="">전체</option>
			    		<option value="000">기간만료</option>
			    		<option value="001">부분만료</option>
			    		<option value="100">미사용</option>
			    		<option value="400">사용완료</option>
			    		<option value="800">발행취소</option>
			    	</select>
			    	&nbsp&nbsp
			    	<label>공급사 : </label>
					<select id="type" class="searchInput">
			    		<option value="">전체</option>
			    		<option value="1">큐피콘</option>
			    	</select>
			    	&nbsp&nbsp
			    	<label>구매처 : </label>
					<select id="issueservice" class="searchInput">
			    		<option value="">전체</option>
			    		<option value="0">식권</option>
			    		<option value="1">매점</option>
			    	</select>
				</div>
				<div class="form-inline"  style="margin-top: 10px;">
					<label>기간 : </label>
					<input type="text" id="startdate" placeholder="시작" class="datePicker searchInput">~
					<input type="text" id="enddate" placeholder="종료" class="datePicker searchInput">
					&nbsp&nbsp
					<label>정산유무 : </label>
					<select id="calcflag" class="searchInput">
			    		<option value="">전체</option>
			    		<option value="false">정산전</option>
			    		<option value="true">정산완료</option>
			    	</select>
			    	&nbsp&nbsp
					<button type="button" style="margin-left: 70px;" class="btn btn-inverse" onclick="javascript:searchGifticonlog(1)">검색</button>
					&nbsp&nbsp&nbsp&nbsp
					<button type="button" class="btn btn-inverse" onclick="javascript:exportExcel()">엑셀 출력</button>
				</div>
				<table class="table table-borderd" id="gifticonlogTable" style="margin-top:10px">
					
				</table>
				<div id="gifticonlogPageNum" class="pagination pagination-centered">
				  
				</div>
			</div>
		</div>
		</div>
		
		<!-- 제휴점 찾기 모달 뷰 -->
		<%@ include file="../selectmodal/storeSelecter.jsp" %>
		
		<!-- 메뉴 찾기 모달 뷰 -->
		<%@ include file="../selectmodal/menuSelecter.jsp" %>
		
		<!-- 사용자 찾기 모달 뷰 -->
		<%@ include file="../selectmodal/userSelecter.jsp" %>
		
		
	</body>
</html>