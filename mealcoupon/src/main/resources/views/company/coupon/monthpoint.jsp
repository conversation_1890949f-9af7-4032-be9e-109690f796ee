
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>식권대장 관리자 페이지</title>
		<link rel="stylesheet" type="text/css" href="${ctx}/css/bootstrap.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/style.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/jquery-ui.css">
		<script type="text/javascript" src="${ctx}/js/jquery-1.9.1.js"></script>
		<script type="text/javascript" src="${ctx}/js/jquery-ui.js"></script>
		<script type="text/javascript" src="${ctx}/js/bootstrap.js"></script>
		<script type="text/javascript" src="${ctx}/js/javascript.js"></script>
        <script type="text/javascript" src="${ctx}/js/jquery-ui-sliderAccess.js"></script>
        <script type="text/javascript" src="${ctx}/js/jquery-ui-timepicker-addon.js"></script>
		<script type="text/javascript">
			var pointlogData;
			var page = 1;
			var pageRow = 20;
			
			
			$(document).ready(function(){
				/* 달력 피커 */
				$('.datePicker').datepicker({
					dateFormat: 'yy-mm-dd',
				    monthNamesShort: ['1월','2월','3월','4월','5월','6월','7월','8월','9월','10월','11월','12월'],
				    dayNamesMin: ['일','월','화','수','목','금','토'],
				    weekHeader: 'Wk',
				    changeMonth: true, //월변경가능
				    changeYear: true, //년변경가능
				    yearRange:'1900:+0', // 연도 셀렉트 박스 범위(현재와 같으면 1988~현재년)
			    	showMonthAfterYear: true, //년 뒤에 월 표시
			    	buttonImageOnly: true, //이미지표시  
			    	buttonText: '날짜를 선택하세요'
				});

                $('.dateTimePicker').datetimepicker({
                    dateFormat: 'yy-mm-dd',
                    monthNamesShort: ['1월','2월','3월','4월','5월','6월','7월','8월','9월','10월','11월','12월'],
                    dayNamesMin: ['일','월','화','수','목','금','토'],
                    weekHeader: 'Wk',
                    changeMonth: true, //월변경가능
                    changeYear: true, //년변경가능
                    yearRange:'1900:+0', // 연도 셀렉트 박스 범위(현재와 같으면 1988~현재년)
                    showMonthAfterYear: true, //년 뒤에 월 표시
                    buttonImageOnly: true, //이미지표시
                    buttonText: '날짜를 선택하세요',
                    timeFormat: 'HH:mm:ss',
                    showMilisec: false,
                    showSecond: true,
                    timeText: '시간',
                    hourText: '시',
                    minuteText: '분',
                    secondText: '초',
                    currentText: '지금',
                    closeText: '완료'
                });
				
				/* nav 선택 */
				/* $('#nav_mypoint').attr("class", "active"); */
				$('#menu_mealc').attr("src", "${ctx }/img/mealc_pressed.png");
				$('#submenu_mealc').css("display", "");
				$('#nav_meal_point').css("color", "#c4dc2c");
				
				/* 
				$('#addpointlogcausetype').change(function(){
					var type = $(this).val();
					if(type>0){
						$('#addpointlogway').html('증가');
					} else if(type<0){
						$('#addpointlogway').html('감소');
					} else {
						$('#addpointlogway').html('변경타입을 선택해주세요');
					}
				});
				$('#logstatus').change(function(){
					searchPointlog(1);
				});
				 */
				
				
				/* 페이지 호출하기 */
				searchComPointSchedulelog(1);
				
			});
			
			function searchComPointSchedulelog(input){
				if(input==null){
					input = 1;
				}
				page = input;
				/* 회사명으로 검색 */
				var startdate = $('#c_startdate').val();
				var enddate = $('#c_enddate').val();
				var chargetype = $('#c_companypointtype').val();
                var status = $('#c_companypointstatus').val();
				
				var parameter = "startdate=" + startdate
							+ "&enddate=" + enddate
							+ "&chargetype="+chargetype
                            + "&status=" + status
							+ "&page=" + page
							+ "&pageRow=" + pageRow;
				loadAJAXRest("GET","${ctx}/com/meal/cpoint", parameter, handleSearchComPointSchedulelogSuccess, null);
			}
			
			function handleSearchComPointSchedulelogSuccess(request, status, response){
				/* 회사명 검색 성공시 */
				/* 결과 테이블로 보여주기 시작 */
				pointlogData = response.content;
				if (response.status == 1){
					
					pageController('pointlogPageNum', response.page, response.pageRow, response.total, 'searchComPointSchedulelog');
					
					var table = $('#pointlogTable');
					var html = "<tr><th>회사명</th><th>개별충전량</th><th>대상사용자</th><th>적용사용자</th><th>실패사용자</th><th>소멸일</th><th>형태</th><th>사유</th><th>실행일</th></tr>";
					for (var i = 0; i < pointlogData.length; i++){
						html += '<tr onclick="javascript:showMealPointTaskModal(\''+pointlogData[i].cprid+'\',searchComPointSchedulelog,\''+pointlogData[i].status+'\');">';
						html += "<td>"+pointlogData[i].comname+"</td>";
						html += "<td>"+getCurrency(pointlogData[i].amount)+"</td>";
						html += "<td>"+getCurrency(pointlogData[i].usernum)+"</td>";
						html += "<td>"+getCurrency(pointlogData[i].userapply)+"</td>";
						html += "<td>"+getCurrency(pointlogData[i].userapplyfail)+"</td>";
						
						if(pointlogData[i].expiredate==null){
							html += "<td>제한없음</td>";
						} else {
							html += "<td>"+transDateTime(pointlogData[i].expiredate)+"</td>";
						}
						
						html += "<td>"+$('#c_companypointtype option[value='+pointlogData[i].chargetype+']').html()+"</td>";
						html += "<td>"+pointlogData[i].cause+"</td>";
						if(pointlogData[i].status=="EXD"){
							html += "<td>"+transDateTime(pointlogData[i].executedate)+"</td>";
						} else {
							html += "<td>"+$('#c_companypointstatus option[value='+pointlogData[i].status+']').html();
                            if(pointlogData[i].status=="REV"){
                                html += '대기 '+transDateTime(pointlogData[i].reservationdate);
                            }
                            html += "</td>";
						}
						
						html += "</tr>";
						
					}
					table.html(html);
					
					/* 결과 테이블로 보여주기 끝 */
				} else {
					alert("오류가 발생하였습니다.");
				}
			}
			
			function searchUserPointlog(input){
				page = input;
				/* 회사명으로 검색 */
				var userid = $('#u_userid').val();
				var startdate = $('#u_startdate').val();
				var enddate = $('#u_enddate').val();
				var altertype = $('#u_userpointaltertype').val();
				var status = $('#u_userpointstatus').val();

				var parameter = "uid=" + userid
							+ "&startdate=" + startdate
							+ "&enddate=" + enddate
							+ "&altertype="+altertype
							+ "&status="+status
							+ "&page=" + page
							+ "&pageRow=" + pageRow;
				loadAJAXRest("GET","${ctx}/com/meal/cpointlog", parameter, handleSearchUserPointlogSuccess, null);
			}
			
			function handleSearchUserPointlogSuccess(request, status, response){
				
				pointlogData = response.content;
				if (response.status != 1){
					alert('오류가 발생하였습니다\n'+response.message);
					return;
				}
					
				pageController('pointlogPageNum', response.page, response.pageRow, response.total, 'searchUserPointlog');
				
				var html = "<tr><th>사용자</th><th>회사명</th><th>변경량</th><th>변경후</th><th>사용(충전시)</th><th>소멸일</th><th>형태</th><th>상태</th><th>사유</th><th>실행일</th></tr>";
				for (var i = 0; i < pointlogData.length; i++){
					html += "<tr>";
					html += "<td>"+pointlogData[i].username+"</td>";
					html += "<td>"+pointlogData[i].comname+"</td>";
					html += "<td>"+getCurrency(pointlogData[i].amount)+"</td>";
					html += "<td>"+getCurrency(pointlogData[i].outamount)+"</td>";
					html += "<td>"+getCurrency(pointlogData[i].useamount)+"</td>";
					
					if(pointlogData[i].expiredate==null){
						html += "<td>제한없음</td>";
					} else {
						html += "<td>"+transDateTime(pointlogData[i].expiredate)+"</td>";
					}
					
					html += "<td>"+$('#u_userpointaltertype option[value='+pointlogData[i].altertype+']').html()+"</td>";
					html += "<td>"+$('#u_userpointstatus option[value='+pointlogData[i].status+']').html()+"</td>";
					
					html += "<td>"+pointlogData[i].cause+"</td>";
					html += "<td>"+transDateTime(pointlogData[i].regdate)+"</td>";
					
					html += "</tr>";
				}
				
				$('#pointlogTable').html(html);
			}
			
			function exportUserComPointExcel(){
				/* 회사명으로 검색 */
				var userid = $('#u_userid').val();
				var altertype = $('#u_userpointaltertype').val();
				var status = $('#u_userpointstatus').val();
				
				var startdate = $('#u_startdate').val();
				var enddate = $('#u_enddate').val();
				
				if(startdate==""){
					alert("기간 시작을 입력해주세요");
					return;
				}
				if(enddate==""){
					alert("기간 종료을 입력해주세요");
					return;
				}
				
				var columns = "";
				var name = $('#u_username').val();
				if(name!=""){columns += name+",";}
				name = $('#u_userpointaltertype option[value='+altertype+']').html();
				if(altertype!=""){columns += name+",";}
				name = $('#u_userpointstatus option[value='+status+']').html();
				if(status!=""){columns += name+",";}
				
				if(columns==""){columns="없음";}
				
				if(!confirm("아래와 같은 조건으로 엑셀출력하시겠습니까?\n결과가 너무 많은경우 생성되지 않을 수 있습니다\n조건 : "+columns+"\n일시 : "+startdate+" ~ "+enddate)){
					return;
				}
				
				var parameter = "contenttype=CP"
							+ "&uid=" + userid
							+ "&sid=" + altertype
							+ "&mid=" + status
							+ "&startdate=" + startdate
							+ "&enddate=" + enddate
							+ "&columns=" + columns;
				
				loadAJAX("${ctx}/com/excel/export.json", parameter, handleExcelExportSuccess, null);
			}
			
			function handleExcelExportSuccess(request, status, response){
				if(response.status==1){
					alert("생성 성공\n엑셀 출력 메뉴에서 확인하실수 있습니다");
				} else {
					alert("오류!\n"+response.message+" : "+response.status);
				}
			}
			
			function setCategory(cate){
				if(cate=='c'){
					$('#companypointlog').addClass('active');
					$('#userpointlog').removeClass('active');
					
					$('#companypointsection').attr('style','');
					$('#userpointsection').attr('style','display:none');
					searchComPointSchedulelog(1);
				} else if(cate=='u'){
					
					$('#userpointlog').addClass('active');
					$('#companypointlog').removeClass('active');
					
					$('#companypointsection').attr('style','display:none');
					$('#userpointsection').attr('style','');
					searchUserPointlog(1);
				}
			}
		</script>
	</head>
	<body>
		<%@ include file="../topmenu.jsp" %>
		<div class="span17">
		<div class="row">
			<!-- sidebar nav 시작 -->
			<%@ include file="../menu.jsp" %>
			<!-- sidebar nav 끝 -->
			<div class="span14 contents-section">
				<!-- alert 영역 시작 -->
				<div id="message">
				</div>
				<!-- alert 영역 끝 -->
				<div class="form-inline">
					<ul class="nav nav-pills">
					  <li id="companypointlog" class="active"><a class="section-tap" href="#" onclick="javascript:setCategory('c')">회사입력기록</a></li>
					  <li id="userpointlog"><a class="section-tap" href="#" onclick="javascript:setCategory('u')">사용자입력기록</a></li>
					</ul>
				</div>
				<div id="companypointsection">
					<div class="form-inline">
					<!-- 
						<button type="button" class="btn btn-success" onclick="javascript:showMealPointTaskModal('add',searchComPointSchedulelog)">추가하기</button>
						 -->
					</div>
					<div class="form-inline">
						<label>기간 : </label>
						<input type="text" id="c_startdate" placeholder="시작" class="datePicker searchInput">~
						<input type="text" id="c_enddate" placeholder="종료" class="datePicker searchInput">
						&nbsp&nbsp
						<label>변경사유 : </label>
						<select id="c_companypointtype" class="searchInput">
				    		<option value="">전체</option>
				    		<c:forEach items="${compointrighttype}" var="item" >
				    			<option value="${item.type}">${item.name}</option>
				    		</c:forEach>
				    	</select>
				    	&nbsp&nbsp
						<label>상태 : </label>
						<select id="c_companypointstatus" class="searchInput">
				    		<option value="">전체</option>
				    		<c:forEach items="${compointrightstatus}" var="item" >
				    			<option value="${item.status}">${item.name}</option>
				    		</c:forEach>
				    	</select>
				    	&nbsp&nbsp
						<button type="button" class="btn btn-inverse" onclick="javascript:searchComPointSchedulelog(1)">검색</button>
					</div>
				</div>
				<div id="userpointsection" style="display:none">
					<div class="form-inline">
						<button type="button" class="btn btn-success" onclick="javascript:showMealPointSingleModal(searchUserPointlog)">추가하기</button>
					</div>
					<div class="form-inline">
						<label>사용자 : </label>
						<input type="text" readonly="readonly" class="input-small" placeholder="이름" id="u_username">
						<input type="hidden" id="u_userid">
						<a href="javascript:$('u_username').val('');$('u_userid').val('');"><i class="icon-remove"></i></a>
						<button type="button" class="btn" onclick="javascript:showUserSelecter('u_username', 'u_userid')">찾기</button>
						&nbsp&nbsp
						<label>기간 : </label>
						<input type="text" id="u_startdate" placeholder="시작" class="datePicker searchInput">~
						<input type="text" id="u_enddate" placeholder="종료" class="datePicker searchInput">
					</div>
					<div class="form-inline">
						<label>변경사유 : </label>
						<select id="u_userpointaltertype" class="searchInput">
				    		<option value="">전체</option>
				    		<c:forEach items="${usercompointtype}" var="item" >
				    			<option value="${item.type}">${item.name}</option>
				    		</c:forEach>
				    	</select>
				    	&nbsp&nbsp
				    	<label>상태 : </label>
				    	<select id="u_userpointstatus" class="searchInput">
				    		<option value="">전체</option>
				    		<c:forEach items="${usercompointstatus}" var="item" >
				    			<option value="${item.status}">${item.name}</option>
				    		</c:forEach>
			    		</select>
				    	&nbsp&nbsp
						<button type="button" class="btn btn-inverse" onclick="javascript:searchUserPointlog(1)">검색</button>
						&nbsp&nbsp&nbsp&nbsp
						<button type="button" class="btn btn-inverse" onclick="javascript:exportUserComPointExcel()">엑셀 출력</button>
					</div>
				</div>
				
				<table class="table table-borderd table-hover" id="pointlogTable" style="margin-top:10px">
					
				</table>
				<div id="pointlogPageNum" class="pagination pagination-centered">
			  
				</div>
			</div>
		</div>
		</div>
		
		
		<!-- 포인트 단체입력 모달뷰 -->
		<%@ include file="./monthpointManualModal.jsp" %>
		
		<!-- 포인트 단체입력 모달뷰 -->
		<%@ include file="./monthpointManualSingleModal.jsp" %>
		
		<!-- 사용자 찾기 모달 뷰 -->
		<%@ include file="../selectmodal/userSelecter.jsp" %>
		
	</body>
	<%@ include file="../footer.jsp" %>
</html>