
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>식권대장 관리자 페이지</title>
		<link rel="stylesheet" type="text/css" href="${ctx}/css/bootstrap.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/style.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/jquery-ui.css">
		<script type="text/javascript" src="${ctx}/js/jquery-1.9.1.js"></script>
		<script type="text/javascript" src="${ctx}/js/jquery-ui.js"></script>
		<script type="text/javascript" src="${ctx}/js/bootstrap.js"></script>
		<script type="text/javascript" src="${ctx}/js/javascript.js"></script>
		<script type="text/javascript" src="${ctx}/js/jquery-ui-sliderAccess.js"></script>
		<script type="text/javascript" src="${ctx}/js/jquery-ui-timepicker-addon.js"></script>
		<script type="text/javascript">
			var page = 1;
			var pageRow = 50;
			
			$(document).ready(function(){
				/* 달력 피커 */
				$('.datePicker').datepicker({
					dateFormat: 'yy-mm-dd',
				    monthNamesShort: ['1월','2월','3월','4월','5월','6월','7월','8월','9월','10월','11월','12월'],
				    dayNamesMin: ['일','월','화','수','목','금','토'],
				    weekHeader: 'Wk',
				    changeMonth: true, //월변경가능
				    changeYear: true, //년변경가능
				    yearRange:'1900:+0', // 연도 셀렉트 박스 범위(현재와 같으면 1988~현재년)
			    	showMonthAfterYear: true, //년 뒤에 월 표시
			    	buttonImageOnly: true, //이미지표시  
			    	buttonText: '날짜를 선택하세요'
				});
				$('.usedatePicker').datetimepicker({ 
					dateFormat: 'yy-mm-dd',
				    monthNamesShort: ['1월','2월','3월','4월','5월','6월','7월','8월','9월','10월','11월','12월'],
				    dayNamesMin: ['일','월','화','수','목','금','토'],
				    weekHeader: 'Wk',
				    changeMonth: true, //월변경가능
				    changeYear: true, //년변경가능
				    yearRange:'1900:+0', // 연도 셀렉트 박스 범위(현재와 같으면 1988~현재년)
			    	showMonthAfterYear: true, //년 뒤에 월 표시
			    	buttonImageOnly: true, //이미지표시  
			    	buttonText: '날짜를 선택하세요',
			    	timeFormat: 'HH:mm:ss',
			    	showMilisec: false,
			    	showSecond: true,
			    	timeText: '시간',
			    	hourText: '시',
			    	minuteText: '분',
			    	secondText: '초',
			    	currentText: '지금',
			    	closeText: '완료'
				});
				
				/* nav 선택 */
				/* $('#nav_stat').attr("class", "active"); */
				$('#menu_mealc').attr("src", "${ctx }/img/mealc_pressed.png");
				$('#submenu_mealc').css("display", "");
				$('#nav_stat_coupon_group').css("color", "#c4dc2c");
				
				/* 기본 시작날짜 입력 */
				$('#startdate').val(transDate(new Date()-86400000*10));
				
				/* 페이지 호출하기 */
				//searchUseStatistic();
			});
			
			function selectOption(){
				alert("지우기");
			}
			
			function searchUseStatistic(input){
				if(input==null){
					input = 1;
				}
				page = input;
				
				var dateradio = $( "input[name=datesort]:checked" ).val() ;
				
				var comid = $('#comid').val();
				var storeid = $('#storeid').val();
				var userid = $('#userid').val();
				
				var companysort = $('#companysort').prop("checked"); 
				var usersort =  $('#usersort').prop("checked");
				var storesort = $('#storesort').prop("checked");
				var vendertypesort = $('#vendertypesort').prop("checked");
				var coupontypesort = $('#coupontypesort').prop("checked");
				var statussort = $('#statussort').prop("checked");
				
				var startdate = $('#startdate').val();
				var enddate = $('#enddate').val();
				var calcflag = $('#calcflag').val();
				var vendertype = $('#vendertype').val();
				var coupontype = $('#coupontype').val();
				var status = $('#status').val();
				
				// 제휴점별
				var parameter = "comid=" + comid
							+ "&sid=" + storeid
							+ "&uid=" + userid
							+ "&datesort=" + dateradio
							+ "&companysort=" + companysort 
							+ "&usersort=" + usersort
							+ "&storesort=" + storesort
							+ "&vendertypesort=" + vendertypesort
							+ "&coupontypesort=" + coupontypesort
							+ "&statussort=" + statussort
							+ "&startdate=" + startdate
							+ "&enddate=" + enddate
							+ "&calcflag=" + calcflag
							+ "&vendertype=" + vendertype
							+ "&coupontype=" + coupontype
							+ "&status=" + status
							+ "&page=" + page
							+ "&pageRow=" + pageRow;
				//alert(parameter);
				loadAJAXRest("GET","${ctx}/com/stat/coupongroup", parameter, handleSearchUseStatisticSuccess, null);
			}
			
			function handleSearchUseStatisticSuccess(request, status, response){
				/* 회사명 검색 성공시 */
				/* 결과 테이블로 보여주기 시작 */
				useData = response.content;
				if (response.status == 1){
					var resultDate = "탐색 기간 : "+transDate(response.startdate)+" ~ ";
					if(response.enddate!=null){
						resultDate += transDate(response.enddate);
					} else {
						resultDate += transDate(new Date());
					}
					$('#resultDate').html(resultDate);
					
					pageController('couponStatisticPageNum', response.page, response.pageRow, response.total, 'searchUseStatistic');
					
					var dateradio = $( "input[name=datesort]:checked" ).val() ;
					var usersort =  $('#usersort').prop("checked");
					var storesort = $('#storesort').prop("checked");
					var vendertypesort = $('#vendertypesort').prop("checked");
					var coupontypesort = $('#coupontypesort').prop("checked");
					var statussort = $('#statussort').prop("checked");
					var table = $('#couponStatisticTable');
					
					var html = "<tr>";
					if(dateradio==""){
					} else if(dateradio=="day") {
						html +="<th>사용일</th>";
					} else if(dateradio=="week") {
						html +="<th>사용주</th>";
					} else if(dateradio=="month") {
						html +="<th>사용월</th>";
					} else {
						html +="<th>"+datesort+"</th>";
					}
					if(usersort){
						html +="<th>결제자</th>";
					} else {
						html +="<th>결제자수</th>";
					}
					if(storesort){
						html +="<th>상점</th>";
					} else {
						html +="<th>이용상점</th>";
					}
					if(vendertypesort){
						html +="<th>공급처</th>";
					}
					if(coupontypesort){
						html +="<th>사용형태</th>";
					}
					if(statussort){
						html +="<th>상태</th>";
					}
					html += "<th>정가</th><th>판매가</th><th>개인부담금액</th><th>총식권발행</th><th>사용</th><th>취소</th><th>취소금액</th>";
					
					html += "</tr>";
					
					for (var i = 0; i < useData.length; i++){
						html += "<tr>";
						if(dateradio!=""){
							html += "<td>"+useData[i].sdate+"</td>";
						}
						
						if(usersort){
							html +="<td>"+useData[i].leadername+"</td>";
						} else {
							html +="<td>"+getCurrency(useData[i].usercount)+"</td>";
						}
						if(storesort){
							html +="<td>"+useData[i].storename+"</td>";
						} else {
							html +="<td>"+useData[i].storecount+"</td>";
						}
						if(vendertypesort){
							html +="<td>"+$('#vendertype option[value='+useData[i].vendertype+']').html()+"</td>";
						}
						if(coupontypesort){
							html +="<td>"+$('#coupontype option[value='+useData[i].coupontype+']').html()+"</td>";
						}
						if(statussort){
							html +="<td>"+$('#status option[value='+useData[i].status+']').html()+"</td>";
						}
						html += "<td>"+getCurrency(useData[i].sumprice)+"</td>";
						html += "<td>"+getCurrency(useData[i].sumsalesprice)+"</td>";
						html += "<td>"+getCurrency(useData[i].sumpersonalpay)+"</td>";
						html += "<td>"+getCurrency(useData[i].usecount+useData[i].cancelmealcount)+"</td>";
						html += "<td>"+getCurrency(useData[i].usecount)+"</td>";
						html += "<td>"+getCurrency(useData[i].cancelmealcount)+"</td>";
						html += "<td>"+getCurrency(useData[i].cancelprice)+"</td>";
						
						html += "</tr>";
						
					}
					 
					table.html(html);
					
					/* 결과 테이블로 보여주기 끝 */
				} else {
					alert("오류가 발생하였습니다.");
				}
			}
			
			function removeSelectedCompany(){
				$('#comname').val('');
				$('#comid').val('');
			}
			
			function removeSelectedStore(){
				$('#storename').val('');
				$('#storeid').val('');
			}
			
			function removeSelectedMenu(){
				$('#menuname').val('');
				$('#menuid').val('');
			}
			
			function removeSelectedUser(){
				$('#username').val('');
				$('#userid').val('');
			}
			
		</script>
	</head>
	<body>
		<%@ include file="../topmenu.jsp" %>
		<div class="span18">
		<div class="row">
			<!-- sidebar nav 시작 -->
			<%@ include file="../menu.jsp" %>
			<!-- sidebar nav 끝 -->
			<div class="span15 contents-section">
				<!-- alert 영역 시작 -->
				<div id="message">
				</div>
				<!-- alert 영역 끝 -->
				<div class="form-inline">
					<ul class="nav nav-pills">
					  <li class="active"><a class="section-tap" href="#">식권별</a></li>
					  <li><a class="section-tap" href="#" onclick="javascript:location.href='${ctx}/com/stat/couponmember.vm';">개인별</a></li>
					</ul>
				</div>
				<div class="form-inline">
					<label style="width:80px">통계구간</label>
					<label class="radio">
					  <input type="radio" name="datesort" id="optionsRadios1" value="day" checked>
					  일별
					</label>&nbsp
					<!-- 
					<label class="radio">
					  <input type="radio" name="datesort" id="optionsRadios2" value="week">
					  주별
					</label>&nbsp
					 -->
					<label class="radio">
					  <input type="radio" name="datesort" id="optionsRadios3" value="month">
					  월별
					</label>&nbsp
					<label class="radio">
					  <input type="radio" name="datesort" id="optionsRadios4" value="">
					  합계
					</label>
				</div>
				<div class="form-inline">
					<label class="checkbox inline">
					  <input type="checkbox" id="usersort" > 결제자별
					</label>
					<label class="checkbox inline">
					  <input type="checkbox" id="storesort"> 제휴점별
					</label>
					<label class="checkbox inline">
					  <input type="checkbox" id="vendertypesort" > 공급사별
					</label>
					<label class="checkbox inline">
					  <input type="checkbox" id="coupontypesort" > 사용형태별
					</label>
					<label class="checkbox inline">
					  <input type="checkbox" id="statussort" > 상태별
					</label>
				</div>
				<div class="form-inline">
					<label>제휴점 : </label>
					<input type="text" readonly="readonly" class="input-small" placeholder="제휴점명" id="storename">
					<input type="hidden" id="storeid">
					<a href="javascript:removeSelectedStore()"><i class="icon-remove"></i></a>
					<button type="button" class="btn" onclick="javascript:showStoreSelecter('storename', 'storeid')">찾기</button>
					
					&nbsp					
					<label>결제자 : </label>
					<input type="text" readonly="readonly" class="input-small" placeholder="이름" id="username">
					<input type="hidden" id="userid">
					<a href="javascript:removeSelectedUser()"><i class="icon-remove"></i></a>
					<button type="button" class="btn" onclick="javascript:showUserSelecter('username', 'userid')">찾기</button>
					&nbsp&nbsp
					<label>사용형태 : </label>
					<select id="coupontype" class="searchInput">
			    		<option value="">전체</option>
			    		<c:forEach items="${coupontype}" var="item" >
			    			<option value="${item.type}">${item.name}</option>
			    		</c:forEach>
			    	</select>
				</div>
				<div class="form-inline">
					<label>기간 : </label>
					<input type="text" id="startdate" placeholder="시작" class="datePicker searchInput">~
					<input type="text" id="enddate" placeholder="종료" class="datePicker searchInput">
					&nbsp&nbsp
					<label>정산유무 : </label>
					<select id="calcflag" class="searchInput">
			    		<option value="">전체</option>
			    		<option value="false">정산전</option>
			    		<option value="true">정산완료</option>
			    	</select>
			    	&nbsp&nbsp
					<label>공급자 : </label>
					<select id="vendertype" class="searchInput">
			    		<option value="">전체</option>
			    		<c:forEach items="${vendertype}" var="item" >
			    			<option value="${item.type}">${item.name}</option>
			    		</c:forEach>
			    	</select>
			    	&nbsp&nbsp
			    	<label>상태 : </label>
			    	<select id="status" class="searchInput">
			    		<option value="">전체</option>
			    		<c:forEach items="${statusval}" var="item" >
			    			<option value="${item.status}">${item.name}</option>
			    		</c:forEach>
		    		</select>
					<button type="button" class="btn btn-inverse" onclick="javascript:searchUseStatistic()">검색</button>
				</div>
				<div class="form-inline">
					<label id="resultDate"></label>
				</div>
				<table class="table table-borderd" id="couponStatisticTable" style="margin-top:10px">
					
				</table>
				<div id="couponStatisticPageNum" class="pagination pagination-centered">
				</div>
			</div>
		</div>
		</div>
		
		<!-- 제휴점 찾기 모달 뷰 -->
		<%@ include file="../selectmodal/storeSelecter.jsp" %>
		
		<!-- 메뉴 찾기 모달 뷰 -->
		<%@ include file="../selectmodal/menuSelecter.jsp" %>
		
		<!-- 사용자 찾기 모달 뷰 -->
		<%@ include file="../selectmodal/userSelecter.jsp" %>
		
		
	</body>
	<%@ include file="../footer.jsp" %>
</html>