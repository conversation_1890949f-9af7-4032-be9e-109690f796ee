
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>

<!-- 제휴점 선택 모달 뷰 -->
<script>
	var storenamevar;
	var storeidvar;
	var subStoreData;
	var callbackfnc;
	
	function showStoreSelecter(name, id){
		callbackfnc = null;
		storenamevar = name;
		storeidvar = id;
		$('#findStore').modal('show');
	}
	
	function showStoreSelecterCallback(name, id, callback){
		showStoreSelecter(name, id);
		callbackfnc = callback;
	}
	
	function searchStore(input){
		/* 제휴점 이름으로 검색 */
		var name = $('#substorename').val();
		var parameter = "name=" + name 
					+ "&page=" + input
					+ "&pageRow=" + 10;
		loadAJAX("${ctx}/com/info/store.json", parameter, handleSearchStoreSuccess, null);
	}
	
	function handleSearchStoreSuccess(request, status, response){
		/* 제휴점 이름 검색 성공시 */
		/* 결과 테이블로 보여주기 시작 */
		subStoreData = response.content;
		if (response.status == 1){
			
			pageController('subStorePageNum', response.page, response.pageRow, response.total, 'searchStore');
			
			var table = $('#subStoreTable');
			var html = "<tr><th>제휴점명</th><th>업종</th><th>매장 전화번호</th><th>지역</th></tr>";
			for (var i = 0; i < subStoreData.length; i++){
				html += "<tr><td><a href='javascript:chooseStore("+i+");'>" + subStoreData[i].name + "</td>";
				if(subStoreData[i].category!=null){
					html += "<td>" + subStoreData[i].category + "</td>";
				} else {html +="<td></td>";}
				if(subStoreData[i].phone!=null){
					html += "<td>" + subStoreData[i].phone + "</td>";
				} else {html +="<td></td>";}
				if(subStoreData[i].region!=null){
					html += "<td>" + subStoreData[i].region + "</td>";
				} else {html +="<td></td>";}
				html += "</tr>";
								 
								
			}
			table.html(html);
			
			/* 결과 테이블로 보여주기 끝 */
		} else {
			alert("오류가 발생하였습니다.");
		}
	}
	
	function chooseStore(index){
		$('#'+storenamevar).val(subStoreData[index].name);
		$('#'+storeidvar).val(subStoreData[index].sid);
		$('#findStore').modal('hide');
		if(callbackfnc!=null){
			callbackfnc();
		}
	}
	
</script>
<div id="findStore" class="modal hide fade">
  <div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
    <h3>제휴점 선택하기</h3>
  </div>
  <div class="modal-body">
    <div class="well">
		<div class="form-inline">
			<label>제휴점 : </label>
			<input type="text" class="input-small" placeholder="제휴점명" id="substorename" onkeydown="javascript:if(event.keyCode == 13){searchStore(1);}">
			<a href="javascript:inputCleaner('substorename')"><i class="icon-remove"></i></a>
			<button type="button" class="btn btn-inverse" onclick="javascript:searchStore(1)">검색</button>
		</div>
	</div>
	<div class="well">
		<table class="table table-borderd" id="subStoreTable">
			
		</table>
		<div id="subStorePageNum" class="pagination pagination-centered">
		  
		</div>
	</div>
  </div>
  <div class="modal-footer">
    <button class="btn" data-dismiss="modal" aria-hidden="true">닫기</button>
  		<!-- <button class="btn btn-primary">선택</button> -->
  </div>
</div>
	