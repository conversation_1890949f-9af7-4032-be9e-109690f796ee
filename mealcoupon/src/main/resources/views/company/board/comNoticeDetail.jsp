<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>식권대장 기업회원</title>
		<link rel="stylesheet" type="text/css" href="${ctx}/css/bootstrap.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/jquery-ui.css">
		<script type="text/javascript" src="${ctx}/js/jquery-1.9.1.js"></script>
		<script type="text/javascript" src="${ctx}/js/jquery-ui.js"></script>
		<script type="text/javascript" src="${ctx}/js/bootstrap.js"></script>
		<script type="text/javascript" src="${ctx}/js/javascript.js"></script>
		<script type="text/javascript" src="${ctx}/js/jquery-ui-sliderAccess.js"></script>
		<script type="text/javascript" src="${ctx}/js/jquery-ui-timepicker-addon.js"></script>
		<script type="text/javascript">
			$(document).ready(function(){
				/* 시간 피커 */
				$('.timePicker').timepicker({
					timeFormat: 'HH:mm:ss',
				    yearRange:'1900:+0', // 연도 셀렉트 박스 범위(현재와 같으면 1988~현재년)
			    	showMonthAfterYear: true, //년 뒤에 월 표시
			    	buttonImageOnly: true, //이미지표시  
			    	buttonText: '시간을 선택하세요',
			    	showMilisec: false,
			    	showSecond: false,
			    	timeText: '시간',
			    	hourText: '시',
			    	minuteText: '분',
			    	currentText: '지금',
			    	closeText: '완료'
				});
				
				/* nav 선택 */
				//$('#nav_comnotice').attr("class", "active");
				$('#menu_companysetting').attr("src", "${ctx }/img/companysetting_pressed.png");
				$('#submenu_companysetting').css("display", "");
				$('#nav_comnotice').css("color", "#c4dc2c");
				
				if ($('#statusvalue').val() == 'true'){
					$('#status').val("1");
				}
			});
			
			function handleHttpError(request, status, error){
				/* 통신 에러 */
				alert("code : " + request.status + "\r\nmessage : " + request.reponseText);
			}
			
			function modifyNotice(){
				/* 유효성 검사 시작 */
				var title = $('#title').val();
				var status = $('#status').val();
				var contents = $('#contents').val();
				var postid = "<%=request.getParameter("postid")%>";
				
				if (title == ""){
					alert("제목을 입력하세요.");
					return false;
				}
				if (contents == ""){
					alert("내용을 입력하세요.");
					return false;
				}
				/* 유효성 검사 끝 */
				
				/* 새로운 회사 추가하기 */
				var parameter = "title=" + title
								+ "&contents=" + contents
								+ "&status=" + status
								+ "&postid=" + postid
								+ "&comid=" + "${user.comid}";
				loadAJAX("${ctx}/com/board/com/edit.json", parameter, handleModifyNoticeSuccess, handleHttpError);
			}
			
			function handleModifyNoticeSuccess(request, status, response){
				/* 공지사항 추가 성공시 */
				if(response.status==1){
					alert("공지사항 수정 성공");
					location.href = "${ctx}/com/board/com/index.vm";
				} else {
					alert("Error\n"+response.message+" : "+response.status);
				}
			}
		</script>
	</head>
	<body>
		<%@ include file="../topmenu.jsp" %>
		<div class="span12">
		<div class="row">
			<!-- sidebar nav 시작 -->
			<%@ include file="../menu.jsp" %>
			<!-- sidebar nav 끝 -->
			<!-- 새로운 회사 추가 입력 폼 시작 -->
			<div class="span9 contents-section">
				<!-- alert 영역 시작 -->
				<div id="message">
				</div>
				<!-- alert 영역 끝 -->
				<p style="margin-left:10px;margin-bottom:15px;font-size:15pt">공지사항을 수정하세요</p>
				<div class="container">
					<div class="form-horizontal">
						<div class="control-group">
							<label class="control-label" for="title">제목</label>
							<div class="controls">
						    	<input type="text" id="title" placeholder="제목" value="${notice.title }">
						    </div>
						</div>
						<div class="control-group">
							<label class="control-label" for="contents">내용</label>
							<div class="controls">
						    	<textarea id="contents" placeholder="내용" cols="10" rows="10" style="width: 500px;">${notice.contents }</textarea>
						    </div>
						</div>
						<div class="control-group">
							<input type="hidden" id="statusvalue" value="${notice.status }">
							<label class="control-label" for="status">상태</label>
							<div class="controls">
								<select id="status">
									<option value="0" >비활성</option>	
									<option value="1" >활성</option>
								</select>
						    </div>
						</div>
						<div class="span2 offset2">
							<button type="button" class="btn btn-primary" onclick="javascript:modifyNotice();">수정하기</button>
						</div>
					</div>
				</div>
			</div>
			<!-- 새로운 회사 추가 입력 폼 끝 -->
		</div>
		</div>
	</body>
	<%@ include file="../footer.jsp" %>
</html>