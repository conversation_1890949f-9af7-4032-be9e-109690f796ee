<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>식권대장 관리자 페이지</title>
    <link rel="stylesheet" type="text/css" href="${ctx}/css/bootstrap.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/style.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/jquery-ui.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/onoffbutton.css">
    <script type="text/javascript" src="${ctx}/js/jquery-1.9.1.js"></script>
    <script type="text/javascript" src="${ctx}/js/jquery-ui.js"></script>
    <script type="text/javascript" src="${ctx}/js/bootstrap.js"></script>
    <script type="text/javascript" src="${ctx}/js/javascript.js?_tz=<%=System.currentTimeMillis()%>"></script>
    <script type="text/javascript" src="${ctx}/js/jquery-ui-timepicker-addon.js"></script>
    <script type="text/javascript" src="/admin/js/util.js?_tz=<%=System.currentTimeMillis()%>"></script>
    <script type="text/javascript" src="/admin/js/activitylog/activitylog.js?_tz=<%=System.currentTimeMillis()%>"></script>
    <script type="text/javascript" src="/admin/js/moment.js"></script>
    <script type="text/javascript">
      /* nav 선택 */
      $('#menu_mealc').attr('src', `${ctx}/img/mealc_pressed.png`);
      $('#submenu_mealc').css('display', '');
      $('#nav_use_member').css('color', 'white');
    </script>
</head>
<body>
<%@ include file="../topmenu.jsp" %>
<div class="span19">
    <div class="row">
        <!-- sidebar nav 시작 -->
        <%@ include file="../menu.jsp" %>
        <!-- sidebar nav 끝 -->
        <div class="span16 contents-section">
            <!-- alert 영역 시작 -->
            <div id="message"></div>
            <!-- alert 영역 끝 -->
            <h3>개인 정보 관리 내역</h3>
            <div class="form-inline">
                <h5>백오피스 (GR) 에서 관리자가 개인정보 관련하여 조회/생성/수정/삭제 등의 활동한 내역을 확인할 수 있습니다.</h5>
            </div>
            <div class="form-inline" id="activityLogSection">
                <span>
                    <label for="activityType">활동 타입 : </label>
                    <select id="activityType" name="activityType" class="searchInput">
                        <option value="">전체</option>
                        <c:forEach items="${activityTypes}" var="item">
                            <option value="${item}">${item.name}</option>
                        </c:forEach>
                    </select>
                </span>
                <span style="padding-left: 20px">
                    <label>기간 : </label>
                    <input type="text" id="startDate" placeholder="시작" class="datePicker searchInput">~
                    <input type="text" id="endDate" placeholder="종료" class="datePicker searchInput">
                </span>
                <span style="float: right">
                    <select id="searchType" name="searchType" class="searchInput">
                        <option value="adminName">관리자명</option>
                        <option value="ip">IP</option>
                    </select>
                    <input type="text" class="input-big" placeholder="검색어 입력" id="searchKeyword" name="searchKeyword">
                    <button type="button" class="btn btn-inverse" id="btnActivityLogSearch">검색</button>
                </span>

                <div class="span16" style="padding-left: 0px;" id="activityLogArea">
                    <table class="table table-borderd table-hover" id="activityLogTable" style="margin-top:10px;WORD-BREAK: keep-all;">
                        <thead id="activityLogThead">
                            <th>위치</th>
                            <th>활동 타입</th>
                            <th>활동 결과</th>
                            <th>IP</th>
                            <th>관리자명</th>
                            <th>일시</th>
                        </thead>
                        <tbody id="activityTbody"></tbody>
                    </table>
                    <div id="activityLogPageNum" class="pagination pagination-centered"></div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
