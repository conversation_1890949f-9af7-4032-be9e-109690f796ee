
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>식권대장 관리자 페이지</title>
		<link rel="stylesheet" type="text/css" href="${ctx}/css/bootstrap.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/style.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/jquery-ui.css">
		<script type="text/javascript" src="${ctx}/js/jquery-1.9.1.js"></script>
		<script type="text/javascript" src="${ctx}/js/jquery-ui.js"></script>
		<script type="text/javascript" src="${ctx}/js/bootstrap.js"></script>
		<script type="text/javascript" src="${ctx}/js/javascript.js"></script>
		<script type="text/javascript">
			var calcData;
			var page = 1;
			var pageRow = 20;
		
			$(document).ready(function(){
				/* 달력 피커 */
				$('.datePicker').datepicker({
					dateFormat: 'yy-mm-dd',
				    monthNamesShort: ['1월','2월','3월','4월','5월','6월','7월','8월','9월','10월','11월','12월'],
				    dayNamesMin: ['일','월','화','수','목','금','토'],
				    weekHeader: 'Wk',
				    changeMonth: true, //월변경가능
				    changeYear: true, //년변경가능
				    yearRange:'1900:+0', // 연도 셀렉트 박스 범위(현재와 같으면 1988~현재년)
			    	showMonthAfterYear: true, //년 뒤에 월 표시
			    	buttonImageOnly: true, //이미지표시  
			    	buttonText: '날짜를 선택하세요'
				});
				
				/* nav 선택 */
				/* $('#nav_calc_gifticon').attr("class", "active"); */
				$('#submenu_calculate').css("display", "");
				$('#nav_calc_gifticon').css("color", "white");
				
				$("#type").change(function(){
					searchGifticonCalc(1);
				});
				
				/* 페이지 호출하기 */
				searchGifticonCalc(1);
			});
			
			function searchGifticonCalc(input){
				page = input;
				/* 회사명으로 검색 */
				var type = $('#addcalcgifticontype').val();
				var startdate = $('#startdate').val();
				var enddate = $('#enddate').val();
				
				var parameter = "type=" + type
							+ "&startdate=" + startdate
							+ "&enddate=" + enddate
							+ "&page=" + page
							+ "&pageRow=" + pageRow;
				loadAJAX("${ctx}/admin/gifticon/calc/inquiry.json", parameter, handleSearchGifticonCalcSuccess, null);
			}
			
			function handleSearchGifticonCalcSuccess(request, status, response){
				/* 회사명 검색 성공시 */
				/* 결과 테이블로 보여주기 시작 */
				calcData = response.content;
				if (response.status == 1){
					
					pageController('gifticonCalcPageNum', response.page, response.pageRow, response.total, 'searchGifticonCalc');
					
					var table = $('#gifticonCalcTable');
					var html = "<tr><th>공급사</th><th>정산구간</th><th>정산일</th><th>쿠폰수</th><th>정가</th><th>거래금액(판매가)</th><th>정산금액(공급가)</th><th>상태</th></tr>";
					for (var i = 0; i < calcData.length; i++){
						html += "<tr>";
						html += "<td>"+calcData[i].storename+"</td>";
						html += "<td>"+transDate(calcData[i].usetimestart)+"~"+transDate(calcData[i].usetimeend)+"</td>";
						html += "<td>"+transDateTime(calcData[i].calcdate)+"</td>";
						html += "<td>"+calcData[i].countcoupon+"</td>";
						html += "<td>"+getCurrency(calcData[i].price)+"</td>";
						html += "<td>"+getCurrency(calcData[i].salesprice)+"</td>";
						html += "<td>"+getCurrency(calcData[i].calcprice)+"</td>";
						if(calcData[i].status=="1"){
							html += '<td><a href="javascript:setDepositDone(\''+calcData[i].calcid+'\');">입금처리하기</a></td>';
						} else if(calcData[i].status=="5"){
							html += "<td>입금 완료</td>";
						} else {
							html += "<td>"+calcData[i].status+"</td>";
						}
						html += "</tr>";
						
					}
					table.html(html);
					
					/* 결과 테이블로 보여주기 끝 */
				} else {
					alert("오류가 발생하였습니다.");
				}
			}
			
			function setDepositDone(calcid){
				var parameter = "calcid=" + calcid;
				loadAJAX("${ctx}/admin/gifticon/calc/deposit.json", parameter, handleGifticonCalcDepositSuccess, null);
			}
			
			function handleGifticonCalcDepositSuccess(request, status, response){
				if(response.status==1){
					alert("처리 완료");
					searchGifticonCalc(page);
				} else {
					alert(response.message+" : "+response.status);
				}
			}
			
			function showAddGifticonCalc(){
				$('#addcalcstoreid').val('');
				$('#addcalcstorename').val('');
				$('#addcalcstorestart').val('');
				$('#addcalcstoreend').val('');
				$('#addGifticonCalc').modal('show');
			}
			
			
			function addGifticonCalc(){
				var type = $('#addcalcgifticontype').val();
				var startdate = $('#addcalcstorestart').val();
				var enddate = $('#addcalcstoreend').val();
				
				if (type == ""){
					alert("기프티콘 공급사를 선택해주세요");
					return false;
				}
				
				if (startdate == ""){
					alert("정산 구간 시작일을 입력해주세요");
					return false;
				}
				
				if (enddate == ""){
					alert("정산 구간 종료일을 입력해주세요");
					return false;
				} else {
					var now = new Date();
					var dateText = $('#addcalcstoreend').val();
					
					var selectedDate = new Date(dateText.substr(0, 4), dateText.substr(5, 2)-1, dateText.substr(8, 2));
					if (now.getTime() - selectedDate.getTime() < 86400){
						
						alert("정산 종료일은 최대 어제날짜까지 가능합니다."+now+" "+selectedDate);
						return false;
					}
				}
				
				var parameter = "type=" + type
							+ "&startdate=" + startdate
							+ "&enddate=" + enddate;
				// alert(parameter);
				loadAJAX("${ctx}/admin/gifticon/calc/calculate.json", parameter, handleAddGifticonCalcSuccess, null);
			}
			
			function handleAddGifticonCalcSuccess(request, status, response){
				if(response.status==1){
					$('#addStoreCalc').modal('hide');
					alert("정산 완료");
					searchGifticonCalc(1);
				} else {
					alert("정산 싪패 - "+response.message+" : "+response.status);
				}
			}
			
			
			// export excel
			/* 
			function exportExcel(){
				var storeid = $('#storeid').val();
				var startdate = $('#startdate').val();
				var enddate = $('#enddate').val();
				
				if(startdate==""){
					alert("기간 시작을 입력해주세요");
					return;
				}
				if(enddate==""){
					alert("기간 종료을 입력해주세요");
					return;
				}
				
				var columns = "";
				var name = $('#storename').val();
				if(name!=""){columns += name+",";}
				
				if(columns==""){columns="없음";}
				
				if(!confirm("아래와 같은 조건으로 엑셀출력하시겠습니까?\n결과가 너무 많은경우 생성되지 않을 수 있습니다\n조건 : "+columns+"\n일시 : "+startdate+" ~ "+enddate)){
					return;
				}
				
				var parameter = "contenttype=GC"
							+ "&sid=" + storeid
							+ "&startdate=" + startdate
							+ "&enddate=" + enddate
							+ "&columns=" + columns;
				
				loadAJAX("${ctx}/admin/excel/export.json", parameter, handleExcelExportSuccess, null);
			}
			
			function handleExcelExportSuccess(request, status, response){
				if(response.status==1){
					alert("생성 성공\n엑셀 출력 메뉴에서 확인하실수 있습니다");
				} else {
					alert("오류!\n"+response.message+" : "+response.status);
				}
			}
			*/
		</script>
	</head>
	<body>
		<%@ include file="../topmenu.jsp" %>
		<div class="span15">
		<div class="row">
			<!-- sidebar nav 시작 -->
			<%@ include file="../menu.jsp" %>
			<!-- sidebar nav 끝 -->
			<div class="span12 contents-section">
				<!-- alert 영역 시작 -->
				<div id="message">
				</div>
				<!-- alert 영역 끝 -->
				<div class="form-inline">
					<button type="button" class="btn btn-success" onclick="javascript:showAddGifticonCalc()">정산하기</button>
				</div>
				<div class="form-inline">
					<label>공급사 : </label>
					<select id="type" class="searchInput">
			    		<option value="">전체</option>
			    		<option value="1">큐피콘</option>
			    	</select>
					&nbsp&nbsp
					<input type="text" id="startdate" placeholder="시작" class="datePicker searchInput">~
					<input type="text" id="enddate" placeholder="종료" class="datePicker searchInput">
					&nbsp&nbsp
					<button type="button" class="btn btn-inverse" onclick="javascript:searchGifticonCalc(1)">검색</button>
					&nbsp&nbsp&nbsp&nbsp
					<!-- 
					<button type="button" class="btn btn-inverse" onclick="javascript:exportExcel()">엑셀 출력</button>
					 -->
				</div>
				
				<table class="table table-borderd" id="gifticonCalcTable" style="margin-top:10px">
					
				</table>
				<div id="gifticonCalcPageNum" class="pagination pagination-centered">
			  
				</div>
			</div>
		</div>
		</div>
		
		<!-- 새로운 제휴점 정산 기록 넣기 모달 뷰 -->
		<div id="addGifticonCalc" class="modal hide fade modal-wide">
		  <div class="modal-header">
		    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
		    <h3>정산</h3>
		  </div>
		  <div class="modal-body">
		  	<h4>한번 진행한 정산은 취소 할 수 없습니다</h4>
		  	<br/>
		  	<div class="form-horizontal">
	    	<div class="control-group">
				<label class="control-label" for="addcalcgifticontype">공급사 : </label>
				<div class="controls">
					<select id="addcalcgifticontype" class="searchInput">
			    		<option value="">전체</option>
			    		<option value="1">큐피콘</option>
			    	</select>
			    	<!-- 
					<input type="text" readonly="readonly" class="input-small" placeholder="제휴점명" id="addcalcstorename">
					<input type="hidden" id="addcalcstoreid">
					<a href="javascript:removeSelectedMenu()"><i class="icon-remove"></i></a>
					<button type="button" class="btn" onclick="javascript:showStoreSelecter('addcalcstorename', 'addcalcstoreid')">찾기</button>
					 -->
	    		</div>
	    	</div>
	    	<div class="control-group">
				<label class="control-label" for="addcashlogpoint" >범위 : </label>
				<div class="controls">
					<input type="text" id="addcalcstorestart" placeholder="시작" class="datePicker searchInput">~
					<input type="text" id="addcalcstoreend" placeholder="종료" class="datePicker searchInput"><span class="help-inline">정산 종료일은 최대 어제날짜까지 가능합니다.</span>
    			</div>
			</div>
			
		  </div>
		  </div>
		  <div class="modal-footer">
		  	<button id="menuConfirm" class="btn btn-primary" aria-hidden="true" onclick="javascript:addGifticonCalc()">정산</button>
		    <button class="btn" data-dismiss="modal" aria-hidden="true">닫기</button>
		  </div>
		</div>
		
	</body>
</html>