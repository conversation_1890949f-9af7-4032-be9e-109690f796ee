
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>식권대장 관리자 페이지</title>
		<link rel="stylesheet" type="text/css" href="${ctx}/css/bootstrap.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/style.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/jquery-ui.css">
		<script type="text/javascript" src="${ctx}/js/jquery-1.9.1.js"></script>
		<script type="text/javascript" src="${ctx}/js/jquery-ui.js"></script>
		<script type="text/javascript" src="${ctx}/js/bootstrap.js"></script>
		<script type="text/javascript" src="${ctx}/js/javascript.js"></script>
        <script type="text/javascript" src="${ctx}/js/jquery-ui-sliderAccess.js"></script>
        <script type="text/javascript" src="${ctx}/js/jquery-ui-timepicker-addon.js"></script>
		<script type="text/javascript">
			var pointlogData;
			var page = 1;
			var pageRow = 20;
			var cprid = '${cprlog.cprid}';
			var comid = '${cprlog.comid}';
			
			var mptask_page = 1;
			var mptask_pageRow = 8;
			var mptask_selected_user_new;
			var mptask_selected_user_name;
			
			var change_uid = new Array();
			var change_amount = new Array();
			var change_cause = new Array();
			var commit_update = false;
			var page_change = -1;
			var delete_change_uid = null;
			
			$(document).ready(function(){
				/* 달력 피커 */
				$('.datePicker').datepicker({
					dateFormat: 'yy-mm-dd',
				    monthNamesShort: ['1월','2월','3월','4월','5월','6월','7월','8월','9월','10월','11월','12월'],
				    dayNamesMin: ['일','월','화','수','목','금','토'],
				    weekHeader: 'Wk',
				    changeMonth: true, //월변경가능
				    changeYear: true, //년변경가능
				    yearRange:'1900:+1', // 연도 셀렉트 박스 범위(현재와 같으면 1988~현재년)
			    	showMonthAfterYear: true, //년 뒤에 월 표시
			    	buttonImageOnly: true, //이미지표시  
			    	buttonText: '날짜를 선택하세요'
				});

                $('.dateTimePicker').datetimepicker({
                    dateFormat: 'yy-mm-dd',
                    monthNamesShort: ['1월','2월','3월','4월','5월','6월','7월','8월','9월','10월','11월','12월'],
                    dayNamesMin: ['일','월','화','수','목','금','토'],
                    weekHeader: 'Wk',
                    changeMonth: true, //월변경가능
                    changeYear: true, //년변경가능
                    yearRange:'1900:+1', // 연도 셀렉트 박스 범위(현재와 같으면 1988~현재년)
                    showMonthAfterYear: true, //년 뒤에 월 표시
                    buttonImageOnly: true, //이미지표시
                    buttonText: '날짜를 선택하세요',
                    timeFormat: 'HH:mm:ss',
                    showMilisec: false,
                    showSecond: true,
                    timeText: '시간',
                    hourText: '시',
                    minuteText: '분',
                    secondText: '초',
                    currentText: '지금',
                    closeText: '완료'
                });
				
				/* nav 선택 */
				/* $('#nav_mypoint').attr("class", "active"); */
				$('#menu_mealc').attr("src", "${ctx }/img/mealc_pressed.png");
				$('#submenu_mealc').css("display", "");
				$('#nav_meal_pointalter').css("color", "#c4dc2c");
				
				mptask_selected_user_new = new Array();
				mptask_selected_user_name = new Array();
				
				$('#findUser').on('hide', function(){
					addCompanyPointTaskMember();
				});
				
				if(cprid==null||cprid==''){
					location.href = "${ctx}/admin/meal/cpoint.vm";
				} else {
					inquiryCompanyPointTask(1);
				}
			});
			
			/*===============
			 *  task 조회
			 */
			 
			function pageSelectListener(input){
				if(change_uid.length>0){
					page_change = input;
					updateCompanyPointRecord();
				}
				inquiryCompanyPointTask(input);
			}
			
			function inquiryCompanyPointTask(input){
				if(input==null){input = 1;}
				mptask_page = input;
				var parameter = "page="+mptask_page
						+"&pageRow="+mptask_pageRow;
				
				loadAJAXRest("GET","${ctx}/admin/meal/cpoint/"+cprid, parameter, handleInquiryCompanyPointTask, null, false);
			}
			
			function handleInquiryCompanyPointTask(request, status, response){
				
				if(response.status!=1){
					alert("오류가 발생하였습나다\n"+response.message);
					return;
				}
				var pending = true;
				
				var list = response.content.selectlist;
				var html = '<tr><th>사용자명</th><th>충전량</th><th>사유</th><th>삭제</th></tr>';
				
				var per = 0;
				for(var i=0;i<list.length;i++){
					html += '<tr>';
					html += '<td>'+list[i].username+'</td>';
					html += '<td><input type="number" id="'+list[i].uid+'_amount" value="'+list[i].amount+'" onfocusout="eventInputFocus(this)"></td>';
					html += '<td><input type="text" id="'+list[i].uid+'_cause" value="'+dc(list[i].cause)+'" onfocusout="eventInputFocus(this)"></td>';
					html += '<td><button class="btn" aria-hidden="true" onclick="deleteCompanyPointTaskMemeber(\''+list[i].uid+'\')">삭제</button></td>';
					// <button class="btn" aria-hidden="true" onclick="updateCompanyPointTaskMemeber(\''+list[i].uid+'\')">수정</button>
					html += '</tr>';
				}
				
				if(pending&&per!=0){
					for(var i=0;i<4-per;i++){
						html += '<td></td>';
					}
					html += '</tr>';
				}
				
				pageController('mptask_pageNum', response.page, response.pageRow, response.total, 'pageSelectListener');
				
				$('#mptask_targetUser').html(html);
			}
			
			function searchComPointSchedulelog(input){
				if(input==null){
					input = 1;
				}
				page = input;
				/* 회사명으로 검색 */
				var startdate = $('#c_startdate').val();
				var enddate = $('#c_enddate').val();
				var chargetype = $('#c_companypointtype').val();
				
				var parameter = "startdate=" + startdate
							+ "&enddate=" + enddate
							+ "&chargetype="+chargetype
							+ "&page=" + page
							+ "&pageRow=" + pageRow;
				loadAJAXRest("GET","${ctx}/admin/meal/cpoint", parameter, handleSearchComPointSchedulelogSuccess, null);
			}
			
			function handleSearchComPointSchedulelogSuccess(request, status, response){
				/* 회사명 검색 성공시 */
				/* 결과 테이블로 보여주기 시작 */
				pointlogData = response.content;
				if (response.status != 1){
					alert("오류가 발생하였습니다.\n"+response.message);
				} else {
				}
			}
			
			/*===============
			 *  사용자 지정 추가
			 */
			
			function onMpTaskSelectUser(name, uid, division, position, level, select, selectedindex){
				if(select){
					mptask_selected_user_name[mptask_selected_user_name.length] = name;
				} else {
					selecteduserids.splice(selectedindex, 1);
				}
			}
			
			function addCompanyPointTaskMember(){
				if(mptask_selected_user_new.length==0){
					return;
				}
				var parameter = 'comid='+comid;
				for(var i=0;i<mptask_selected_user_new.length;i++){
					parameter += '&uids='+mptask_selected_user_new[i];
					parameter += '&names='+mptask_selected_user_name[i];
				}
				//alert(parameter);
				loadAJAXRest("POST","${ctx}/admin/meal/cpoint/"+cprid+"/member", parameter, handleAddCompanyPointTaskMember, null, false);
			}
			
			function handleAddCompanyPointTaskMember(request, status, response){
				
				if(response.status != 1){
					alert("오류가 발생하였습니다\n"+reponse.message);
					return;
				}
				if(response.content.length!=0){
					var msg = '추가 실패 목록\n';
					for(var i=0;i<response.content.length;i++){
						msg += response.content[i]+'\n';
					}
					alert(msg);
				}
				mptask_selected_user_new = new Array();
				mptask_selected_user_name = new Array();
				inquiryCompanyPointTask(1);
			}
			
			/*================
			 * 사용자, 변동량 지정 변경
			 ===============*/
			 
		 	function eventInputFocus(obj){
				var id = obj.id;
				var index_ = id.indexOf("_",0);
				var uid = id.substring(0, index_);
				/* 
				var change_uid = new Array();
				var change_amount = new Array();
				var change_cause = new Array();
				 */
				var amount = $('#'+uid+'_amount').val();
				var cause = $('#'+uid+'_cause').val();
				
				var uid_index = -1;
				for(var i=0;i<change_uid.length;i++){
					if(change_uid[i]==uid){
						uid_index = i;
						break;
					}
				}
				
				if(uid_index==-1){
					uid_index = change_uid.length;
					change_uid[uid_index] = uid;
				}
				change_amount[uid_index] = amount;
				change_cause[uid_index] = cause;
				
				/* 
				for(var i=0;i<change_uid.lengh;i++){
				}
				 */
			}
			
			function updateCompanyPointRecord(){
				
				var parameter = "comid="+comid+"&names=";
				for(var i=0;i<change_uid.length;i++){
					parameter += "&uids="+change_uid[i]+"&amounts="+change_amount[i]+"&cause="+change_cause[i];
				}
				loadAJAXRest("POST","${ctx}/admin/meal/cpoint/"+cprid+"/memberupdate", parameter, handleUpdateCompanyPointRecord, null, false);
				
			}
			
			function handleUpdateCompanyPointRecord(request, status, response){
				if(response.status != 1){
					alert("오류가 발생하였습니다\n"+response.message);
					commit_update = false;
					return;
				}
				change_uid = [];
				change_amount = [];
				change_cause = [];
				if(delete_change_uid!=null){
					deleteCompanyPointTaskMemeber(delete_change_uid);
					delete_change_uid = null;
					return;
				} else if(page_change!=-1){
					inquiryCompanyPointTask(page_change);
					page_change = -1;
					return;
				} else if(!confirm("수정된 내용이 적용되었습니다\n계속 진행하시겠습니까?")){
					return;
				}
				if(commit_update){
					executeCompanyPointTask();
					commit_update = false;
				}
			}
			 
			function updateCompanyPointTaskMemeber(uid){
				var amount = $('#'+uid+'_amount').val();
				var cause = $('#'+uid+'_cause').val();
				var parameter = "names= &uids="+uid+"&amounts="+amount+"&cause="+cause;
				loadAJAXRest("PUT","${ctx}/admin/meal/cpoint/"+cprid+"/member?"+parameter, '', handleUpdateCompanyPointTaskMemeber, null, false);
			}
			
			function handleUpdateCompanyPointTaskMemeber(request, status, response){
				if(response.status != 1){
					alert("오류가 발생하였습니다\n"+response.message);
					return;
				}
				alert("수정 완료");
				inquiryCompanyPointTask(mptask_page);
			}
			
			/*===============
			 *  사용자 지정 삭제
			 */
			
			function deleteCompanyPointTaskMemeber(uid){
				if(change_uid.length>0){
					delete_change_uid = uid;
					updateCompanyPointRecord();
					return;
				}
				var parameter = 'names= &uids='+uid;
				loadAJAXRest("DELETE","${ctx}/admin/meal/cpoint/"+cprid+"/member?"+parameter, '', handleDeleteCompanyPointTaskMember, null, false);
			}
			
			function handleDeleteCompanyPointTaskMember(request, status, response){
				if(response.status != 1){
					alert("오류가 발생하였습니다\n"+response.message);
					return;
				}
				alert("삭제 완료");
				inquiryCompanyPointTask(mptask_page);
			}
			
			function executeCompanyPointTask(){
				
				if(change_uid.length>0){
					commit_update = true;
					updateCompanyPointRecord();
				} else {
                    var reservationdate = $('#mptask_reservationdate').val();
					loadAJAXRest("PUT","${ctx}/admin/meal/cpoint/"+cprid+'?comid='+comid+"&reservationdate="+reservationdate, '', handleExecuteCompanyPointTask, null, false);
				}
				
			}
			
			function handleExecuteCompanyPointTask(request, status, response){
				if(response.status!=1){
					alert("오류가 발생하였습니다\n"+response.message);
					return;
				}
				alert("실행 대기열에 등록되었습니다");
				
				location.href = "${ctx}/admin/meal/cpoint.vm";
			}
			
		</script>
	</head>
	<body>
		<%@ include file="../topmenu.jsp" %>
		<div class="span14">
		<div class="row">
			<!-- sidebar nav 시작 -->
			<%@ include file="../menu.jsp" %>
			<!-- sidebar nav 끝 -->
			<div class="span11 contents-section">
				<!-- alert 영역 시작 -->
				<div id="message">
				</div>
				<!-- alert 영역 끝 -->
				<h3 id="menusubtitle">회사포인트 사용자 지정입력 - 사용자 선택</h3>
				<div class="form-horizontal">
					<div class="control-group" id="mptask_chargetype_select">
						<label class="control-label" for="mptask_chargetype">대상지정방법 : </label>
						<div class="controls">
							<input type="text" class="input-big" disabled="disabled" value="사용자 및 포인트 지정">
						</div>
					</div>
					<div class="control-group">
						<label class="control-label" for="mptask_amount">변동량 : </label>
						<div class="controls">
			    			<input type="text" value="사용자별 지정" class="input-big" disabled="disabled" placeholder="음수로 차감가능" id="mptask_amount" onKeyUp="javascript:only_num(this, true, false, true)" >
			    		</div>
			    	</div>
			    	<div class="control-group">
						<label class="control-label" for="mptask_expiredate">만료일 : </label>
						<div class="controls">
					    	<input type="text" value="<fmt:formatDate value="${cprlog.expiredate}" pattern="yyyy-MM-dd"/>" id="mptask_expiredate" disabled="disabled" placeholder="사용기한" class="datePicker">
					    </div>
					</div>
					<div class="control-group">
						<label class="control-label" for="mptask_cause">사유 : </label>
						<div class="controls">
			    			<input type="text" value="${cprlog.cause}" class="input-big" placeholder="변동 사유" disabled="disabled" id="mptask_cause" maxlength="40">
			    		</div>
			    	</div>
			    	<div class="control-group" id="mptask_resultment_section">
			    		<div class="controls">
			    			<label id="mptask_resultment"></label>
			    		</div>
			    	</div>
							
					<div class="control-group" id="mptask_resultment_section">
			    		<div class="controls">
							 <button id="mptask_addUser" class="btn btn-primary" aria-hidden="true" onclick="javascript:showUserMultiSelecterOnCompany(comid, onMpTaskSelectUser, mptask_selected_user_new);">사용자 추가</button>
			    		</div>
			    	</div>
					<label class="control-label">대상자 목록</label>
					<table class="table table-borderd" id="mptask_targetUser" style="margin-top:10px"></table>
					<div id="mptask_pageNum" class="pagination pagination-centered"></div>
			    	<div class="control-group">
			    		<div class="controls">
							<input type="text" id="mptask_reservationdate" placeholder="예약실행" class="dateTimePicker">
						  	<button id="mptaskExecute" class="btn btn-success" aria-hidden="true" onclick="javascript:executeCompanyPointTask()">입력 실행하기</button>
			    		</div>
			    	</div>
		    	</div>
			</div>
		</div>
		</div>
	</body>
	
	<!-- 사용자 찾기 모달 뷰 -->
	<%@ include file="../selectmodal/userSelecter.jsp" %>
	
	<%@ include file="../footer.jsp" %>
</html>