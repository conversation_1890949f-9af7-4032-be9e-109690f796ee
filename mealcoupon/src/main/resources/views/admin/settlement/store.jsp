<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>식권대장 관리자 페이지</title>
    <link rel="stylesheet" type="text/css" href="${ctx}/css/normalize.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/bootstrap.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/jquery-ui.css">
    <link rel="stylesheet" type="text/css" href="${ctx}/css/style.css">
    <script type="text/javascript" src="${ctx}/js/jquery-1.9.1.js"></script>
    <script type="text/javascript" src="${ctx}/js/jquery-ui.js"></script>
    <script type="text/javascript" src="${ctx}/js/bootstrap.js"></script>
    <script type="text/javascript" src="${ctx}/js/jquery-ui-sliderAccess.js"></script>
    <script type="text/javascript" src="${ctx}/js/jquery-ui-timepicker-addon.js"></script>
    <script type="text/javascript" src="/mealcoupon/js/javascript.js?_tz=<%=System.currentTimeMillis()%>"></script>
    <script type="text/javascript" src="/admin/js/util.js?_tz=<%=System.currentTimeMillis()%>"></script>
    <script type="text/javascript">
        $(document).ready(function(){
            /* nav 선택 */
            $('#submenu_calculate').css("display", "");
            $('#nav_settlement_store').css("color", "white");
            $("#excelExportButton").on('click',function (){
                makeStoreExcel();
            });

        });
        function makeStoreExcel () {
            loadAJAX("/mealcoupon/admin/excel/store", "", handleAddStoreSuccess, handleHttpError);
        }
        function handleAddStoreSuccess(request, status, response){
            /* 회사 추가 성공시 */
            if(response.status==1){
                alert("요청한 엑셀 문서는 관리 > 엑셀 출력 메뉴에서 다운로드 할 수 있습니다.");
            } else {
                alert("Error\n"+response.message+" : "+response.status);
            }
        }
        function handleHttpError(request, status, error){
            /* 통신 에러 */
            alert("code : " + request.status + "\r\nmessage : " + request.reponseText);
        }
    </script>
</head>
<body>
<%@ include file="../topmenu.jsp" %>
<div class="span18">
    <div class="row">
        <!-- sidebar nav 시작 -->
        <%@ include file="../menu.jsp" %>
        <!-- sidebar nav 끝 -->
        <div class="span15">
            <!-- alert 영역 시작 -->
            <div id="message">
                <h3>정산 > 제휴식당 목록</h3>
                <h5>제휴식당 리스트를 엑셀 문서로 다운로드 할 수 있습니다.</h5>
                <a id="excelExportButton" class="btn btn-primary downloadAuthCheck">엑셀 출력</a>
            </div>
            <!-- alert 영역 끝 -->
        </div>
    </div>
</div>

</body>
</html>