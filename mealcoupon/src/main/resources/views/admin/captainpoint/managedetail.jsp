<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<style>
    .manageInfoTd {
        width: 100px;
        background-color: #dddddd;
        font-weight: bold;
    }
    .processTd {
        text-align: center !important;
    }
</style>
<script type="text/javascript">
  $(document).ready(function(){
    $("#detailCloseBtn").on("click", function(){
      manageInit();
      manageDetailInit();
      manageInfoList(1);
      areaShowHide("#detailArea, #processArea", "#infoArea");
    });

    // 상태 선택시
    $("#targetStatus").on("change", function(){
      targetList(1);
    });

    // 지급/차감 진행 버튼 클릭 시
    $(document).on("click", "#processStart", function (){
      if (!menuFlag) {
        return;
      }
      // 소멸 예정일이 지난 경우
      if (!validCheck($("#detailExtension").html())
          && $("#detailExtension").html() !== "-"
            && moment($("#detailExtension").html()).valueOf() < moment().valueOf()) {
        alert("이미 설정된 소멸 예정일이 지났습니다. 대장포인트 지급은 자동으로 취소 처리 됩니다. 다시 처음부터 설정해 주세요.");
        processCancel("afterExtentionCancel");
        return;
      }
      // 유효성 체크 성공 상태가 0일때
      if ($("#confirmed_successArea").html() === "0") {
        alert("사용자 유효성 체크 결과, 지급/차감 가능한 사용자가 없습니다. 유효성 체크 실패 사유 확인 후, 다시 진행 부탁 드립니다.");
        processCancel("confirmCheckFail");
        return;
      }
      progressInit();
      $(".reservationModal").modal("show");
    });

    // 지급/차감 취소, 예약취소 버튼 클릭 시 &
    $(document).on("click", "#processCancel, #reservationCancel", function(){
        if (!menuFlag) {
            return;
        }
        processCancel($(this).attr("id"));
    });

    // 검색결과 엑셀 출력 클릭 시
    $(document).on("click", "#excelManageTarget", function(){
      if ($("#statusMsgs").html() === "사용자 유효성 확인 중") {
        alert("요청 처리가 진행 중으로 현재 엑셀을 진행할 수 없습니다.");
        return;
      }
      let parameter = "";
      parameter += "page=" + 1;
      parameter += "&pageRow=" + 1000;
      parameter += "&manageId=" + $("#manageId").attr("value");
      if ($("#targetStatus").val() !== "") {
        parameter += "&manageTargetStatus=" + $("#targetStatus").val();
      }
      loadAJAXRest("POST", "/mealcoupon/admin/captainpoint/manage/target/excel", parameter, excelExportSuccess, handleHttpError);
    });

    // 새로고침 버튼
    $(document).on("click", "#refreshBtn", function(){
      updateDetailStatus(500);
    });
  });

  // 관리자취소
  function processCancel(cancelId){
    console.log("cancelId>>",cancelId);
    let bodyJson = {};
    if (cancelId === "processCancel" || cancelId === "afterExtentionCancel") {
      bodyJson.processType = "cancel";
    } else if (cancelId === "reservationCancel") {
      bodyJson.processType = "reserveCancel";
    } else if (cancelId === "confirmCheckFail") {
      bodyJson.processType = "confirmCheckFail";
    }
    bodyJson.manageId = $("#manageId").attr("value");

    if(cancelId === "processCancel" && confirm("취소시, 복구 할 수 없습니다. 진행하시겠습니까?")
    || cancelId === "reservationCancel" || cancelId === "confirmCheckFail" || cancelId === "afterExtentionCancel") {
        $.ajax({
            url: "/mealcoupon/admin/captainpoint/manage/process",
            type: "PUT",
            contentType: 'application/json',
            data: JSON.stringify(bodyJson),
            dataType: "json",
            success: function (request, status) {
                if (status === "success") {
                    updateDetailStatus();
                    return;
                }
            },
            error: function () {
                alert("등록중 오류가 발생했습니다.");
                return;
            }
        });
    }
    updateDetailStatus();
    return;
  }

  // 상세정보 초기화
  function manageDetailInit() {
    $("#targetStatus").val("");
    let tdInit = "#detailType, #detailBehaviorType, #detailDescription, #detailExtensionm, #detailStatus";
    tdInit += "#detilRefund, #detailAllCount, #detailCreateUserName, #detailCreateDate, #detailUpdateUserName, #detailUpdateDate";
    tdInit += "#manageTypeProcessing, #manageTypeProcessing, #manageTypeSuccess, #manageTypeFail";
    $(tdInit).html("");
  }

  // 상세정보 불러오기
  function manageDetail(key) {
    let parameter = "manageId=" + key;
    loadAJAXRest("GET", "/mealcoupon/admin/captainpoint/manage/detail", parameter, manageDetailSuccess, handleHttpError);
  }

  // 상세정보 셋팅
  function manageDetailSuccess(request, status, response) {
    console.log(response);
    let contents = response.content;
    let manageInfo = contents["manageInfo"];

    // 상세
    $("#detailType").html(replaceNull(manageInfo["type"]));
    $("#detailBehaviorType").html(replaceNull(manageInfo["behaviorTypeDescription"]));
    $("#detailDescription").html(replaceNull(manageInfo["description"]));
    $("#detailExtension").html(replaceTime(manageInfo["extinctionDate"], true));
    $("#detailStatus").html(statusButtonTag(replaceNull(manageInfo["type"]), replaceNull(manageInfo["status"]), replaceTime(manageInfo["processDate"], true)));
    $("#detilRefund").html(isReturnMsg(replaceNull(manageInfo["type"]), replaceNull(manageInfo["isRefund"])));
    $("#detailAllCount").html(manageInfo["totalUsers"]);
    $("#detailCreateUserName").html(replaceNull(manageInfo["createUserName"]));
    $("#detailCreateDate").html(replaceTime(manageInfo["createDate"], true));

    if (["사용자 유효성 확인 중", "가능"].includes(replaceNull(manageInfo["status"]))
    || replaceNull(manageInfo["status"]) === "예약" && moment(replaceTime(manageInfo["processDate"], true)).valueOf() > moment().valueOf()) {
      $("#detailUpdateUserName, #detailUpdateDate").html("-");
    } else {
      $("#detailUpdateUserName").html(replaceNull(manageInfo["updateUserName"]));
      $("#detailUpdateDate").html(replaceTime(manageInfo["processDate"], true));
    }

    // 새로고침 버튼
    let refreshButtonTags = '';
    if (["사용자 유효성 확인 중", "처리중"].includes(replaceNull(manageInfo["status"]))) {
      refreshButtonTags = '<button class="btn" style="float:right;" id="refreshBtn"><span class="icon-refresh"></span>새로고침</button>';
    }
    $("#refreshBtnArea").html(refreshButtonTags);

    // 사용자별 진행 현황 [지급/차감] 구분
    $("#manageTypeProcessing, #manageTypeProcessing, #manageTypeSuccess, #manageTypeFail").html(manageInfo["type"]);

    // 사용자별 진행 현황
    $("#totalCountArea").html(manageInfo["totalUsers"]);
    let processCount = contents["manageProcessCounts"];
    let plusCount = 0;
    for (let key in processCount) {
      plusCount += processCount[key];
      $("#" + key + "Area").html(processCount[key]);
    }
    let waitingVal = manageInfo["totalUsers"] - plusCount;
    $("#waitingArea").html(waitingVal);
  }

  // 진행 처리 후 업데아트
  function updateDetailStatus(timers = 1000) {
    setTimeout(function(){
      manageDetail($("#manageId").attr("value"));
      targetList(1);
      areaShowHide("#processArea, #infoArea", "#detailArea");
      $(".reservationModal").modal("hide");
    }, timers);
  }

  // 상태 버튼 태그
  function statusButtonTag(type, status, reservateDate) {
    let tags = "";
    if (status === "사용자 유효성 확인 중") {
      tags += "<div style='margin-bottom:5px;color:blue;' id='statusMsgs'>" + status + "</div>";
    } else if (status === "관리자취소") {
      tags += "<div style='margin-bottom:5px;color:red;' id='statusMsgs'>" + status + "</div>";
    } else {
      tags += "<div style='margin-bottom:5px;color:blue;' id='statusMsgs'>" + type + " " + status + "</div>";
      if (status === "예약") {
        tags += "<div style='margin-bottom:3px;' id='statusMsgs'>(예약 일시 : " + reservateDate + ")</div>";
      }
    }
    if (status === "가능") {
      tags += "<div style='margin-bottom:5px;'><button class='btn writeAuthCheck' id='processStart'>" + type + " 진행</button></div>";
      tags += "<div><button class='btn writeAuthCheck' id='processCancel'>" + type + " 취소</button></div>";
    } else if (status === "예약") {
      tags += "<div style='margin-bottom:5px;'><button class='btn writeAuthCheck' id='reservationCancel'>예약 취소</button></div>";
    }
    return tags;
  }

  // 타겟 사용자 리스트
  function targetList(input) {
    let parameter = "";
    parameter += "page=" + input;
    parameter += "&pageRow=" + 15;
    parameter += "&manageId=" + $("#manageId").attr("value");
    if ($("#targetStatus").val() !== "") {
      parameter += "&manageTargetStatus=" + $("#targetStatus").val();
    }
    loadAJAXRest("GET", "/mealcoupon/admin/captainpoint/manage/target/list", parameter, targetListSuccess, handleHttpError);
  }

  function targetListSuccess(request, status, response) {
    let contents = response.content;

    // 타겟 사용자
    let targetInfo = contents["targetUserInfo"];
    $("#userTargetTbody").html(userTargetTbodyMake(targetInfo));

    // 타겟 사용자 페이징
    let targetPageInfo = contents["targetPageInfo"];
    pageController('manageDetailPageNum', targetPageInfo.page, targetPageInfo.pageRow, targetPageInfo.total, 'targetList');
  }

  // 타겟 사용자 셋팅
  function userTargetTbodyMake(targetInfo) {
    let tags = "";
    for (let data in targetInfo) {
      let detail = targetInfo[data];
      tags += "<tr key='" + detail['captainPointUserTargetId'] + "'>";
      tags += "<td>" + replaceNull(detail['userId']) + "</td>";
      tags += "<td>" + replaceNull(detail['processingPoint']) + "</td>";
      tags += "<td>" + replaceNull(detail['targetStatus']) + "</td>";
      tags += "<td>" + replaceNull(detail['userName']) + "</td>";
      tags += "<td>" + replaceNull(detail['description']) + "</td>";
      tags += "<td>" + replaceTime(detail['updateDate'], true) + "</td>";
      tags += "</tr>";
    }
    return tags;
  }
</script>
<div>
    <h4>대장포인트 내역 및 관리 > 대장포인트 지급/차감 > 상세</h4>
    <div class="row">
        <div class="span12" style="width: 1000px;">
            <!-- 지급/차감 정보 -->
            <input type="hidden" id="manageId" value="">
            <div class="form-inline subinline">
                <table class="table table-borderd captainPointTable" id="manageDetailTable">
                    <tbody id="manageDetailTbody">
                        <tr>
                            <td class="manageInfoTd">지급/차감</td>
                            <td style="width:150px;" id="detailType"></td>
                            <td class="manageInfoTd">타입</td>
                            <td style="width:150px;" id="detailBehaviorType"></td>
                            <td class="manageInfoTd" style="width:150px;">상태</td>
                        </tr>
                        <tr>
                            <td class="manageInfoTd">사유</td>
                            <td colspan="3" id="detailDescription"></td>
                            <td rowspan="5" style="text-align:center;padding-top: 8px;">
                                <div style="margin-bottom:80px;" id="refreshBtnArea"></div>
                                <div id="detailStatus"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="manageInfoTd">소멸 예정일</td>
                            <td id="detailExtension"></td>
                            <td class="manageInfoTd">환불</td>
                            <td id="detilRefund"></td>
                        </tr>
                        <tr>
                            <td class="manageInfoTd">사용자 수</td>
                            <td colspan="3" id="detailAllCount"></td>
                        </tr>
                        <tr>
                            <td class="manageInfoTd">이벤트 생성 관리자 명</td>
                            <td id="detailCreateUserName"></td>
                            <td class="manageInfoTd">이벤트 생성 일시</td>
                            <td id="detailCreateDate"></td>
                        </tr>
                        <tr>
                            <td class="manageInfoTd">지급/차감/취소<br>실행 관리자명</td>
                            <td id="detailUpdateUserName"></td>
                            <td class="manageInfoTd">지급/차감/취소<br>실행 일시</td>
                            <td id="detailUpdateDate"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!-- /지급/차감 정보 -->

            <!-- 사용자별 진행 현황 -->
            <div class="form-inline subinline">
                <h4> 사용자별 진행 현황 </h4>
                <table class="table table-borderd captainPointTable" id="manageProcessTable">
                    <tbody id="manageProcessTbody">
                    <tr>
                        <td class="manageInfoTd" style="background-color:lightgoldenrodyellow">총 사용자 수</td>
                        <td class="manageInfoTd">유효성 체크 대기중</td>
                        <td class="manageInfoTd">유효성 체크 성공</td>
                        <td class="manageInfoTd">유효성 체크 실패</td>
                        <td class="manageInfoTd"><span id="manageTypeProcessing"></span>진행중</td>
                        <td class="manageInfoTd"><span id="manageTypeSuccess"></span>성공</td>
                        <td class="manageInfoTd"><span id="manageTypeFail"></span>실패</td>
                    </tr>
                    <tr>
                        <td class="processTd" id="totalCountArea"></td>
                        <td class="processTd" id="waitingArea"></td>
                        <td class="processTd" id="confirmed_successArea"></td>
                        <td class="processTd" id="confirmed_failArea"></td>
                        <td class="processTd" id="processingArea"></td>
                        <td class="processTd" id="successArea"></td>
                        <td class="processTd" id="failArea"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <!-- /사용자별 진행 현황 -->

            <!-- 옵션 선택 영역 -->
            <div class="form-inline subinline2">
                <span class="subSpan">
                    <label>상태 : </label>
                    <select class="input-small" id="targetStatus" style="width: 120px;">
                        <option value="">전체</option>
                        <option value="waiting">유효성 체크 대기중</option>
                        <option value="checking">유효성 검사중</option>
                        <option value="confirmed_success">유효성 체크 성공</option>
                        <option value="confirmed_fail">유효성 체크 실패</option>
                        <option value="processing">지급/차감 진행중</option>
                        <option value="success">지급/차감 성공</option>
                        <option value="fail">지급/차감 실패</option>
                    </select>
                </span>
                <button type="button" class="btn btn-inverse downloadAuthCheck" id="excelManageTarget" style="float:right;">검색결과 엑셀 출력</button>
            </div>
            <!-- /옵션 선택 영역 -->

            <!-- 테이블 영역 -->
            <div>
                <table class="table table-borderd captainPointTable" id="manageTable">
                    <tr style="background-color:#f5f5f5;" class="captainPointTableTh">
                        <th style="width: 300px;">UID</th>
                        <th style="width: 60px;">금액</th>
                        <th style="width: 100px;">상태</th>
                        <th style="width: 60px;">사용자</th>
                        <th style="width: 250px;">실패명 사유</th>
                        <th style="width: 140px;">처리 일시</th>
                    </tr>
                    <tbody id="userTargetTbody"></tbody>
                </table>
                <div id="manageDetailPageNum" class="pagination pagination-centered"></div>
            </div>
            <!-- /테이블 영역 -->
            <button class="btn" id="detailCloseBtn" style="width:90px;float:left;">리스트</button>
        </div>
    </div>
    <%@ include file="progress.jsp" %>
</div>
