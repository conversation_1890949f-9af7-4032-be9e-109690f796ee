
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>
<script>
  $(document).ready(function(){
    $("#modalClose").on('click', function(){
      $('#updateHistoryModal').modal('hide');
    });
  });
</script>
<div id="updateHistoryModal" class="modal hide fade" style="width: 800px;margin-left: -280;margin-right: 0px;left: 800px;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" aria-hidden="true" id="modalClose">&times;</button>
                <h4 style="margin-bottom: 5px;margin-top: 5px;">제휴식당 정보 - 감사로그</h4>
            </div>
            <div class="modal-body" style="padding-bottom: 5px;padding-top: 5px;">
                <div class="form-horizontal">
                    <h6 style="margin-top: 5px;margin-bottom:5px;">수정 사항에 대한 상세 내용은 제품 본부에 확인 요청해 주시길 바랍니다.</h6>
                    <div class="control-group">
                        <table class="table table-borderd table-hover" id="updateHistoryList" style="margin-top:10px">
                            <thead>
                                <tr>
                                    <th>수정일</th>
                                    <th>수정 관리자</th>
                                </tr>
                            </thead>
                            <tbody id="updateHistoryData"></tbody>
                        </table>
                        <div id="updateHistoryPageNum" class="pagination pagination-centered"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
