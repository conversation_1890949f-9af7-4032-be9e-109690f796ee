<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>

<div id="feeChangeLogModal" class="modal hide fade modal-wide">
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
        <h3>수수료 중도 변경 감사로그</h3>
    </div>
    <div class="modal-body">
        <form name="frmFeeChangeLog" id="frmFeeChangeLog">
        <div class="form-horizontal">
            <table class="table table-bordered" id="storeFeeChangeLogTable">
                <thead>
                    <tr>
                        <th style="background-color:#ecf0f2; text-align:center;width:150px;">
                            변경적용 완료일자
                        </th>
                        <th style="background-color:#ecf0f2; text-align:center;width:150px;">
                            식대 요율(VAT 포함)
                        </th>
                        <th style="background-color:#ecf0f2; text-align:center;width:150px;">
                            대장포인트(VAT 포함)
                        </th>
                        <th style="background-color:#ecf0f2; text-align:center;width:150px;">
                            수정일시
                        </th>
                        <th style="background-color:#ecf0f2; text-align:center;width:150px;">
                            수정자
                        </th>
                    </tr>
                </thead>
                <tbody id="tbodyStoreFeeChangeLogTable" class="text-center text-black"></tbody>
            </table>
        </div>
        </form>
    </div>
    <div class="modal-footer">
        <button class="btn" data-dismiss="modal" aria-hidden="true" id="feeChangeBtnLogClose">닫기</button>
    </div>
</div>