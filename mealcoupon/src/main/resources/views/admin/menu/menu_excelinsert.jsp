
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>식권대장 관리자 페이지</title>
		<link rel="stylesheet" type="text/css" href="${ctx}/css/bootstrap.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/style.css?_tz=<%=System.currentTimeMillis()%>">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/jquery-ui.css">
		<script type="text/javascript" src="${ctx}/js/jquery-1.9.1.js"></script>
		<script type="text/javascript" src="${ctx}/js/jquery-ui.js"></script>
		<script type="text/javascript" src="${ctx}/js/bootstrap.js"></script>
		<script type="text/javascript" src="${ctx}/js/javascript.js"></script>
		<script type="text/javascript" src="/admin/js/menu/excelinsert.js?_tz=<%=System.currentTimeMillis()%>"></script>
		<script type="text/javascript">
			$(document).ready(function () {
				$("#excelDownloadButton").on("click" ,function (){
					location.href="/excelform/menu_excel_insert_form_V2.xlsx";
				});
				$("#excelPreviewButton").on("click" ,function (){
					makePreview();
				});
				$(".excelUploadButton").on("click" ,function (){
					let status = parseInt($(this).attr("id").split("_")[1]);
					uploadMenus(status);
				});
			});
		</script>
	</head>
	<body>
		<%@ include file="../topmenu.jsp" %>
		<div class="span18">
		<div class="row">
			<!-- sidebar nav 시작 -->
			<%@ include file="../menu.jsp" %>
			<!-- sidebar nav 끝 -->
			<div class="span15 contents-section">
				<!-- alert 영역 시작 -->
				<div id="message">
				</div>
				<!-- alert 영역 끝 -->
				<div class="form-inline">
					<a id="excelDownloadButton" class="btn  btn-info">엑셀입력폼 다운로드</a>
				</div>
				<div class="form-inline">
					<span style="margin-right:10px;">
						<label>제휴점 : </label>
						<input type="text" readonly="readonly" class="input-big" placeholder="제휴점명" id="storename">
						<input type="hidden" id="storeid">
						<a href="javascript:removeStore()"><i class="icon-remove"></i></a>
						<button type="button" class="btn" onclick="javascript:showStoreSelecter('storename','storeid')">찾기</button>
					</span>
					<span>
						<label>엑셀파일 : </label>
						<input type="file" id="menu-exfile" onchange="javascript:onExcelFileChange();void 0;"/>
						<button type="button" class="btn btn-inverse writeAuthCheck" id="excelPreviewButton">입력 미리보기</button>
					</span>
				</div>
				<div class="form-inline" id="summit-saction" style="display:none;float: right;margin-right: 30px;margin-bottom: 10px;">
					<label><input type='checkbox' id="isDefaultOpen" checked class="btn btn-info writeAuthCheck margin-right-5 margin-top-auto"/>연결 고객사 메뉴 자동 등록</label>
					<button id="byStatus_1" type="button" class="btn btn-info excelUploadButton writeAuthCheck">판매중 상태로 등록</button>
					<button id="byStatus_2" type="button" class="btn btn-warning excelUploadButton writeAuthCheck">일시품절 상태로 등록</button>
					<button id="byStatus_0" type="button" class="btn btn-danger excelUploadButton writeAuthCheck">판매종료 상태로 등록</button>
				</div>
				<div class="form-inline" id="summuryArea" style="display:none;">
					<label id="summury">총정보 : </label>
				</div>
				<table class="table table-borderd" id="menuInsertPreview" style="margin-top:10px"></table>
			</div>
		</div>
		</div>

        <%-- menu type --%>
        <select id="mealtype" style="display:none">
            <c:forEach items="${mealtype}" var="item" >
                <option value="${item.type}">${item.name}</option>
            </c:forEach>
        </select>

		<!-- 제휴점- 제휴점 검색 모달 뷰 -->
		<%@ include file="../selectmodal/storeSelecter.jsp" %>
		
	</body>
</html>
