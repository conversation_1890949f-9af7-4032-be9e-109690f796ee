<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<style>
    .mt10{
      margin-top:10px
    }
    .mt30{
      margin-top: 30px
    }
</style>
<div class="span16 contents-section">
    <%-- ### 복지그룹 Not Use --%>
    <section id="notUseWelfare" style="display: none">
        <div class="row">
            <h3 style="color: red">복지대장 서비스를 이용하지 않는 고객사입니다.</h3>
        </div>
    </section>

    <%-- ### 복지그룹 Use --%>
    <section id="useWelfare">
        <div class="form-inline" style="margin-bottom:20px;">
            <div class="span4">
                <button id="welfare_addform_bt" type="button" class="btn btn-info writeAuthCheck">복지그룹 추가</button>
            </div>
            <button id="welfare_close_form_bt" type="button" class="btn">닫기</button>
        </div>
        <div class="row">
            <div class="span4">
                <h3>복지그룹 목록</h3>
                <table class="table table-borderd table-hover mt10" id="welfareTable"></table>
                <div id="welfareGroupPagenum" class="pagination pagination-centered"></div>
            </div>
            <div class="span12">
                <h3 id="selectedGroupTitle"></h3>
                <%-- 복지그룹 추가 TAB--%>
                <div id="welfare-form" style="display:none">
                    <ul id="welfareGroup-section-tab" class="nav nav-tabs">
                        <li id="welfare_form_info" class="active mealgroup-form-tab" linksection="welfare_form_info">
                            <a href="#" >그룹정보</a>
                        </li>
                        <li id="welfare_form_policy" class="mealgroup-form-tab" linksection="welfare_form_policy">
                            <a href="#" >복지정책</a>
                        </li>
                    </ul>
                    <div class="form-horizontal welfareGroup-section-tab" id="welfare-form-info-section">
                        <div class="control-group mealgroup-info mealgroup-info-edit" style="margin-top: 20px;">
                            <label class="control-label" for="welfareGroup_id">groupid</label>
                            <div class="controls">
                                <input type="text" id="welfareGroup_id" placeholder="그룹번호" disabled="disabled">
                            </div>
                        </div>
                        <div class="control-group mealgroup-info">
                            <label class="control-label" for="welfare_comname">회사명</label>
                            <div class="controls">
                                <input type="text" readonly="readonly" class="input-small" placeholder="회사명" id="welfare_comname" >
                                <input type="hidden" id="welfare_comid" value="">
                                <span class="companyUnselectedSection">
                                    <a id="mealgroup_com_x" href="javascript:removeSelectedCompany()"><i class="icon-remove"></i></a>
                                    <button id="mealgroup_com_search" type="button" class="btn" >찾기</button>
                                </span>
                            </div>
                        </div>
                        <div class="control-group mealgroup-info">
                            <label class="control-label" for="welfare_name">
                                복지그룹명 <span style="color: red">*</span>
                            </label>
                            <div class="controls">
                                <input type="text" id="welfare_name" placeholder="최대 45자" value="" maxlength="46">
                            </div>
                        </div>
                        <div class="control-group mealgroup-info">
                            <label class="control-label" for="welfare_memo">설명</label>
                            <div class="controls">
                                <textarea rows="5" class="span5" placeholder="복지그룹 및 정책설명" id="welfare_memo"></textarea>
                            </div>
                        </div>
                        <div class="control-group mealgroup-info mealgroup-info-edit">
                            <label class="control-label" for="mealgroup_isactive">상태</label>
                            <div class="controls">
                                <ul class="nav nav-pills" style="margin-bottom:0px;" id="mealgroup_isactive">
                                    <li class="toActive"><a href="#" onclick="javascript:$(this).parent().addClass('active').next().removeClass('disable');">활성</a></li>
                                    <li class="toDisable"><a href="#" onclick="javascript:$(this).parent().addClass('disable').prev().removeClass('active');">비활성</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="control-group">
                            <div class="controls">
                                <button id="welfare_addbutton" type="button" class="btn btn-success mealgroup-info mealgroup-info-add writeAuthCheck" >그룹추가</button>
                                <button id="welfare_editbutton" type="button" class="btn btn-success mealgroup-info mealgroup-info-edit writeAuthCheck" >정보수정</button>
                            </div>
                        </div>
                        <hr>
                    </div>
                    <div class="form-horizontal welfareGroup-section-tab" id="welfare-form-policy-section">
                        <%@include file="companyWelfarePolicyTab.jsp"%>
                    </div>
                    <div class="row welfareGroup-section-tab" id="welfare-form-member-section" style="display:none">
                        <div>
                            <div class="form-inline" align="center" style="margin-left:20px">
                                <a class="btn btn-small btn-warning writeAuthCheck" href="#" id="welfareGroup-member-remove-bt" >
                                    <span class="mealgroup-name">그룹</span>에서제외&nbsp;<i class="icon-arrow-right"></i>
                                </a>
                                &nbsp;&nbsp;&nbsp;
                                <a class="btn btn-small btn-success writeAuthCheck" href="#" id="welfareGroup-member-add-bt" >
                                    <i class="icon-arrow-left"></i>&nbsp;<span class="mealgroup-name">상위</span>그룹으로
                                </a>
                            </div>
                        </div>
                        <div class="span6">
                            <h4>
                                그룹에 소속된 사용자
                            </h4>
                            <div class="hero-unit" style="padding:10px;margin-bottom:15px;">
                                <%-- 현재 소속된 그룹에서 선택된 사용자 --%>
                                <div id="welfareGroup-beto-out-user" ></div>
                            </div>
                            <div style="margin-bottom:15px;">
                                <a id="welfareClearSelectedGroupMember" class="btn btn-small btn-danger" href="#" ><i class="icon-remove"></i>&nbsp;선택목록 초기화</a>
                            </div>
                            <hr>
                            <div class="form-inline" style="margin-top:30px">
                                <label>검색 : </label>
                                <input type="text" class="input-big" placeholder="이름, 전화번호, 이메일, 사번" id="welfareGroup-member-search" >
                                &nbsp&nbsp
                                <button id="searchWelfareGroupMember" type="button" class="btn btn-inverse" >검색</button>
                            </div>
                            <div class="form-inline">
                                <a id="selectSearchMealGroupMemberAll" class="btn btn-small" >전체선택</a>
                                <select class="input-small" id="searchMealGroupMemberPageingNumber">
                                    <option value="15">15</option>
                                    <option value="30">30</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                            <hr style="margin:20px 0 0 0">
                            <table class="table table-borderd table-hover table-fixed-header" id="welfareGroupMember">
                                <thead class="header" style="">
                                    <th>복지그룹</th>
                                    <th>부서</th>
                                    <th>성명</th>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <hr style="margin:0 0 0 0">
                            <div id="welfareGroupMemberPagenum" class="pagination pagination-centered"></div>
                        </div>
                        <div class="span6">
                            <h4>
                                사용자 검색
                            </h4>
                            <div class="hero-unit" style="padding:10px;margin-top:10px;margin-bottom:15px;">
                                <%-- 검색할 사용자에서 선택된 사용자 --%>
                                <div id="welfareGroup-beto-in-user"></div>
                            </div>
                            <div>
                                <a id="welfareClearSelectedAnotherUser" class="btn btn-small btn-danger" ><i class="icon-remove"></i>&nbsp;선택목록 초기화</a>
                            </div>
                            <hr style="margin:20px 0 0 0">
                            <div class="form-inline" style="margin-top: 30px">
                                <label>검색 : </label>
                                <input type="text" class="input-big" placeholder="이름, 전화번호, 이메일, 사번" id="welfareGroup-another-search" />
                                &nbsp&nbsp
                                <select id="search-welfareGroup-list" class="input-small" ></select>
                                &nbsp&nbsp
                                <button id="searchWelfareGroupAnother" type="button" class="btn btn-inverse" >검색</button>
                            </div>
                            <div class="form-inline">
                                <a id="selectSearchMealGroupAnotherMemberAll" class="btn btn-small" href="#" >전체선택</a>
                                <select class="input-small" id="searchMealGroupMemberAnotherPageingNumber">
                                    <option value="15">15</option>
                                    <option value="30">30</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                            <hr style="margin:20px 0 0 0">
                            <table class="table table-borderd table-hover" id="welfareGroupMemberAnother">
                                <thead class="header" style="">
                                <tr>
                                    <th>복지그룹</th>
                                    <th>부서</th>
                                    <th>성명</th>
                                </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                            <hr style="margin:0 0 0 0">
                            <div id="welfareGroupAnotherPagenum" class="pagination pagination-centered"></div>
                        </div>
                        <hr>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 그룹타입 -->
    <select id="policytype" style="display:none">
        <c:forEach items="${mealpolicytype}" var="item" >
            <option value="${item.type}">${item.name}</option>
        </c:forEach>
    </select>

    <!-- 유저레벨 -->
    <select id="userlevel" style="display:none">
        <c:forEach items="${userlevel}" var="item" >
            <option value="${item.level}">${item.name}</option>
        </c:forEach>
    </select>

    <div>
        <div class="btn-group mealgroup-member-selected-user" id="welfare-module-member-selected" style="display:none;">
            <button class="btn btn-warning btn-member-user" onclick="javascript:companyWelfareGroupSelectedWelfareGroupMemberRowInfoEvent(this.parentNode.id, 'member');void 0;"></button>
            <button class="btn btn-member-clear" onclick="javascript:companyWelfareGroupSelectedWelfareGroupAnotherMemberRowInfoEvent(this.parentNode.id, 'member');"><i class="icon-remove"></i></button>
        </div>
    </div>
    <%-- 그룹 미소속 사용자 선택목록 UI --%>
    <div>
        <div class="btn-group mealgroup-member-selected-user" id="welfare-module-another-selected" style="display:none;">
            <button class="btn btn-success btn-another-user" onclick="companyWelfareGroupSelectedWelfareGroupMemberRowDropEvent(this.parentNode.id, 'another');"></button>
            <button class="btn btn-another-clear" onclick="companyWelfareGroupSelectedWelfareGroupAnotherMemberRowDropEvent(this.parentNode.id, 'another');"><i class="icon-remove"></i></button>
        </div>
    </div>

    <!-- 정책추가 모달뷰 -->
    <div id="menuDetail" class="modal hide fade modal-wide">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            <h3 id="menusubtitle">정책 추가하기</h3>
        </div>
        <div class="modal-body">
            <div class="form-horizontal">

            </div>
        </div>
        <div class="modal-footer">
            <button id="menuConfirm" class="btn btn-primary writeAuthCheck" aria-hidden="true" >추가하기</button>
            <button class="btn" data-dismiss="modal" aria-hidden="true">닫기</button>
        </div>
    </div>
</div>