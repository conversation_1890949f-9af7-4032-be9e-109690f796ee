<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>

<button id="welfare-policy_add_bt" type="button" class="btn btn-info writeAuthCheck" >복지정책 추가</button>
<table class="table table-borderd table-hover" id="welfarePolicyTable" style="margin:20px 0 0 0">
    <tr class="welfareGroup-policy-add info">
        <td colspan="6">복지정책 추가</td>
    </tr>
    <tr class="welfareGroup-policy-add" id="welfare-policy-add-section">
        <td style="background-color: #d9edf7;"></td><th style="border-left:1px solid #dddddd;">복지정책 형태</th>
        <td colspan="4">
            <select id="welfare-policy-add-type" class="span2" disabled>
                <option value="LONGTERM" selected>장기</option>
            </select>
        </td>
    </tr>
    <tr class="mealgroup-policy-row">
        <th>정책명</th>
        <th>지급방식</th>
        <th>입력시기</th>
        <th>유효기간</th>
        <th>상태</th>
        <th>수정</th>
    </tr>
    <tr class="welfareGroup-policy mealgroup-policy-common" id="welfareGroup-policy-id-section">
        <td></td>
        <th>정책 ID</th>
        <td id="mealgroup-policy-common-idx">id</td>
        <td colspan="3">
            <ul class="nav nav-pills" style="margin-bottom:0px;" id="welfareGroup-policy-common-isactive">
                <li class="toActive writeAuthCheck"><a href="#" >활성</a></li>
                <li class="toDisable writeAuthCheck"><a href="#" >비활성</a></li>
            </ul>
        </td>
    </tr>
    <tr class="welfareGroup-policy mealgroup-policy-common">
        <td></td><th>정책명</th>
        <td colspan="4">
            <input type="text" class="input-small" placeholder="아점" id="welfare-policy-name" style="width:385px;"/>
        </td>
    </tr>
<%-- 2021.11.15 ( 자동지급옵션 추후개발 )
    <tr class="welfareGroup-policy mealgroup-policy-common">
        <td></td>
        <th>사용가능요일</th>
        <td colspan="4">
            <span style="display: inline-table;">
                <ul class="nav nav-pills" style="margin-bottom:0px;" id="welfare-policy-7day">
                    <li id="welfare-policy-7day-mon" class="active"><a class="section-tap active" href="#">월</a></li>
                    <li id="welfare-policy-7day-tue" class="active"><a class="section-tap active" href="#">화</a></li>
                    <li id="welfare-policy-7day-wed" class="active"><a class="section-tap active" href="#">수</a></li>
                    <li id="welfare-policy-7day-thu" class="active"><a class="section-tap active" href="#">목</a></li>
                    <li id="welfare-policy-7day-fri" class="active"><a class="section-tap active" href="#">금</a></li>
                    <li id="welfare-policy-7day-sat" class="active"><a class="section-tap active" href="#">토</a></li>
                    <li id="welfare-policy-7day-sun" class="active"><a class="section-tap active" href="#">일</a></li>
                </ul>
            </span>
        </td>
    </tr>
--%>
    <tr class="welfareGroup-policy mealgroup-policy-common">
        <td style="align-content: center"></td>
        <th>사용시간</th>
        <td colspan="4">
            <div id="policy-time-box-1" style="float: left">
                <select class="span1 policy-day-time" id="welfare-policy-starttime-hour"  style="padding-left: 0;" disabled>
                    <option value="00" selected>00</option>
                </select>
                :
                <select class="span1 policy-day-time" id="welfare-policy-starttime-min"  style="padding-left: 0;"disabled>
                    <option value="00" selected>00</option>
                </select>
                ~
                <span class="nextDayFlag" id="welfare-policy-day-nextday" style="display: none">익일</span>
                <select class="span1 policy-day-time" id="welfare-policy-expiretime-hour"  style="padding-left: 0;" disabled>
                    <option value="00" selected>00</option>
                </select>
                :
                <select class="span1 policy-day-time" id="welfare-policy-expiretime-min"  style="padding-left: 0;" disabled>
                    <option value="00" selected>00</option>
                </select>
            </div>
        </td>
    </tr>
    <tr class="welfareGroup-policy mealgroup-policy-common">
        <td></td><th>소멸 예정 표시</th>
        <td colspan="4">
            <input type="text" style="text-align: right;" class="span1" id="welfare-policy-input" onKeyUp="javascript:only_num(this, false, false, false)"  />
            <select class="span2" id="welfareGroup-policy-expireNotice-unit">
                <option value="off">
                    표시 안 함
                </option>
                <option value="D">
                    일 전 표시
                </option>
                <option value="M" disabled>
                    개월 전 표시
                </option>
                <option value="H" disabled>
                    시간 전 표시
                </option>
            </select>
        </td>
    </tr>
    <tr class="welfareGroup-policy mealgroup-policy-common">
        <td></td><th>자동지급옵션</th>
        <td colspan="4">
            <ul class="nav nav-pills" style="margin-bottom:0px;" id="mealgroup-policy-long-autoinput">
             <li class="active"><a href="#" id="welfare-policy-long-option-bt">켜짐</a></li>
<%--                <li style="line-height: 30px">--%>
<%--                    <b id="autoOptionMsg" style="color: #ff0000">--%>
<%--                        &nbsp;실제 지급은 대장페이먼츠 > 복지포인트 지급/차감 > 지급에서 가능합니다.--%>
<%--                    </b>--%>
<%--                </li>--%>
            </ul>
        </td>
    </tr>
    <%-- 자동지급옵션 ON --%>
    <tr class="welfareGroup-policy mealgroup-policy-long-option">
        <td></td><th>지급금액</th>
        <td colspan="4">
            <input type="text" class="input-small" placeholder="8,000" id="welfare-policy-amount" value="${limit.meallimit1}" onKeyUp="javascript:only_num(this, true, false, false)">&nbsp원
        </td>
    </tr>
    <tr class="welfareGroup-policy mealgroup-policy-long-option">
        <td></td><th>지급일</th>
        <td colspan="4">
            <select class="span1" id="welfare-policy-offset-period-month" style="padding-left: 0;">
                <option value="M" selected>매월</option>
            </select>
            <select class="span1" id="welfare-policy-offset-period-day" style="padding-left: 0;">
                <option value="">선택</option>
                <c:forEach begin="1" end="28" step="1" var="i" >
                    <option value="${i}">${i}</option>
                </c:forEach>
            </select>
        </td>
    </tr>
    <tr class="welfareGroup-policy mealgroup-policy-long-option">
        <td></td><th>유효기간</th>
        <td colspan="4">
            <select class="span2" id="welfare-policy-range">
                <option value="1M">1달</option>
                <option value="2M">2달</option>
                <option value="3M">3달</option>
                <option value="4M">4달</option>
                <option value="6M">6달</option>
                <option value="12M">12달</option>
            </select>
        </td>
    </tr>
    <%-- END 자동지급옵션 ON --%>
    <tr class="welfareGroup-policy mealgroup-policy-common">
        <td style="align-content: center; background-color: rgb(217, 237, 247);"></td>
        <th></th>
        <td colspan="4">
            <button id="welfarePolicyAdd" class="btn btn-success policy-confirm policy-confirm-add writeAuthCheck" aria-hidden="true">추가하기</button>
            <button id="welfarePolicyEdit" class="btn btn-success policy-confirm policy-confirm-edit writeAuthCheck" aria-hidden="true">수정하기</button>
            <button id="welfarePolicyCancel" class="btn policy-confirm policy-confirm-edit policy-confirm-add" data-dismiss="modal" aria-hidden="true">취소</button>
        </td>
    </tr>
</table>
<hr style="margin:0 0 0 0">
