
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>식권대장 관리자 페이지</title>
		<link rel="stylesheet" type="text/css" href="${ctx}/css/normalize.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/bootstrap.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/jquery-ui.css">
        <link rel="stylesheet" type="text/css" href="${ctx}/css/style.css">
		<script type="text/javascript" src="${ctx}/js/jquery-1.9.1.js"></script>
		<script type="text/javascript" src="${ctx}/js/jquery-ui.js"></script>
		<script type="text/javascript" src="${ctx}/js/bootstrap.js"></script>
		<script type="text/javascript" src="${ctx}/js/javascript.js"></script>
		<script type="text/javascript" src="/admin/js/mealgroup/mealpoint.js?_tz=<%=System.currentTimeMillis()%>"></script>
        <style type="text/css">

        </style>
	</head>
	<body>
		<%@ include file="../topmenu.jsp" %>
		<div class="span15">
			<div class="row">
			<!-- sidebar nav 시작 -->
			<%@ include file="../menu.jsp" %>
			<!-- sidebar nav 끝 -->
			<div class="span12 contents-section">
				<!-- alert 영역 시작 -->
				<div id="message">
				</div>
				<!-- alert 영역 끝 -->
                <div class="form-inline">
                    <ul class="nav nav-tabs">
                        <li id="task_form_info" class="active">
                            <a href="#" onclick="javascript:showTaskInfoTab();void 0;">단체변동내역</a>
                        </li>
                        <li id="personal_form_info">
                            <a href="#" onclick="javascript:showUserPointLogTab();void 0;">개인내역</a>
                        </li>
                    </ul>
                </div>
                <div id="task-saction">
                    <ul class="nav nav-pills" style="margin-left:-12px;margin-bottom:2px" id="searchDetailButton">
                        <li id="a--" ><a class="section-tap" href="#" onclick="javascript:toggleSearchDetail(true);">검색도구&nbsp;<i class="icon-chevron-right"></i></a></li>
                        <li id="b--" class="active" style="display:none;"><a class="section-tap" href="#" onclick="javascript:toggleSearchDetail(false);">검색도구&nbsp;<i class="icon-chevron-down"></i></a></li>
                    </ul>
                    <div id="search-detail-option" style="display:none;">
                        <ul class="nav nav-pills detail-filter detail-filter-task" style="margin-left:-12px;margin-bottom:2px">
                            <li><a class="section-tap" href="#">입력방식 : </a></li>
                            <li flag="chargetype" flagtype="INSERT"><a class="section-tap" href="#">입력</a></li>
                            <li flag="chargetype" flagtype="RESET"><a class="section-tap" href="#">초기화</a></li>
                        </ul>
                        <ul class="nav nav-pills detail-filter detail-filter-task" style="margin-left:-12px;margin-bottom:2px">
                            <li><a class="section-tap" href="#">사유&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : </a></li>
                            <li flag="causetype" flagtype="GROUP_CHANGE"><a class="section-tap" href="#">그룹이동</a></li>
                            <li flag="causetype" flagtype="USER_WITHDRAW"><a class="section-tap" href="#">퇴사</a></li>
                            <li flag="causetype" flagtype="POLICY_ACTIONS"><a class="section-tap" href="#">자동수행</a></li>
                            <li flag="causetype" flagtype="POLICY_RESET"><a class="section-tap" href="#">정책 초기화</a></li>
                            <li flag="causetype" flagtype="REFUND"><a class="section-tap" href="#">환불</a></li>
                            <li flag="causetype" flagtype="ADMIN_MODIFY"><a class="section-tap" href="#">식대 담당자 지금/차감</a></li>
                            <li flag="causetype" flagtype="SUPER_MODIFY"><a class="section-tap" href="#">임의 결제 차감</a></li>
                            <li flag="causetype" flagtype="ETC"><a class="section-tap" href="#">사용자정의</a></li>
                        </ul>
                        <ul class="nav nav-pills detail-filter detail-filter-task" style="margin-left:-12px;margin-bottom:2px;">
                            <li><a class="section-tap" href="#">상태&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : </a></li>
                            <li flag="status" flagtype="INPUT"><a class="section-tap" href="#">입력중</a></li>
                            <li flag="status" flagtype="READY"><a class="section-tap" href="#">실행대기</a></li>
                            <li flag="status" flagtype="RESERVE"><a class="section-tap" href="#">예약중</a></li>
                            <li flag="status" flagtype="EXECUTE"><a class="section-tap" href="#">진행중</a></li>
                            <li flag="status" flagtype="CANCEL"><a class="section-tap" href="#">취소</a></li>
                            <li flag="status" flagtype="END"><a class="section-tap" href="#">진행완료</a></li>
                        </ul>
                        <ul class="nav nav-pills detail-filter detail-filter-task" style="margin-left:-12px;margin-bottom:2px;">
                            <li><a class="section-tap" href="#">임의필터 : </a></li>
                            <li flag="filter" flagtype="infail"><a class="section-tap" href="#">실패가 있는 내역</a></li>
                        </ul>
                    </div>
                    <div class="form-inline" style="margin-bottom:20px;">
                        <label>회사 : </label>
                        <input type="text" readonly="readonly" class="input-small" placeholder="회사명" id="comname">
                        <input type="hidden" id="comid">
                        <a href="javascript:removeSelectedForm('task_com')"><i class="icon-remove"></i></a>
                        <button type="button" class="btn" onclick="javascript:showCompanySelecter('comname','comid')">찾기</button>
                        &nbsp;&nbsp;
                        <label>수행자 : </label>
                        <input type="text" class="input-big" placeholder="담당자명" id="search-username" onkeydown="javascript:if(event.keyCode == 13){searchMealGroupAnother(1);}">
                        &nbsp&nbsp
                        <button type="button" class="btn btn-inverse" onclick="javascript:searchMealpointTask(1)">검색</button>
                    </div>
                    <table class="table table-borderd table-hover" id="mealpointTaskTable" style="margin-top:10px"></table>
                    <div id="mealpointTaskPagenum" class="pagination pagination-centered"></div>
                </div>
                <div id="personal-saction" style="display:none;">
                    <ul class="nav nav-pills" style="margin-left:-12px;margin-bottom:2px" id="searchPointLogDetailButton">
                        <li id="c--" ><a class="section-tap" href="#" onclick="javascript:toggleSearchPointLogDetail(true);">검색도구&nbsp;<i class="icon-chevron-right"></i></a></li>
                        <li id="d--" class="active" style="display:none;"><a class="section-tap" href="#" onclick="javascript:toggleSearchPointLogDetail(false);">검색도구&nbsp;<i class="icon-chevron-down"></i></a></li>
                    </ul>
                    <div id="search-pointlog-option" style="display:none;">
                        <ul class="nav nav-pills detail-filter detail-filter-pointlog" style="margin-left:-12px;margin-bottom:2px">
                            <li><a class="section-tap" href="#">사유&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; : </a></li>
                            <li flag="causetype" flagtype="GROUP_CHANGE"><a class="section-tap" href="#">그룹이동</a></li>
                            <li flag="causetype" flagtype="USER_WITHDRAW"><a class="section-tap" href="#">퇴사</a></li>
                            <li flag="causetype" flagtype="POLICY_ACTIONS"><a class="section-tap" href="#">자동수행</a></li>
                            <li flag="causetype" flagtype="USE_MEALCOUPON"><a class="section-tap" href="#">식권사용</a></li>
                            <li flag="causetype" flagtype="ADMIN_ENTRY"><a class="section-tap" href="#">임의 결제 기록</a></li>
                            <li flag="causetype" flagtype="EXPIRE_DATE"><a class="section-tap" href="#">만료</a></li>
                            <li flag="causetype" flagtype="REFUND"><a class="section-tap" href="#">환불</a></li>
                            <li flag="causetype" flagtype="ADMIN_MODIFY"><a class="section-tap" href="#">식대 담당자 지금/차감</a></li>
                            <li flag="causetype" flagtype="SUPER_MODIFY"><a class="section-tap" href="#">임의 결제 차감</a></li>
                            <li flag="causetype" flagtype="ETC"><a class="section-tap" href="#">사용자정의</a></li>
                        </ul>
                        <ul class="nav nav-pills detail-filter detail-filter-pointlog" style="margin-left:-12px;margin-bottom:2px;">
                            <li><a class="section-tap" href="#">임의필터 : </a></li>
                            <li flag="filter" flagtype="insert"><a class="section-tap" href="#">모든추가</a></li>
                            <li flag="filter" flagtype="deduction"><a class="section-tap" href="#">모든차감</a></li>
                            <li flag="filter" flagtype="usable"><a class="section-tap" href="#">사용가능포인트</a></li>
                        </ul>
                    </div>
                    <div class="form-inline" style="margin-bottom:20px;">
                        <label>회사 : </label>
                        <input type="text" readonly="readonly" class="input-small" placeholder="회사명" id="log_comname">
                        <input type="hidden" id="log_comid">
                        <%--<a href="javascript:removeSelectedForm('log_com')"><i class="icon-remove"></i></a>--%>
                        <%--<button type="button" class="btn" onclick="javascript:showCompanySelecterCallback('log_comname','log_comid', onSelectCompany)">찾기</button>--%>
                        &nbsp;&nbsp;
                        <label>사용자 : </label>
                        <input type="text" readonly="readonly" class="input-small" placeholder="이름" id="log_username">
                        <input type="hidden" id="log_userid">
                        <a href="javascript:removeSelectedForm('log_user')"><i class="icon-remove"></i></a>
                        <button type="button" class="btn" onclick="javascript:showUserSelecterWithCompany('log_username', 'log_userid','log_comname','log_comid', onSelectUser);">찾기</button>
                        <div style="display:none;">
                        &nbsp;&nbsp;
                        <label>검색 : </label>
                        <input type="text" class="input-big" placeholder="사용자명" id="search-log-username" onkeydown="javascript:if(event.keyCode == 13){searchMealpointLog(1);}">
                        </div>
                        &nbsp;&nbsp;
                        <button type="button" class="btn btn-inverse" onclick="javascript:searchMealpointLog(1);">검색</button>
                    </div>
                    <div class="form-inline" style="margin-bottom:20px;">
                        <%--<div id="mealgroup-saction" style="display:inline-block;visibility:hidden;">--%>
                            <%--<label>식대그룹 : </label>--%>
                            <%--<select id="mealgroup"></select>--%>
                        <%--</div>--%>
                        <%--&nbsp;&nbsp;--%>
                        <div id="mealgroupolicy-saction" style="display:inline-block;visibility:hidden;">
                            <label>식대정책 : </label>
                            <select id="mealgroupolicy"></select>
                        </div>
                    </div>
                    <table class="table table-borderd table-hover" id="mealpointLogTable" style="margin-top:10px"></table>
                    <div id="mealpointLogPagenum" class="pagination pagination-centered"></div>
                </div>
			</div>
			</div>
		</div>

        <!-- 변경타입 -->
        <select id="chargetype" style="display:none">
            <c:forEach items="${chargetype}" var="item" >
                <option value="${item.type}">${item.name}</option>
            </c:forEach>
        </select>

        <!-- 변경사유 -->
        <select id="causetype" style="display:none">
            <c:forEach items="${causetype}" var="item" >
                <option value="${item.type}">${item.description}</option>
            </c:forEach>
        </select>

        <!-- 상태 -->
        <select id="task-status" style="display:none">
            <c:forEach items="${status}" var="item" >
                <option value="${item.status}">${item.name}</option>
            </c:forEach>
        </select>

        <%-- 포인트로그 변경사유 --%>
        <select id="pointlog-causetype" style="display:none">
            <c:forEach items="${logCauseType}" var="item" >
                <option value="${item.type}">${item.description}</option>
            </c:forEach>
        </select>

		<!-- 회사 찾기 모달 뷰 -->
		<%@ include file="../selectmodal/companySelecter.jsp" %>

        <!-- 사용자 찾기 모달 뷰 -->
        <%@ include file="../selectmodal/userSelecter.jsp" %>

        <%-- 식권 자세히 보기 모달 뷰 --%>
        <%@ include file="../coupon/couponDetail.jsp"%>
	</body>
</html>