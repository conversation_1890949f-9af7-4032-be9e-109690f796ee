<%@ page import="com.vlocally.mealcoupon.vo.CouponDemandUser" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<div class="sectionRoot">

    <table class="table table-borderd table-hover" id="demandList">
        <thead>
            <tr>
                <th><input type="checkbox" id="checkAll"></th>
                <th>회사명</th>
                <th>정책명</th>
                <th>상태</th>
                <th>신청자</th>
                <th>부서</th>
                <th>지급액/장</th>
                <th>사유</th>
                <th>신청시간</th>
                <th>수락시간</th>
            </tr>
        </thead>
        <tbody></tbody>
    </table>
    <div id="demandPageNum" class="pagination pagination-centered"></div>

    <select id="demandUserStatus" style="display:none;">
    <%
        CouponDemandUser.Status[] statuses = CouponDemandUser.Status.values();
        for(int i=0;i< statuses.length;i++){
    %>
        <option value="<%=statuses[i].name()%>">
            <%=statuses[i].getDescription()%>
        </option>
    <%
        }
    %>
    </select>
</div>