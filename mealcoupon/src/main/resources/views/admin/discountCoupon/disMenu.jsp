
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>식권대장 관리자 페이지</title>
		<link rel="stylesheet" type="text/css" href="${ctx}/css/bootstrap.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/style.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/jquery-ui.css">
		<script type="text/javascript" src="${ctx}/js/jquery-1.9.1.js"></script>
		<script type="text/javascript" src="${ctx}/js/jquery-ui.js"></script>
		<script type="text/javascript" src="${ctx}/js/bootstrap.js"></script>
		<script type="text/javascript" src="${ctx}/js/javascript.js"></script>
		<script type="text/javascript">
			var menuData;
			var page = 1;
			var pageRow = 20;
		
			$(document).ready(function(){
				/* 달력 피커 */
				$('.datePicker').datepicker({
					dateFormat: 'yy-mm-dd',
				    monthNamesShort: ['1월','2월','3월','4월','5월','6월','7월','8월','9월','10월','11월','12월'],
				    dayNamesMin: ['일','월','화','수','목','금','토'],
				    weekHeader: 'Wk',
				    changeMonth: true, //월변경가능
				    changeYear: true, //년변경가능
				    yearRange:'1900:+0', // 연도 셀렉트 박스 범위(현재와 같으면 1988~현재년)
			    	showMonthAfterYear: true, //년 뒤에 월 표시
			    	buttonImageOnly: true, //이미지표시  
			    	buttonText: '날짜를 선택하세요'
				});
				
				/* nav 선택 */
				/* $('#nav_discouponuse').attr("class", "active"); */
				$('#menu_coupon').attr("src", "${ctx }/img/coupon_pressed.png");
				$('#submenu_coupon').css("display", "");
				$('#nav_discountmenu').css("color", "white");
				
				/* modal 뷰 이벤트 설정 */
				$('#discountMenuDetail').on('hide', function () {
					searchDiscountMenu(page);
				});
				
				/* selector 이벤트 설정*/
				$( "#menustatus" ).change(function(){
					searchDiscountMenu(1);
				});
				
				/* 페이지 호출하기 */
				searchDiscountMenu(1);
			});
			
			function searchDiscountMenu(input){
				page = input;
				/* 회사명으로 검색 */
				//var storeid = $('#storeid').val();
				var storename = $('#storename').val();
				var name = $('#menuname').val();
				var menustatus = $('#menustatus').val();
				
				var parameter = 
							+ "storename=" + storename
							+ "&name=" + name
							+ "&menustatus=" + menustatus
							+ "&page=" + page
							+ "&pageRow=" + pageRow;
				loadAJAX("${ctx}/admin/discoupon/menu/inquiry.json", parameter, handleSearchDiscountMenuSuccess, null);
			}
			
			function handleSearchDiscountMenuSuccess(request, status, response){
				/* 회사명 검색 성공시 */
				/* 결과 테이블로 보여주기 시작 */
				menuData = response.content;
				if (response.status == 1){
					
					pageController('menuPageNum', response.page, response.pageRow, response.total, 'searchMenu');
					
					var table = $('#companyCalcTable');
					var html = "<tr><th>쿠폰명</th><th>제휴점명</th><th>잔여수량</th><th>수수료</th><th>등록일</th><th>판매여부</th></tr>";
					for (var i = 0; i < menuData.length; i++){
						html += "<tr>";
						html += '<td><a href="javascript:showDiscountMenuDetail(\''+menuData[i].did+'\')">'+menuData[i].menuname+'</a></td>';
						html += "<td>"+menuData[i].storename+"</td>";
						html += "<td>"+getCurrency(menuData[i].countlimit)+"</td>";
						html += "<td>"+getCurrency(menuData[i].due)+"</td>";
						html += "<td>"+transDateTime(menuData[i].regdate)+"</td>";
						if(menuData[i].status==1){
							html += "<td>공개</td>";
						} else {
							html += "<td>숨김</td>";
						}
						html += "</tr>";
						
					}
					table.html(html);
					
					/* 결과 테이블로 보여주기 끝 */
				} else {
					alert("오류가 발생하였습니다.");
				}
			}
			
			function onSelectedStore(){
				searchDiscountMenu(1);
			}
			
			function removeStore(){
				$('#storename').val('');
				$('#storeid').val('');
				searchMenu(1);
			}
			
		</script>
	</head>
	<body>
		<%@ include file="../topmenu.jsp" %>
		<div class="span15">
		<div class="row">
			<!-- sidebar nav 시작 -->
			<%@ include file="../menu.jsp" %>
			<!-- sidebar nav 끝 -->
			<div class="span12 contents-section">
				<!-- alert 영역 시작 -->
				<div id="message">
				</div>
				<!-- alert 영역 끝 -->
				<div class="form-inline">
					<button type="button" class="btn btn-success" onclick="javascript:showDiscountMenuDetail('add')">할인쿠폰추가</button>
				</div>
				<div class="form-inline">
					<label>제휴점 : </label>
					<input type="text" class="input-small" placeholder="제휴점명" id="storename">
					<input type="hidden" id="storeid">
					<!-- 
					<a href="javascript:removeStore()"><i class="icon-remove"></i></a>
					<button type="button" class="btn" onclick="javascript:showStoreSelecterCallback('storename','storeid',onSelectedStore)">찾기</button>
					 -->
					&nbsp&nbsp
					<label>쿠폰명 : </label>
					<input type="text" class="input-small" placeholder="쿠폰명" id="menuname">
					&nbsp&nbsp
					<label>판매여부 : </label>
					<select id="menustatus" class="searchInput">
			    		<option value="">전체</option>
			    		<option value="0">숨김</option>
			    		<option value="1">판매</option>
			    	</select>
					&nbsp&nbsp
					<button type="button" class="btn btn-inverse" onclick="javascript:searchDiscountMenu(1)">검색</button>
					
				</div>
				<table class="table table-borderd" id="companyCalcTable" style="margin-top:10px">
					
				</table>
				<div id="menuPageNum" class="pagination pagination-centered">
			  
				</div>
			</div>
		</div>
		</div>
		<!-- 메뉴추가 및 수정 모달 뷰 -->
		<%@ include file="disMenuDetailModal.jsp" %>
		
		<!-- 회사 찾기 모달 뷰 -->
		<%@ include file="../selectmodal/companySelecter.jsp" %>
		
		<%--  
		<!-- 제휴점 검색 모달 뷰 -->
		<%@ include file="../selectmodal/storeSelecter.jsp" %>
		 --%>
	</body>
</html>