
<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>

<!-- 회사 선택 모달 뷰 -->
<script>
	var comnamevar;
	var comidvar;
	var subComData;
	var callbackfnc;
	
	function showCompanySelecter(name, id){
		callbackfnc = null;
		comnamevar = name;
		comidvar = id;
		$('#findCompany').modal('show');
		searchSubCompany(1);
	}
	
	function showCompanySelecterCallback(name, id, callback){
		showCompanySelecter(name, id);
		callbackfnc = callback;
	}
	
	function searchSubCompany(input){
		/* 회사명으로 검색 */
		var name = $('#subcomname').val();
		var parameter = "name=" + name 
					+ "&page=" + input
					+ "&pageRow=" + 10;
		loadAJAX("${ctx}/store/info/company.json", parameter, handleSearchSubCompanySuccess, null);
	}
	
	function handleSearchSubCompanySuccess(request, status, response){
		/* 회사명 검색 성공시 */
		/* 결과 테이블로 보여주기 시작 */
		subComData = response.content;
		if (response.status == 1){
			
			pageController('subCompanyPageNum', response.page, response.pageRow, response.total, 'searchCompany');
			
			var table = $('#subCompanyTable');
			var html = "<tr><th>회사명</th><th>담당자 이름</th><th>상태</th></tr>";
			for (var i = 0; i < subComData.length; i++){
				html += "<tr><td><a href='javascript:chooseSubCompany("+i+");'>" + subComData[i].name + "</td><td>" 
								+ subComData[i].chargename + "</td>";
				if(subComData[i].status==1){
					html += "<td>활성</td></tr>";
				} else {
					html += "<td>비활성</td></tr>";
				}
			}
			table.html(html);
			
			/* 결과 테이블로 보여주기 끝 */
		} else {
			alert("오류가 발생하였습니다.");
		}
	}
	
	function chooseSubCompany(index){
		$('#'+comnamevar).val(subComData[index].name);
		$('#'+comidvar).val(subComData[index].comid);
		$('#findCompany').modal('hide');
		if(callbackfnc!=null){
			callbackfnc();
		}
	}
	
</script>
<div id="findCompany" class="modal hide fade">
  <div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
    <h3>회사 선택하기</h3>
  </div>
  <div class="modal-body">
    <div class="well">
		<div class="form-inline">
			<label>회사명 검색 : </label>
			<input type="text" class="input-small" placeholder="회사명" id="subcomname" onkeydown="javascript:if(event.keyCode == 13){searchSubCompany(1);}">
			<a href="javascript:inputCleaner('subcomname')"><i class="icon-remove"></i></a>
			<button type="button" class="btn btn-inverse" onclick="javascript:searchSubCompany(1)">검색</button>
		</div>
	</div>
	<div class="well">
		<table class="table table-borderd" id="subCompanyTable">
			
		</table>
		<div id="subCompanyPageNum" class="pagination pagination-centered">
	  
		</div>
	</div>
  </div>
  <div class="modal-footer">
    <button class="btn" data-dismiss="modal" aria-hidden="true">닫기</button>
  		<!-- <button class="btn btn-primary">선택</button> -->
  </div>
</div>
	