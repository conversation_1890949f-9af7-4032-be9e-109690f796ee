<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<c:set var="ctx" value="${pageContext.request.contextPath}/mealcoupon"/>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>식권대장 제휴점 회원</title>
		<link rel="stylesheet" type="text/css" href="${ctx}/css/bootstrap.css">
		<link rel="stylesheet" type="text/css" href="${ctx}/css/jquery-ui.css">
		<script type="text/javascript" src="${ctx}/js/jquery-1.9.1.js"></script>
		<script type="text/javascript" src="${ctx}/js/jquery-ui.js"></script>
		<script type="text/javascript" src="${ctx}/js/bootstrap.js"></script>
		<script type="text/javascript" src="${ctx}/js/javascript.js"></script>
		<script type="text/javascript">
			$(document).ready(function(){
				$("#img1").css({
					'width':'250px',
					'height':'250px'
				});
			});
			function login(){
				/* 로그인 하기 */
				var account = false;
				if($("input[name=loginway]:checked" ).val()=='account'){
					account = true;
				}
				var parameter = "loginid=" + $('#loginid').val()
								+ "&passwd=" + $('#passwd').val()
								+ "&admin=" + account;
				
				loadAJAX("${ctx}/store/login.json", parameter, handleLoginSuccess, null, null);
			}
			
			function enterKeyPressed(){
				if (event.keyCode == 13){
					login();
				}
			}
			
			function handleLoginSuccess(request, status, response){
				/* 로그인 성공시 */
				if (response.status == 1){
					location.href = "${ctx}/store/coupon/group.vm";
				} else {
					alert("아이디 혹은 비밀번호가 일치하지 않습니다");
				}
			}
		</script>
	</head>
	<body  style="background-color: #35403c;">
		<div class="container">
			<div class="row" style="margin-top: 200px;">
				<div class="span8 offset2">
					<div style="">
						<p style="padding:0px 0px 0px 10px;color:#ffffff;font-size:17pt">제휴점회원</p>
						<form class="form-horizontal" style="color:#ffffff;background-image: url('${ctx}/img/login.png'); width: 604px; height: 379px;">
							<div class="form-inline" style="padding-bottom:10px;margin-left:200px;padding-top:170px">
								<label class="radio">
						    		<input type="radio" name="loginway" id="loginway" value="store" checked>매니저
						    	</label>
						    	&nbsp
						    	<label class="radio">
						    		<input type="radio" name="loginway" id="loginway" value="account">관리자
						    	</label>
							</div>
							<div class="control-group">
								<label class="control-label" for="loginid">아이디</label>
							    <div class="controls">
							    	<input type="text" id="loginid" placeholder="아이디" class="span3">
							    </div>
							</div>
							<div class="control-group">
								<label class="control-label" for="passwd">비밀번호</label>
							    <div class="controls">
							    	<input type="password" id="passwd" placeholder="비밀번호" class="span3" onkeydown="javascript:enterKeyPressed();">
							    </div>
							</div>
							<div class="control-group">
								<div class="controls">
									<button type="button" class="btn btn-success" onclick="javascript:login();">로그인</button>
								</div>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</body>
</html>