;public server
;server_id      Mall 서버를 구분을 위한 값
server_id = 01

;timeout        API 요청 timeout 시간 (단위:초)
timeout = 60

;log_level      0: FATAL; 1: ERROR; 2: WARNING; 3: INFO; 4: DEBUG
log_level = 4

;verify_cert    1: 인증서 검증; 0: 인증서 검증하지 않음
verify_cert = 0

;verify_host    1: 인증서 내의 domain name 확인; 0: 확인하지 않음 (test 시만 사용)
verify_host = 0

;report_error   1: error 시 dacom 서버에 report 전송; 0: 전송하지 않음 (error 관련 내용만 전송함)
report_error = 1

;output_UTF8    1: 서버 응답을 UTF-8로 return; 0: EUC-KR로 리턴
output_UTF8 = 1

;auto_rollback  0: 자동취소 사용안함,  1: 자동취소 사용
auto_rollback = 1


;log_dir        log directory full path  (로그 생성위치에 log 폴더를 반드시 만들어야 로그가 쌓임)
log_dir = /httpdocs/log/mealcoupon

;>>>>>>>>>>>>>>>>>>>> 반드시 입력해 주세요 <<<<<<<<<<<<<<<<<<<<

;상점 ID는 LG텔레콤으로 부터 발급받으신 상점아이디를 입력하세요. (발급받은 아이디 앞에 "t" 를 붙이시면 테스트아이디 입니다.)
;MertKey는 상점관리자 -> 계약정보 -> 상점정보관리 --> 시스템연동정보 에서 확인하실수 있습니다.  

;상점 ID = MertKey (서비스 및 테스트, 2개의 상점 아이디를 모두 입력해주세요. 입력시 주석표시(;)는 넣으시면 안됩니다.)

;입력 예) 
X_vendys = 141224c88aab3414388bd540b4cbb4b8
tX_vendys = 141224c88aab3414388bd540b4cbb4b8 
;lgdacomxpay = 95160cce09854ef44d2edb2bfb05f9f3

;>>>>>>>>>>>>>>>>>>>> 반드시 입력해 주세요 <<<<<<<<<<<<<<<<<<<<