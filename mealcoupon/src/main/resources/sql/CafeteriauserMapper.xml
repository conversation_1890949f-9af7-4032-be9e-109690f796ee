<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="CafeteriauserMapper">
  <resultMap id="BaseResultMap" type="com.vlocally.mealcoupon.vo.Cafeteriauser">
    <id column="idx" jdbcType="INTEGER" property="idx" />
    <result column="codeid" jdbcType="CHAR" property="codeid" />
    <result column="comid" jdbcType="CHAR" property="comid" />
    <result column="uid" jdbcType="CHAR" property="uid" />
    <result column="mid" jdbcType="CHAR" property="mid" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="regdate" jdbcType="TIMESTAMP" property="regdate" />
    <result column="deldate" jdbcType="TIMESTAMP" property="deldate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    idx, codeid, comid, uid, mid, status, regdate, deldate
  </sql>
  <select id="selectByExample" parameterType="com.vlocally.mealcoupon.vo.CafeteriauserExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from CafeteriaUser
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from CafeteriaUser
    where idx = #{idx,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from CafeteriaUser
    where idx = #{idx,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vlocally.mealcoupon.vo.CafeteriauserExample">
    delete from CafeteriaUser
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vlocally.mealcoupon.vo.Cafeteriauser">
    insert into CafeteriaUser (idx, codeid, comid, 
      uid, mid, status, regdate, 
      deldate)
    values (#{idx,jdbcType=INTEGER}, #{codeid,jdbcType=CHAR}, #{comid,jdbcType=CHAR}, 
      #{uid,jdbcType=CHAR}, #{mid,jdbcType=CHAR}, #{status,jdbcType=CHAR}, #{regdate,jdbcType=TIMESTAMP}, 
      #{deldate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.vlocally.mealcoupon.vo.Cafeteriauser">
    insert into CafeteriaUser
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="idx != null">
        idx,
      </if>
      <if test="codeid != null">
        codeid,
      </if>
      <if test="comid != null">
        comid,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="mid != null">
        mid,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="regdate != null">
        regdate,
      </if>
      <if test="deldate != null">
        deldate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="idx != null">
        #{idx,jdbcType=INTEGER},
      </if>
      <if test="codeid != null">
        #{codeid,jdbcType=CHAR},
      </if>
      <if test="comid != null">
        #{comid,jdbcType=CHAR},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=CHAR},
      </if>
      <if test="mid != null">
        #{mid,jdbcType=CHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=CHAR},
      </if>
      <if test="regdate != null">
        #{regdate,jdbcType=TIMESTAMP},
      </if>
      <if test="deldate != null">
        #{deldate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vlocally.mealcoupon.vo.CafeteriauserExample" resultType="java.lang.Integer">
    select count(*) from CafeteriaUser
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update CafeteriaUser
    <set>
      <if test="record.idx != null">
        idx = #{record.idx,jdbcType=INTEGER},
      </if>
      <if test="record.codeid != null">
        codeid = #{record.codeid,jdbcType=CHAR},
      </if>
      <if test="record.comid != null">
        comid = #{record.comid,jdbcType=CHAR},
      </if>
      <if test="record.uid != null">
        uid = #{record.uid,jdbcType=CHAR},
      </if>
      <if test="record.mid != null">
        mid = #{record.mid,jdbcType=CHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=CHAR},
      </if>
      <if test="record.regdate != null">
        regdate = #{record.regdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deldate != null">
        deldate = #{record.deldate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update CafeteriaUser
    set idx = #{record.idx,jdbcType=INTEGER},
      codeid = #{record.codeid,jdbcType=CHAR},
      comid = #{record.comid,jdbcType=CHAR},
      uid = #{record.uid,jdbcType=CHAR},
      mid = #{record.mid,jdbcType=CHAR},
      status = #{record.status,jdbcType=CHAR},
      regdate = #{record.regdate,jdbcType=TIMESTAMP},
      deldate = #{record.deldate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vlocally.mealcoupon.vo.Cafeteriauser">
    update CafeteriaUser
    <set>
      <if test="codeid != null">
        codeid = #{codeid,jdbcType=CHAR},
      </if>
      <if test="comid != null">
        comid = #{comid,jdbcType=CHAR},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=CHAR},
      </if>
      <if test="mid != null">
        mid = #{mid,jdbcType=CHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=CHAR},
      </if>
      <if test="regdate != null">
        regdate = #{regdate,jdbcType=TIMESTAMP},
      </if>
      <if test="deldate != null">
        deldate = #{deldate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where idx = #{idx,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vlocally.mealcoupon.vo.Cafeteriauser">
    update CafeteriaUser
    set codeid = #{codeid,jdbcType=CHAR},
      comid = #{comid,jdbcType=CHAR},
      uid = #{uid,jdbcType=CHAR},
      mid = #{mid,jdbcType=CHAR},
      status = #{status,jdbcType=CHAR},
      regdate = #{regdate,jdbcType=TIMESTAMP},
      deldate = #{deldate,jdbcType=TIMESTAMP}
    where idx = #{idx,jdbcType=INTEGER}
  </update>
  
  <!-- custom -->
  
  <insert id="insertSelectiveKey" parameterType="com.vlocally.mealcoupon.vo.Cafeteriauser">
    insert into CafeteriaUser
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="idx != null">
        idx,
      </if>
      <if test="codeid != null">
        codeid,
      </if>
      <if test="comid != null">
        comid,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="mid != null">
        mid,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="regdate != null">
        regdate,
      </if>
      <if test="deldate != null">
        deldate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="idx != null">
        #{idx,jdbcType=INTEGER},
      </if>
      <if test="codeid != null">
        #{codeid,jdbcType=CHAR},
      </if>
      <if test="comid != null">
        #{comid,jdbcType=CHAR},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=CHAR},
      </if>
      <if test="mid != null">
        #{mid,jdbcType=CHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=CHAR},
      </if>
      <if test="regdate != null">
        #{regdate,jdbcType=TIMESTAMP},
      </if>
      <if test="deldate != null">
        #{deldate,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <selectKey keyProperty="idx" order="AFTER" resultType="Integer">
	  SELECT LAST_INSERT_ID()
	</selectKey>
  </insert>
</mapper>