<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="UsercategoryMapper">
  <resultMap id="BaseResultMap" type="com.vlocally.mealcoupon.vo.Usercategory">
    <id column="cateid" jdbcType="INTEGER" property="cateid" />
    <result column="comid" jdbcType="CHAR" property="comid" />
    <result column="type" jdbcType="CHAR" property="type" />
    <result column="seq" jdbcType="TINYINT" property="seq" />
    <result column="name" jdbcType="CHAR" property="name" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    cateid, comid, type, seq, name
  </sql>
  <select id="selectByExample" parameterType="com.vlocally.mealcoupon.vo.UsercategoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from UserCategory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from UserCategory
    where cateid = #{cateid,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from UserCategory
    where cateid = #{cateid,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vlocally.mealcoupon.vo.UsercategoryExample">
    delete from UserCategory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vlocally.mealcoupon.vo.Usercategory">
    insert into UserCategory (cateid, comid, type, 
      seq, name)
    values (#{cateid,jdbcType=INTEGER}, #{comid,jdbcType=CHAR}, #{type,jdbcType=CHAR}, 
      #{seq,jdbcType=TINYINT}, #{name,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vlocally.mealcoupon.vo.Usercategory">
    insert into UserCategory
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cateid != null">
        cateid,
      </if>
      <if test="comid != null">
        comid,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="name != null">
        name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cateid != null">
        #{cateid,jdbcType=INTEGER},
      </if>
      <if test="comid != null">
        #{comid,jdbcType=CHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=CHAR},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vlocally.mealcoupon.vo.UsercategoryExample" resultType="java.lang.Integer">
    select count(*) from UserCategory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update UserCategory
    <set>
      <if test="record.cateid != null">
        cateid = #{record.cateid,jdbcType=INTEGER},
      </if>
      <if test="record.comid != null">
        comid = #{record.comid,jdbcType=CHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=CHAR},
      </if>
      <if test="record.seq != null">
        seq = #{record.seq,jdbcType=TINYINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=CHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update UserCategory
    set cateid = #{record.cateid,jdbcType=INTEGER},
      comid = #{record.comid,jdbcType=CHAR},
      type = #{record.type,jdbcType=CHAR},
      seq = #{record.seq,jdbcType=TINYINT},
      name = #{record.name,jdbcType=CHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vlocally.mealcoupon.vo.Usercategory">
    update UserCategory
    <set>
      <if test="comid != null">
        comid = #{comid,jdbcType=CHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=CHAR},
      </if>
      <if test="seq != null">
        seq = #{seq,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=CHAR},
      </if>
    </set>
    where cateid = #{cateid,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vlocally.mealcoupon.vo.Usercategory">
    update UserCategory
    set comid = #{comid,jdbcType=CHAR},
      type = #{type,jdbcType=CHAR},
      seq = #{seq,jdbcType=TINYINT},
      name = #{name,jdbcType=CHAR}
    where cateid = #{cateid,jdbcType=INTEGER}
  </update>
  
  <!-- ====== custom ========= -->
  
  <insert id="insertAndGet" parameterType="com.vlocally.mealcoupon.vo.Categorystore">
    insert into UserCategory (cateid, parentCateId, comid, type, seq, name)
    values (#{cateid,jdbcType=INTEGER}, #{parentCateId,jdbcType=INTEGER}, #{comid,jdbcType=CHAR}, #{type,jdbcType=CHAR}, #{seq,jdbcType=TINYINT}, #{name,jdbcType=CHAR})
    <selectKey keyProperty="cateid" resultType="integer">
   		SELECT LAST_INSERT_ID() 
    </selectKey>
  </insert>
  
  <update id="updateByPrimaryKeyForCom" parameterType="com.vlocally.mealcoupon.vo.Usercategory">
  	update UserCategory
    set type = #{type,jdbcType=CHAR},
      seq = #{seq,jdbcType=TINYINT},
      name = #{name,jdbcType=CHAR}
    where cateid = #{cateid,jdbcType=INTEGER}
    	and comid = #{comid,jdbcType=CHAR}
  </update>
  
  <delete id="deleteByPrimaryKeyForCom" parameterType="map">
  	delete from UserCategory
    where cateid = #{cateid,jdbcType=INTEGER}
    	and comid = #{comid,jdbcType=CHAR}
  </delete>
  
  <select id="selectForCompany" parameterType="hashmap" resultMap="BaseResultMap">
  	select <include refid="Base_Column_List" />
  	from UserCategory
  	<trim prefix="where " prefixOverrides="and">
      <if test="cateid != null">
        and cateid = #{cateid,jdbcType=INTEGER}
      </if>
      <if test="comid != null">
        and comid = #{comid,jdbcType=CHAR}
      </if>
      <if test="seq != null">
        and seq = #{seq,jdbcType=TINYINT}
      </if>
      <if test="name != null">
        and name = #{name,jdbcType=CHAR}
      </if>
      <if test="type != null">
        and type = #{type,jdbcType=CHAR}
      </if>
    </trim>
    order by type, seq ASC
  </select>
</mapper>