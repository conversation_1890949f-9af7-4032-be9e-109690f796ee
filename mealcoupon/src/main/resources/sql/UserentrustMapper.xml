<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="UserentrustMapper">
  <resultMap id="BaseResultMap" type="com.vlocally.mealcoupon.vo.Userentrust">
    <id column="trustuid" jdbcType="CHAR" property="trustuid" />
    <id column="receiveruid" jdbcType="CHAR" property="receiveruid" />
    <result column="regdate" jdbcType="TIMESTAMP" property="regdate" />
    <result column="trust" jdbcType="BIT" property="trust" />
    <result column="daylimit" jdbcType="INTEGER" property="daylimit" />
    <result column="oncelimit" jdbcType="INTEGER" property="oncelimit" />
    <result column="uselimit" jdbcType="INTEGER" property="uselimit" />
    <result column="uselimitdate" jdbcType="TIMESTAMP" property="uselimitdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    trustuid, receiveruid, regdate, trust, daylimit, oncelimit, uselimit, uselimitdate
  </sql>
  <select id="selectByExample" parameterType="com.vlocally.mealcoupon.vo.UserentrustExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from UserEntrust
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="com.vlocally.mealcoupon.vo.UserentrustKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from UserEntrust
    where trustuid = #{trustuid,jdbcType=CHAR}
      and receiveruid = #{receiveruid,jdbcType=CHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.vlocally.mealcoupon.vo.UserentrustKey">
    delete from UserEntrust
    where trustuid = #{trustuid,jdbcType=CHAR}
      and receiveruid = #{receiveruid,jdbcType=CHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.vlocally.mealcoupon.vo.UserentrustExample">
    delete from UserEntrust
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vlocally.mealcoupon.vo.Userentrust">
    insert into UserEntrust (trustuid, receiveruid, regdate, 
      trust, daylimit, oncelimit, 
      uselimit, uselimitdate)
    values (#{trustuid,jdbcType=CHAR}, #{receiveruid,jdbcType=CHAR}, #{regdate,jdbcType=TIMESTAMP}, 
      #{trust,jdbcType=BIT}, #{daylimit,jdbcType=INTEGER}, #{oncelimit,jdbcType=INTEGER}, 
      #{uselimit,jdbcType=INTEGER}, #{uselimitdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.vlocally.mealcoupon.vo.Userentrust">
    insert into UserEntrust
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="trustuid != null">
        trustuid,
      </if>
      <if test="receiveruid != null">
        receiveruid,
      </if>
      <if test="regdate != null">
        regdate,
      </if>
      <if test="trust != null">
        trust,
      </if>
      <if test="daylimit != null">
        daylimit,
      </if>
      <if test="oncelimit != null">
        oncelimit,
      </if>
      <if test="uselimit != null">
        uselimit,
      </if>
      <if test="uselimitdate != null">
        uselimitdate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="trustuid != null">
        #{trustuid,jdbcType=CHAR},
      </if>
      <if test="receiveruid != null">
        #{receiveruid,jdbcType=CHAR},
      </if>
      <if test="regdate != null">
        #{regdate,jdbcType=TIMESTAMP},
      </if>
      <if test="trust != null">
        #{trust,jdbcType=BIT},
      </if>
      <if test="daylimit != null">
        #{daylimit,jdbcType=INTEGER},
      </if>
      <if test="oncelimit != null">
        #{oncelimit,jdbcType=INTEGER},
      </if>
      <if test="uselimit != null">
        #{uselimit,jdbcType=INTEGER},
      </if>
      <if test="uselimitdate != null">
        #{uselimitdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vlocally.mealcoupon.vo.UserentrustExample" resultType="java.lang.Integer">
    select count(*) from UserEntrust
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update UserEntrust
    <set>
      <if test="record.trustuid != null">
        trustuid = #{record.trustuid,jdbcType=CHAR},
      </if>
      <if test="record.receiveruid != null">
        receiveruid = #{record.receiveruid,jdbcType=CHAR},
      </if>
      <if test="record.regdate != null">
        regdate = #{record.regdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.trust != null">
        trust = #{record.trust,jdbcType=BIT},
      </if>
      <if test="record.daylimit != null">
        daylimit = #{record.daylimit,jdbcType=INTEGER},
      </if>
      <if test="record.oncelimit != null">
        oncelimit = #{record.oncelimit,jdbcType=INTEGER},
      </if>
      <if test="record.uselimit != null">
        uselimit = #{record.uselimit,jdbcType=INTEGER},
      </if>
      <if test="record.uselimitdate != null">
        uselimitdate = #{record.uselimitdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update UserEntrust
    set trustuid = #{record.trustuid,jdbcType=CHAR},
      receiveruid = #{record.receiveruid,jdbcType=CHAR},
      regdate = #{record.regdate,jdbcType=TIMESTAMP},
      trust = #{record.trust,jdbcType=BIT},
      daylimit = #{record.daylimit,jdbcType=INTEGER},
      oncelimit = #{record.oncelimit,jdbcType=INTEGER},
      uselimit = #{record.uselimit,jdbcType=INTEGER},
      uselimitdate = #{record.uselimitdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vlocally.mealcoupon.vo.Userentrust">
    update UserEntrust
    <set>
      <if test="regdate != null">
        regdate = #{regdate,jdbcType=TIMESTAMP},
      </if>
      <if test="trust != null">
        trust = #{trust,jdbcType=BIT},
      </if>
      <if test="daylimit != null">
        daylimit = #{daylimit,jdbcType=INTEGER},
      </if>
      <if test="oncelimit != null">
        oncelimit = #{oncelimit,jdbcType=INTEGER},
      </if>
      <if test="uselimit != null">
        uselimit = #{uselimit,jdbcType=INTEGER},
      </if>
      <if test="uselimitdate != null">
        uselimitdate = #{uselimitdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where trustuid = #{trustuid,jdbcType=CHAR}
      and receiveruid = #{receiveruid,jdbcType=CHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vlocally.mealcoupon.vo.Userentrust">
    update UserEntrust
    set regdate = #{regdate,jdbcType=TIMESTAMP},
      trust = #{trust,jdbcType=BIT},
      daylimit = #{daylimit,jdbcType=INTEGER},
      oncelimit = #{oncelimit,jdbcType=INTEGER},
      uselimit = #{uselimit,jdbcType=INTEGER},
      uselimitdate = #{uselimitdate,jdbcType=TIMESTAMP}
    where trustuid = #{trustuid,jdbcType=CHAR}
      and receiveruid = #{receiveruid,jdbcType=CHAR}
  </update>
  
  <!-- ========== custom =========== -->
  
  <select id="countEntrustUser" parameterType="string" resultType="integer">
  	select count(*)
  	from UserEntrust
  	where trustuid = #{value}
  </select>
  
  <select id="selectEntrustUser" parameterType="string" resultType="hashmap">
  	select uid, name, division, rankposition, position, trust, daylimit, oncelimit, uselimit, uselimitdate
  	from UserEntrust left join User on UserEntrust.receiveruid = User.uid
  	where trustuid = #{value}
  	  AND User.isDormant = false
  	order by regdate DESC
  </select>
  
  <select id="countTrustMe" parameterType="string" resultType="integer">
  	select count(*)
  	from UserEntrust
  	where receiveruid = #{value}
  		and trust = true
  </select>
  
  <select id="selectTrustMe" parameterType="string" resultType="hashmap">
  	select uid, name, division, rankposition, position, daylimit, oncelimit, uselimit, uselimitdate
  	from UserEntrust left join User on UserEntrust.trustuid = User.uid
  	where receiveruid = #{value}
  		and trust = true
  		AND User.isDormant = false
  </select>
  
  
  <select id="selectIsTrust" parameterType="map" resultType="boolean">
  	select count(*)
  	from UserEntrust
  	where trustuid = #{peer}
  		and receiveruid = #{leader}
  		and trust = true
  </select>
</mapper>