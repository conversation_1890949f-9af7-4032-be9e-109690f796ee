<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ExcelCustomMapper">

    <select id="iljin_daily" parameterType="date" resultType="hashmap">
        select
        User.division as division,
        CouponMember.username as username,
        User.rankposition as rankposition,
        User.comidnum as comidnum,
        CouponMember.storename as storename,
        CouponMember.usedate,
        CouponMember.paytype,
        CouponMember.compoint,
        CouponMember.mypoint,
--         UserCompanyPointLog.amount as etc,
--         UserCompanyDayPointLog.amount as day

        from CouponMember left join User on CouponMember.uid = User.uid
--         left join UserCompanyPointLog on CouponMember.cid = UserCompanyPointLog.causelink and CouponMember.uid = UserCompanyPointLog.uid
--         left join UserCompanyDayPointLog on CouponMember.cid = UserCompanyDayPointLog.causelink and CouponMember.uid = UserCompanyDayPointLog.uid
        where CouponMember.comid = '74F37CCF-22B4-E7A3-49B5-8B4299C963EE'
        and DATE_FORMAT(usedate, '%Y-%m-%d') = #{value}
        order by usedate DESC, cid ASC
    </select>

    <select id="iljin_mealc" parameterType="string" resultType="hashmap">
        select *
        from CouponGroup
        where comid = '74F37CCF-22B4-E7A3-49B5-8B4299C963EE'
          and DATE_FORMAT(usedate, '%Y-%m-%d') >= #{start}
          and #{end} >= DATE_FORMAT(usedate, '%Y-%m-%d')
          and status >=0
        order by usedate ASC
    </select>

    <select id="iljin_member" parameterType="string" resultType="hashmap">
      select
      User.division as division,
        CouponMember.username as username,
        User.position as position,
        User.rankposition as rankposition,
        User.comidnum as comidnum,
        CouponMember.storename as storename,
        CouponMember.usedate,
        CouponMember.paytype,
        CouponMember.compoint,
        CouponMember.mypoint,
        CouponMember.uid as uid
      from CouponMember left join User on CouponMember.uid = User.uid
      where cid = #{value}
      order by username
    </select>
    <select id="iljin_menu" parameterType="string" resultType="hashmap">
      select *
      from CouponMenu
      where cid = #{value}
    </select>

    <select id="iljin_daypoint" parameterType="string" resultType="hashmap">
        select *
        from UserCompanyDayPointLog
        where uid = #{uid}
          and causelink = #{cid}
    </select>

    <select id="iljin_longpoint" parameterType="string" resultType="hashmap">
        select *
        from UserCompanyPointLog
        where uid = #{uid}
        and causelink = #{cid}
    </select>


</mapper>