<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="MealpointlogMapper">

  <!-- ======= custom ======== -->

  <sql id="searchWhereClause">
    <where>
      <trim prefix="(" prefixOverrides="and" suffix=")">
        <if test="comid != null">
          and MealPointLog.comId = #{comid}
        </if>
        <if test="uid != null">
          and MealPointLog.userId = #{uid}
        </if>
        <if test="username != null">
          and MealPointLog.userName like '%${username}%'
        </if>
        <if test="groupidx != null">
          and MealPointLog.groupIdx = #{groupidx}
        </if>
        <if test="policyidx != null">
          and MealPointLog.policyIdx = #{policyidx}
        </if>
        <if test="causetype and causetype.size()!=0">
          and MealPointLog.causeType in
          <trim prefix="(" prefixOverrides="," suffix=")">
            <foreach collection="causetype" item="criterion">
              , #{criterion}
            </foreach>
          </trim>
        </if>
        <if test="filter_insert">
          and MealPointLog.amount &gt;= 0
        </if>
        <if test="filter_deduction">
          and 0 &gt; MealPointLog.amount
        </if>
        <if test="filter_usable">
          and isActive = 1
        </if>
      </trim>
    </where>
  </sql>

  <select id="countByAdmin" parameterType="map" resultType="int">
    select count(*)
    from MealPointLog
    <include refid="searchWhereClause" />
  </select>

  <select id="selectByAdmin" parameterType="map" resultType="com.vlocally.mealcoupon.vo.Mealpointlog">
    select
      MealPointLog.pointLogIdx,
      MealPointLog.groupIdx,
      MealPointLog.policyIdx,
      MealPointLog.comid,
      MealPointLog.comName,
      MealPointLog.userId,
      MealPointLog.userName,
      MealPointLog.type,
      MealPointLog.amount,
      MealPointLog.balance,
      MealPointLog.isActive,
      MealPointLog.expireDate,
      MealPointLog.regDate,
      MealPointLog.policyName,
      MealPointLog.policyType,
      MealPointLog.cause,
      MealPointLog.causeType,
      MealPointLog.causeLink,
      MealPointLog.extra

    from MealPointLog
    <include refid="searchWhereClause" />
    order by MealPointLog.regDate DESC
  </select>

</mapper>