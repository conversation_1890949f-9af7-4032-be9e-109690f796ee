<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vlocally.mealcoupon.mapper.master.BarCodeMapper">

    <select id="isActiveBarCodeStaticSearch" parameterType="String" resultType="Boolean">
        SELECT IF(count(BCS.isActive)>=1, TRUE, FALSE) AS isActive
        FROM BarCodeStatic AS BCS
        JOIN BarCodeUser AS BCU ON BCS.barCodeUserIdx = BCU.barCodeUserIdx
        WHERE BCU.userId = #{uid}
        AND BCS.isActive = 1;
    </select>

    <update id="setBarCodeReissuance" parameterType="String">
        UPDATE BarCodeStatic AS BCS
        JOIN BarCodeUser AS BCU ON BCS.barCodeUserIdx = BCU.barCodeUserIdx
        SET BCS.isActive = 0
        WHERE BCU.userId = #{uid}
        AND BCS.isActive = 1;
    </update>
</mapper>