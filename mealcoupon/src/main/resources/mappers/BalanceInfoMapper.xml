<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vlocally.mealcoupon.mapper.master.BalanceInfoMapper">

    <sql id="Company_Columns">
        CompanyBalanceInfo.comId,
        CompanyBalanceInfo.bizSerial,
        CompanyBalanceInfo.bizSubSerial,
        CompanyBalanceInfo.bizName,
        CompanyBalanceInfo.bizPresidentName,
        CompanyBalanceInfo.bizConditions,
        CompanyBalanceInfo.bizType,
        CompanyBalanceInfo.bizAddress,
        CompanyBalanceInfo.monthlyFee,
        CompanyBalanceInfo.feeBySales,
        CompanyBalanceInfo.feeByUser,
        CompanyBalanceInfo.feeVatInclude,
        CompanyBalanceInfo.balancePeriod,
        CompanyBalanceInfo.balanceDepositDate,
        CompanyBalanceInfo.storeDepositDate,
        CompanyBalanceInfo.balanceMethod,
        CompanyBalanceInfo.balanceProofMethod,
        CompanyBalanceInfo.balanceProofNumber,
        CompanyBalanceInfo.balanceProofWriteDate,
        CompanyBalanceInfo.niceBankName,
        CompanyBalanceInfo.niceBankOwner,
        CompanyBalanceInfo.niceBankAccount,

        CompanyBalanceInfo.contractDate,
        CompanyBalanceInfo.serviceStartDate,
        CompanyBalanceInfo.serviceEndDate,
        CompanyBalanceInfo.memo
    </sql>

    <insert id="insertCompany" parameterType="CompanyBalanceInfo">
        insert into CompanyBalanceInfo
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="comId != null" >
                comId,
            </if>
            <if test="bizSerial != null" >
                bizSerial,
            </if>
            <if test="bizSubSerial != null" >
                bizSubSerial,
            </if>
            <if test="bizName != null" >
                bizName,
            </if>
            <if test="bizPresidentName != null" >
                bizPresidentName,
            </if>
            <if test="bizConditions != null" >
                bizConditions,
            </if>
            <if test="bizType != null" >
                bizType,
            </if>
            <if test="bizAddress != null" >
                bizAddress,
            </if>
            <if test="monthlyFee != null" >
                monthlyFee,
            </if>
            <if test="feeBySales != null" >
                feeBySales,
            </if>
            <if test="feeByUser != null" >
                feeByUser,
            </if>
            <if test="feeVatInclude != null" >
                feeVatInclude,
            </if>
            <if test="balancePeriod != null" >
                balancePeriod,
            </if>
            <if test="balanceDepositDate != null" >
                balanceDepositDate,
            </if>
            <if test="storeDepositDate != null" >
                storeDepositDate,
            </if>
            <if test="balanceMethod != null" >
                balanceMethod,
            </if>
            <if test="balanceProofMethod != null" >
                balanceProofMethod,
            </if>
            <if test="balanceProofNumber != null" >
                balanceProofNumber,
            </if>
            <if test="balanceProofWriteDate != null" >
                balanceProofWriteDate,
            </if>
            <if test="niceBankName != null" >
                niceBankName,
            </if>
            <if test="niceBankOwner != null" >
                niceBankOwner,
            </if>
            <if test="niceBankAccount != null" >
                niceBankAccount,
            </if>

            <if test="contractDate != null" >
                contractDate,
            </if>
            <if test="serviceStartDate != null" >
                serviceStartDate,
            </if>
            <if test="serviceEndDate != null" >
                serviceEndDate,
            </if>
            <if test="memo != null">
                memo,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="comId != null" >
                #{comId},
            </if>
            <if test="bizSerial != null" >
                #{bizSerial},
            </if>
            <if test="bizSubSerial != null" >
                #{bizSubSerial},
            </if>
            <if test="bizName != null" >
                #{bizName},
            </if>
            <if test="bizPresidentName != null" >
                #{bizPresidentName},
            </if>
            <if test="bizConditions != null" >
                #{bizConditions},
            </if>
            <if test="bizType != null" >
                #{bizType},
            </if>
            <if test="bizAddress != null" >
                #{bizAddress},
            </if>
            <if test="monthlyFee != null" >
                #{monthlyFee},
            </if>
            <if test="feeBySales != null" >
                #{feeBySales},
            </if>
            <if test="feeByUser != null" >
                #{feeByUser},
            </if>
            <if test="feeVatInclude != null" >
                #{feeVatInclude},
            </if>
            <if test="balancePeriod != null" >
                #{balancePeriod},
            </if>
            <if test="balanceDepositDate != null" >
                #{balanceDepositDate},
            </if>
            <if test="storeDepositDate != null" >
                #{storeDepositDate},
            </if>
            <if test="balanceMethod != null" >
                #{balanceMethod},
            </if>
            <if test="balanceProofMethod != null" >
                #{balanceProofMethod},
            </if>
            <if test="balanceProofNumber != null" >
                #{balanceProofNumber},
            </if>
            <if test="balanceProofWriteDate != null" >
                #{balanceProofWriteDate},
            </if>
            <if test="niceBankName != null" >
                #{niceBankName},
            </if>
            <if test="niceBankOwner != null" >
                #{niceBankOwner},
            </if>
            <if test="niceBankAccount != null" >
                #{niceBankAccount},
            </if>
            <if test="contractDate != null" >
                #{contractDate},
            </if>
            <if test="serviceStartDate != null" >
                #{serviceStartDate},
            </if>
            <if test="serviceEndDate != null" >
                #{serviceEndDate},
            </if>
            <if test="memo != null">
                #{memo, jdbcType=LONGVARCHAR},
            </if>
        </trim>
        ON DUPLICATE KEY UPDATE
            <trim suffixOverrides="," >
                <if test="bizSerial != null" >
                    bizSerial = #{bizSerial},
                </if>
                <if test="bizSubSerial != null" >
                    bizSubSerial = #{bizSubSerial},
                </if>
                <if test="bizName != null" >
                    bizName = #{bizName},
                </if>
                <if test="bizPresidentName != null" >
                    bizPresidentName = #{bizPresidentName},
                </if>
                <if test="bizConditions != null" >
                    bizConditions = #{bizConditions},
                </if>
                <if test="bizType != null" >
                    bizType = #{bizType},
                </if>
                <if test="bizAddress != null" >
                    bizAddress = #{bizAddress},
                </if>
                <if test="monthlyFee != null" >
                    monthlyFee = #{monthlyFee},
                </if>
                <if test="feeBySales != null" >
                    feeBySales = #{feeBySales},
                </if>
                <if test="feeByUser != null" >
                    feeByUser = #{feeByUser},
                </if>
                <if test="feeVatInclude != null" >
                    feeVatInclude = #{feeVatInclude},
                </if>
                <if test="balancePeriod != null" >
                    balancePeriod = #{balancePeriod},
                </if>
                <if test="balanceDepositDate != null" >
                    balanceDepositDate = #{balanceDepositDate},
                </if>
                <if test="storeDepositDate != null" >
                    storeDepositDate = #{storeDepositDate},
                </if>
                <if test="balanceMethod != null" >
                    balanceMethod = #{balanceMethod},
                </if>
                <if test="balanceProofMethod != null" >
                    balanceProofMethod = #{balanceProofMethod},
                </if>
                <if test="balanceProofNumber != null" >
                    balanceProofNumber = #{balanceProofNumber},
                </if>
                <if test="balanceProofWriteDate != null" >
                    balanceProofWriteDate = #{balanceProofWriteDate},
                </if>
                <if test="niceBankName != null" >
                    niceBankName = #{niceBankName},
                </if>
                <if test="niceBankOwner != null" >
                    niceBankOwner = #{niceBankOwner},
                </if>
                <if test="niceBankAccount != null" >
                    niceBankAccount = #{niceBankAccount},
                </if>
                <if test="contractDate != null" >
                    contractDate = #{contractDate},
                </if>
                <if test="serviceStartDate != null" >
                    serviceStartDate = #{serviceStartDate},
                </if>
                <if test="serviceEndDate != null" >
                    serviceEndDate = #{serviceEndDate},
                </if>
                <if test="memo != null">
                    memo = #{memo},
                </if>
            </trim>
    </insert>

    <update id="updateCompany" parameterType="CompanyBalanceInfo">
        update CompanyBalanceInfo
        <set >
            <if test="bizSerial != null" >
                bizSerial = #{bizSerial},
            </if>
            <if test="bizSubSerial != null" >
                bizSubSerial = #{bizSubSerial},
            </if>
            <if test="bizName != null" >
                bizName = #{bizName},
            </if>
            <if test="bizPresidentName != null" >
                bizPresidentName = #{bizPresidentName},
            </if>
            <if test="bizConditions != null" >
                bizConditions = #{bizConditions},
            </if>
            <if test="bizType != null" >
                bizType = #{bizType},
            </if>
            <if test="bizAddress != null" >
                bizAddress = #{bizAddress},
            </if>
            <if test="monthlyFee != null" >
                monthlyFee = #{monthlyFee},
            </if>
            <if test="feeBySales != null" >
                feeBySales = #{feeBySales},
            </if>
            <if test="feeByUser != null" >
                feeByUser = #{feeByUser},
            </if>
            <if test="feeVatInclude != null" >
                feeVatInclude = #{feeVatInclude},
            </if>
            <if test="balancePeriod != null" >
                balancePeriod = #{balancePeriod},
            </if>
            <if test="balanceDepositDate != null" >
                balanceDepositDate = #{balanceDepositDate},
            </if>
            <if test="storeDepositDate != null" >
                storeDepositDate = #{storeDepositDate},
            </if>
            <if test="balanceMethod != null" >
                balanceMethod = #{balanceMethod},
            </if>
            <if test="balanceProofMethod != null" >
                balanceProofMethod = #{balanceProofMethod},
            </if>
            <if test="balanceProofWriteDate != null" >
                balanceProofWriteDate = #{balanceProofWriteDate},
            </if>
            <if test="balanceProofNumber != null" >
                balanceProofNumber = #{balanceProofNumber},
            </if>
            <if test="niceBankName != null" >
                niceBankName = #{niceBankName},
            </if>
            <if test="niceBankOwner != null" >
                niceBankOwner = #{niceBankOwner},
            </if>
            <if test="niceBankAccount != null" >
                niceBankAccount = #{niceBankAccount},
            </if>
            <if test="contractDate != null" >
                contractDate = #{contractDate},
            </if>
            <if test="serviceStartDate != null" >
                serviceStartDate = #{serviceStartDate},
            </if>
            <if test="serviceEndDate != null" >
                serviceEndDate = #{serviceEndDate},
            </if>
            <if test="memo !=null">
                memo = #{memo},
            </if>
        </set>
        where comId = #{comId}
    </update>

    <select id="selectCompany" parameterType="string" resultType="CompanyBalanceInfo">
        SELECT
          <include refid="Company_Columns"/>
        from CompanyBalanceInfo
        where comId = #{value}
    </select>

    <select id="countTargetCompany" parameterType="com.vlocally.mealcoupon.domain.master.BalanceInfoTO$TargetCompanyQuery" resultType="long">
        select
            count(*)
        from Company
        where LOWER(Company.name) LIKE CONCAT('%',LOWER(#{searchWord}),'%')
    </select>

    <select id="selectTargetCompany" parameterType="com.vlocally.mealcoupon.domain.master.BalanceInfoTO$TargetCompanyQuery" resultType="hashmap">
        select
          <include refid="Company_Columns"/>,
          Company.name as comName,
          Company.cash,
          Company.trustcash
        from Company left join CompanyBalanceInfo on Company.comid = CompanyBalanceInfo.comId
        where LOWER(Company.name) like CONCAT('%',LOWER(#{searchWord}),'%')
        limit #{offset}, #{limit}
    </select>

    <sql id="Store_Columns">
        StoreBalanceInfo.storeId,
        StoreBalanceInfo.bizSerial,
        StoreBalanceInfo.bizSubSerial,
        StoreBalanceInfo.bizName,
        StoreBalanceInfo.bizPresidentName,
        StoreBalanceInfo.bizConditions,
        StoreBalanceInfo.bizType,
        StoreBalanceInfo.bizAddress,
        StoreBalanceInfo.contractDate,
        StoreBalanceInfo.contractExpireDate,
        StoreBalanceInfo.taxGrade,
        StoreBalanceInfo.bankName,
        StoreBalanceInfo.bankOwner,
        StoreBalanceInfo.bankAccount,
        StoreBalanceInfo.niceId,
        StoreBalanceInfo.memo
    </sql>

    <insert id="insertStore" parameterType="StoreBalanceInfo">
        insert into StoreBalanceInfo
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="storeId != null" >
                storeId,
            </if>
            <if test="bizSerial != null" >
                bizSerial,
            </if>
            <if test="bizSubSerial != null" >
                bizSubSerial,
            </if>
            <if test="bizName != null" >
                bizName,
            </if>
            <if test="bizPresidentName != null" >
                bizPresidentName,
            </if>
            <if test="bizConditions != null" >
                bizConditions,
            </if>
            <if test="bizType != null" >
                bizType,
            </if>
            <if test="bizAddress != null" >
                bizAddress,
            </if>
            <if test="contractDate != null" >
                contractDate,
            </if>
            <if test="contractExpireDate != null" >
                contractExpireDate,
            </if>
            <if test="taxGrade != null" >
                taxGrade,
            </if>
            <if test="bankName != null">
                bankName,
            </if>
            <if test="bankOwner != null">
                bankOwner,
            </if>
            <if test="bankAccount != null">
                bankAccount,
            </if>
            <if test="niceId != null">
                niceId,
            </if>
            <if test="memo != null">
                memo,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="storeId != null" >
                #{storeId},
            </if>
            <if test="bizSerial != null" >
                #{bizSerial},
            </if>
            <if test="bizSubSerial != null" >
                #{bizSubSerial},
            </if>
            <if test="bizName != null" >
                #{bizName},
            </if>
            <if test="bizPresidentName != null" >
                #{bizPresidentName},
            </if>
            <if test="bizConditions != null" >
                #{bizConditions},
            </if>
            <if test="bizType != null" >
                #{bizType},
            </if>
            <if test="bizAddress != null" >
                #{bizAddress},
            </if>
            <if test="contractDate != null" >
                #{contractDate},
            </if>
            <if test="contractExpireDate != null" >
                #{contractExpireDate},
            </if>
            <if test="taxGrade != null" >
                #{taxGrade},
            </if>
            <if test="bankName != null">
                #{bankName},
            </if>
            <if test="bankOwner != null">
                #{bankOwner},
            </if>
            <if test="bankAccount != null">
                #{bankAccount},
            </if>
            <if test="niceId != null">
                #{niceId},
            </if>
            <if test="memo != null">
                #{memo},
            </if>
        </trim>
        ON DUPLICATE KEY UPDATE
            <trim suffixOverrides="," >
                <if test="bizSerial != null" >
                    bizSerial = #{bizSerial},
                </if>
                <if test="bizSubSerial != null" >
                    bizSubSerial = #{bizSubSerial},
                </if>
                <if test="bizName != null" >
                    bizName = #{bizName},
                </if>
                <if test="bizPresidentName != null" >
                    bizPresidentName = #{bizPresidentName},
                </if>
                <if test="bizConditions != null" >
                    bizConditions = #{bizConditions},
                </if>
                <if test="bizType != null" >
                    bizType = #{bizType},
                </if>
                <if test="bizAddress != null" >
                    bizAddress = #{bizAddress},
                </if>
                <if test="contractDate != null" >
                    contractDate = #{contractDate},
                </if>
                <if test="contractExpireDate != null" >
                    contractExpireDate = #{contractExpireDate},
                </if>
                <if test="taxGrade != null" >
                    taxGrade = #{taxGrade},
                </if>
                <if test="bankName != null">
                    bankName = #{bankName},
                </if>
                <if test="bankOwner != null">
                    bankOwner = #{bankOwner},
                </if>
                <if test="bankAccount != null">
                    bankAccount = #{bankAccount},
                </if>
                <if test="niceId != null">
                    niceId = #{niceId},
                </if>
                <if test="memo != null">
                    memo = #{memo},
                </if>
            </trim>
    </insert>

    <update id="updateStore" parameterType="StoreBalanceInfo">
        update StoreBalanceInfo
        <set >
            <if test="bizSerial != null" >
                bizSerial = #{bizSerial},
            </if>
            <if test="bizSubSerial != null" >
                bizSubSerial = #{bizSubSerial},
            </if>
            <if test="bizName != null" >
                bizName = #{bizName},
            </if>
            <if test="bizPresidentName != null" >
                bizPresidentName = #{bizPresidentName},
            </if>
            <if test="bizConditions != null" >
                bizConditions = #{bizConditions},
            </if>
            <if test="bizType != null" >
                bizType = #{bizType},
            </if>
            <if test="bizAddress != null" >
                bizAddress = #{bizAddress},
            </if>
            <if test="contractDate != null" >
                contractDate = #{contractDate},
            </if>
            <if test="contractExpireDate != null" >
                contractExpireDate = #{contractExpireDate},
            </if>
            <if test="taxGrade != null" >
                taxGrade = #{taxGrade},
            </if>
            <if test="bankName != null">
                bankName = #{bankName},
            </if>
            <if test="bankOwner != null">
                bankOwner = #{bankOwner},
            </if>
            <if test="bankAccount != null">
                bankAccount = #{bankAccount},
            </if>
            <if test="niceId != null">
                niceId = #{niceId},
            </if>
            <if test="memo != null">
                memo = #{memo},
            </if>
        </set>
        where storeId = #{storeId}
    </update>

    <select id="selectStore" parameterType="string" resultType="StoreBalanceInfo">
        SELECT
          <include refid="Store_Columns"/>
        from StoreBalanceInfo
        where storeId = #{value}
    </select>

    <sql id="Columns_Email">
        BalanceInfoEmail.emailIdx,
        BalanceInfoEmail.comId,
        BalanceInfoEmail.storeId,
        BalanceInfoEmail.email,
        BalanceInfoEmail.name,
        BalanceInfoEmail.useType,
        BalanceInfoEmail.phone,
        BalanceInfoEmail.mobile
    </sql>

    <insert id="insertEmail" parameterType="BalanceInfoEmail" useGeneratedKeys="true">
        insert into BalanceInfoEmail
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="emailIdx != null">
                emailIdx,
            </if>
            <if test="comId != null" >
                comId,
            </if>
            <if test="storeId != null" >
                storeId,
            </if>
            <if test="email != null" >
                email,
            </if>
            <if test="name != null" >
                name,
            </if>
            <if test="useType != null" >
                useType,
            </if>
            <if test="phone != null" >
                phone,
            </if>
            <if test="mobile != null" >
                mobile,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="emailIdx != null">
                #{emailIdx},
            </if>
            <if test="comId != null" >
                #{comId},
            </if>
            <if test="storeId != null" >
                #{storeId},
            </if>
            <if test="email != null" >
                #{email},
            </if>
            <if test="name != null" >
                #{name},
            </if>
            <if test="useType != null" >
                #{useType},
            </if>
            <if test="phone != null" >
                #{phone},
            </if>
            <if test="mobile != null" >
                #{mobile},
            </if>
        </trim>
        ON DUPLICATE KEY UPDATE
        <trim suffixOverrides="," >
            <if test="comId != null" >
                comId = #{comId},
            </if>
            <if test="storeId != null" >
                storeId = #{storeId},
            </if>
            <if test="email != null" >
                email = #{email},
            </if>
            <if test="name != null" >
                name = #{name},
            </if>
            <if test="useType != null" >
                useType = #{useType},
            </if>
            <if test="phone != null" >
                phone = #{phone},
            </if>
            <if test="mobile != null" >
                mobile = #{mobile},
            </if>
        </trim>
    </insert>

    <select id="selectEmail" parameterType="com.vlocally.mealcoupon.domain.master.BalanceInfoTO$EmailQuery" resultType="BalanceInfoEmail">
        select
          <include refid="Columns_Email" />
        from BalanceInfoEmail
        <where>
            <trim suffixOverrides="and">
                <if test="emailIdx != null">
                    emailIdx = #{emailIdx} and
                </if>
                <if test="comId != null" >
                    comId = #{comId} and
                </if>
                <if test="storeId != null" >
                    storeId = #{storeId} and
                </if>
                <if test="useType != null" >
                    useType = #{useType} and
                </if>
            </trim>
        </where>
        order by emailIdx ASC
    </select>

    <delete id="deleteEmail" parameterType="long">
        delete from BalanceInfoEmail
        where emailIdx = #{value}
    </delete>
</mapper>