<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vlocally.mealcoupon.mapper.master.CaptainPaymentServiceApplyMapper">

  <update id="update" parameterType="com.vlocally.mealcoupon.service.entity.CaptainPaymentServiceApplyDto$UpdateParam">
    update CaptainPaymentServiceApply
    set status = #{status}
      ,updateUser = #{updateUser}
      ,updateDate = #{updateDate}
    where comId = #{comId}
  </update>

</mapper>
