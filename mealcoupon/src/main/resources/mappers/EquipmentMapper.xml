<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vlocally.mealcoupon.mapper.master.EquipmentMapper">

  <select id="selectEquipmentPos"
    parameterType="com.vlocally.mealcoupon.vo.EquipmentPosVo"
    resultType="com.vlocally.mealcoupon.vo.EquipmentPosVo">

    SELECT
      *
    FROM
      EquipmentPos
    WHERE
      mac = #{mac}
  </select>

  <update id="updateEquipmentRelation" parameterType="com.vlocally.mealcoupon.vo.EquipmentRelationVo">
    UPDATE
      EquipmentRelation
    SET
      status = #{status}
    WHERE
      productIdx = #{productIdx}
  </update>

</mapper>