<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vlocally.mealcoupon.mapper.master.CategoryMapper">

    <update id="updateMenuCategory" parameterType="com.vlocally.mealcoupon.vo.Categorymenu">
        update CategoryMenu
        <set >
            <if test="priority != null" >
                priority = #{priority},
            </if>
            <if test="name != null" >
                name = #{name},
            </if>
        </set>
        where mcateid = #{mcateid}
    </update>

    <delete id="deleteMenuCategory" parameterType="integer">
        delete from CategoryMenu
        where mcateid = #{value}
    </delete>

    <update id="updateMenuCategoryToMenu" parameterType="string">
        update Menu M
          left join CategoryMenu C
            on M.sid = C.sid
        set M.category = C.name, M.categorySeq = C.priority
        where M.sid = #{value}
          and M.categoryid = C.mcateid
    </update>
</mapper>