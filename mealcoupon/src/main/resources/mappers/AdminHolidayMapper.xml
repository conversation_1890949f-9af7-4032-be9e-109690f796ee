<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vlocally.mealcoupon.mapper.master.AdminHolidayMapper">
    <select id="readHolidayCountryInfoList" parameterType="string" resultType="com.vlocally.mealcoupon.vo.HolidayCountryInfo">
      SELECT
        idx,
        year,
        startDate,
        endDate,
        duration,
        name,
        description,
        updateDate,
        isDelete
      FROM
        HolidayCountryInfo
      WHERE
        year = #{year}
      AND
        isDelete = 0
      ORDER BY
        startDate ASC, endDate ASC, idx ASC
    </select>
    <select id="readHolidayCountryInfo" parameterType="Long" resultType="com.vlocally.mealcoupon.vo.HolidayCountryInfo">
      SELECT
        idx,
        year,
        startDate,
        endDate,
        duration,
        name,
        description,
        updateDate,
        isDelete
      FROM
        HolidayCountryInfo
      WHERE
        idx = #{idx}
    </select>
    <select id="readYear" resultType="string">
      SELECT year
      FROM
        HolidayCountryInfo
      GROUP BY
        year
      HAVING
        year &lt;= YEAR(NOW()) + 2
      ORDER BY
        year
     </select>
      <insert id="updateHolidayCountryInfo" parameterType="com.vlocally.mealcoupon.vo.HolidayCountryInfo" useGeneratedKeys="true" keyProperty="idx">
        INSERT INTO HolidayCountryInfo
            (idx, year, startDate, endDate, duration, name, description, createDate, updateDate, updateId, isDelete)
        VALUES
            (#{idx}, #{year}, #{startDate}, #{endDate}, #{duration}, #{name}, #{description}, now(), now(), #{updateId}, 0)
        ON duplicate KEY UPDATE
          idx = #{idx},
          year = #{year},
          startDate = #{startDate},
          endDate = #{endDate},
          duration = #{duration},
          name = #{name},
          description = #{description},
          updateDate = now(),
          updateId = #{updateId},
          isDelete = #{isDelete}
      </insert>
      <insert id="insertHolidayCompanyInfo" parameterType="com.vlocally.mealcoupon.vo.HolidayCompanyInfo">
        INSERT INTO HolidayCompanyInfo
          (holidayCountryIdx, comId, year, startDate, endDate, duration, name, description, isActive, updatable, deletable, createDate, updateDate, isDelete)
        VALUES
        <foreach collection="comIdList" index="index" item="comId" separator=",">
          (#{holidayCountryIdx}, #{comId}, #{year}, #{startDate}, #{endDate}, #{duration}, #{name}, #{description}, 0, 0, 0, now(), now(), 0)
        </foreach>
      </insert>
      <update id="updateHolidayCompanyInfo" parameterType="com.vlocally.mealcoupon.vo.HolidayCompanyInfo">
      UPDATE HolidayCompanyInfo
      <set>
        <if test="comId != null">
          comId = #{comId},
        </if>
        <if test="year != null">
          year = #{year},
        </if>
        <if test="startDate != null">
          startDate = #{startDate},
        </if>
        <if test="endDate != null">
          endDate = #{endDate},
        </if>
        <if test="duration != null">
          duration = #{duration},
        </if>
        <if test="name != null">
          name = #{name},
        </if>
        <if test="description != null">
          description = #{description},
        </if>
        <if test="isDelete != null">
          isDelete = #{isDelete},
        </if>
        updateDate = now()
      </set>
      WHERE
        holidayCountryIdx = #{holidayCountryIdx}
      </update>
      <select id="readHolidayCompanyInfoList" parameterType="string" resultType="com.vlocally.mealcoupon.vo.HolidayCompanyInfo">
        SELECT
          idx,
          year,
          startDate,
          endDate,
          duration,
          name,
          description,
          updateDate,
          isDelete
        FROM
          HolidayCompanyInfo
        WHERE
          comId = #{comId}
        ORDER BY
          startDate, endDate
      </select>
      <insert id="insertHolidayCompanyInfoList" parameterType="string">
      INSERT INTO HolidayCompanyInfo
        (holidayCountryIdx, comId, year, startDate, endDate, duration, name, description, isActive, updatable, deletable, createDate, updateDate, updateId, isDelete)
      SELECT
        idx as holidayCountryIdx,
        #{comId},
        year,
        startDate,
        endDate,
        duration,
        name,
        description,
        0 as isActive,
        0 as updatable,
        0 as deletable,
        now() as createDate,
        now() as updateDate,
        updateId,
        isDelete
      FROM
        HolidayCountryInfo
      WHERE
        year &gt;= YEAR(NOW())
      </insert>
</mapper>
