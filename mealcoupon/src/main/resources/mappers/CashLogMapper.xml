<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vlocally.mealcoupon.mapper.master.CashLogMapper">

    <sql id="List_Clause">
        <where>
            <trim suffixOverrides="and">
                <if test="comid != null and comid != ''">
                    comid = #{comid} and
                </if>
                <if test="startdate != null">
                    regdate >= #{startdate} and
                </if>
                <if test="enddate != null">
                    DATE_ADD(#{enddate},INTERVAL 1 DAY) > regdate
                </if>
                <if test="cashlogtype != null and cashlogtype.size() != 0">
                    <trim prefix="causetype in(" suffix=") and">
                        <foreach collection="cashlogtype" item="item" separator=",">
                            #{item}
                        </foreach>
                    </trim>
                </if>
            </trim>
        </where>
    </sql>


    <select id="countList" parameterType="CashLogTO" resultType="long">
        select count(*)
        from CashLog
        <include refid="List_Clause"/>
    </select>

    <select id="selectList" parameterType="CashLogTO" resultType="Cashlog">
        select
          useid, uid, username, comid, comname, changepoint, outpoint, regdate, cause, causetype, causelink
        from CashLog
        <include refid="List_Clause"/>
        order by regdate DESC
        limit #{offset}, #{limit}
    </select>
</mapper>