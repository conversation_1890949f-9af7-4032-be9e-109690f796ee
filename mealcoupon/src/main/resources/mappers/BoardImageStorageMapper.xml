<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vlocally.mealcoupon.mapper.master.BoardImageStorageMapper">
    <sql id="commonWhere">
        WHERE isDelete = FALSE
        <if test="idx != null">
          AND idx = #{idx}
        </if>
        <if test="postId != null">
          AND postId = #{postId}
        </if>
        <if test="requestType != null and requestType != ''">
          AND requestType = #{requestType}
        </if>
    </sql>

    <select id="postIdByImageCount" parameterType="com.vlocally.mealcoupon.vo.BoardImageStorageDto$BoardImageStorageRequestDto"
        resultType="int">
        SELECT  COUNT(*)
        FROM    BoardImageStorage
        <include refid="commonWhere"/>
    </select>

    <select id="findByIdx" parameterType="Integer" resultType="com.vlocally.mealcoupon.vo.BoardImageStorage">
      SELECT
          idx,
          requestType,
          postId,
          originImageName,
          imageUrl,
          mobileImageUrl,
          isDelete,
          createDate,
          createUser,
          updateDate,
          updateUser
      FROM BoardImageStorage
      WHERE isDelete = FALSE
            AND idx = #{value}
    </select>

  <select id="postIdByImages"
            parameterType="com.vlocally.mealcoupon.vo.BoardImageStorageDto$BoardImageStorageRequestDto"
            resultType="com.vlocally.mealcoupon.vo.BoardImageStorage">
      SELECT
          idx,
          requestType,
          postId,
          originImageName,
          imageUrl,
          mobileImageUrl,
          isDelete,
          createDate,
          createUser,
          updateDate,
          updateUser
      FROM BoardImageStorage
      <include refid="commonWhere"/>
      ORDER BY idx
    </select>

  <insert id="boardImageStorageSave" parameterType="com.vlocally.mealcoupon.vo.BoardImageStorage">
    INSERT INTO BoardImageStorage
    <trim prefix="(" suffix=")" suffixOverrides="," >
      requestType,
      postId,
      originImageName,
      imageUrl,
      mobileImageUrl,
      isDelete,
      createDate,
      createUser
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      #{requestType},
      #{postId},
      #{originImageName},
      #{imageUrl},
      #{mobileImageUrl},
      FALSE,
      NOW(),
      #{createUser}
    </trim>
  </insert>

  <update id="updateBoardImageStorage" parameterType="com.vlocally.mealcoupon.vo.BoardImageStorage">
        UPDATE  BoardImageStorage
        <set>
          <if test="postId != null">
            postId = #{postId},
          </if>
          <if test="originImageName != null">
            originImageName = #{originImageName},
          </if>
          <if test="imageUrl != null">
            imageUrl = #{imageUrl},
          </if>
          <if test="mobileImageUrl != null">
            mobileImageUrl = #{mobileImageUrl},
          </if>
          <if test="isDelete != null">
            isDelete = #{isDelete},
          </if>
          updateDate = NOW(),
          updateUser = #{updateUser}
        </set>
        WHERE   idx = #{idx}
    </update>
</mapper>