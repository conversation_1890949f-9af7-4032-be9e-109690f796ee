<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vlocally.mealcoupon.mapper.master.OfficeMapper">

    <sql id="UserOfficeRelation">
        UserOfficeRelation.userId,
        UserOfficeRelation.officeIdx,
        UserOfficeRelation.regDate,
    </sql>

    <select id="selectUserOfficeRelationByUser" parameterType="string" resultType="com.vlocally.mealcoupon.vo.UserOfficeRelation">

        SELECT
          <trim suffixOverrides=",">
              <include refid="UserOfficeRelation" />
              OfficeHistory.name as name
          </trim>
        FROM UserOfficeRelation left join OfficeHistory on UserOfficeRelation.officeIdx = OfficeHistory.officeIdx
        WHERE userId = #{value}
          and OfficeHistory.isActive = true

    </select>

    <insert id="insertUserOfficeRelation" parameterType="com.vlocally.mealcoupon.vo.UserOfficeRelation">

        INSERT INTO UserOfficeRelation
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="userId != null">
                    userId,
                </if>
                <if test="officeIdx != null">
                    officeIdx,
                </if>
                <if test="regDate != null">
                    regDate,
                </if>
            </trim>
        VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="userId != null">
                    #{userId},
                </if>
                <if test="officeIdx != null">
                    #{officeIdx},
                </if>
                <if test="regDate != null">
                    #{regDate},
                </if>
            </trim>
        ON DUPLICATE KEY UPDATE
            regDate = #{regDate, jdbcType=TIMESTAMP}

    </insert>

    <select id="selectIsAvailableOfficeForUser" parameterType="com.vlocally.mealcoupon.vo.UserOfficeRelation" resultType="boolean">
        SELECT
            count(*) > 0
        FROM User
            left join Office on User.comid = Office.orgCode
        WHERE
            Office.officeIdx = #{officeIdx}
            and User.uid = #{userId}
            AND User.isDormant = false
    </select>

    <delete id="deleteUserOfficeRelation" parameterType="com.vlocally.mealcoupon.vo.UserOfficeRelation">
        DELETE FROM UserOfficeRelation
        WHERE
            officeIdx = #{officeIdx}
            and userId = #{userId}
    </delete>

    <select id="selectOfficeForUser" parameterType="string" resultType="long">
        SELECT
            UserOfficeRelation.officeIdx
        FROM User
            left join UserOfficeRelation on User.uid = UserOfficeRelation.userId
        WHERE User.uid = #{value}
          AND User.isDormant = false
        LIMIT 0, 1
    </select>

    <select id="selectUserOfficeRelationByOfficeIdx" parameterType="long" resultType="com.vlocally.mealcoupon.vo.UserOfficeRelation">
        SELECT
            B.userId,
            B.officeIdx,
            B.regDate
        FROM User A
            INNER JOIN UserOfficeRelation B ON A.uid = B.userId
        WHERE A.status = "ACTIVE"
          AND B.officeIdx = #{value}
    </select>

    <select id="selectOfficeHistory" parameterType="com.vlocally.mealcoupon.vo.OfficeHistory" resultType="com.vlocally.mealcoupon.vo.OfficeHistory">
        SELECT
          officeHistoryIdx,
          officeIdx,
          name,
          officialName,
          gpslat,
          gpslon,
          region,
          regDate,
          inactiveDate
        FROM
          OfficeHistory
        WHERE
          officeIdx = #{officeIdx}
        <if test="isActive != null">
            AND isActive = #{isActive}
        </if>
        ORDER BY
          officeHistoryIdx DESC
    </select>
    <insert id="insertOfficeHistory" parameterType="com.vlocally.mealcoupon.vo.OfficeHistory">
        INSERT INTO OfficeHistory
        (officeIdx, name, officialName, region, gpslat, gpslon, isActive, regDate, inactiveDate)
        VALUES
        (#{officeIdx}, #{name}, #{officialName}, #{region}, #{gpslat}, #{gpslon}, #{isActive}, now(), #{inactiveDate})
    </insert>
    <update id="updateOfficeHistory" parameterType="com.vlocally.mealcoupon.vo.OfficeHistory">
        UPDATE OfficeHistory
        <set>
            <if test="isActive != null">
                isActive = #{isActive},
            </if>
            <if test="inactiveDate != null">
                inactiveDate = now()
            </if>
        </set>
        WHERE
            officeIdx = #{officeIdx}
        AND
            isActive = 1
        AND
            inactiveDate is null
    </update>
    <select id="selectOfficeIdx" parameterType="string" resultType="Long">
        select
            o.officeIdx
        from
            Office o
        where
            o.orgCode = #{orgCode}
    </select>
    <select id="readOfficeList" parameterType="string" resultType="com.vlocally.mealcoupon.vo.Office">
        select
            o.officeIdx,
            o.orgCode,
            o.gpslat,
            o.gpslon,
            o.regDate
        from
            Office o
        where
            o.orgCode = #{orgCode}
        and
            o.inactiveDate is null
        order by
            o.regDate desc
    </select>
    <select id="readByOfficeIdx" parameterType="Long" resultType="com.vlocally.mealcoupon.vo.Office">
        select
            o.officeIdx,
            o.orgCode,
            o.gpslat,
            o.gpslon,
            o.regDate
        from
            Office o
        where
            o.officeIdx = #{officeIdx}
    </select>
    <update id="updateOffice" parameterType="com.vlocally.mealcoupon.vo.Office">
        UPDATE Office
        <set>
            <if test="gpslat != null">
                gpslat = #{gpslat},
            </if>
            <if test="gpslon != null">
                gpslon = #{gpslon},
            </if>
        </set>
        WHERE
        officeIdx = #{officeIdx}
        AND
        inactiveDate is null
    </update>
</mapper>
