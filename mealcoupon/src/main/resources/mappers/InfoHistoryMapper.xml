<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vlocally.mealcoupon.mapper.master.InfoHistoryMapper">
    <insert id="insertInfoHistory" parameterType="com.vlocally.mealcoupon.vo.InfoHistory">
        insert into
            InfoHistory(
                infoHistoryIdx,
                orgCode,
                createdId,
                updatedId,
                executed,
                reserved,
                created,
                updated
            )
        values(
            #{infoHistoryIdx},
            #{orgCode},
            #{createdId},
            #{updatedId},
            #{executed},
            #{reserved},
            #{created},
            #{updated}
        )
        <selectKey resultType="long" keyProperty="infoHistoryIdx">
            SELECT LAST_INSERT_ID()
        </selectKey>
    </insert>

    <insert id="insertInfoUser" parameterType="com.vlocally.mealcoupon.vo.InfoUser">
        insert into
            InfoUser(
                infoUserIdx,
                infoHistoryIdx,
                userId,
                type,
                created
            )
        values(
            #{infoUserIdx},
            #{infoHistoryIdx},
            #{userId},
            #{type},
            #{created}
        )
        <selectKey resultType="long" keyProperty="infoUserIdx">
            SELECT LAST_INSERT_ID()
        </selectKey>
    </insert>
    <select id="selectInfoHistory" parameterType="string" resultType="com.vlocally.mealcoupon.vo.InfoHistory">
        select
            IH.infoHistoryIdx,
            IH.orgCode,
            IH.createdId,
            IH.updatedId,
            IH.executed,
            IH.reserved,
            IH.created,
            IH.updated
        from
            InfoHistory IH
        join
            InfoUser IU on(IH.infoHistoryIdx = IU.infoHistoryIdx)
        where
            IH.reserved > now()
            and IH.status = 'READY'
            and IU.userId = #{userId}
        order by IH.infoHistoryIdx
        limit 1
    </select>
    <select id="selectInfoHistories" parameterType="string" resultType="com.vlocally.mealcoupon.vo.InfoHistory">
        select
            IH.infoHistoryIdx,
            IH.orgCode,
            IH.createdId,
            IH.updatedId,
            IH.executed,
            IH.reserved,
            IH.created,
            IH.updated
        from
            InfoHistory IH
                join
            InfoUser IU on(IH.infoHistoryIdx = IU.infoHistoryIdx)
        where
            IH.reserved > now()
          and IH.status = 'READY'
          and IU.userId = #{userId}
        order by IH.infoHistoryIdx
    </select>
    <update id="updateInfoHistoryCanceled" parameterType="com.vlocally.mealcoupon.vo.InfoHistory">
        update InfoHistory
        set status = 'CANCELED'
        where infoHistoryIdx = #{infoHistoryIdx}
        and status = 'READY'
        and reserved > now()
    </update>
    <select id="selectInfoUser" parameterType="long" resultType="com.vlocally.mealcoupon.vo.InfoUser">
        select
            infoUserIdx,
            infoHistoryIdx,
            userId,
            type,
            created
        from
            InfoUser
        where
            infoHistoryIdx = #{infoHistoryIdx}
    </select>
</mapper>