package com.vlocally.mealcoupon.mapper.master;

import java.util.List;

import com.vlocally.mealcoupon.domain.master.PageInfo;
import com.vlocally.mealcoupon.vo.MarketStoreCategory;

public interface MarketStoreCategoryMapper {
    List<MarketStoreCategory> readMarketStoreCategoryList();
    List<MarketStoreCategory> readMarketStoreCategoryListByPage(PageInfo pageInfo);
    MarketStoreCategory readMarketStoreCategory(Integer categoryId);
    Integer readLastIndex();
    Long countAll();
    void insert(MarketStoreCategory category);
    void updateMarketCategory(MarketStoreCategory requestDto);
}
