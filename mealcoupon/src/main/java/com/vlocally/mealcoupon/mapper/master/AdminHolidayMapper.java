package com.vlocally.mealcoupon.mapper.master;

import java.util.List;
import org.springframework.stereotype.Repository;

import com.vlocally.mealcoupon.vo.HolidayCompanyInfo;
import com.vlocally.mealcoupon.vo.HolidayCountryInfo;

@Repository
public interface AdminHolidayMapper {
    List<HolidayCountryInfo> readHolidayCountryInfoList(String year);
    HolidayCountryInfo readHolidayCountryInfo(Long idx);
    List<String> readYear();
    void updateHolidayCountryInfo(HolidayCountryInfo holidayCountryInfo);
    void insertHolidayCompanyInfo(HolidayCompanyInfo holidayCompanyInfo);
    void updateHolidayCompanyInfo(HolidayCompanyInfo holidayCompanyInfo);
    List<HolidayCompanyInfo> readHolidayCompanyInfoList(String comId);
    void insertHolidayCompanyInfoList(String comId);
}
