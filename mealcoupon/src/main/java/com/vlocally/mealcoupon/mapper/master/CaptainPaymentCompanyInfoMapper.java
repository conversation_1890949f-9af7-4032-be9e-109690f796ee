package com.vlocally.mealcoupon.mapper.master;

import java.util.List;
import org.springframework.stereotype.Repository;

import com.vlocally.mealcoupon.service.entity.CaptainPaymentCompanyInfoDto;
import com.vlocally.mealcoupon.service.entity.CaptainPaymentCompanyInfoDto.InsertOrUpdateInfo;
import com.vlocally.mealcoupon.service.entity.CaptainPaymentCompanyInfoDto.ServiceTypeSearchInfo;
import com.vlocally.mealcoupon.vo.CaptainPaymentCompanyInfo;
import com.vlocally.mealcoupon.vo.CaptainPaymentCompanyInfoHst;

@Repository
public interface CaptainPaymentCompanyInfoMapper {

    List<CaptainPaymentCompanyInfo> selectAllByComId(String comId);
    CaptainPaymentCompanyInfo selectByComIdAndServiceType(ServiceTypeSearchInfo serviceTypeSearchInfo);
    void update(InsertOrUpdateInfo updateInfo);
    void insert(InsertOrUpdateInfo insertOrUpdateInfo);
    void insertHistory(InsertOrUpdateInfo insertOrUpdateInfo);
    List<CaptainPaymentCompanyInfoHst> selectHistoryByComIdAndServiceType(CaptainPaymentCompanyInfoDto.HistorySearchParam historySearchParam);
}
