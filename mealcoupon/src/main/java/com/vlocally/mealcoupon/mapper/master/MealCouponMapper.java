package com.vlocally.mealcoupon.mapper.master;

import com.vlocally.mealcoupon.domain.master.CouponGroupTO;
import com.vlocally.mealcoupon.domain.master.CouponGroupTO.CouponByPolicy;
import com.vlocally.mealcoupon.domain.master.CouponMemberTO;
import com.vlocally.mealcoupon.domain.master.CouponMenuTO;
import com.vlocally.mealcoupon.domain.master.CouponMenuTO.CouponMenuVo;
import com.vlocally.mealcoupon.domain.master.CouponThirdPartyTO;
import com.vlocally.mealcoupon.domain.master.ShippingVo.CouponShippingInfoVo;
import com.vlocally.mealcoupon.dto.coupon.cancel.PartialCancelDto.CouponDetailResponseDto.CancelCouponMember.CaptainCoupon;
import com.vlocally.mealcoupon.vo.Coupongroup;
import com.vlocally.mealcoupon.vo.Couponmember;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by ttobii on 2015. 11. 27..
 */
@Repository
public interface MealCouponMapper {

    void insertGroup(Coupongroup group);
    void insertMember(Couponmember member);

    Long countGroupList(CouponGroupTO.ListQuery query);
    List<CouponGroupTO.CouponGroup> selectGroupList(CouponGroupTO.ListQuery query);

    Long countMemberList(CouponMemberTO.ListQuery query);
    List<CouponMemberTO.CouponMember> selectMemberList(CouponMemberTO.ListQuery query);
    List<CouponMemberTO.CouponMember> selectMemberByCouponId(String cid);


    Long countMenuList(CouponMenuTO.ListQuery query);
    List<CouponMenuTO.CouponMenu> selectMenuList(CouponMenuTO.ListQuery query);
    List<CouponMenuTO.CouponMenu> selectMenuByCouponId(String cid);

    CouponThirdPartyTO.CouponGs selectCouponGsByCouponId(String cid);
    CouponThirdPartyTO.CouponBeat selectCouponBeatByCouponId(String cid);
    CouponThirdPartyTO.CaptainCode selectCaptainCodeByCouponId(String cid);

    List<CouponMenuVo> selectCouponWithMenu(String cid);

    CouponShippingInfoVo selectCouponWithShipping(String cid);

    List<CouponByPolicy> selectCouponInfo(String cid);

    List<CouponGroupTO.CouponByPolicy.CouponPolicy> selectCouponWithPolicy(CouponByPolicy couponByPolicy);

}
