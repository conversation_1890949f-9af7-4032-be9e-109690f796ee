package com.vlocally.mealcoupon.util;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.vlocally.mealcoupon.constant.Errors;
import com.vlocally.mealcoupon.constant.InitProperties;
import com.vlocally.mealcoupon.exception.VlocallyException;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import java.net.URI;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by ttobii on 2018. 1. 31..
 */
@Component
public class GoogleAPIWrapper {

	@Autowired
	private InitProperties initProperties;

	private String apiKey;
	private final String baseUrl = "https://content.googleapis.com/";

	private RestTemplate rt = new RestTemplate();

	public GoogleAPIWrapper(){
		HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
		factory.setReadTimeout(10000);
		factory.setConnectTimeout(10000);
		rt.setRequestFactory(factory);

	}

	@PostConstruct
	private void initialize(){
		apiKey = initProperties.getGoogleApiKey();
	}

	public UserInfo getUserInfo(String accessToken) throws VlocallyException {

		HttpEntity<String> entity = getDefaultHeaderEntity(accessToken);

		try{
			ResponseEntity<UserInfo> res = rt.exchange(getUri("oauth2/v2/userinfo", getDefaultParameter()), HttpMethod.GET, entity, UserInfo.class);

			return res.getBody();
		} catch (Exception e){
			throw new VlocallyException(Errors.Account_UnknownUser, e);
		}

	}

	private HttpHeaders getDefaultHeaders(String accessToken){
		HttpHeaders header = new HttpHeaders();
		header.add(HttpHeaders.AUTHORIZATION, "Bearer "+accessToken);
//		header.add(HttpHeaders.ORIGIN, "localhost");

		return header;
	}

	private HttpEntity<String> getDefaultHeaderEntity(String accessToken){
		HttpHeaders header = getDefaultHeaders(accessToken);
		HttpEntity<String> entity = new HttpEntity<String>("", header);
		return entity;
	}

	private MultiValueMap<String, String> getDefaultParameter(){
		MultiValueMap<String, String> param = new LinkedMultiValueMap<>();
		param.add("key", apiKey);

		return param;
	}

	private URI getUri(String path, MultiValueMap<String, String> urlParameters){
		UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl+path);
		builder.queryParams(urlParameters);

		return builder.build(true).toUri();
	}

	@Data
	public static class UserInfo {

		private String id;
		private String email;
		@JsonProperty("verified_email")
		private Boolean verifiedEmail;
		private String name;
		@JsonProperty("given_name")
		private String givenName;
		@JsonProperty("family_name")
		private String familyName;
		private String link;
		private String picture;
		private String gender;
		private String locale;
		private String hd;


//		"id": "113287812491903313716",
//				"email": "<EMAIL>",
//				"verified_email": true,
//				"name": "한선호",
//				"given_name": "선호",
//				"family_name": "한",
//				"link": "https://plus.google.com/113287812491903313716",
//				"picture": "https://lh4.googleusercontent.com/-ege-44O569o/AAAAAAAAAAI/AAAAAAAAAH0/S-cR9HU9zTU/photo.jpg",
//				"gender": "male",
//				"locale": "ko",
//				"hd": "vendys.co.kr"
	}
}
