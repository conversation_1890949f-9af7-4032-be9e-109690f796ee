package com.vlocally.mealcoupon.util;

import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.net.Socket;
import java.net.URLEncoder;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Map;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;

import com.vlocally.mealcoupon.constant.Errors;
import com.vlocally.mealcoupon.exception.VlocallyException;
import lombok.Data;
import org.apache.commons.httpclient.Header;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.DeleteMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.json.simple.JSONObject;

@Component
public class HttpCommunicator {

	public String getHttpResponse(String url, NameValuePair[] params) throws Exception{
		
		HttpClient client = new HttpClient();
		PostMethod method = new PostMethod(url);
		
		int status = 0;
		String result = "";
		
		try {
//			NameValuePair[] parametersBody = new NameValuePair[] {
//					new NameValuePair("key1", "test"),
//					new NameValuePair("key2", "한글메세지") };
//			method.setRequestBody(parametersBody);
			method.setRequestBody(params);

			method.getParams().setParameter("http.socket.timeout", 10000);
			method.getParams().setParameter("http.protocol.content-charset","UTF-8");

//			System.out.println(method.getParams().getParameter("http.protocol.version"));
//			System.out.println(method.getParams().getParameter("http.socket.timeout"));
//			System.out.println(method.getParams().getParameter("http.protocol.content-charset"));

			status = client.executeMethod(method);
			result = method.getResponseBodyAsString();
			
//			System.out.println(status);
//			System.out.println(result);
			return result;
		} catch (Exception e) {
			throw e;
		} finally {
			method.releaseConnection();
		}

	}

    public HttpDTO doHttpResponse(RequestMethod method, String url, Header[] headers, Map<String, Object> params) throws Exception {

        HttpClient client = new HttpClient();
        HttpCommunicatorMethod request = new HttpCommunicatorMethod(method, url);

        HttpDTO response = new HttpDTO();

        try {

            if(headers != null){
                for( Header h : headers){
                    request.setRequestHeader(h);
                }
            }
            request.addRequestHeader("content-type", "application/json");

            if(params != null){
                request.setRequestEntity(new StringRequestEntity(JSONObject.toJSONString(params), "application/json", "UTF-8"));
            }

            request.getParams().setParameter("http.socket.timeout", 10000);
            request.getParams().setParameter("http.protocol.content-charset","UTF-8");

            response.setStatus(client.executeMethod(request));
            response.setBody(request.getResponseBodyAsString());

            return response;
        } catch (Exception e){
            throw new VlocallyException(Errors.General_Unknown, "외부서버와 통신 실패\n"+method.name()+" "+url);
        } finally {
            request.releaseConnection();
        }
    }

    public HttpDTO doHttpResponse(RequestMethod method, String url, Header[] headers, Object params) throws Exception {
        HttpClient client = new HttpClient();
        HttpCommunicatorMethod request = new HttpCommunicatorMethod(method, url);

        HttpDTO response = new HttpDTO();

        try {
            if(headers != null){
                for( Header h : headers){
                    request.setRequestHeader(h);
                }
            }
            request.addRequestHeader("content-type", "application/json");

            if(params != null){
                request.setRequestEntity(new StringRequestEntity(JsonUtil.toJson(params), "application/json", "UTF-8"));
            }

            request.getParams().setParameter("http.socket.timeout",30000);
            request.getParams().setParameter("http.protocol.content-charset","UTF-8");

            response.setStatus(client.executeMethod(request));
            response.setBody(request.getResponseBodyAsString());

            return response;
        } catch (Exception e){
            throw new VlocallyException(Errors.General_Unknown, "외부서버와 통신 실패\n"+method.name()+" "+url);
        } finally {
            request.releaseConnection();
        }
    }

    public HttpDTO getHttpPostResponse(String url, Header[] headers, Map<String, Object> params) throws Exception{

        HttpClient client = new HttpClient();
        PostMethod method = new PostMethod(url);

        int status = 0;
        String body = "";

        try {
            if(headers != null){
                for( Header h : headers){
                    method.setRequestHeader(h);
                }
            }
            method.addRequestHeader("content-type", "application/json");

            method.setRequestEntity(new StringRequestEntity(JSONObject.toJSONString(params), "application/json", "UTF-8"));

            method.getParams().setParameter("http.socket.timeout", 10000);
            method.getParams().setParameter("http.protocol.content-charset","UTF-8");

            status = client.executeMethod(method);
            body = method.getResponseBodyAsString();

            HttpDTO result = new HttpDTO();
            result.setStatus(status);
            result.setBody(body);

            return result;
        } catch (Exception e) {
            throw e;
        } finally {
            method.releaseConnection();
        }
    }

	public String getResponse(String host, String url, String body) throws UnknownHostException, IOException{
		
		String server = host;
		int port = 80;
		int index = host.indexOf(":");
		if (index == -1) {
			server = host;
		} else {
			server = host.substring(0, index);
			port = Integer.parseInt(host.substring(index + 1));
		}
		
		System.out.print("> connecting to http://" + server + ":" + port+ "...... ");
		Socket socket = new Socket(server, port);
		System.out.println("connected");
		InputStream in = null;
		OutputStream out = null;
        long start = System.currentTimeMillis();
        try{
            in = socket.getInputStream();
            out = socket.getOutputStream();

            PrintWriter pw = new PrintWriter(new BufferedWriter(new OutputStreamWriter(out)));

            String header = 
                "POST " + url + " HTTP/1.1\n" +
                "Accept: image/gif, image/x-xbitmap, image/jpeg, image/pjpeg, application/vnd.ms-powerpoint, application/vnd.ms-excel, application/msword, */*\n" +
                "Accept-Language: ko\n" +
                "Content-Type: application/x-www-form-urlencoded\n" +
                "Accept-Encoding: gzip, deflate\n" +
                "User-Agent: Mozilla/4.0 (compatible; MSIE 6.0b; Windows NT 5.0)\n" +
                "Host: " + host + "\n" +
                "Content-Length: " + body.getBytes().length + "\n" +
                "Connection: Keep-Alive\n" +
                "Cache-Control: no-cache\n";
            
            System.out.println("> -----------------------------------------------------------------------------");
            System.out.print(header);
            System.out.println(); // dummy line
            pw.print(header);
            pw.println(); // dummy line
            pw.flush();

            System.out.println(body);
            pw.print(body);
            pw.flush();
            System.out.println("> -----------------------------------------------------------------------------");
            System.out.println("> header & data flushed");
            System.out.println("> now, wait response .....");
            System.out.println("> -----------------------------------------------------------------------------");

            //For testing, read all data
            //System.out.println(new String(read_data(in)));
            //if ( true ) System.exit(1);
            
            int length = -1;
            //read header info
            String line = null;
            StringBuilder sb = new StringBuilder();
            while( ( line = read_line(in)) != null ) {
                System.out.println(line);
                sb.append(line);
//                if ( line.length() == 0 ) break;
//                String key = "Content-Length:";
//                int x = line.indexOf(key);
//                // If the content-length field exists in the header.
//                if ( x > -1 ) {
//                    length = Integer.valueOf(line.substring(x+key.length()+1)).intValue();
//                    //System.out.println(length);
//                }
            }
            return sb.toString();
            
//            if ( line == null ) throw new Exception("unexcepted header data format");
//            //System.out.println();
//
//            if ( length != -1 ) {
//                // (mayby) HTTP 1.1 "Content-Length" field exist in the header.
//                System.out.println(new String(read_data(in, length)));
//                
//            } else {
//                // NO "Content-Length" field exist in the header
//                String length_hex = read_line(in);
//                if ( length_hex == null ) throw new Exception("there is no HTTP body data");
//                System.out.println(length_hex);
//                // If WebSphere 3.0.2.x/3.5.x, the content-length field will be comming 
//                // with 16 radix number on the first line of BODY data.
//                // read body length for WebSphere 3.0.2.x/3.5.x
//                try{
//                    length = Integer.valueOf(length_hex.trim(),16).intValue();
//                }
//                catch(Exception e){
//                    //This means, the following data is just normal BODY data.
//                    //And length is still -1.
//                }
//                if ( length != -1 ) {
//                    // Yap. WebSphere 3.0.x, 3.5.x
//                    //System.out.println(read_line(in)); // dummy line
//                    System.out.println(new String(read_data(in,length)));
//                }
//                else {
//                    System.out.println(new String(read_data(in)));
//                }
//            }
//            System.out.println("> -----------------------------------------------------------------------------");
//            System.out.println("> end !!");
        }
        catch(Exception e){
            e.printStackTrace();
            return null;
        } finally {
            if ( in != null ) try{in.close();}catch(Exception e){}
            if ( out != null ) try{out.close();}catch(Exception e){}
            if ( socket != null ) try{socket.close();}catch(Exception e){}

            long end = System.currentTimeMillis();
            System.out.println("Elapsed Time: " + (end-start) + " ms(" + ((end-start)/1000) + " sec)");
        }
		
	}
	
	public final int INTPUTSTREAM_READ_RETRY_COUNT = 10;

    /** 
     * The <code>read_data</code> method of <code>SocketUtil</code> reads the
     * specified length of bytes from the given input stream.
     *
     * @param      in   an inputstream
     * @param      len  the number of bytes read.
     * @return     The specified number of bytes read until the end of
     *             the stream is reached.
     * @exception  Exception  if an I/O error or unexpected EOF occurs
     */
	private byte[] read_data(InputStream in, int len) throws Exception {
        java.io.ByteArrayOutputStream bout = new java.io.ByteArrayOutputStream();
        int bcount = 0;
        byte[] buf = new byte[2048];
        int read_retry_count = 0;
        while( bcount < len ) {
            int n = in.read(buf,0, len-bcount < 2048 ? len-bcount : 2048 );
            // What would like to do if you've got an unexpected EOF before
            // reading all data ?
            //if (n == -1) break;
            if ( n == -1 ) throw 
                 new java.io.IOException("inputstream has returned an unexpected EOF");

            if ( n == 0 && (++read_retry_count == INTPUTSTREAM_READ_RETRY_COUNT) )
                throw new java.io.IOException("inputstream-read-retry-count( " +
                    INTPUTSTREAM_READ_RETRY_COUNT + ") exceed !");
            if ( n != 0 ) {
                bcount += n;
                bout.write(buf,0,n);
            }
        }
        bout.flush();
        return bout.toByteArray();
    }
	
	/** 
     * The <code>read_data</code> method of <code>SocketUtil</code> reads all
     * the bytes from the given inputstream until the given input stream 
     * has not returned an EOF(end-of-stream) indicator.
     *
     * @param      in   an inputstream
     * @return     all bytes read if the end of the stream is reached.
     * @exception  Exception  if an I/O error occurs
     */
    private byte[] read_data(InputStream in) throws Exception {
        java.io.ByteArrayOutputStream bout = new java.io.ByteArrayOutputStream();
        int bcount = 0;
        byte[] buf = new byte[2048];
        int read_retry_count = 0;
        while( true ) {
            int n = in.read(buf);
            if (n == -1) break;
            if ( n == 0 && (++read_retry_count == INTPUTSTREAM_READ_RETRY_COUNT) )
                throw new java.io.IOException("inputstream-read-retry-count( " +
                    INTPUTSTREAM_READ_RETRY_COUNT + ") exceed !");
            if ( n != 0 ) {
                bcount += n;
                bout.write(buf,0,n);
            }
        }
        bout.flush();
        return bout.toByteArray();
    }
  
    /**
     * Read a line of text.  A line is considered to be terminated by a line
     * feed ('\n') or a carriage return followed immediately by a linefeed.
     *
     * @return     A String containing the contents of the line, not including
     *             any line-termination characters, or null if the end of the
     *             stream has been reached
     *
     * @exception  IOException  If an I/O error occurs
     */
    private String read_line(InputStream in) throws Exception {
        java.io.ByteArrayOutputStream bout = new java.io.ByteArrayOutputStream();
        boolean eof = false;
        while( true ) {
            int b = in.read();
            if (b == -1) { eof = true; break;}
            if ( b != '\r' && b != '\n' ) bout.write((byte)b);
            if (b == '\n') break;
        }
        bout.flush();
        if ( eof && bout.size() == 0 ) return null; 
        //Or return ""; ? what's fit for you?
        return bout.toString();
    }

    public HttpDTO deleteHttpResponse(String url, NameValuePair[] params, Header[] headers) throws IOException {
        HttpClient client = new HttpClient();
        String url_param = url+"?";
        for(NameValuePair n : params){
            url_param += URLEncoder.encode(n.getName(), "UTF-8")+"="+URLEncoder.encode(n.getValue(), "UTF-8")+"&";
        }
        DeleteMethod method = new DeleteMethod(url_param);

        int status = 0;
        String result = "";

        try {
            if(headers != null){
                for( Header h : headers){
                    method.setRequestHeader(h);
                }
            }

            method.getParams().setParameter("http.socket.timeout", 10000);
            method.getParams().setParameter("http.protocol.content-charset","UTF-8");

            status = client.executeMethod(method);
            result = method.getResponseBodyAsString();

            HttpDTO resultDto = new HttpDTO();
            resultDto.setStatus(status);
            resultDto.setBody(result);

            return resultDto;
        } catch (Exception e) {
            throw e;
        } finally {
            method.releaseConnection();
        }
    }

    public Header getCustomerServceHeader(String userId) {
        HashMap<String, String> payload = new HashMap<>(3);
        payload.put("client","AdminAPI");
        payload.put("userId", userId);
        payload.put("version", "1.0");
        String customerHeader = JSONObject.toJSONString(payload); // payload json 화
        return new Header("X-Customer", customerHeader);
    }

    @Data
    public static class HttpDTO {
        private int status = 0;
        private String body;
    }

}
