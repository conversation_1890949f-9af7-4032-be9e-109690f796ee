package com.vlocally.mealcoupon.payment;

import org.springframework.web.servlet.ModelAndView;

public interface PaymentService<T extends Deal> {

	public ModelAndView getInitParamsPC(T deal) throws Exception;
	
	public ModelAndView getInitParamsMobile(T deal) throws Exception;

	/**
	 * 동기식 처리방법, 마지막 결제확인 루틴
	 * @return
	 */
	public boolean setPaymentConfirm(T deal);
	
	public void setPaymentCancel();
	
	/**
	 * 비동기시 호출 결과 처리
	 */
	public void setPaymentResult(T result);
}
