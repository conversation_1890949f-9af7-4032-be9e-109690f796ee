package com.vlocally.mealcoupon.dao;

import org.mybatis.spring.support.SqlSessionDaoSupport;
import org.springframework.stereotype.Repository;

import com.vlocally.mealcoupon.vo.CompanymenuprivilegeExample;
import com.vlocally.mealcoupon.vo.CompanymenuprivilegeKey;
import com.vlocally.mealcoupon.vo.CompanyprivilegeKey;
import com.vlocally.mealcoupon.vo.DiscountprivilegeKey;

@Repository
public class CompanyPrivilegeDAO extends SqlSessionDAO{

	public void insert(CompanyprivilegeKey key) {
		getSqlSession().insert("CompanyprivilegeMapper.insert", key);
	}

	public int delete(CompanyprivilegeKey key) {
		return getSqlSession().delete("CompanyprivilegeMapper.deleteByPrimaryKey", key);
	}

	public void insert(CompanymenuprivilegeKey key){
		getSqlSession().insert("CompanymenuprivilegeMapper.insert", key);
	}
	
	public int delete(CompanymenuprivilegeKey key){
		return getSqlSession().delete("CompanymenuprivilegeMapper.deleteByPrimaryKey", key);
	}
	
	public void insert(DiscountprivilegeKey key) {
		getSqlSession().insert("DiscountprivilegeMapper.insert", key);
	}

	public int delete(DiscountprivilegeKey key) {
		return getSqlSession().delete("DiscountprivilegeMapper.deleteByPrimaryKey", key);
	}
}
