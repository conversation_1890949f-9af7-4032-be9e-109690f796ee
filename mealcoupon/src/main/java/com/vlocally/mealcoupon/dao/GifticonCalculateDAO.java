package com.vlocally.mealcoupon.dao;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.session.RowBounds;
import org.mybatis.spring.support.SqlSessionDaoSupport;
import org.springframework.stereotype.Repository;

import com.vlocally.mealcoupon.vo.Gifticoncalculate;
import com.vlocally.mealcoupon.vo.GifticoncalculateExample;

@Repository
public class GifticonCalculateDAO extends SqlSessionDAO{

	public void insert(Gifticoncalculate calc) {
		getSqlSession().insert("GifticoncalculateMapper.insertSelective", calc);
	}
	
	public int update(Gifticoncalculate calc) {
		return getSqlSession().update("GifticoncalculateMapper.updateByPrimaryKeySelective", calc);
	}
	
	public Date selectLastEndCalculateDate(int vender) {
		return (Date) getSqlSession().selectOne("GifticoncalculateMapper.selectLastEndCalculateDate", vender);
	}

	public Gifticoncalculate select(String calcid) {
		return (Gifticoncalculate) getSqlSession().selectOne("GifticoncalculateMapper.selectByPrimaryKey", calcid);
	}

	public int count(GifticoncalculateExample ex) {
		return (Integer) getSqlSession().selectOne("GifticoncalculateMapper.countByExample", ex);
	}

	@SuppressWarnings("unchecked")
	public List<Gifticoncalculate> select(GifticoncalculateExample ex, RowBounds rb) {
		return getSqlSession().selectList("GifticoncalculateMapper.selectByExample", ex, rb);
	}
	
}
