package com.vlocally.mealcoupon.dao;

import com.vlocally.mealcoupon.vo.Mealpolicy;
import com.vlocally.mealcoupon.vo.MealpolicyExample;
import org.mybatis.spring.support.SqlSessionDaoSupport;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

/**
 * Created by ttobii on 2015. 10. 20..
 */
@Repository
public class MealPolicyDAO extends SqlSessionDAO{


    public void insert(Mealpolicy policy) {
        getSqlSession().insert("MealpolicyMapper.insertSelective", policy);
    }

    public int update(Mealpolicy policy) {
        return getSqlSession().update("MealpolicyMapper.updateByPrimaryKeySelective2", policy);
    }

    @Deprecated
    public List<Mealpolicy> getList(MealpolicyExample ex){
        return getSqlSession().selectList("MealpolicyMapper.selectByExample", ex);
    }

    public List<HashMap<String, Object>> getListByGroup(Long groupidx) {
        return getSqlSession().selectList("MealpolicyMapper.selectByGroup", groupidx);
    }

    public List<Mealpolicy> getListByGroupMealpolicy(Long groupidx) {
        return getSqlSession().selectList("MealpolicyMapper.selectByGroup", groupidx);
    }

    public List<HashMap<String, Object>> getListByUser(String uid) {
        return getSqlSession().selectList("MealpolicyMapper.selectByUserid", uid);
    }
}
