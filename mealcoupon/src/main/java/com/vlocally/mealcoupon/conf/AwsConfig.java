package com.vlocally.mealcoupon.conf;

import com.amazonaws.services.cloudfront.AmazonCloudFront;
import com.amazonaws.services.cloudfront.AmazonCloudFrontClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;

@Configuration
public class AwsConfig {

    @Value("${aws.s3.access}")
    private String accessKey;
    @Value("${aws.s3.secret}")
    private String secretKey;
    @Value("${aws.s3.region}")
    private String region;
    @Value("${vendys.env}")
    private String env;

    @Bean
    public AmazonS3 initializeAmazon() {
        if ("production".equals(this.env)) {
            return AmazonS3ClientBuilder.standard()
                .withRegion(Regions.fromName(region))
                .build();
        } else {
            AWSCredentials credentials = new BasicAWSCredentials(this.accessKey, this.secretKey);
            return AmazonS3ClientBuilder.standard()
                .withRegion(Regions.fromName(region))
                .withCredentials(new AWSStaticCredentialsProvider(credentials))
                .build();
        }
    }

    @Bean
    public AmazonCloudFront initializeCloudFront() {
        if("production".equals(this.env)){
            return AmazonCloudFrontClientBuilder.standard()
                    .withRegion(Regions.fromName(region))
                    .build();
        }else{
            AWSCredentials credentials = new BasicAWSCredentials(this.accessKey, this.secretKey);
            return AmazonCloudFrontClientBuilder.standard()
                    .withRegion(Regions.fromName(region))
                    .withCredentials(new AWSStaticCredentialsProvider(credentials))
                    .build();
        }
    }
}
