package com.vlocally.mealcoupon.controller.admin.dto;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;

@Getter
@Setter
@NoArgsConstructor(access = AccessLevel.PROTECTED)
public class SettleManagerRequest {
    @NotBlank(message = "정산 담당자명은 필수 값 입니다.")
    private String settleManagerName;
    @NotBlank(message = "정산 담당자 메일은 필수 값 입니다.")
    private String settleManagerEmail;
    @NotBlank(message = "정산 담당자 번호는 필수 값 입니다.")
    private String settleManagerPhone;
}
