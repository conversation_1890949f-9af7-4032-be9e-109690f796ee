package com.vlocally.mealcoupon.controller.admin.entity;

import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.Data;

@Data
public class StoreBlackListRequestDto {
    @NotNull(message = "companyId 는 필수 입력 입니다.")
    private String companyId;
    @NotNull(message = "groupIdx 는 필수 입력 입니다.")
    private Long groupIdx;
    private List<Long> groupIdxList;
    @NotEmpty(message = "storeIdList 는 필수 입력 입니다.")
    private List<String> storeIdList;
}
