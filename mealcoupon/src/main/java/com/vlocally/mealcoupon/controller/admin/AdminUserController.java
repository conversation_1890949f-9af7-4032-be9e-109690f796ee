package com.vlocally.mealcoupon.controller.admin;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;

import com.vlocally.mealcoupon.controller.admin.dto.SubtractDaejangPointRequest;
import com.vlocally.mealcoupon.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.vlocally.mealcoupon.constant.InitProperties;
import com.vlocally.mealcoupon.constant.db.CaptainPaymentGrade;
import com.vlocally.mealcoupon.constant.db.CorpAccessAuthority;
import com.vlocally.mealcoupon.constant.db.UserLevel;
import com.vlocally.mealcoupon.constant.db.UserStatusV2;
import com.vlocally.mealcoupon.domain.master.UserTO;
import com.vlocally.mealcoupon.domain.master.UserTO.ListSearchQuery;
import com.vlocally.mealcoupon.exception.VlocallyException;
import com.vlocally.mealcoupon.service.entity.AuthApiDto.Type;
import com.vlocally.mealcoupon.service.ezwel.EzwelService;
import com.vlocally.mealcoupon.util.DateUtil;
import com.vlocally.mealcoupon.util.JSONResultBuilder;
import com.vlocally.mealcoupon.util.PageUtil;
import com.vlocally.mealcoupon.vo.AdminUser;
import com.vlocally.mealcoupon.vo.Company;
import com.vlocally.mealcoupon.vo.Companylimit;
import com.vlocally.mealcoupon.vo.User;
import com.vlocally.mealcoupon.vo.User.Status;
import com.vlocally.mealcoupon.vo.UserExample;
import com.vlocally.mealcoupon.vo.UserExample.Criteria;
import jxl.read.biff.BiffException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Controller
@RequestMapping(value="mealcoupon/admin/user")
public class AdminUserController {
	
	@Autowired private UserService userService;
	@Autowired private CategoryService cateService;
	@Autowired private CompanyDivisionService divService;
	@Autowired private InfoHistoryService infoHistoryService;
	@Autowired private CompanyService companyService;
	@Autowired private OrganizationService orgService;
	@Autowired private OfficeService officeService;
	@Autowired private MealGroupService mgroupService;
	@Autowired private MealPointLogService mpService;
	@Autowired private InitProperties initProp;
	@Autowired private AuthService authService;
	@Autowired private BarCodeService barCodeService;
	@Autowired private CaptainPayService captainPayService;
	@Autowired private ThirdPartyConsentService thirdPartyConsentService;
	@Autowired private EzwelService ezwelService;
	@Autowired private CaptainPointService captainPointService;

	/**
	 * 사용자 관리 뷰
	 * @return
	 */
	@RequestMapping(value="")
	public ModelAndView user(){
		ModelAndView mo = new ModelAndView("admin/user/user");
		mo.addObject("userlevel", UserLevel.values());
		mo.addObject("userStatus", UserStatusV2.values());
		mo.addObject("corpAccessAuthority", CorpAccessAuthority.values());
		mo.addObject("captainPaymentGrade", CaptainPaymentGrade.values());
		return mo;
	}
	
	/**
	 * 새로운 사용자 추가하기 뷰
	 * @return
	 */
	@RequestMapping(value="add.vm")
	public ModelAndView addUser(){
		ModelAndView mo = new ModelAndView();
		mo.setViewName("admin/user/addUser");
		return mo;
	}

    /**
	 * 새로운 사용자 추가하기
	 * @param req
	 * @param user
	 * @return
	 * @throws IOException 
	 * @throws VlocallyException 
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value="add.json", method = RequestMethod.POST)
	public @ResponseBody Object addUserJson(
			HttpSession session,
			HttpServletRequest req,
			User user,
			@RequestParam(required = false) Long officeIdx
			) throws Exception {

		AdminUser admin = PageUtil.getAdminSessionInfo(req, true);

		String token = (String) session.getAttribute("Authorization");

		User userInfo = this.userService.insert(token, user, officeIdx, admin);
		HashMap<String, Object> resultMap = new HashMap<>();
		if (!ObjectUtils.isEmpty(userInfo)) {
			resultMap.put("status", "1");
			resultMap.put("uid", userInfo.getUid());
		}
		return resultMap;
	}

    @RequestMapping(value="excelPreview", method = RequestMethod.POST)
    public @ResponseBody Object addUserForExcel_json(
            @RequestParam("exfile") MultipartFile exfile,
            HttpServletRequest req) {

        File file = new File(initProp.getExcelStorePath()+"/"+System.currentTimeMillis()+"."+exfile.getName());

        try {
            exfile.transferTo(file);
            List<User> result = userService.setUsersForExcel(file, null, true);
            file.delete();
            return JSONResultBuilder.buildResult(null, result, 1);
        } catch (IllegalStateException e) {
            return JSONResultBuilder.buildResult(null, 0, "파일 처리중 에러(State)\n"+e.getMessage());
        } catch (IOException e) {
            return JSONResultBuilder.buildResult(null, 0, "파일 처리중 에러(IO)\n"+e.getMessage());
        } catch (BiffException e) {
            return JSONResultBuilder.buildResult(null, 0, "엑셀 로드중 에러(Biff)\n"+e.getMessage());
        } catch (VlocallyException e) {
            return JSONResultBuilder.buildResult(null, 0, e.getMessage());
        }
    }

    @RequestMapping(value="excel", method = RequestMethod.POST)
    public @ResponseBody Object addUserForExcel_json(
            @RequestParam("exfile") MultipartFile exfile,
            @RequestParam("comid") String comid,
            @RequestParam("defaultpw") String defaultpw,
			@RequestParam("birthpw") boolean birthpw,
            HttpServletRequest req) throws VlocallyException {

        File file = new File(initProp.getExcelStorePath()+"/"+System.currentTimeMillis()+"."+exfile.getName());

        try {
            exfile.transferTo(file);
            List<User> result = userService.setUsersForExcel(file, comid, false);

            // 비밀번호 변경

			Map<String, String> pwMap = new HashMap<>();
			for (User user : result) {
				String pw = defaultpw;
				if (birthpw) {
					pw = user.getBirthday()+"";
				}
				pwMap.put(user.getUid(), pw);
			}
			this.authService.setPasswordReset(pwMap, Type.USER);

            file.delete();
            //result = result.replace("\n", "<br>");
            return JSONResultBuilder.buildResult(null, result, 1);
        } catch (IllegalStateException e) {
            return JSONResultBuilder.buildResult(null, 0, "파일 처리중 에러(State)\n"+e.getMessage());
        } catch (IOException e) {
            return JSONResultBuilder.buildResult(null, 0, "파일 처리중 에러(IO)\n"+e.getMessage());
        } catch (BiffException e) {
            return JSONResultBuilder.buildResult(null, 0, "엑셀 로드중 에러(Biff)\n"+e.getMessage());
        } catch (VlocallyException e) {
            return JSONResultBuilder.buildResult(null, 0, e.getMessage());
        }
    }

	
	/**
	 * 이름으로 사용자 조회하기
	 * @param req
	 * @param name
	 * @param page
	 * @param pageRow
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@RequestMapping(value="inquiry.json")
	public @ResponseBody HashMap userList(
			HttpServletRequest req,
			String name,
			String comid,
			String phone,
			Integer level,
			String division,
			String position,
			@DateTimeFormat(pattern="yyyy-MM-dd") Date birthstart,
			@DateTimeFormat(pattern="yyyy-MM-dd") Date birthend,
			@RequestParam("page") int page,
			@RequestParam("pageRow") int pageRow
			){
		HashMap<String,Integer> result = PageUtil.genListInfo(page, pageRow);
		HashMap rr = result;
		UserExample userExample = new UserExample();
		Criteria cri = userExample.createCriteria();
		cri.andIsDormant(false);
		if(name != null && name.compareTo("") != 0){cri.andNameLike("%" + name + "%");}
		if(phone != null && !phone.equals("")){cri.andCellphoneLike("%" + phone + "%");}
		if(comid!=null&&!comid.equals("")){cri.andComidEqualTo(comid);}
		if(level != null ){cri.andLevelEqualTo(level.byteValue());}
		if(division !=null && !division.equals("")){cri.andDivisionEqualTo(division);}
		if(position !=null && !position.equals("")){cri.andPositionEqualTo(position);}
		if(birthstart!=null){cri.andBirthdayGreaterThanOrEqualTo(birthstart);}
		if(birthend!=null){cri.andBirthdayLessThanOrEqualTo(birthend);}
		userExample.setOrderByClause("comid ASC, level DESC, name ASC, joindate DESC");
		return JSONResultBuilder.buildResult(rr, userService.getUsers(userExample, result), 1);
	}

	/**
	 * 사용자 세부 정보 보기 뷰
	 * @param req
	 * @param uid
	 * @return
	 */
	@RequestMapping(value="detail.vm")
	public ModelAndView userDetail(
			HttpServletRequest req,
			String uid
			) throws VlocallyException {

		AdminUser info = PageUtil.getAdminSessionInfo(req, true);

		ModelAndView mo = new ModelAndView();
		User user = this.userService.getUser(uid);
		mo.addObject("isConsentThirdParty", this.thirdPartyConsentService.isConsentThirdParty(uid));
		String tableNameByAdminModifyLog = "User";
		 if (user.getIsDormant() == 1 && user.getStatus() != Status.WITHDRAW) { // 휴면
			mo.setViewName("admin/user/dormantUserDetail");
		} else if (user.getStatus() == Status.WITHDRAW) { // 탈퇴
			 boolean isRestore = true;
			 if (!ObjectUtils.isEmpty(user.getWithdrawDate()) &&  DateUtil.daysBetween(user.getWithdrawDate()) >= 30) {
				 isRestore = false;
			 }
			 mo.addObject("isRestore", isRestore);
			 mo.setViewName("admin/user/withdrawUserDetail");
		} else {
			mo.setViewName("admin/user/userDetail");
		}
		mo.addObject("user", user);
		mo.addObject("modifyLog", this.userService.readUserModifyLogByOne(tableNameByAdminModifyLog, user.getUid()));
		mo.addObject("status", this.infoHistoryService.getStatusMessage(uid));
		Companylimit companylimit = this.companyService.getLimit(user.getComid());
		mo.addObject("companylimit", companylimit);
		mo.addObject("marketingAgreeInfo", this.userService.getUserMarketingReeceiveAgreeInfo(uid));

		if (!ObjectUtils.isEmpty(user)) {
			Company company = this.companyService.getCompany(user.getComid());
			if (ObjectUtils.isEmpty(company)) { // 탈퇴 회원이고 복구 불가일 경우
				mo.addObject("companyVersion", null);
				return mo;
			}
			mo.addObject("companyVersion", company.getVersion());
			if ("v1".equals(company.getVersion())) {
				mo.addObject("organization", this.orgService.getOrganizationByCompany(info.getAdminId(), user.getComid()));
				mo.addObject("officeIdx", this.officeService.getUserOfficeRelationByUser(user.getUid()).get(0).getOfficeIdx());
				mo.addObject("userOfficeRelation", this.officeService.getUserOfficeRelationByUser(user.getUid()));
				if(user.getOrgCode()!=null){
					mo.addObject("organizationPath", this.orgService.getOrganizationPath(info.getAdminId(), user.getComid(), user.getOrgCode()));
				}
			} else {
				mo.addObject("divisionlist", this.divService.selectForCompany(user.getComid()));
			}
			mo.addObject("catelist", this.cateService.getUserCate(user.getComid(), null));
			if(companylimit.getIsmealgroup()){
				HashMap<String, Integer> mealListinfo = PageUtil.genListInfo(1, 1000);
				HashMap<String, Object> mealParam = new HashMap<>(){{
					put("comid", companylimit.getComid());
					put("groupType", "MEAL");
				}};
				mo.addObject("mealgroups", this.mgroupService.getGroupTypeList(mealParam, mealListinfo));
			}

			HashMap<String, Integer> welfareListinfo = PageUtil.genListInfo(1, 1000);
			HashMap<String, Object> welfareParam = new HashMap<>(){{
				put("comid", company.getComid());
				put("groupType", "WELFARE");
			}};

			mo.addObject("welfareGroups", this.mgroupService.getGroupTypeList(welfareParam, welfareListinfo));
			mo.addObject("welfarePoints", this.mpService.getUserCurrentWelfarePoint(uid));
			mo.addObject("mealpoints", this.mpService.getUserCurrentPoint(uid));
			mo.addObject("barCodeIsActive", this.barCodeService.isActiveBarCodeStaticSearch(uid));
			mo.addObject("userCardCount", this.captainPayService.readUserCreditCardCount(uid));
			mo.addObject("userCardList", this.captainPayService.readUserCreditCardList(uid));
		}
		return mo;
	}

	@RequestMapping(value = "changeStatus", method = RequestMethod.POST)
	@ResponseBody
	public HashMap changeStatus(HttpServletRequest req, @RequestParam String uid, @RequestParam Status status) throws VlocallyException {

		AdminUser admin = PageUtil.getAdminSessionInfo(req, true);

		this.userService.setStatus(admin, uid, status);

		return JSONResultBuilder.buildResult(null, 1, "상태 변경 완료");
	}
	
	/**
	 * 회원 탈퇴 하기
	 * @param req
	 * @param uid
	 * @return
	 * @throws VlocallyException
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value="delete.json", method = RequestMethod.DELETE)
	public @ResponseBody HashMap deleteUser(HttpServletRequest req, String uid) throws VlocallyException {

		AdminUser admin = PageUtil.getAdminSessionInfo(req, true);

		this.userService.setStatus(admin, uid, Status.WITHDRAW);

		return JSONResultBuilder.buildResult(null, 1, "탈퇴 성공");
	}

	/**
	 * 고객사 회원 일괄 탈퇴
	 * @param req
	 * @param comId
	 * @return
	 * @throws VlocallyException
	 */
	@SuppressWarnings("rawtypes")
	@RequestMapping(value="deleteAll.json")
	public @ResponseBody HashMap deleteAllUserByComId(HttpServletRequest req, String comId) throws VlocallyException {

		AdminUser admin = PageUtil.getAdminSessionInfo(req, true);

		this.userService.setWithdrawAllUserByComId(admin, comId);

		return JSONResultBuilder.buildResult(null, 1, "일괄 탈퇴 성공");
	}

	@RequestMapping(value="isValidEmail", method=RequestMethod.GET)
	public @ResponseBody HashMap getIsValidEmail(
			String email,
			String exclude
	){
		return JSONResultBuilder.buildResult(null, userService.getEmailAvailable(email, exclude), 1);
	}

	@RequestMapping(value="isValidSignId", method=RequestMethod.GET)
	public @ResponseBody HashMap getIsValidSignId(
			String signId,
			String exclude
	){
		return JSONResultBuilder.buildResult(null, userService.getSignIdAvailable(signId, exclude), 1);
	}

	/**
	 * 사용자 정보 수정하기
	 */
	@RequestMapping(value="modify.json", method = RequestMethod.PUT)
	public @ResponseBody HashMap modifyUserJson(@RequestBody User user, HttpServletRequest req) throws Exception {

		AdminUser info = PageUtil.getAdminSessionInfo(req, true);

		String token = (String) req.getSession().getAttribute("Authorization");

		this.userService.updateForAdmin(user, info, token);

        if(StringUtils.hasText(user.getPassword())) {
            this.authService.setPasswordReset(user.getUid(), null, user.getPassword(), Type.USER);
        }

		return JSONResultBuilder.buildResult(null, 1, "수정 성공");
	}

	@RequestMapping(value="dormant/modify.json", method = RequestMethod.PUT)
	public @ResponseBody HashMap modifyDormantUserJson(@RequestBody User user, HttpServletRequest req) throws Exception {

		AdminUser info = PageUtil.getAdminSessionInfo(req, true);

		this.userService.updateDormantUserForAdmin(user, info);

		if(StringUtils.hasText(user.getPassword())) {
			this.authService.setPasswordReset(user.getUid(), null, user.getPassword(), Type.USER);
		}

		return JSONResultBuilder.buildResult(null, 1, "수정 성공");
	}


	@RequestMapping(value="setPasswd", method = RequestMethod.PUT)
	public @ResponseBody HashMap setPassword(
			String uid,
			String password,
            HttpServletRequest req
	) throws VlocallyException {

		if (StringUtils.hasText(password)) {
		    this.authService.setPasswordReset(uid, null, password, Type.USER);
		    User param = new User();
		    param.setUid(uid);
		    param.setPasswordreset("N");
		    this.userService.updateBySystem(param);
        }

		return JSONResultBuilder.buildResult(null, 1, "수정 성공");
	}
	
	/**
	 * 내 정보 보기 뷰
	 * @param req
	 * @return
	 */
	@RequestMapping(value="myinfo.vm")
	public ModelAndView myinfo(
			HttpServletRequest req
			){
		HttpSession session = req.getSession(false);
		AdminUser loginInfo = (AdminUser) session.getAttribute("loginAdminInfo");
		
		ModelAndView mo = new ModelAndView();
		mo.addObject("user", loginInfo);
		mo.setViewName("admin/myinfo/myinfo");
		return mo;
	}

	@RequestMapping(value={"search", "inquiry", "dormant/inquiry", "withdraw/inquiry"}, method= RequestMethod.GET)
	public @ResponseBody Object searchUser(
			ListSearchQuery query,
			HttpServletRequest req
		) {
		String getUri = req.getRequestURI();
		List<UserTO.User> list = this.userService.getUserSearch(query, getUri);
        HashMap<String, Object> result = new HashMap<>();
		result.put("page",query.getPage());
		result.put("pageRow",query.getPageRow());
		result.put("total", query.getTotalCount());
		result.put("corpAccessAuthority", CorpAccessAuthority.values());
		return JSONResultBuilder.buildResult(result, list, 1);
	}

	/*
	* 바코드 재발급
	* */
	@RequestMapping(value="/barcode/reissuance", method = RequestMethod.PUT)
	public @ResponseBody Object setBarCodeReissuance(String uid){

		Boolean result = this.barCodeService.setBarCodeReissuance(uid);
		String message = "바코드재발급 실패";
		int status = 0;
		if(result) {
			message = "바코드 재발급 완료";
			status = 1;
		}

		return JSONResultBuilder.buildResult(null, status, message);
	}

	/*
	* 이지웰 회원탈퇴
	* */
	@RequestMapping(value="/ezwel/{userId}", method = RequestMethod.PUT)
	public @ResponseBody Object ezwelUserWithdraw(@PathVariable("userId") String userId){
		return JSONResultBuilder.buildResult(null, 1, this.ezwelService.ezwelUserWithdraw(userId));
	}

	@RequestMapping(value = "/daejang-point", method = RequestMethod.POST)
	public @ResponseBody Object subtractDaejangPoint(
			@RequestBody @Valid SubtractDaejangPointRequest request
	) {
		captainPointService.subtractDaejangPoint(request.getUserId(), request.getPoint());

		return JSONResultBuilder.buildResult(null, 1, "차감 완료했습니다");
	}

	/*
	 * 본인인증 필수
	 * */
	@RequestMapping(value = "/certification-required/{userId}", method = RequestMethod.PUT)
	public @ResponseBody Object userCertificationRequired(@PathVariable("userId") String userId,
			@RequestParam("certificationRequired") boolean certificationRequired) {
		return JSONResultBuilder.buildResult(null, 1,
				this.userService.userCertificationRequiredUpdate(userId, certificationRequired));
	}
}