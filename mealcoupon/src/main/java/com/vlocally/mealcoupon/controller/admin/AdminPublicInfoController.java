package com.vlocally.mealcoupon.controller.admin;

import java.util.HashMap;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.vlocally.mealcoupon.service.InfoService;
import com.vlocally.mealcoupon.util.JSONResultBuilder;
import com.vlocally.mealcoupon.vo.Term;

@Controller
@RequestMapping(value="mealcoupon/admin/publicInfo")
public class AdminPublicInfoController {

	@Autowired
	private InfoService infoService;
	
	/**
	 * 전체 정보 관리 뷰
	 * @param req
	 * @return
	 */
	@RequestMapping(value="index.vm")
	public ModelAndView user(
			HttpServletRequest req
			){
		
		List<Term> list = infoService.getInfos();
		
		ModelAndView mo = new ModelAndView();
		mo.setViewName("admin/publicInfo/info");
		for(Term tt : list){
			mo.addObject(tt.getType(), tt.getContent());
		}
		return mo;
	}
	
	@RequestMapping(value="edit.json")
	public @ResponseBody HashMap<String, Object> edit(
			Term tt,
			HttpServletRequest req){
		
		int result = infoService.updateInfo(tt);
		if(result==1){
			return JSONResultBuilder.buildResult(null, 1, "수정완료");
		} else {
			return JSONResultBuilder.buildResult(null, 0, "수정실패");
		}
	}
}
