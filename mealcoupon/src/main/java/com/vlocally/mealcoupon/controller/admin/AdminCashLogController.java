package com.vlocally.mealcoupon.controller.admin;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;

import javax.servlet.http.HttpServletRequest;

import com.vlocally.mealcoupon.domain.master.CashLogTO;
import com.vlocally.mealcoupon.vo.AdminUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.vlocally.mealcoupon.constant.db.CashType;
import com.vlocally.mealcoupon.exception.VlocallyException;
import com.vlocally.mealcoupon.service.CashlogService;
import com.vlocally.mealcoupon.util.JSONResultBuilder;
import com.vlocally.mealcoupon.util.PageUtil;
import com.vlocally.mealcoupon.vo.CashlogExample;

@Controller
@RequestMapping(value="mealcoupon/admin/cashlog")
public class AdminCashLogController {

	@Autowired
	private CashlogService cashService;
	
	@RequestMapping(value="index.vm")
	public ModelAndView index(){
		
		ModelAndView mo = new ModelAndView("admin/company/cashBalance");
		mo.addObject("cashtype", CashType.values());
		return mo;
	}
	
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@RequestMapping(value="inquiry.json")
	public @ResponseBody HashMap<String, Object> inquiry(
			@RequestParam("page") int page,
			@RequestParam("pageRow") int pageRow,
			String comid,
			@DateTimeFormat(pattern="yyyy-MM-dd") Date startdate,
			@DateTimeFormat(pattern="yyyy-MM-dd") Date enddate,
			String[] cashlogtype
	){
		
		CashlogExample ex = new CashlogExample();
		CashlogExample.Criteria cri = ex.createCriteria();
		if(comid!=null&&!comid.equals("")){cri.andComidEqualTo(comid);}
		if(startdate!=null){cri.andRegdateGreaterThan(startdate);}
		if(enddate!=null){cri.andRegdateLessThan(new Date(enddate.getTime()+86400000));}
		if(cashlogtype!=null&&cashlogtype.length!=0){cri.andCausetypeIn(Arrays.asList(cashlogtype));}
		
		
		ex.setOrderByClause("regdate DESC");
		
		HashMap<String, Integer> listinfo = PageUtil.genListInfo(page, pageRow);
		HashMap result = listinfo;
		return JSONResultBuilder.buildResult(result, cashService.selectCashlog(ex, listinfo), 1);
	}

	@RequestMapping(value="inquiry", method = RequestMethod.GET)
	public @ResponseBody Object inquery(
			CashLogTO query
		){

		HashMap<String, Object> result = JSONResultBuilder.buildResult(null, cashService.selectCashlog(query), 1);
		result.put("page",query.getPage());
		result.put("pageRow",query.getPageRow());
		result.put("total", query.getTotalCount());
		return result;
	}
	
	@RequestMapping(value="add.json")
	public @ResponseBody HashMap<String, Object> addCompanyCalc(
			@RequestParam("comid") String comid,
			@RequestParam("changepoint") int changepoint,
			@RequestParam("causetype") String causetype,
			@RequestParam("cause") String cause,
			HttpServletRequest req
			) throws VlocallyException{

		AdminUser info = PageUtil.getAdminSessionInfo(req, true);
		
		String cid = cashService.setChangeCash(info.getAdminId(), info.getName(), comid, changepoint, cause, causetype, info.getAdminId());
		
		return JSONResultBuilder.buildResult(null, cid, 1);
	}
}
