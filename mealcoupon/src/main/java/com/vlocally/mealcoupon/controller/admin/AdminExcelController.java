package com.vlocally.mealcoupon.controller.admin;

import com.vlocally.mealcoupon.constant.db.ExcelContentType;
import com.vlocally.mealcoupon.domain.slave.MealCouponTO;
import com.vlocally.mealcoupon.exception.VlocallyException;
import com.vlocally.mealcoupon.service.*;
import com.vlocally.mealcoupon.service.admin.excel.AdminExcelService;
import com.vlocally.mealcoupon.util.GUIDGenerator;
import com.vlocally.mealcoupon.util.JSONResultBuilder;
import com.vlocally.mealcoupon.util.PageUtil;
import com.vlocally.mealcoupon.vo.AdminUser;
import com.vlocally.mealcoupon.vo.ExcellogExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Date;
import java.util.HashMap;

@Slf4j
@Controller
@RequestMapping(value="mealcoupon/admin/excel")
public class AdminExcelController {

	@Autowired private ExcelService excelService;
	@Autowired private ExcelV2MealCouponService excelV2MealCouponService;
	@Autowired private ExcelV2MyPointService excelV2MyPointService;
	@Autowired private ExcelV2MealCouponMemberService excelV2MealCouponMemberService;
	@Autowired private ExcelV2GifticonUseService excelV2GifticonUseService;
	@Autowired private ExcelV3CouponStatisticService excelV3CouponStatisticService;
	@Autowired private AdminExcelService adminExcelService;

	@RequestMapping(value="index.vm")
	public ModelAndView index(){
		return new ModelAndView("admin/excel/excel");
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@RequestMapping(value="inquiry.json")
	public @ResponseBody HashMap<String, Object> inquiry(
			@DateTimeFormat(pattern="yyyy-MM-dd") Date startdate,
			@DateTimeFormat(pattern="yyyy-MM-dd") Date enddate,
			@RequestParam("page") int page,
			@RequestParam("pageRow") int pageRow,
			HttpServletRequest req) throws VlocallyException {
		
		ExcellogExample ex = new ExcellogExample();
		ExcellogExample.Criteria cri = ex.createCriteria();
		AdminUser userInfo = PageUtil.getAdminSessionInfo(req, true);
		cri.andIdEqualTo(userInfo.getAdminId());
		cri.andRequesttypeEqualTo("M");
		if(startdate!=null){cri.andRequestdateGreaterThanOrEqualTo(startdate);}
		if(enddate!=null){cri.andRequestdateLessThanOrEqualTo(new Date(enddate.getTime()+86400000L));}
		
		ex.setOrderByClause("requestdate DESC");
		
		HashMap<String, Integer> listinfo = PageUtil.genListInfo(page, pageRow);
		HashMap result = listinfo;
		return JSONResultBuilder.buildResult(result, excelService.getExcellog(ex, listinfo), 1);
	}

	@RequestMapping(value="/v2/export.json")
	public @ResponseBody HashMap<String, Object> export(
			@RequestParam("contenttype") String contenttype,
			String comid,
			String sid,
			String mid,
			String uid,
			String pin,
			@RequestParam("startdate") @DateTimeFormat(pattern="yyyy-MM-dd") Date startdate,
			@RequestParam("enddate") @DateTimeFormat(pattern="yyyy-MM-dd") Date enddate,
			@RequestParam(value = "starttime", required = false) @DateTimeFormat(pattern="HH:mm:ss") Date starttime,
			@RequestParam(value = "endtime", required = false) @DateTimeFormat(pattern="HH:mm:ss") Date endtime,
			String coupontype,
            String paytype,
			Boolean calcflag,
			Integer type,
			Integer status,
			String gifticonStatus,
			Integer issueservice,
			String columns,
			String vendertype,
			Byte[] pointlogtype,
			HttpServletRequest req) throws VlocallyException{

		AdminUser info = PageUtil.getAdminSessionInfo(req, true);

		String fileid = GUIDGenerator.getRandomGUIDString();
		String fileName = fileid + ".xlsx";

		if(contenttype.equals(ExcelContentType.MealCouponUselog.type)){

			this.excelV2MealCouponService.setExcelV2MealCoupon(comid, sid, uid, startdate, enddate, starttime, endtime,
					coupontype, status, columns, vendertype,
					info.getAdminId(), info.getName(), ExcelContentType.MealCouponUselog.type, fileid, fileName, "식권사용현황");

		} else if(contenttype.equals(ExcelContentType.MyPointlog.type)) {

			this.excelV2MyPointService.setExcelV2MyPoint(comid, uid, startdate, enddate,
					pointlogtype, columns, info.getAdminId(), info.getName(),
					ExcelContentType.MyPointlog.type, fileid, fileName, "대장포인트 사용현황");

		} else if(contenttype.equals(ExcelContentType.MealCouponMemberlog.type)) {

			this.excelV2MealCouponMemberService.setExcelV2MealCouponMember(sid, comid, uid, startdate, enddate,
                    coupontype, paytype, columns, info.getAdminId(), info.getName(),
                    ExcelContentType.MealCouponMemberlog.type, fileid, fileName, "식권사용사용자현황");

        } else if(contenttype.equals(ExcelContentType.GifticonUselog.type)) {

            this.excelV2GifticonUseService.setExcelV2GifticonUse(sid, mid, uid, pin, gifticonStatus, type, issueservice,
                    startdate, enddate, calcflag, columns, info.getAdminId(), info.getName(),
                    ExcelContentType.GifticonUselog.type, fileid, fileName, "기프티콘사용내역");
        } else {
			return JSONResultBuilder.buildResult(null, 0, "무슨 내용을 출력하시겠습니까?");
		}

		return JSONResultBuilder.buildResult(null, 1, "요청 완료");
	}

	@RequestMapping(value="/v3/export.json")
	public @ResponseBody HashMap<String, Object> exportV3(
			MealCouponTO mealCouponTO,
			String columns,
			HttpServletRequest req) throws VlocallyException {

		AdminUser info = PageUtil.getAdminSessionInfo(req, true);

		String fileid = GUIDGenerator.getRandomGUIDString();
		String fileName = fileid + ".xlsx";

		this.excelV3CouponStatisticService.setExcelV3CouponStatistic(mealCouponTO, info.getAdminId(), info.getName(), columns,
				ExcelContentType.CouponStatisticslog.type, fileid, fileName, ExcelContentType.CouponStatisticslog.name);

		return JSONResultBuilder.buildResult(null, 1, "요청 완료");
	}

	@RequestMapping(value="down.vm")
	public void excelDown(
			@RequestParam("fileid")      String fileid,
			@RequestParam("serfix")      String serfix,
			@RequestParam("chk")         String chk,
			@RequestParam("requesttype") String requesttype,
			@RequestParam("password")    String password,
			@RequestParam("cause")       String cause,
			HttpSession         session,
			HttpServletRequest  req,
			HttpServletResponse res) throws VlocallyException{

		adminExcelService.excelDown(fileid, serfix, chk, requesttype, password, cause, session, req, res);
	}

	//정산>고객사목록
	@RequestMapping(value="company")
	public @ResponseBody HashMap<String, Object> excelCompany(HttpServletResponse res,HttpServletRequest req) throws VlocallyException {
		AdminUser info = PageUtil.getAdminSessionInfo(req, true);

		String fileid = GUIDGenerator.getRandomGUIDString();
		String fileName = fileid + ".xlsx";

		this.excelV3CouponStatisticService.setExcelCompany(info, fileid,fileName);
		return JSONResultBuilder.buildResult(null, 1, "요청 완료");
	}

	//정산>제휴점목록
	@RequestMapping(value="store")
	public @ResponseBody HashMap<String, Object> excelStore(HttpServletResponse res,HttpServletRequest req)
			throws VlocallyException {
		AdminUser info = PageUtil.getAdminSessionInfo(req, true);

		String fileid = GUIDGenerator.getRandomGUIDString();
		String fileName = fileid + ".xlsx";

		this.excelV3CouponStatisticService.setExcelStore(info, fileid,fileName);
		return JSONResultBuilder.buildResult(null, 1, "요청 완료");
	}


	/**
	 * 엑셀 다운로드 내역 조회
	 */
	@RequestMapping(value = "downloadLog", method = RequestMethod.GET)
	public @ResponseBody Object downloadLogList(
			@RequestParam("fileid") String fileid
	) throws VlocallyException {
		return JSONResultBuilder.buildResult(null, this.excelService.getExcelDownloadLogList(fileid), 1);
	}
}
