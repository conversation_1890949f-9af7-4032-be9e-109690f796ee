package com.vlocally.mealcoupon.service;

import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.vlocally.mealcoupon.dao.UserDAO;
import com.vlocally.mealcoupon.mapper.master.CaptainPaymentCompanyInfoMapper;
import com.vlocally.mealcoupon.service.entity.CaptainPaymentCompanyInfoDto.HistorySearchParam;
import com.vlocally.mealcoupon.service.entity.CaptainPaymentCompanyInfoDto.Result;
import com.vlocally.mealcoupon.service.entity.CaptainPaymentCompanyInfoDto.ServiceTypeSearchInfo;
import com.vlocally.mealcoupon.vo.CaptainPaymentCompanyInfo;
import com.vlocally.mealcoupon.vo.CaptainPaymentCompanyInfo.ServiceType;
import com.vlocally.mealcoupon.vo.CaptainPaymentCompanyInfo.Status;
import com.vlocally.mealcoupon.vo.CaptainPaymentCompanyInfoHst;
import com.vlocally.mealcoupon.vo.User;

@Service
public class CaptainPaymentCompanyService {
    
    @Autowired private CaptainPaymentCompanyInfoMapper captainPaymentCompanyInfoMapper;
    @Autowired private UserDAO userDAO;

    public List<Result> getCaptainPaymentInfo(String comId) {
        List<CaptainPaymentCompanyInfo> captainPaymentCompanyInfos = this.captainPaymentCompanyInfoMapper
                .selectAllByComId(comId);
        return this.generateList(captainPaymentCompanyInfos);
    }

    public Result getCaptainPaymentInfo(ServiceTypeSearchInfo serviceTypeSearchInfo) {
        CaptainPaymentCompanyInfo companyInfo = this.captainPaymentCompanyInfoMapper
                .selectByComIdAndServiceType(serviceTypeSearchInfo);
        return this.generate(companyInfo);
    }

    public Result getCaptainPaymentInfo(String comId, ServiceType serviceType) {
        ServiceTypeSearchInfo serviceTypeSearchInfo = new ServiceTypeSearchInfo();
        serviceTypeSearchInfo.setServiceType(serviceType);
        serviceTypeSearchInfo.setComId(comId);
        return this.getCaptainPaymentInfo(serviceTypeSearchInfo);
    }

    private List<Result> generateList(List<CaptainPaymentCompanyInfo> infos) {
        List<Result> list = new ArrayList<>();
        for (CaptainPaymentCompanyInfo info : infos) {
            Result generate = this.generate(info);
            list.add(generate);
        }
        return list;
    }

    private Result generate(CaptainPaymentCompanyInfo info) {
        ServiceType serviceType = info.getServiceType();

        HistorySearchParam historySearchParam = new HistorySearchParam();
        historySearchParam.setComId(info.getComId());
        historySearchParam.setServiceType(serviceType);

        List<CaptainPaymentCompanyInfoHst> historyList =
                this.captainPaymentCompanyInfoMapper.selectHistoryByComIdAndServiceType(historySearchParam);

        // 전체 이력중, 취소 이력이 있는지 검사, 현재 이력 포함
        boolean existInactiveHistory = info.getStatus() == Status.INACTIVE;
        for (CaptainPaymentCompanyInfoHst captainPaymentCompanyInfoHst : historyList) {
            if (captainPaymentCompanyInfoHst.getStatus() == Status.INACTIVE) {
                existInactiveHistory = true;
                break;
            }
        }

        Long createDate = Long.MAX_VALUE;
        Long lastUpdateDate = Long.MIN_VALUE;

        CaptainPaymentCompanyInfoHst createUserHistory = null;
        CaptainPaymentCompanyInfoHst lastUpdateUserHistory = null;

        for (CaptainPaymentCompanyInfoHst history : historyList) {

            Long historyDate = history.getHistoryDate().getTime();
            Status historyStatus = history.getStatus();
            // 맨 처음에 Active 된 날짜를 찾는다.
            if (historyStatus == Status.ACTIVE && historyDate < createDate) {
                createDate = historyDate;
                createUserHistory = history;
                continue;
            }

            // 맨 마지막으로 업데이트된 날짜를 찾는다. (일시정지 이력이 있을 때만)
            if (existInactiveHistory && historyDate > lastUpdateDate) {
                lastUpdateDate = historyDate;
                lastUpdateUserHistory = history;
            }
        }


        String lastUpdateUserName = "";
        String createUserName = "";

        if (existInactiveHistory && !ObjectUtils.isEmpty(lastUpdateUserHistory)) {
            User lastUpdateUser = this.userDAO.select(lastUpdateUserHistory.getUpdateUser());
            lastUpdateUserName = ObjectUtils.isEmpty(lastUpdateUser) ? "식권대장" : lastUpdateUser.getName();
        } else if (existInactiveHistory) { // 히스토리가 없는경우 대비.
            lastUpdateUserName = info.getUpdateUserName();
            lastUpdateDate = info.getUpdateDate().getTime();
        }

        if (ObjectUtils.isEmpty(createUserHistory)) {
            createDate = info.getCreateDate().getTime();
            createUserName = info.getCreateUserName();
        } else {
            User createUser = this.userDAO.select(createUserHistory.getCreateUser());
            createUserName = ObjectUtils.isEmpty(createUser) ? "식권대장" : createUser.getName();
        }

        Result result = new Result();
        result.setServiceType(info.getServiceType());
        result.setStatus(info.getStatus());
        result.setUpdateUserName(lastUpdateUserName);
        result.setCreateUserName(createUserName);
        result.setUpdateDate(lastUpdateDate);
        result.setCreateDate(createDate);
        result.setIsReactive(existInactiveHistory);

        return result;

    }

}
