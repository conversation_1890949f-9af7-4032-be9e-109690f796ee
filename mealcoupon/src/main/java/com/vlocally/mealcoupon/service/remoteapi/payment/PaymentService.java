package com.vlocally.mealcoupon.service.remoteapi.payment;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.AsyncRestTemplate;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import com.vlocally.mealcoupon.constant.Errors;
import com.vlocally.mealcoupon.constant.InitProperties;
import com.vlocally.mealcoupon.controller.admin.dto.CouponDetail;
import com.vlocally.mealcoupon.dao.CouponGroupDAO;
import com.vlocally.mealcoupon.dto.coupon.cancel.PartialCancelDto.UserCancelDto;
import com.vlocally.mealcoupon.exception.CommonException;
import com.vlocally.mealcoupon.service.remoteapi.common.RemoteCommonException;
import com.vlocally.mealcoupon.service.remoteapi.common.RemoteHeader;
import com.vlocally.mealcoupon.service.remoteapi.payment.entity.OfficeStoreDto;
import com.vlocally.mealcoupon.service.remoteapi.payment.entity.PaymentRemoteDto.CaptainCouponIssueRequestDto;
import com.vlocally.mealcoupon.service.remoteapi.payment.entity.PaymentRemoteDto.CaptainCouponIssueRequestDto.IssueMethod;
import com.vlocally.mealcoupon.service.remoteapi.payment.entity.PaymentRemoteDto.CaptainCouponIssueRequestDto.SystemTriggerCondition;
import com.vlocally.mealcoupon.vo.AdminUser;
import com.vlocally.mealcoupon.vo.Coupongroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.Header;
import org.json.simple.JSONObject;

/**
 * create by jangjungsu on 2018. 3. 13..
 */
@Slf4j
@Service
@Transactional
public class PaymentService {

    @Autowired
    private RestTemplate restTemplate;
    private final AsyncRestTemplate asyncRestTemplate = new AsyncRestTemplate();
    @Autowired
    private RemoteHeader remoteHeader;
    @Autowired
    private RemoteCommonException commonException;
    @Autowired
    private InitProperties initProperties;
    @Autowired
    private CouponGroupDAO couponGroupDAO;


    /**
     * 고객사 생성
     */
    public List<OfficeStoreDto.StoreResponse> getStoreOfficeRelations(String userId, Long officeIdx) {
        // Header Setting ready
        HttpHeaders httpHeaders = this.remoteHeader.getPaymentHeaders(userId);

        HttpEntity<Object> httpEntity = new HttpEntity<>(null, httpHeaders);

        Map<String, String> pathParams = new HashMap<>();
        pathParams.put("officeIdx", String.valueOf(officeIdx));

        // API Calling
        URI uri = UriComponentsBuilder
                .fromUriString(this.initProperties.getVendysServerPaymentHost() + "/relation/v1/office/{officeIdx}/store")
                .buildAndExpand(pathParams).toUri();

        ResponseEntity<List<OfficeStoreDto.StoreResponse>> responseEntity = null;
        try {
            responseEntity = this.restTemplate.exchange(uri, HttpMethod.GET, httpEntity, new ParameterizedTypeReference<>() {
            });
        } catch (HttpServerErrorException hse) {
            this.commonException.serverException(hse);
        } catch (HttpClientErrorException hce) {
            throw new CommonException(
                    Errors.Customer_Service_Api_4XX_Error, hce.getStatusCode().toString() + " => " + hce.getResponseBodyAsString()
            );
        }

        return responseEntity.getBody();
    }

    public void refundNaverpay(String orderId, String cause, String adminId) {

        HttpHeaders httpHeaders = this.remoteHeader.getPaymentHeaders(adminId);

        HttpEntity<Object> httpEntity = new HttpEntity<>(null, httpHeaders);

        Map<String, String> pathParams = new HashMap<>();
        pathParams.put("orderId", orderId);
        MultiValueMap<String, String> urlParameters = new LinkedMultiValueMap<>();
        try {
            urlParameters.add("cause", URLEncoder.encode(cause, "UTF-8"));
        } catch (UnsupportedEncodingException ignored) {
        }

        // API Calling
        URI uri = UriComponentsBuilder
                .fromUriString(this.initProperties.getVendysServerPaymentHost() + "/captainpoint/v1/order/{orderId}/naverpay")
                .queryParams(urlParameters)
                .buildAndExpand(pathParams).toUri();

        ResponseEntity<Object> responseEntity = null;
        try {
            responseEntity = this.restTemplate.exchange(
                    uri, HttpMethod.DELETE, httpEntity, Object.class);
            if (responseEntity.getStatusCode() != HttpStatus.OK) {
                throw new CommonException(Errors.MSA_INTERFACE_ERROR);
            }
        } catch (HttpServerErrorException hse) {
            this.commonException.serverException(hse);
        } catch (HttpClientErrorException hce) {
            throw new CommonException(
                    Errors.Customer_Service_Api_4XX_Error, hce.getStatusCode().toString() + " => " + hce.getResponseBodyAsString()
            );
        }
    }

    public void captainCouponIssue(String userId) {

        CaptainCouponIssueRequestDto request = new CaptainCouponIssueRequestDto();
        request.setIssueMethod(IssueMethod.USER_TRIGGER);
        request.setUserId(userId);
        request.setSystemTriggerCondition(SystemTriggerCondition.JOIN);
        HttpHeaders httpHeaders = this.remoteHeader.getPaymentHeaders(userId);
        HttpEntity<Object> httpEntity = new HttpEntity<Object>(request, httpHeaders);

        URI uri = UriComponentsBuilder.fromUriString(
                this.initProperties.getVendysServerPaymentHost() + "/captain-coupon/v1/coupons/coupon/issue")
                .build().toUri();

        try {
            this.asyncRestTemplate.exchange(uri, HttpMethod.POST, httpEntity, String.class);
        } catch (HttpServerErrorException hse) {
            this.commonException.serverException(hse);
        } catch (HttpClientErrorException hce) {
            throw new CommonException(
                    Errors.Customer_Service_Api_4XX_Error, hce.getStatusCode().toString() + " => " + hce.getResponseBodyAsString()
            );
        }
    }

    public HttpStatus deletePayRoomByAdmin(String userId, Long roomIdx) {

        HttpHeaders httpHeaders = this.remoteHeader.getPaymentHeaders(userId);
        HttpEntity<Object> httpEntity = new HttpEntity<Object>(null, httpHeaders);

        Map<String, String> pathParams = new HashMap<>();
        pathParams.put("roomIdx", roomIdx.toString());

        URI uri = UriComponentsBuilder.fromUriString(
                this.initProperties.getVendysServerPaymentHost() + "/payment/v1/admin/room/{roomIdx}")
                .buildAndExpand(pathParams).toUri();

        ResponseEntity<Object> responseEntity = null;

        try {
            responseEntity = this.restTemplate.exchange(uri, HttpMethod.DELETE, httpEntity, Object.class);
        } catch (HttpServerErrorException hse) {
            this.commonException.serverException(hse);
        } catch (HttpClientErrorException hce) {
            throw new CommonException(
                    Errors.Payment_ServiceProcess, hce.getStatusCode().toString() + " => " + hce.getResponseBodyAsString()
            );
        }

        return responseEntity.getStatusCode();
    }

    public CouponDetail getCouponDetail(String couponId) {

        Coupongroup couponGroup = this.couponGroupDAO.select(couponId);
        HttpHeaders httpHeaders = this.remoteHeader.getPaymentHeaders(couponGroup.getLeaderid());
        HttpEntity<Object> httpEntity = new HttpEntity<Object>(null, httpHeaders);

        Map<String, String> pathParams = new HashMap<>();
        pathParams.put("couponId", couponId);

        URI uri = UriComponentsBuilder.fromUriString(
                this.initProperties.getVendysServerPaymentHost() + "/payment/v1/cancel/{couponId}/confirm")
                .buildAndExpand(pathParams).toUri();

        ResponseEntity<CouponDetail> responseEntity = null;

        try {
            responseEntity = this.restTemplate.exchange(uri, HttpMethod.GET, httpEntity, CouponDetail.class);
        } catch (HttpServerErrorException hse) {
            this.commonException.serverException(hse);
        } catch (HttpClientErrorException hce) {
            throw new CommonException(
                    Errors.Payment_ServiceProcess, hce.getStatusCode().toString() + " => " + hce.getResponseBodyAsString()
            );
        }

        return responseEntity.getBody();
    }

    public void partialRefund(String couponId, UserCancelDto userCancelDto, String adminId) {

        HttpHeaders httpHeaders = this.remoteHeader.getPaymentHeaders(adminId);

        HttpEntity<Object> httpEntity = new HttpEntity<>(userCancelDto, httpHeaders);

        Map<String, String> pathParams = new HashMap<>();
        pathParams.put("couponId", couponId);

        // API Calling
        URI uri = UriComponentsBuilder
                .fromUriString(this.initProperties.getVendysServerPaymentHost() + "/payment/v2/coupon/{couponId}/partial/refund")
                .buildAndExpand(pathParams).toUri();

        ResponseEntity<Object> responseEntity = null;
        try {
            responseEntity = this.restTemplate.exchange(
                    uri, HttpMethod.PUT, httpEntity, Object.class);
            if (responseEntity.getStatusCode() != HttpStatus.OK) {
                throw new CommonException(Errors.MSA_INTERFACE_ERROR);
            }
        } catch (HttpServerErrorException hse) {
            this.commonException.serverException(hse);
        } catch (HttpClientErrorException hce) {
            throw new CommonException(
                    Errors.Customer_Service_Api_4XX_Error, hce.getStatusCode().toString() + " => " + hce.getResponseBodyAsString()
            );
        }
    }

    public void relationMarketStores(Long officeIdx, String adminId) {
        HttpHeaders httpHeaders = this.remoteHeader.getPaymentHeaders(adminId);

        HttpEntity<Object> httpEntity = new HttpEntity<>(httpHeaders);

        Map<String, String> pathParams = new HashMap<>();
        pathParams.put("officeIdx", String.valueOf(officeIdx));

        // API Calling
        URI uri = UriComponentsBuilder
                .fromUriString(this.initProperties.getVendysServerPaymentHost() + "/relation/v1/office/{officeIdx}/stores/market")
                .buildAndExpand(pathParams).toUri();

        ResponseEntity<Object> responseEntity = null;
        try {
            responseEntity = this.restTemplate.exchange(
                    uri, HttpMethod.POST, httpEntity, Object.class);
            if (responseEntity.getStatusCode() != HttpStatus.CREATED) {
                throw new CommonException(Errors.MSA_INTERFACE_ERROR);
            }
        } catch (HttpServerErrorException hse) {
            this.commonException.serverException(hse);
        } catch (HttpClientErrorException hce) {
            log.error("relationMarketStores : {}", hce.getMessage(), hce);
            throw new CommonException(
                    Errors.Customer_Service_Api_4XX_Error, hce.getStatusCode().toString() + " => " + hce.getResponseBodyAsString()
            );
        }
    }

    /**
     * 사업장 - 제휴점 연결 수정
     */
    public void updateOfficeStoreRelations(String adminId, Long officeIdx, String storeId, Map<String, Object> body) {
        HttpHeaders httpHeaders = this.remoteHeader.getPaymentHeaders(adminId);
        HttpEntity<Object> httpEntity = new HttpEntity<>(body, httpHeaders);

        Map<String, String> pathParams = new HashMap<>();
        pathParams.put("officeIdx", String.valueOf(officeIdx));
        pathParams.put("storeId", storeId);

        URI uri = UriComponentsBuilder
                .fromUriString(this.initProperties.getVendysServerPaymentHost() + "/relation/v1/office/{officeIdx}/store/{storeId}")
                .buildAndExpand(pathParams)
                .toUri();
        try {
            restTemplate.exchange(uri, HttpMethod.PUT, httpEntity, Object.class);
        } catch (HttpServerErrorException hse) {
            this.commonException.serverException(hse);
        } catch (HttpClientErrorException hce) {
            log.error("updateOfficeStoreRelations : {}", hce.getMessage(), hce);
            throw new CommonException(
                    Errors.Customer_Service_Api_4XX_Error, hce.getStatusCode().toString() + " => " + hce.getResponseBodyAsString()
            );
        }
    }
}
