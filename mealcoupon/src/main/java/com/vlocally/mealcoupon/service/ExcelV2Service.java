package com.vlocally.mealcoupon.service;

import com.vlocally.mealcoupon.constant.Errors;
import com.vlocally.mealcoupon.exception.VlocallyException;
import com.vlocally.mealcoupon.mapper.master.ExcellogV2Mapper;
import com.vlocally.mealcoupon.vo.Excellog;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Date;

@Slf4j
@Service
@Transactional
public class ExcelV2Service {

	@Autowired private ExcellogV2Mapper excellogV2Mapper;

	//엑셀로그등록
	public String insertExcelV2log(String comid, String sid, String uid, Date startdate, Date enddate, String columns,
								String vendertype, String fileName, String excelUid, long total, String fileid,
								String excelUsername, int status, String contentType, String excelMsg) {
		Excellog el = new Excellog();
		el.setFileid(fileid);
		el.setFilename(fileName);
		el.setStatus((byte)status);
		el.setRequestdate(new Date());
		el.setMakedate(new Date());
		el.setDowncount(0);
		el.setRequesttype("M");
		el.setContenttype(contentType);
		el.setStart(startdate);
		el.setEnd(enddate);
		el.setId(excelUid);
		el.setName(excelUsername);
		el.setUid(uid);
		el.setSid(sid);
		el.setMid(vendertype);
		el.setComid(comid);
		el.setRowcount((int)total);
		el.setColumns(columns);
		el.setStorage("S3");

        //엑셀 로그 기록
		this.excellogV2Mapper.insertExcelLog(el);
		log.info(el+" "+excelMsg+" 엑셀 출력 요청");
		return fileid;
	}

	//엑셀쓰기
	public byte[] excelV2Write(SXSSFWorkbook wb) throws VlocallyException {
		try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
			wb.write(baos);
			byte[] rtn = baos.toByteArray();
			return rtn;
		} catch (IOException e) {
			throw new VlocallyException(Errors.Excel_IOError, e);
		}
	}
}
