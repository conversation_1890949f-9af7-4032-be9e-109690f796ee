package com.vlocally.mealcoupon.service;

import com.vlocally.mealcoupon.constant.Errors;
import com.vlocally.mealcoupon.exception.VlocallyException;
import com.vlocally.mealcoupon.persist.MainDeepLinkPersist;
import com.vlocally.mealcoupon.service.entity.MainDeepLinkDto;
import com.vlocally.mealcoupon.service.entity.MainDeepLinkDto.TargetParamByJson;
import com.vlocally.mealcoupon.util.ConverterUtil;
import com.vlocally.mealcoupon.vo.MainDeepLink;
import com.vlocally.mealcoupon.vo.MainDeepLink.FeatureType;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

@Transactional
@Service
public class MainDeepLinkService {

    @Autowired ConverterUtil converterUtil;
    @Autowired MainDeepLinkPersist mainDeepLinkPersist;

    public void checkMainDeepLinkByMarketCategory(Integer categoryId) throws VlocallyException {
        List<MainDeepLink> v1 = this.mainDeepLinkPersist.checkMainDeepLinkByMarketCategoryV1(categoryId);
        if (!ObjectUtils.isEmpty(v1)) {
            throw new VlocallyException(Errors.LINKED_MAIN_DEEP_LINK,
                    Errors.LINKED_MAIN_DEEP_LINK.msg.replace("{name}", String.valueOf(v1.get(0).getName())));
        }

        List<MainDeepLink> v2 = this.mainDeepLinkPersist.readMainDeepLinkByV2();
        for (MainDeepLink mainDeepLink : v2) {
            TargetParamByJson targetParam =
                    this.converterUtil.toObject(mainDeepLink.getTargetParamByJson(), MainDeepLinkDto.TargetParamByJson.class);
            if (mainDeepLink.getFeatureType() == FeatureType.MARKET_HOME && targetParam.getMarketCategory().equals(categoryId)) {
                throw new VlocallyException(Errors.LINKED_MAIN_DEEP_LINK,
                        Errors.LINKED_MAIN_DEEP_LINK.msg.replace("{name}", String.valueOf(mainDeepLink.getName())));
            }
        }
    }
}
