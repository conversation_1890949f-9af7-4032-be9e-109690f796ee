package com.vlocally.mealcoupon.service.remoteapi.customer.entity;

import java.util.List;

import com.vlocally.mealcoupon.vo.User.CaptainPaymentGrade;
import com.vlocally.mealcoupon.vo.User.Grade;
import lombok.Data;


/**
 * create by jungsu on 2020. 2. 12.
 */
public class OrganizationUserRemoteDto {

    @Data
    public static class CreateUserBody {

        private String id;
        private String password;
        private String name;
        private String email;
        private String phone;
        private String code;
        private String sex;
        private String birthday;
        private Long group;
        private Long welfareGroup;
        private String position;
        private String job;
        private String employeeid;
        private Boolean active;
        private Long officeIdx;
        private Grade grade;
        private Boolean isUserAuthRelation;
        private CaptainPaymentGrade captainPaymentGrade;
        private List<Long> menuAuthIdxList;
    }

    @Data
    public static class RequestEzwelUserBody {
        private String userId;
    }
}
