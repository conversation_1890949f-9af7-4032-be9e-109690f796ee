package com.vlocally.mealcoupon.service;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.servlet.ModelAndView;

import com.vlocally.mealcoupon.constant.db.NoticeOpentype;
import com.vlocally.mealcoupon.persist.NoticePersist;
import com.vlocally.mealcoupon.service.entity.NoticeDto;
import com.vlocally.mealcoupon.service.entity.NoticeDto.NoticeCompanyResponse;
import com.vlocally.mealcoupon.vo.BoardNoticeControl;
import com.vlocally.mealcoupon.vo.Boardnotice;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional
public class NoticeService {

    @Autowired private NoticePersist noticePersist;

    /**
     * 고객사 불러오기
     */
    public List<NoticeCompanyResponse> readCompanies(NoticeDto.NoticeCompanyRequest noticeCompanyRequest) {
        if (ObjectUtils.isEmpty(noticeCompanyRequest) || ObjectUtils.isEmpty(noticeCompanyRequest.getOpentype())) {
            return null;
        }
        switch (noticeCompanyRequest.getOpentype()) {
            case serviceOption:
                return this.noticePersist.readCompaniesByCompanyOption(noticeCompanyRequest);
            case specificStore:
                return this.noticePersist.readCompaniesByStore(noticeCompanyRequest);
            case specificSupplytype:
                return this.noticePersist.readCompaniesBySupplytype(noticeCompanyRequest);
        }
        return null;
    }

    /**
     * 공지사항 설정정보 노출
     * */
    public ModelAndView selectBoardNoticeControl(Boardnotice boardnotice, ModelAndView mo) {
        BoardNoticeControl boardNoticeControl = new BoardNoticeControl();
        boardNoticeControl.setPostid(boardnotice.getPostid());
        if (NoticeOpentype.specificStore == boardnotice.getOpentype()) {
            boardNoticeControl.setControlType("store");
        }

        List<BoardNoticeControl> boardNoticeControlList = this.noticePersist.selectBoardNoticeControl(boardNoticeControl);
        if (NoticeOpentype.serviceOption == boardnotice.getOpentype()) { // 고객사의 서비스 옵션에 따른 고객사
            for (BoardNoticeControl vo : boardNoticeControlList) {
                mo.addObject(vo.getControlType(), vo.getControlParam());
            }
        } else if (NoticeOpentype.specificStore == boardnotice.getOpentype()) { // 특정 제휴식당을 이용하는 고객사
            mo.addObject("storelist", boardNoticeControlList);
        } else if (NoticeOpentype.specificSupplytype == boardnotice.getOpentype()) { // 특정 판매형태를 이용하는 고객사
            for (BoardNoticeControl vo : boardNoticeControlList) {
                mo.addObject("supplytype$$" + vo.getControlParam(), true);
            }
        }
        return mo;
    }
}
