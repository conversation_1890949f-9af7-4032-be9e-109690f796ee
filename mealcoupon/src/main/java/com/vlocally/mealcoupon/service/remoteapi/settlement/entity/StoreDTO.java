package com.vlocally.mealcoupon.service.remoteapi.settlement.entity;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;

import com.vlocally.mealcoupon.vo.Store;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;

@Data
public class StoreDTO {


    @Data
    public static class StoreCreateRequest {
        private String storeId;
        private String storeName;
        private boolean useFlag;
        private String registerId;
        private String registerName;
    }

    @Data
    public static class StoreCreateResponse {
        private Long storeSeq;
        private String storeId;
        private String registerId;
        private LocalDateTime registered;
    }


    @Data
    public static class StoreUpdateRequest {
        private String storeName;
        private String phone;
        private int level;
        private Long rootStoreSeq;
        private Long parentStoreSeq;
        private boolean useFlag;
        private String registerId;
        private String registerName;
    }


    @Data
    public static class StoreResponse {
        private Long storeSeq;
        private String storeId;
        private String storeName;
        private Long rootStoreSeq;
        private Long parentStoreSeq;
        private Long payUnitStoreSeq;
        private int level;
        private boolean useFlag;
        private boolean bizInfoFlag;
        private boolean commonSettleInfoFlag;
        private boolean settleInfoFlag;
        private Date registered;
        private String registerId;
        private Date updated;
        private String updateId;
    }


    /**
     * 제휴점 상세 정보 응답 DTO (변경건)
     * */
    @Data
    public static class StoreSettlementResponse {
        private String storeId; // 제휴점 아이디 (GUID)
        private String storeName; // 제휴점 명
        private Long storeIdx; // 제휴점 일련 번호
        private Long rootStoreIdx; // 최상위 제휴점 일련 번호
        private String rootStoreName; // 최상위 제휴점명
        private Long parentStoreIdx; // 부모 제휴점 일련 번호
        private Long payUnitStoreIdx; // 결제 단위 제휴점 일련 번호
        private Integer level; // 제휴점 계층
        private Boolean payUnitFlag; // 지급 단위 여부
        private Boolean contactFlag; // 제휴점 정산 계약 정보 유무
        private StoreDTO.StoreContact contact; // 제휴점 정산 계약 정보
        private Boolean periodFlag; //정산 정보 등록 여부
        private StoreDTO.StorePeriod period; // 정산 정보
        private Boolean bizInfoFlag; // 사업자 정보 등록 여부
        private StoreDTO.StoreBizInfo bizInfo; //사업자 정보
        private StoreDTO.StoreFeeChangeResponse feeChange; //수수료 중도 변경 정보
        private String registerId; // 등록 아이디
        private String registerDate; // 등록 일시
        private String updateId; // 갱신 아이디
        private String updateDate; // 갱신 일시
        private Boolean isStoreLoanContract; //선정산 계약 여부
    }


    @Data
    public static class StoreContact {
        private Double mealRate; // 식대 이용 요율
        private Boolean mealVatFlag; // 식대 VAT 포함 여부
        private Double myPointRate; // 대장포인트 이용 요율
        private Boolean myPointVatFlag; // 대장포인트 VAT 포함 여부
        private Double shippingRate; // 배송비 이용 요율
        private Boolean shippingVatFlag; // 배송비 VAT 포함 여부
        private Long advertiseCost; // 광고 비용
        private Boolean advertiseCostVatFlag; // 광고 비용 VAT 포함 여부
        private List<StoreDTO.OtherCost> otherCosts; // 부가 비용 항목
        private Boolean otherCostVatFlag; // 부가 비용 VAT 포함 여부
        private String accountHolder; // 예금주
        private String bankName; // 은행 명
        private String bankCode; // 은행 코드
        private String accountNo; // 계좌 번호
        private boolean accountNumberValid; //계좌번호 유효성 체크 여부
        private String bankBookFileNo; // 통장 사본 첨부 파일 번호
        private Boolean useFlag; // 사용 여부
        private String registerId; // 등록 아이디
        private String registerDate; // 등록 일시
        private String updateId; // 갱신 아이디
        private String updateDate; // 갱신 일시
    }


    @Data
    public static class OtherCost {
        private Long otherCost; // 기타비용
        private String otherCostPayTypeName; //기타 비용 지급 타입 명
        private Integer otherCostPayTypeCode; // 기타 비용 지급 타입 코드
        private String otherCostPayPeriodName; // 기타 비용 지급 주기 명
        private Integer otherCostPayPeriodCode; // 기타 비용 지급 주기 코드
        private String otherCostPayDay; // 기타 비용 지급 일
        private String otherCostCause; // 기타 비용 사유
    }

    @Data
    public static class StorePeriod {
        private Boolean selfPayFlag; // 자체 지급 여부
        private String selfPayTypeName; // 자체 지급 유형 명
        private Integer selfPayTypeCode; // 자체 지급 유형 코드
        private String selfPayDay; // 자체 지급 일
        private String selfPayDayOptionName; // 자체 지급 일 옵션 명
        private Integer selfPayDayOptionCode; // 자체 지급 일 옵션 코드

        private Boolean selfSettlePeriodFlag; // 자체 정산 주기 여부
        private String selfSettlePeriodName; // 자체 정산 주기 명
        private Integer selfSettlePeriodCode; // 자체 정산 주기 코드
        private String selfSettleStartDay; // 자체 정산 시작일
        private String selfSettleEndDay; // 자체 정산 종료일
        private String selfSettleStartDay2; // 자체 정산 시작일 02
        private String selfSettleEndDay2; // 자체 정산 종료일 02
        private String selfSettleStartDayName; // 자체 정산 시작 요일 코드
        private Integer selfSettleStartDayCode; // 자체 정산 시작 요일 코드
        private String selfSettleEndDayName; // 자체 정산 종료 요일 코드
        private Integer selfSettleEndDayCode; // 자체 정산 종료 요일 코드

        private Boolean companyIntegrateManageFlag; // 고객사 통합 관리 여부
        private Boolean evidenceOffsetFlag; // 적격증빙 상계 여부
        private Integer evidenceAgencyCode; // 대행발행 동의 여부
        private String evidenceAgencyCodeName; // 대행발행 동의 여부 명
        private String evidenceAgencyApproveDate; // 대행발행 동의 일시(점주)

        private Boolean transferOffsetFlag; // 이체 상계 여부

        private Boolean useFlag; // 사용 여부
        private String registerId; // 등록 아이디
        private String registerDate; // 등록 일시 (yyyyMMdd hhmmss)
        private String updateId; // 갱신 아이디
        private String updateDate; // 갱신 일시 (yyyyMMdd hhmmss)
    }


    @Data
    public static class StoreBizInfo {
        private String bizName; // 상호명
        private String bizSerial; // 사업자 등록 번호
        private String bizSubSerial; // 종사업자 등록 번호
        private String bizRegistrationFileNo; // 사업자 등록증 사본 첨부 파일 번호
        private String taxTypeName; // 과세 유형 명
        private Integer taxTypeCode; // 과세 유형 코드
        private String ntsTaxTypeName; // 국세청 과제 구분 명
        private Integer ntsTaxType; // 국세청 과세 구분 코드
        private String evidenceTypeName; // 적격 증빙 자료 타입 명
        private Integer evidenceTypeCode; // 적격 증빙 자료 타입 코드
        private String taxInvoiceType; // 세금계산서 유형
        private String chargeName; // 대표명
        private String address; // 주소
        private String bizCondition; // 업태
        private String bizType; // 종목
        private String phone; // 휴대폰 번호
        private String email1; // 이메일1
        private String email2; // 이메일2
        private Integer companyExcept;//고객사제외유무
        private List<String> comIdList; // 고객사제외리스트
        private String settleManagerName; // 정산담당자명
        private String settleManagerEmail; // 정산담당자 메일
        private String settleManagerPhone; // 정산담당자 번호
        private Integer taxInvoiceDateCode; // 증빙 정발행 신고일자 유형 코드
        private String taxInvoiceDateName; // 증빙 정발행 신고일자 유형 명
        private Boolean shutdownFlag; // 폐업 여부
        private String shutdownDay; // 폐업일 (yyyyMMdd)
        private String ntsShutdownStateName; // 국세청 폐업 상태 명
        private Integer ntsShutdownState; // 국세청 폐엽 상태
        private List<StoreDTO.StoreManager> managers; // (제휴점) 매니저 정보 목록
        private String barobillId; // 바로빌 회원 아이디
        private String barobillName; // 바로빌 회원 명
        private String niceId; // 나이스 회원 아이디
        private Boolean useFlag; // 사용 여부
        private String registerId; // 등록 아이디
        private String registerDate; // 등록 일시 (yyyyMMdd hhmmss)
        private String updateId; // 갱신 아이디
        private String updateDate; // 갱신 일시 (yyyyMMdd hhmmss)
    }

    @Data
    public static class StoreManager {
        private String name; // 성명
        private String phone; // 연락처
        private String email; // 이메일
    }


    @Data
    public static class UpdateStorePeriodBody {

        private String storeId; //제휴점아이디
        private Boolean selfPayFlag;
        private Integer selfPayTypeCode;
        private Integer selfPayDay;
        private Integer selfSettlePeriodOptionCode;
        private Boolean selfSettlePeriodFlag;
        private Integer selfSettlePeriodCode;
        private Integer selfSettleStartDay;
        private Integer selfSettleEndDay;
        private Integer selfSettleStartDay2;
        private Integer selfSettleEndDay2;
        private Integer selfSettleStartDayCode;
        private Integer selfSettleEndDayCode;
        private Boolean companyIntegrateManageFlag;
        private Boolean evidenceOffsetFlag;
        private Integer evidenceAgencyCode;
        private Boolean transferOffsetFlag;
        private Boolean useFlag;
        private String registerId;
        private String registerName;
    }

    @Data
    public static class UpdateStoreContactBody {
        private String storeId; //제휴점아이디

        @NotNull(message = "식대 이용 요율은 필수 값 입니다.")
        private Double mealRate;

        private Boolean mealVatFlag;

        @NotNull(message = "대장포인트 이용 요율은 필수 값 입니다.")
        private Double myPointRate;

        private Boolean myPointVatFlag;

        private Double shippingRate;
        private Boolean shippingVatFlag;

        private Long advertiseCost;

        private Boolean advertiseCostVatFlag;

        private List<UpdateOtherCostBody> otherCosts;
        private Boolean otherCostVatFlag;

        @NotBlank(message = "계좌 소유자는 필수 값 입니다.")
        private String accountHolder;

        @NotBlank(message = "은행 코드는 필수 값 입니다.")
        private String bankCode;

        @NotBlank(message = "계좌 번호는 필수 값 입니다.")
        private String accountNo;

        private boolean isAccountNumberValid;
        private String bankBookFileNo;
        private Boolean useFlag;
        private String registerId;
        private String registerName;
    }

    @Data
    public static class UpdateOtherCostBody {
        private Long otherCost;
        private Integer otherCostPayType;
        private Integer otherCostPayPeriod;
        private String otherCostPayDay;
        private String otherCostCause;
    }


    /**
     * 제휴점 사업장 정보 수정 요청 DTO
     */
    @Data
    public static class UpdateStoreBizInfoBody {
        @NotBlank(message = "제휴식당 아이디는 필수 값 입니다.")
        private String storeId; //제휴점아이디
        @NotBlank(message = "상호명은 필수 값 입니다.")
        private String bizName; // 상호명
        @NotBlank(message = "사업자 등록 번호는 필수 값 입니다.")
        private String bizSerial; // 사업자 등록 번호

        private String bizSubSerial; // 종사업자 등록 번호
        private String bizRegistrationFileNo; // 사업자 등록증 사본 첨부 번호

        @NotNull(message = "과세유형 코드는 필수 값 입니다.")
        private Integer taxTypeCode; // 과세유형 코드
        private Integer ntsTaxType; // 국세청 과세 구분 코드
        @NotNull(message = "적격증빙자료 유형 코드는 필수 값 입니다.")
        private Integer evidenceTypeCode; // 적격증빙자료 유형 코드

        @NotNull(message = "세금계산서 유형은 필수 값 입니다.")
        private String taxInvoiceType; // 세금계산서 유형

        @NotNull(message = "증빙 정발행 신고일자 유형 코드는 필수 값 입니다.")
        private Integer taxInvoiceDateCode; // 증빙 정발행 신고일자 유형 코드

        @NotBlank(message = "성명은 필수 값 입니다.")
        private String chargeName; // 성명
        @NotBlank(message = "주소는 필수 값 입니다.")
        private String address; // 주소
        @NotBlank(message = "업태는 필수 값 입니다.")
        private String bizCondition; // 업태
        @NotBlank(message = "업종은 필수 값 입니다.")
        private String bizType; // 업종

        @NotNull(message = "폐업 여부는 필수 값 입니다.")
        private Boolean shutdownFlag; // 폐업 여부
        private String shutdownDay; // 폐업 일
        private Integer ntsShutdownState; // 국세청 휴폐업 상태 코드

        private String phone;
        private String email1;
        private String email2;

        @NotBlank(message = "정산 담당자명은 필수 값 입니다.")
        private String settleManagerName;
        @NotBlank(message = "정산 담당자 메일은 필수 값 입니다.")
        private String settleManagerEmail;
        @NotBlank(message = "정산 담당자 번호는 필수 값 입니다.")
        private String settleManagerPhone;

        private String barobillId; // 바로빌 회원 아이디
        private String barobillName; // 바로빌 회원 담당자명
        private String niceId; // 나이스 회원 아이디

        private Boolean useFlag;
        private String registerId;
        private String registerName;
        //과세유형 간이 일경우
        private Integer companyExcept;//고객사제외유무
        private List<String> comIdList; // 고객사제외리스트
    }

    /**
     * 제휴점 사업자 상태 조회 DTO
     */
    @Data
    public static class StoreBizState {

        private String  bizSerial;
        private int     ntsShutdownState;      //국세청 휴폐업 상태 코드 (0:없는사업자 1:계속사업자 2:휴업자 3:폐업자)
        private String  ntsShutdownStateName;
        private String  shutdownDay;           //휴폐업 일자
        private Boolean shutdownFlag;          //휴폐업 여부 (true: 휴폐업, false: 정상)

        private int     taxType;               //벤디스 과세 유형 코드 (0:일반, 1:간이, 2:면세)
        private int     ntsTaxType;            //국세청 과세 구분 코드 (0:없는사업자 1:일반과세자 2:간이과세자 3:법인사업자 4:면세사업자 5:과특사업자 6:비영리법인)
        private String  ntsTaxTypeName;

        //사업자번호 확인시 바로빌ID 체크하여 삽입
        private BarobillInfo baroBillInfo;
    }

    @Getter
    @Setter
    public static class BarobillInfo {
        private String id;
        private String name;
    }

    @Data
    public static class Test {
        private String storeId;
        private String fileType;
        private String registerId;
        // private MultipartFile file;
    }

    /**
     * 제휴점 사업자 등록 사본 첨부 파일 목록 조회 응답 DTO
     */
    @Data
    public static class StoreAttachFileResponse {

        private String storeId; // 제휴점 아이디
        private int count; // 사업자 등록 사본 첨부 파일 카운트
        private List<AttachFile> attachFiles; // 첨부 파일 등록 이력 리스트

    }

    /**
     * 제휴점 첨부파일이력 조회 응답 DTO
     */
    @Data
    public static class AttachFile {

        private Integer fileNo;         // 첨부파일 번호
        private String  fileName;       // 첨부파일명
        private String  fileUploadName; // 업로드 파일 명
        private String  filePath;       // 첨부파일경로
        private boolean isUse;          // 사용여부

        private String registerId;      // 등록 아이디
        private String registerDate;    // 등록일시
    }


    @Data
    public static class UploadAttachFileResponse {
        private String fileNo; // 첨부파일 번호
    }

    /**
     *  사업자등록증 OCR 검증 응답 DTO
     */
    @Data
    public static class checkBizLicenseOCRResponse {
        private StoreDTO.OCRLicense   license;   //사업자 정보
        private StoreDTO.OCRClosedown closedown; //과세 구분, 휴폐업 상태 정보
        private String                documnet;  //사업자 등록증에서 읽어낸 전체 텍스트
    }

    /**
     *  OCR 응답 : 사업자 정보
     */
    @Data
    public static class OCRLicense {
        private String bizNum;   //사업자등록번호
        private String corpNum;  //법인등록번호
        private String corpName; //회사명
        private String ceoName;  //대표자명
        private String addr;     //사업장 소재지

        private String bizClass; //업종
        private String bizType;  //업태
        private String tel;      //대표전화
        private String email;    //이메일
        private String birthday; //대표자 생년월일
    }

    /**
     *  OCR 응답 : 과세 구분, 휴업/폐업 상태 정보
     */
    @Data
    public static class OCRClosedown {
        private String bizNum;            //사업자등록번호
        private String stateChangeDate;   //상태: normal-사업중, down-휴업, close-폐업, unregistered-미등록
        private String taxTypeChangeDate; //상태 변경일자: yyyyMMdd
        private String taxType;           //과세유형: normal-일반과세자, simple-간이과세자, free-면세과세자, nonProfit-비영리
        private String state;             //과세유형 변경일자: yyyyMMdd
    }

    /**
     * 제휴점 계좌 유효성 상태 정보 DTO
     */
    @Data
    public static class StoreAccountState {

        private String storeId;

        @NotBlank(message = "계좌 소유자는 필수 값 입니다.")
        private String accountHolder;

        @NotBlank(message = "은행 코드는 필수 값 입니다.")
        private String bankCode;

        @NotBlank(message = "계좌 번호는 필수 값 입니다.")
        private String accountNo;

        private String fbAccountHolder;
        private String fbResponseCode;
        private Integer certStateCode;

    }

    @Data
    public static class StoreCompanyResponse {
        private String settleTypeInfo;
        private String companySettlePeriodInfo;
        private String companyDepositInfo;
        private String storePayDay;
    }

    @Data
    public static class StoreFeeChangeResponse {
        private Long storeSeq;
        private Double serviceMealRate;
        private Boolean serviceMealVatFlag;
        private Double serviceMyPointRate;
        private Boolean serviceMyPointVatFlag;
        private String settleApplyEndYMD;
        private Date registered;
        private String registerId;
        private String registerName;
        private List<StoreCompanyFeeChangeResponse> companyFeeChangeList;
    }

    @Data
    public static class StoreCompanyFeeChangeResponse {
        private Long storeSeq;
        private Long companySeq;
        private String companyName;
        private Double serviceMealRate;
        private Boolean serviceMealVatFlag;
        private Double serviceMyPointRate;
        private Boolean serviceMyPointVatFlag;
        private String settleApplyEndYMD;
        private Boolean companyFeeExistenceFlag;
        private Boolean separateContractFlag;
        private Date registered;
        private String registerId;
        private String registerName;
    }

    @Data
    public static class StoreAccountInfoResponse {
        private Long storeSeq;
        private Double serviceMealRate;
        private Boolean serviceMealVatFlag;
        private Double serviceMyPointRate;
        private Boolean serviceMyPointVatFlag;
        private String settleApplyEndYMD;
        private Date registered;
        private String registerId;
        private String registerName;
    }

    @Data
    public static class UpdateStoreFeeChangeBody {
        @NotNull(message = "제휴점 일련번호는 필수 값 입니다.")
        private Long storeSeq;

        @NotNull(message = "식대 이용 요율은 필수 값 입니다.")
        private Double serviceMealRate;

        @NotNull(message = "마이포인트 이용 요율은 필수 값 입니다.")
        private Double serviceMyPointRate;

        @NotBlank(message = "변경 적용완료 일자는 필수 값 입니다.")
        private String settleApplyEndYMD;

        @NotBlank(message = "수정자 아이디는 필수 값 입니다.")
        private String registerId;
        private String registerName;

        private List<Long> companyFeeChangeList;

    }

    @Data
    public static class UpdateStoreCompanyFeeChangeBody {
        @NotNull(message = "제휴점 일련번호는 필수 값 입니다.")
        private Long storeSeq;

        @NotNull(message = "고객사 일련번호는 필수 값 입니다.")
        private Long companySeq;

        @NotNull(message = "식대 이용 요율은 필수 값 입니다.")
        private Double serviceMealRate;

        @NotNull(message = "마이포인트 이용 요율은 필수 값 입니다.")
        private Double serviceMyPointRate;

        @NotBlank(message = "변경 적용완료 일자는 필수 값 입니다.")
        private String settleApplyEndYMD;

        @NotBlank(message = "수정자 아이디는 필수 값 입니다.")
        private String registerId;
        private String registerName;
    }

    @Data
    public static class StoreFeeChangeHistResponse {

        private List<StoreFeeChangeResponse> storeFeeChangeHistList;
    }

    @Data
    public static class StoreCompanyFeeChangeHistResponse {

        private List<StoreCompanyFeeChangeResponse> storeCompanyFeeChangeHistList;
    }

    @Data
    public static class StoreAccountInfoListResponse{
        private List<Store> storeAccountInfoList;
        private long totalCount;
    }

    @Data
    public static class StoreSettlementListRequest {
        private List <String> storeIdList;
    }

    @Data
    public static class StoreSettlementListResponse {
        private List<StoreSettlementResponse> storeSettlementList;
    }

    @Data
    public static class CreateBarobillResponse {
        private int barobillCode;
        private String barobillMsg;
    }

    @Data
    public static class StroeCompanyExcept{
        private String storeId;
        private String comid;
        private String comname;
    }
    @Data
    public static class StroeCompanyExceptListResponse{
        private List<StroeCompanyExcept> companyExceptList;
    }
}
