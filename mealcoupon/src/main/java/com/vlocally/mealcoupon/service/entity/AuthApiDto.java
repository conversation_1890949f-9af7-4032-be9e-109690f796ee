package com.vlocally.mealcoupon.service.entity;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Created by j<PERSON>jungsu on 2018. 4. 5.
 */
public class AuthApiDto {

    @Data
    public static class LoginBody {

        private String grant_type;
        private String client_id;
        private String client_secret;
        private String username;
        private String password;
    }

    @Data
    public static class LoginResponse {

        @JsonProperty("access_token")
        private String accessToken;
        @JsonProperty("refresh_token")
        private String refreshToken;
        @JsonProperty("token_type")
        private String tokenType;
        @JsonProperty("expires_in")
        private long expiresIn;
        @JsonProperty("ver")
        private int ver;
    }

    @Data
    public static class PasswordBody {

        private Type type;
        private String targetPw;
        private String sourcePw;

    }

    /**
     * USER : 사용자, STORE : 제휴점
     */
    public enum Type {

        USER, STORE, STORE_ADMIN
    }

    @Data
    public static class MultiPasswordBody {

        private Type type;
        private List<Content> contents;

        @Data
        public static class Content {

            private String id;
            private String targetPw;
            private String sourcePw;
        }
    }

}