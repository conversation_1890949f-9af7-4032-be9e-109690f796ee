package com.vlocally.mealcoupon.service;


import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.imageio.ImageIO;

import com.vlocally.mealcoupon.constant.db.NoticeOpentype;
import com.vlocally.mealcoupon.service.entity.NoticeDto;
import com.vlocally.mealcoupon.util.*;
import com.vlocally.mealcoupon.vo.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Base64Utils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import com.vlocally.mealcoupon.constant.Errors;
import com.vlocally.mealcoupon.constant.InitProperties;
import com.vlocally.mealcoupon.constant.db.OriginType;
import com.vlocally.mealcoupon.constant.db.StoreSupplyType;
import com.vlocally.mealcoupon.controller.admin.entity.AdminModifyLogDto.UpdateHistoryRequest;
import com.vlocally.mealcoupon.controller.admin.entity.AdminModifyLogDto.UpdateHistoryResponse;
import com.vlocally.mealcoupon.dao.MenuDAO;
import com.vlocally.mealcoupon.dao.StoreAccountPrivilegeDAO;
import com.vlocally.mealcoupon.dao.StoreDAO;
import com.vlocally.mealcoupon.domain.master.StoreTO;
import com.vlocally.mealcoupon.domain.master.StoreTO.StoreAccountBizRespones;
import com.vlocally.mealcoupon.exception.CommonException;
import com.vlocally.mealcoupon.exception.VlocallyException;
import com.vlocally.mealcoupon.mapper.master.BrandMapper;
import com.vlocally.mealcoupon.mapper.master.OfficeStoreRelataionMapper;
import com.vlocally.mealcoupon.mapper.master.StoreMapper;
import com.vlocally.mealcoupon.mapper.master.StoreImageMapper;
import com.vlocally.mealcoupon.persist.StoreMainCategoryRelationPersist;
import com.vlocally.mealcoupon.service.remoteapi.settlement.StoreSettlementService;
import com.vlocally.mealcoupon.service.remoteapi.settlement.entity.StoreDTO;
import com.vlocally.mealcoupon.service.remoteapi.settlement.entity.StoreDTO.StoreCreateRequest;
import com.vlocally.mealcoupon.vo.shipping.ShippingStoreAddressRestrictDto;
import org.apache.commons.codec.Decoder;
import org.apache.commons.io.FilenameUtils;
import org.apache.log4j.Logger;

/**
 * 제휴점 정보 수정
 * <AUTHOR>
 */
@Service
@Transactional
public class StoreService {

    @Autowired private StoreDAO storeDAO;
    @Autowired private MenuDAO menuDAO;
    @Autowired private StoreAccountPrivilegeDAO privilegeDAO;
    @Autowired private FileService fileService;
    @Autowired private StoreMapper storeMapper;
    @Autowired private StoreImageMapper storeImageMapper;
    @Autowired private PrivilegeService priService;
    @Autowired private StoreSettlementService settlementStoreSettlementService;
    @Autowired private ImageService imageService;
    @Autowired private AdminModifyLogService adminModifyLogService;
    @Autowired private StoreMainCategoryRelationPersist storeMainCategoryRelationPersist;
    @Autowired private ThirdPartyCouponService thirdPartyCouponService;
    @Autowired private InitProperties initProperties;
    @Autowired private StoreSettlementService storeSettlementService;
    @Autowired private OfficeStoreRelationService officeStoreRelationService;
    @Autowired private OfficeStoreRelataionMapper officeStoreRelationMapper;
    @Autowired private BrandMapper brandMapper;
    @Autowired private BoardService boardService;

    // 썸네일 이미지 사용 판매 형태 (방문, 배달, 방문/배달, 예약)
    private final Byte[] thumbnailUsedBySupplytype = {0, 2, 3, 6, 12};

    // 기본 이미지 사용 판매 형태 (예약, 배송)
    private final Byte[] imgUsedBySupplytype = {6, 7};

    private Logger log = Logger.getLogger("modinfo");

    /**
     * 제휴점 입력
     */
    public String insert(
            Store store, MultipartFile file, MultipartFile imageThumbnailFile, AdminUser info, MultipartFile bannerFile,
            MultipartFile logoFile) {

        // 제휴점 생성
        String sid = GUIDGenerator.getRandomGUIDString();
        store.setId(sid);
        store.setSid(sid);

        if (store.getSupplytype() == StoreSupplyType.QPcon.type) {
            throw new CommonException(Errors.QPcon_NotRegist);
        }

        // 제휴점 상세 이미지, 마켓홈 메인 제휴점 이미지 관련
        if (Arrays.asList(imgUsedBySupplytype).contains(store.getSupplytype())) {
            // 제휴점 상세 이미지 체크
            if (ObjectUtils.isEmpty(file)) {
                throw new CommonException(Errors.Store_Check_Null);
            } else {
                this.checkStoreImage(file, "상세", 1440, 810);
                this.makeImage(store, file);
            }

            if (store.getSupplytype() == StoreSupplyType.SHIPPING.type) {
                // 제휴식당 리스트 이미지가 없으면 디폴트 세팅 (마켓홈 메인 내 제휴점 이미지)
                if (ObjectUtils.isEmpty(bannerFile)) {
                    store.setShippingImg(ImageUtil.getRandomDefaultShippingImg());
                }

                // 제휴점 로고
                if (!ObjectUtils.isEmpty(logoFile)) {
                    this.checkStoreImage(logoFile, "제휴점 로고", 640, 640);
                    String originPath = this.storeLogoImageUpload(store, logoFile);
                    this.enrollBrand(store, logoFile, originPath, info); // 브랜드 등록
                    store.setLogoImage(originPath);
                }
            }
        }

        // 제휴점 썸네일 이미지 관련
        if (Arrays.asList(this.thumbnailUsedBySupplytype).contains(store.getSupplytype())) {
            if (!ObjectUtils.isEmpty(imageThumbnailFile)) {
                this.checkStoreImage(imageThumbnailFile, "썸네일", 1000, 1000);
                this.makeImageThumbnail(store, imageThumbnailFile);
            } else { // 디폴트 썸네일 이미지 등록
                store.setImageThumbnail(this.initProperties.getStoreThumbnailDefaultImgPath());
            }
        }

        String loginId = store.getId();
        if (ObjectUtils.isEmpty(loginId) || loginId.equals("") /*loginId 체크*/) {
            throw new CommonException(Errors.Store_AdminUid_Unknown);
        }
        if (this.getStoreIdCheck(loginId) == false) {
            throw new CommonException(Errors.Store_AdminUid_Already_Exists);
        }
        if (this.countStoreForGps(store) > 0) {
            throw new CommonException(Errors.Store_Gps_Already_Exists);
        }
        store.setId(loginId.replaceAll("\\p{Z}", ""));
        store.setRegdate(new Date());

        Date now = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd_HH:mm:dd");
        store.setNiceid(sdf.format(now) + "_" + GUIDGenerator.getRandomGUIDString().substring(0, 20));

        // 식권 푸시 알림 디폴트 제공
        store.setPushcoupon(true);

        // DB insert
        this.storeMapper.insert(store);

        // StoreImage 추가로 넣어 준다.
        // 제휴점 상세 이미지, 마켓홈 메인 제휴점 이미지 관련
        if (Arrays.asList(imgUsedBySupplytype).contains(store.getSupplytype())) {
            // 제휴점 상세 이미지 체크
            if (!ObjectUtils.isEmpty(file)) {

                //이미지의 Type STORE_IMAGE_TYPE_SIGNATURE(대표 이미지), STORE_IMAGE_TYPE_MENU(메뉴판 이미지)
                store.setStoreImageType("STORE_IMAGE_TYPE_SIGNATURE");

                //dispayOrder Max값 가져온다.
                Integer dispayOrder = this.storeImageMapper.getStoreImageDispayOrderMax(store);

                StoreImage storeImage = new StoreImage();
                storeImage.setStoreId(store.getSid());
                storeImage.setType(store.getStoreImageType());
                storeImage.setImage(store.getImg());
                storeImage.setDisplayOrder(dispayOrder);
                storeImage.setCreatedBy(info.getAdminId());
                storeImage.setCreatedDate(new Date());

                // DB insert
                this.storeImageMapper.insertStoreImage(storeImage);
            }
        }

        // [판매형태: 배송] 관련 정보 등록
        if (store.getSupplytype() == StoreSupplyType.SHIPPING.getType()) {
            if (!ObjectUtils.isEmpty(bannerFile)) {
                this.checkBannerImage(bannerFile);
                this.makeBannerImageAndSetting(store, bannerFile);
            }
            store.setShippingInfo(StringParserUtil.quotationToEscapeString(store.getShippingInfo()));
            store.setShippingInfoDetail(StringParserUtil.quotationToEscapeString(store.getShippingInfoDetail()));
            store.setRefundInfo(StringParserUtil.quotationToEscapeString(store.getRefundInfo()));

            // 원산지 내용 <표시 안함> 이면 원산지 내용 "" 초기화
            if (!ObjectUtils.isEmpty(store.getIsDisplayCountryOrigin())
                && !store.getIsDisplayCountryOrigin()) {
                store.setCountryOriginContent(null);
            }

            this.storeMapper.insertShippingStoreInfo(store);
            this.setShippingStoreAddressRestrict(store, info, true);
            this.setEtcAdditionalFeeUsable(store, info);
        }

        // ===========> 세틀먼트 서비스 제휴점 기본 정보 등록 =================
        StoreDTO.StoreCreateRequest storeCreateRequest = new StoreCreateRequest();
        storeCreateRequest.setStoreId(store.getSid());
        storeCreateRequest.setStoreName(store.getName());
        storeCreateRequest.setRegisterId("backoffice");
        storeCreateRequest.setRegisterName("backoffice");

        this.settlementStoreSettlementService.setStore(storeCreateRequest);
        // ============================================================

        // 메인카테고리 정보 등록
        this.insertStoreMainCategory(sid, store.getMainCategoryList(), info.getAdminId());

        LogUtil.writeBean(log, store, "제휴점 입력");

        // AdminModifyLog 기록
        this.adminModifyLogService.setAdminModifyLog(
                info,
                null,
                store.getSid(),
                "Store",
                null,
                store
        );

        return store.getSid();
    }

    private void enrollBrand(Store store, MultipartFile file, String originPath, AdminUser info) {
        String extension = this.imageService.getStoreExtension(file.getContentType());
        String originName = store.getSid() + "." + extension;

        this.insertBrandLog(store, originPath, originName, info);
    }

    public Store update(Store store) {
        return this.update(store);
    }

    public Store updateStore(Store store) throws VlocallyException {
        int result = this.storeMapper.update(store);
        if (result != 1) {
            throw new VlocallyException(Errors.Store_UnkownStore);
        }
        return this.storeMapper.selectById(store.getSid());
    }

    /**
     * 메뉴 이미지 사이즈와 MIME 타입 체크
     */
    private void checkStoreImage(MultipartFile file, String replaceName, Integer width, Integer height) {
        BufferedImage original;
        try {
            original = ImageIO.read(file.getInputStream());

            System.out.println("###############checkStoreImage:"+original);

        } catch (Exception e) {
            throw new CommonException(Errors.STORE_IMG_FORMAT_ERR,
                    Errors.STORE_IMG_FORMAT_ERR.msg.replace("{name}", replaceName));
        }
        if (ObjectUtils.isEmpty(original)) {
            throw new CommonException(Errors.STORE_IMG_FORMAT_ERR,
                    Errors.STORE_IMG_FORMAT_ERR.msg.replace("{name}", replaceName));
        }

        // width, height 동일 한지 체크
        if ("썸네일".equals(replaceName) && (original.getWidth() != original.getHeight())) {
            throw new CommonException(Errors.STORE_CHECK_WIDTH_HEIGHT_EQUALS);
        }

        // 썸네일 이미지 인데, 가로 1000 & 세로 1000 이하 or 상세 이미지 인데, 가로 & 세로 길이가 지정된 값이 아님
        if (("썸네일".equals(replaceName) && !(original.getWidth() <= width && original.getHeight() <= height))
                || ("상세".equals(replaceName) && !(original.getWidth() == width && original.getHeight() == height))) {
            String replaceSize = "{width}px*{height}px(가로*세로)"
                    .replace("{width}", String.valueOf(width))
                    .replace("{height}", String.valueOf(height));
            throw new CommonException(Errors.Store_Check_Width, Errors.Store_Check_Width.msg
                    .replace("{name}", replaceName)
                    .replace("{size}", replaceSize));
        }

        // 확장자 체크
        if (!("png".equalsIgnoreCase(FilenameUtils.getExtension(file.getOriginalFilename())) ||
                "jpg".equalsIgnoreCase(FilenameUtils.getExtension(file.getOriginalFilename())))) {
            throw new CommonException(Errors.STORE_IMG_FORMAT_ERR,
                    Errors.STORE_IMG_FORMAT_ERR.msg.replace("{name}", replaceName));
        }
    }

    /**
     * 메뉴 이미지 생성[RGB]
     */
    private void makeImageRGB(Store store, MultipartFile file, Integer width, Integer height) {
        String contentType = file.getContentType();
        String extension = this.imageService.getStoreExtension(contentType);

        log.info("##########################file.getSize()"+file.getSize());
        System.out.println("1.contentType:"+contentType);
        System.out.println("2.extension:"+extension);

        try {
            BufferedImage original = ImageIO.read(file.getInputStream());
            String originName = store.getSid() + "." + extension;
            byte[] originBuff = this.imageService.toStoreByteArrayRGB(original, extension, width, height);

            System.out.println("3.originBuff:"+originBuff);
            System.out.println("4.originBuff.length:"+originBuff.length);

            String originPath = this.imageService.storeUploadFile(store.getSid(), originName, contentType, originBuff);
            store.setImg(originPath);
        } catch (Exception e) {
            throw new CommonException(Errors.STORE_IMG_UPLOAD_ERR_IMG,
                    Errors.STORE_IMG_UPLOAD_ERR_IMG.msg.replace("{name}", "상세"));
        }
    }

    /**
     * 메뉴 이미지 생성
     */
    private void makeImage(Store store, MultipartFile file) {
        String contentType = file.getContentType();
        String extension = this.imageService.getStoreExtension(contentType);

        log.info("##########################file.getSize()"+file.getSize());
        System.out.println("1.contentType:"+contentType);
        System.out.println("2.extension:"+extension);

        try {
            BufferedImage original = ImageIO.read(file.getInputStream());
            String originName = store.getSid() + "." + extension;
            byte[] originBuff = this.imageService.toStoreByteArray(original, extension);

            System.out.println("3.originBuff:"+originBuff);
            System.out.println("4.originBuff.length:"+originBuff.length);

            String originPath = this.imageService.storeUploadFile(store.getSid(), originName, contentType, originBuff);
            store.setImg(originPath);
        } catch (Exception e) {
            throw new CommonException(Errors.STORE_IMG_UPLOAD_ERR_IMG,
                    Errors.STORE_IMG_UPLOAD_ERR_IMG.msg.replace("{name}", "상세"));
        }
    }

    /**
     * 썸네일 이미지 생성
     */
    private void makeImageThumbnail(Store store, MultipartFile file) {
        String contentType = file.getContentType();
        String extension = this.imageService.getStoreExtension(contentType);
        try {
            BufferedImage original = ImageIO.read(file.getInputStream());
            String originName = store.getSid() + "." + extension;
            byte[] originBuff = this.imageService.toStoreByteArray(original, extension);
            String originPath = this.imageService.storeUploadFileByThumbnail(store.getSid(), originName, contentType, originBuff);
            store.setImageThumbnail(originPath);
        } catch (Exception e) {
            throw new CommonException(Errors.STORE_IMG_UPLOAD_ERR_THUMB,
                    Errors.STORE_IMG_UPLOAD_ERR_THUMB.msg.replace("{name}", "썸네일"));
        }
    }

    /**
     * 제휴점 로고 이미지 생성
     */
    private String storeLogoImageUpload(Store store, MultipartFile file) {
        String contentType = file.getContentType();
        String extension = this.imageService.getStoreExtension(contentType);
        try {
            BufferedImage original = ImageIO.read(file.getInputStream());
            String originName = store.getSid() + "." + extension;
            byte[] originBuff = this.imageService.toStoreByteArray(original, extension);

            return this.imageService.storeUploadFileByLogo(store.getSid(), originName, contentType, originBuff);
        } catch (Exception e) {
            log.error("storeLogoImageUpload, 제휴점 로고 이미지 생성 중 에러 발생 : {}", e);
            throw new CommonException(Errors.STORE_IMG_UPLOAD_ERR_THUMB,
                    Errors.STORE_IMG_UPLOAD_ERR_THUMB.msg.replace("{name}", "제휴점 로고"));
        }
    }

    private void insertBrandLog(Store store, String originPath, String originName, AdminUser info) {
        Brand brand = new Brand();
        brand.setOrderNo(999);
        brand.setName(store.getName());
        brand.setImageFileUrl(originPath);
        brand.setImageFileOriginName(originName);
        brand.setStoreId(store.getSid());
        brand.setIsExposure(true);
        brand.setCreateUser(info.getAdminId());
        brand.setCreateDate(new Date());
        brand.setUpdateUser(info.getAdminId());
        brand.setUpdateDate(new Date());
        this.brandMapper.insert(brand);

        // 브랜드 로그 기록
        this.adminModifyLogService.setAdminModifyLog(
                info,
                null,
                store.getSid(),
                "Brand",
                null,
                brand
        );
    }

    public void deleteBrandLog(String storeId, AdminUser info) {
        Brand brand = this.brandMapper.selectBySid(storeId);
        Store store = storeMapper.selectById(storeId);
        store.setLogoImage("DELETE");
        this.storeMapper.update(store);

        if (!ObjectUtils.isEmpty(brand)) {
            this.brandMapper.deleteBySid(storeId);

            // 브랜드 로그 기록
            this.adminModifyLogService.setAdminModifyLog(
                    info,
                    null,
                    storeId,
                    "Brand",
                    brand,
                    null
            );
        }
    }

    /**
     * 기획전 배너 이미지 사이즈와 MIME 타입 체크
     */
    private void checkBannerImage(MultipartFile file) {
        BufferedImage original;
        try {
            original = ImageIO.read(file.getInputStream());
        } catch (Exception e) {
            throw new CommonException(Errors.STORE_IMG_FORMAT_ERR,
                    Errors.STORE_IMG_FORMAT_ERR.msg.replace("{name}", "리스트"));
        }
        if (ObjectUtils.isEmpty(original)) {
            throw new CommonException(Errors.STORE_IMG_FORMAT_ERR,
                    Errors.STORE_IMG_FORMAT_ERR.msg.replace("{name}", "리스트"));
        }
        if (original.getWidth() != 1440 || original.getHeight() != 900) {
            throw new CommonException(Errors.STORE_BANNER_IMG_SIZE_ERR);
        }
        if (!("png".equalsIgnoreCase(FilenameUtils.getExtension(file.getOriginalFilename())) ||
                "jpg".equalsIgnoreCase(FilenameUtils.getExtension(file.getOriginalFilename())) ||
                "jpeg".equalsIgnoreCase(FilenameUtils.getExtension(file.getOriginalFilename())))) {
            throw new CommonException(Errors.STORE_IMG_FORMAT_ERR,
                    Errors.STORE_IMG_FORMAT_ERR.msg.replace("{name}", "리스트"));
        }
    }

    /**
     * 기획전 배너 이미지 생성
     */
    private void makeBannerImageAndSetting(Store store, MultipartFile file) {
        String contentType = file.getContentType();
        String extension = this.imageService.getStoreExtension(contentType);

        try {
            BufferedImage original = ImageIO.read(file.getInputStream());
            String originName = store.getSid() + "-banner." + extension;
            byte[] originBuff = this.imageService.toStoreByteArray(original, extension);
            String originPath = this.imageService.storeUploadFile(store.getSid(), originName, contentType, originBuff);
            store.setShippingImg(originPath);
        } catch (Exception e) {
            throw new CommonException(Errors.STORE_IMG_FORMAT_ERR_DEFAULT);
        }
    }

    /**
     * 제휴점 수정
     */
    public Store update(
            AdminUser info, Store store, MultipartFile file, MultipartFile imageThumbnailFile, MultipartFile bannerFile,
            MultipartFile logoFile)
            throws VlocallyException {

        // 이전기록 조회하여 아이디 비교검사
        Store old = storeMapper.selectById(store.getSid());

        // 제휴점 상세 이미지
        if (Arrays.asList(this.imgUsedBySupplytype).contains(store.getSupplytype())) {
            if (!ObjectUtils.isEmpty(file)) {
                this.checkStoreImage(file, "상세", 1440, 810);
                //this.makeImage(store, file);
                this.makeImageRGB(store, file, 1440, 810);
            } else {
                if ((old.getSupplytype() != store.getSupplytype()) || ObjectUtils.isEmpty(old.getImg())) {
                    throw new CommonException(Errors.Store_Check_Null);
                }
            }
        }

        // 마켓홈 내 제휴점 리스트 이미지
        if (store.getSupplytype() == StoreSupplyType.SHIPPING.type) {
            // Banner
            if (!ObjectUtils.isEmpty(bannerFile)) {
                this.checkBannerImage(bannerFile);
                this.makeBannerImageAndSetting(store, bannerFile);
            }

            // 제휴점 로고
            if (!ObjectUtils.isEmpty(logoFile)) {
                this.checkStoreImage(logoFile, "제휴점 로고", 640, 640);
                String originPath = this.storeLogoImageUpload(store, logoFile);
                this.enrollBrand(store, logoFile, originPath, info); // 브랜드 등록
                store.setLogoImage(originPath);
            }
        }

        // 제휴점 썸네일 이미지
        if (Arrays.asList(this.thumbnailUsedBySupplytype).contains(store.getSupplytype())) {
            // 교체 하려는 경우
            if (!ObjectUtils.isEmpty(imageThumbnailFile)) {
                this.checkStoreImage(imageThumbnailFile, "썸네일", 1000, 1000);
                this.makeImageThumbnail(store, imageThumbnailFile);
                // 판매형태가 달라지거나, 교체 안하는데 이미지가 기존에도 없는 경우 -> 기본 이미지로 등록 처리
            } else {
                if ((old.getSupplytype() != store.getSupplytype()) || ObjectUtils.isEmpty(old.getImageThumbnail())) {
                    // 기본 썸네일 이미지 등록
                    store.setImageThumbnail(this.initProperties.getStoreThumbnailDefaultImgPath());
                }
            }
        }

        // 제휴점을 비활성으로 변경하려고 할 시에 쿠폰제휴점인 경우 체크 -> 쿠폰제휴점 아닌경우 패스
        if (store.getStatus() == 0) {
            this.thirdPartyCouponService.checkThirdPartyCouponUser(store.getSupplytype(), store.getSid(), null);
        }

        // 정액정률 요소 검사
        if (store.getIsMenuless() != null && store.getIsMenuless()) {
            Integer fixedCount = priService.getHasFixedFee(store.getSid());
            if (fixedCount != null && fixedCount > 0) {
                throw new CommonException(Errors.General_WrongParam, "정액방식의 과금방식을 가진 제휴점은 메뉴직접입력을 사용할 수 없습니다");
            }
        }

        if (!ObjectUtils.isEmpty(old)) {
            // 로그인 아이디 중복검사
            if (store.getId() != null && !store.getId().equals("") && !store.getId().equals(old.getId())) {
                if (getStoreIdCheck(store.getId()) == false) {
                    throw new CommonException(Errors.Store_AdminUid_Already_Exists);
                }
                store.setId(store.getId().replaceAll("\\p{Z}", ""));
            } else {
                store.setId(null);
            }
            if ((!store.getGpslat().equals(old.getGpslat()) || !store.getGpslon().equals(old.getGpslon()))
                    && this.countStoreForGps(store) > 0) {
                throw new CommonException(Errors.Store_Gps_Already_Exists);
            }

            // 공급형태를 바꾸지 않고 Booking 인데
            if (old.getSupplytype().equals(store.getSupplytype()) && store.getSupplytype() == StoreSupplyType.Booking.type) {
                // 기존 정보에 이미지 정보가 없을때 img 를 등록 안하면 에러
                if (ObjectUtils.isEmpty(old.getImg()) && ObjectUtils.isEmpty(file)) {
                    throw new CommonException(Errors.Store_Check_Null);
                }
            }

            // 공급형태를 Booking 으로 바꾸고 수정할때 이미지 없으면 에러
            if (store.getSupplytype() == StoreSupplyType.Booking.type) {
                if (ObjectUtils.isEmpty(old.getImg()) && ObjectUtils.isEmpty(file)) {
                    throw new CommonException(Errors.Store_Check_Null);
                }
            }

            // 공급형태가 SHIPPING 인데 기존 이미지와 신규 이미지 정보가 없으면 에러
            if (store.getSupplytype() == StoreSupplyType.SHIPPING.type) {
                if (ObjectUtils.isEmpty(old.getImg()) && ObjectUtils.isEmpty(file)) {
                    throw new CommonException(Errors.Store_Check_Null);
                }

                // 제휴식당 리스트 이미지가 없으면 디폴트 세팅
                if (ObjectUtils.isEmpty(old.getShippingImg()) && ObjectUtils.isEmpty(bannerFile)) {
                    store.setShippingImg(ImageUtil.getRandomDefaultShippingImg());
                }
            }
        }

        // 판매형태를 수정하는데 일회용 혹은 고정형이 아닌 경우 과거 데이터 초기화 (supplyType, originType)
        if (!ObjectUtils.isEmpty(store.getSupplytype())
                && store.getSupplytype() != StoreSupplyType.Cafeteria.type
                && store.getSupplytype() != StoreSupplyType.CafeteriaFixed.type) {
            store.setOriginType(null);
            store.setShopCode(null);
        }

        // 배송
        if (store.getSupplytype() == StoreSupplyType.SHIPPING.type) {
            store.setShippingInfo(StringParserUtil.quotationToEscapeString(store.getShippingInfo()));
            store.setShippingInfoDetail(StringParserUtil.quotationToEscapeString(store.getShippingInfoDetail()));
            store.setRefundInfo(StringParserUtil.quotationToEscapeString(store.getRefundInfo()));
            if (!ObjectUtils.isEmpty(store.getIsDisplayCountryOrigin())
                && !store.getIsDisplayCountryOrigin()) {
                store.setCountryOriginContent(null);
            }
        }
        // 고정형 바코드가 아닌경우 isCaptainCode 초기화
        if (!ObjectUtils.isEmpty(store.getSupplytype()) && store.getSupplytype() != StoreSupplyType.CafeteriaFixed.type) {
            store.setIsCaptainCode(false);
        }
        if (ObjectUtils.isEmpty(store.getSupplytype())
                || store.getSupplytype() != StoreSupplyType.CafeteriaFixed.type
                || store.getOriginType() != OriginType.touchb.getCodeValue()) {
            store.setIsMultiplePayment(false);
        }
        // 앱 내 결제 취소가 false 로 넘어온 경우, 결제 취소 비밀번호 옵션 및 취소 비밀번호 초기화
        if (!ObjectUtils.isEmpty(store.getCancelfunc()) && !store.getCancelfunc()) {
            store.setCancelpwchange(false);
            store.setCancelpw(null);
        }
        if (store.getCancelfunc() && store.getCancelpwchange() &&
                (ObjectUtils.isEmpty(old.getCancelpw()) ||
                        (old.getCancelfunc() && !old.getCancelpwchange() && !ObjectUtils.isEmpty(old.getCancelpw())))
        ) {
            // TODO : 취소 비밀번호 자동 변경 선택 시 비밀번호 초기화 처리
            store.setCancelpw(String.valueOf(ThreadLocalRandom.current().nextInt(1000, 9999)));
        }
        if (store.getCancelfunc() && store.getCancelpwchange() &&
                (old.getCancelfunc() && old.getCancelpwchange() && !ObjectUtils.isEmpty(old.getCancelpw()))) {
            store.setCancelpw(old.getCancelpw());
        }

        int result = storeMapper.update(store);
        if (result != 1) {
            throw new CommonException(Errors.Store_UnkownStore);
        }

        // StoreImage 추가로 넣어 준다.
        // 제휴점 상세 이미지, 마켓홈 메인 제휴점 이미지 관련
        if (Arrays.asList(imgUsedBySupplytype).contains(store.getSupplytype())) {
            // 제휴점 상세 이미지 체크
            if (!ObjectUtils.isEmpty(file)) {

                //이미지의 Type STORE_IMAGE_TYPE_SIGNATURE(대표 이미지), STORE_IMAGE_TYPE_MENU(메뉴판 이미지)
                store.setStoreImageType("STORE_IMAGE_TYPE_SIGNATURE");

                //dispayOrder Max값 가져온다.
                Integer dispayOrder = this.storeImageMapper.getStoreImageDispayOrderMax(store);

                StoreImage storeImage = new StoreImage();
                storeImage.setStoreId(store.getSid());
                storeImage.setType(store.getStoreImageType());
                storeImage.setImage(store.getImg());
                storeImage.setDisplayOrder(dispayOrder);
                storeImage.setCreatedBy(info.getAdminId());
                storeImage.setCreatedDate(new Date());

                // DB insert
                this.storeImageMapper.insertStoreImage(storeImage);
            }
        }

        if (store.getSupplytype() == StoreSupplyType.SHIPPING.type) {
            // 공급형태 를 배송 (SHIPPING) 으로 바꾸려는 경우 ?! TODO. 해당 케이스 확인 필요!!
            if (!old.getSupplytype().equals(store.getSupplytype())) {
                if (!ObjectUtils.isEmpty(bannerFile)) {
                    this.checkBannerImage(bannerFile);
                    this.makeBannerImageAndSetting(store, bannerFile);
                }
                this.storeMapper.insertShippingStoreInfo(store);
                this.setShippingStoreAddressRestrict(store, info, true);
            } else {
                this.storeMapper.updateShippingStoreInfo(store);
                this.setShippingStoreAddressRestrict(store, info, false);
            }
            this.setEtcAdditionalFeeUsable(store, info);
        }

        // ============================ 세틀먼트 - 제휴점 정보 수정 동기화 ===================================
        boolean isUseFlag = false;
        if (store.getStatus().intValue() != 0) {
            isUseFlag = true;
        }

        this.settlementStoreSettlementService.updateStore(info, store, isUseFlag);
        // ===========================================================================================

        if (store.getName() != null) {
            MenuExample ex = new MenuExample();
            ex.createCriteria().andSidEqualTo(store.getSid());
            Menu menu = new Menu();
            menu.setStorename(store.getName());
            menuDAO.update(ex, menu);
        }
        LogUtil.writeBean(log, store, "제휴점 수정");

        Store updateStore = storeMapper.selectById(store.getSid());
        this.adminModifyLogService.setAdminModifyLog(
                info,
                null,
                store.getSid(),
                "Store",
                old,
                updateStore
        );

        if (OriginType.touchb.getCodeValue().equals(store.getOriginType())) {
            List<OfficeStoreRelation> officeStoreRElation = this.officeStoreRelationMapper.selectAllOfficeStoreRelationByStoreId(
                    store.getSid());
            boolean isCustomAmount = store.getIsMultiplePayment();

            if (!ObjectUtils.isEmpty(officeStoreRElation)) {
                for (OfficeStoreRelation officeStoreRelation : officeStoreRElation) {
                    officeStoreRelation.setIsCustomAmount(isCustomAmount);
                    this.officeStoreRelationService.updateOfficeStoreRelation(info, officeStoreRelation);
                }
            }
        }

        // 메인카테고리 정보 등록/수정
        this.updateStoreMainCategory(store.getSid(), store.getMainCategoryList(), info.getAdminId());

        // AdminModifyLog 리턴
        List<Store> storeList = new ArrayList<>();
        storeList.add(updateStore);
        storeList = this.convertMainCategoryList(storeList); // mainCategory Setting
        storeList = this.convertUpdateInfo(storeList); // updateHistory Setting

        return storeList.get(0);
    }

    private void setEtcAdditionalFeeUsable(Store store, AdminUser info) {
        if (store.getEtcAdditionalFeeUsable()) {
            // TODO : 제한 지역 제주도 울릉도 고정
            ShippingStoreAddressRestrictDto shippingStoreAddressRestrictDto = new ShippingStoreAddressRestrictDto();
            shippingStoreAddressRestrictDto.setStoreId(store.getSid());
            shippingStoreAddressRestrictDto.setZonecode("63");
            shippingStoreAddressRestrictDto.setName("제주도");
            shippingStoreAddressRestrictDto.setFee(store.getEtcAdditionalFee());
            shippingStoreAddressRestrictDto.setCreateDate(new Date());
            shippingStoreAddressRestrictDto.setCreateUser(info.getAdminId());
            storeMapper.deleteEtcAdditionalFeeUsable(shippingStoreAddressRestrictDto);
            storeMapper.insertEtcAdditionalFeeUsable(shippingStoreAddressRestrictDto);
            shippingStoreAddressRestrictDto = new ShippingStoreAddressRestrictDto();
            shippingStoreAddressRestrictDto.setStoreId(store.getSid());
            shippingStoreAddressRestrictDto.setZonecode("402");
            shippingStoreAddressRestrictDto.setName("울릉도");
            shippingStoreAddressRestrictDto.setFee(store.getEtcAdditionalFee());
            shippingStoreAddressRestrictDto.setCreateDate(new Date());
            shippingStoreAddressRestrictDto.setCreateUser(info.getAdminId());
            storeMapper.insertEtcAdditionalFeeUsable(shippingStoreAddressRestrictDto);
        } else {
            ShippingStoreAddressRestrictDto shippingStoreAddressRestrictDto = new ShippingStoreAddressRestrictDto();
            shippingStoreAddressRestrictDto.setStoreId(store.getSid());
            storeMapper.deleteEtcAdditionalFeeUsable(shippingStoreAddressRestrictDto);
        }
    }

    private void setShippingStoreAddressRestrict(Store store, AdminUser info, boolean isInsert) {
        if (store.getShippingRestrictUsed()) {
            // TODO : 제한 지역 제주도 울릉도 고정
            Date createDate = new Date();
            ShippingStoreAddressRestrictDto searchDto = new ShippingStoreAddressRestrictDto();
            searchDto.setStoreId(store.getSid());
            List<ShippingStoreAddressRestrictDto> shippingStoreAddressRestricts = this.storeMapper
                    .selectShippingStoreAddressRestricts(searchDto);
            if (isInsert || ObjectUtils.isEmpty(shippingStoreAddressRestricts)) {
                ShippingStoreAddressRestrictDto shippingStoreAddressRestrictDto = new ShippingStoreAddressRestrictDto();
                shippingStoreAddressRestrictDto.setStoreId(store.getSid());
                shippingStoreAddressRestrictDto.setZonecode("63");
                shippingStoreAddressRestrictDto.setName("제주도");
                shippingStoreAddressRestrictDto.setCreateDate(createDate);
                shippingStoreAddressRestrictDto.setCreateUser(info.getAdminId());
                this.storeMapper.insertShippingStoreAddressRestrict(shippingStoreAddressRestrictDto);
                shippingStoreAddressRestrictDto = new ShippingStoreAddressRestrictDto();
                shippingStoreAddressRestrictDto.setStoreId(store.getSid());
                shippingStoreAddressRestrictDto.setZonecode("402");
                shippingStoreAddressRestrictDto.setName("울릉도");
                shippingStoreAddressRestrictDto.setCreateDate(createDate);
                shippingStoreAddressRestrictDto.setCreateUser(info.getAdminId());
                this.storeMapper.insertShippingStoreAddressRestrict(shippingStoreAddressRestrictDto);
            }
        } else {
            ShippingStoreAddressRestrictDto shippingStoreAddressRestrictDto = new ShippingStoreAddressRestrictDto();
            shippingStoreAddressRestrictDto.setStoreId(store.getSid());
            this.storeMapper.deleteShippingStoreAddressRestrict(shippingStoreAddressRestrictDto);
        }
    }

    public int update(String sid, MultipartFile imgfile) throws VlocallyException, IOException {
        log.info("+++++++++++++++++++++++++++++++++++update+++++++++++++++++++++++");

        Store store = storeDAO.selectStoreDetail(sid);
        if (store == null) {
            return 0;
        }
        String before_img = store.getImg();
        if (imgfile != null) {
            String filename = fileService.saveFileForStore(imgfile);
            if (filename != null) {
                Store store_u = new Store();
                store_u.setSid(sid);
                store_u.setImg(filename);
                int result = storeDAO.update(store_u);
                if (result == 1) {
                    if (before_img != null) {
                        fileService.deleteFileForStore(before_img);
                    }
                    log.info("sid : " + sid + "\n이미지 교체 " + filename + "\n구이미지 : " + before_img);
                }
            }
        }
        return 1;
    }

    /**
     * 제휴점 아이디 중복체크
     */
    public boolean getStoreIdCheck(String id) {
        return storeDAO.selectStoreIdCheck(id);
    }

    /**
     * 제휴점 아이디로 조회하기
     */
    public Store getStore(String sid) {
        return storeMapper.selectById(sid);
    }

    /**
     * 제휴점 자세히 조회하기
     */
    public Store getStoreDetail(String sid) {
        Store store = storeDAO.selectStoreDetail(sid);
        if (store != null) {
            store.setMenus(menuDAO.selectOfStore(sid));
        }
        return store;
    }

    public int getStoresCount(StoreExample ex) {
        return storeDAO.count(ex);
    }

    /**
     * 제휴점 조건별 목록 조회하기
     */
    public List<Store> getStores(StoreExample ex, HashMap<String, Integer> listinfo) {

        int total = storeDAO.count(ex);
        return storeDAO.select(ex, PageUtil.setPageValue(listinfo, total));
    }

    public List<Store> searchStore(StoreTO.ListQuery query) {
        if (!ObjectUtils.isEmpty(query) && !ObjectUtils.isEmpty(query.getSearchWord())) {
            query.setSearchWord(URLDecoder.decode(query.getSearchWord(), StandardCharsets.UTF_8));
        }

        query.setTotalCount(this.storeMapper.countStore(query));
        List<Store> storeList = this.storeMapper.selectStore(query);
        storeList = this.convertMainCategoryList(storeList);
        storeList = this.convertUpdateInfo(storeList);
        return storeList;
    }

    public List<Store> getStoreSettlementBizInfoList(StoreTO.ListQuery query) {
        List<Store> storeList = new ArrayList<>();
        Map<String, Object> param = new HashMap<>();
        param.put("categoryid", query.getCategoryid());
        param.put("supplyType", query.getSupplyType());
        param.put("marketCategoryType", query.getMarketCategoryType());
        param.put("region", query.getRegion());

        // JS Call
        StoreAccountBizRespones storeAccountBizRespones = this.storeSettleAccountBizNumberList(query);
        List<Store> accountBizList = storeAccountBizRespones.getStoreAccountList();
        for (Store row : accountBizList) {
            param.put("sid", row.getSid());
            Store searchedStore = this.storeMapper.storeSettlementSelectByStoreId(param);
            if (!ObjectUtils.isEmpty(searchedStore)) {
                // new Store Instance JS Info
                Store store = new Store();
                BeanUtils.copyProperties(searchedStore, store);
                store.setBankname(row.getBankname());
                store.setBankowner(row.getBankowner());
                store.setBankaccount(row.getBankaccount());
                store.setBizname(row.getBizname());
                store.setBizserial(row.getBizserial());
                storeList.add(store);
            }
        }

        query.setTotalCount((long) storeList.size());
        storeList = this.convertMainCategoryList(storeList);
        storeList = this.convertUpdateInfo(storeList);
        return storeList;
    }

    public List<Store> convertMainCategoryList(List<Store> storeList) {
        for (Store store : storeList) {
            StoreMainCategoryRelation storeMainCategoryRelation = new StoreMainCategoryRelation();
            storeMainCategoryRelation.setSid(store.getSid());
            List<StoreMainCategoryRelation> storeMainCategoryRelationList
                    = this.storeMainCategoryRelationPersist.readStoreMainCategoryRelationList(storeMainCategoryRelation);
            if (!ObjectUtils.isEmpty(storeMainCategoryRelationList)
                    && storeMainCategoryRelationList.size() > 0) {
                List<Integer> mainCategoryList = new ArrayList<>();
                for (StoreMainCategoryRelation storeMainCategoryRelationVo : storeMainCategoryRelationList) {
                    mainCategoryList.add(storeMainCategoryRelationVo.getMainCategoryIdx());
                }
                store.setMainCategoryList(mainCategoryList);
            }
        }
        return storeList;
    }

    public List<Store> convertUpdateInfo(List<Store> storeList) {
        for (Store store : storeList) {
            UpdateHistoryRequest updateHistoryRequest = new UpdateHistoryRequest();
            updateHistoryRequest.setTableIdx(store.getSid());
            updateHistoryRequest.setTableName("Store");
            updateHistoryRequest.setPage(1);
            updateHistoryRequest.setPageRow(20);
            UpdateHistoryResponse updateHistoryResponse = this.adminModifyLogService.readAdminModifyLog(updateHistoryRequest);

            List<AdminModifyLog> adminModifyLogList = updateHistoryResponse.getList();
            if (!ObjectUtils.isEmpty(adminModifyLogList)
                    && adminModifyLogList.size() > 0) {
                AdminModifyLog adminModifyLogStart = adminModifyLogList.get(0);
                AdminModifyLog adminModifyLogEnd = adminModifyLogList.get(adminModifyLogList.size() - 1);
                store.setCreateDate(store.getRegdate());
                store.setCreateUserName(adminModifyLogEnd.getUserName());
                store.setUpdateDate(adminModifyLogStart.getRegDate());
                store.setUpdateUserName(adminModifyLogStart.getUserName());
            }
        }

        return storeList;
    }

    /**
     * 특정 회사가 이용할 수 있는 제휴점목록
     */
    public List<HashMap> getStoresOfCompany(
            String comid, List<String> name, Integer supplytype, HashMap<String, Integer> listinfo, boolean admin) {
        int total = storeDAO.countStoreOfCompany(comid, name, supplytype, admin);
        return storeDAO.selectStoreOfCompany(comid, name, supplytype, PageUtil.setPageValue(listinfo, total), admin);
    }

    /**
     * 특정 회사가 권한 추가할 수 있는 제휴점 목록
     * 이미 주가된 제휴점은 제외
     */
    public List<HashMap<String, Object>> getStoreAbleOfCompany(String comid, String name, HashMap<String, Integer> listinfo) {
        int total = storeDAO.countStoreAbleOfCompany(comid, name);
        return storeDAO.selectStoreAlbeOfCompany(comid, name, PageUtil.setPageValue(listinfo, total));
    }

    /**
     * 특정 카테고리를 사용하고 있는 제휴점 존재여부
     */
    public Boolean getStoreHasCategory(Integer scateid) {
        return storeDAO.selectStoreHasCategory(scateid);
    }

    /**
     * 특정 마켓 카테고리를 사용하고 있는 제휴점 존재여부
     */
    public Boolean getStoreHasMarketCategory(Integer categoryId) {
        return this.storeMapper.selectStoreHasMarketCategory(categoryId);
    }

    /**
     * 분류정보 수정에 따른 제휴점 정보 변경
     */
    public int updateForCategory(Integer scateid, String name, Byte showorder) {
        if ((name == null || name.equals("")) && showorder == null) {
            return 0;
        }
        StoreExample ex = new StoreExample();
        StoreExample.Criteria cri = ex.createCriteria();
        cri.andCategoryidEqualTo(scateid);

        Store store = new Store();
        if (name != null && !name.equals("")) {
            store.setCategory(name);
        }
        if (showorder != null) {
            store.setCategoryorder(showorder);
        }

        return storeDAO.updateByExample(ex, store);
    }

    /* =================
     * 제휴점 슈퍼관리자 조회용
     * =================*/

    public List<HashMap<String, Object>> getStoreByAccount(String said) {
        return privilegeDAO.selectStoreByAccount(said);
    }

    public List<HashMap<String, Object>> getStoreAbleOfStoreAccount(String said, String name, HashMap<String, Integer> listinfo) {
        Integer total = storeDAO.countStoreAbleOfStoreAccount(said, name);
        return storeDAO.selectStoreAbleOfStoreAccount(said, name, PageUtil.setPageValue(listinfo, total));
    }

    public void deleteImage(String storeId) {
        Store store = this.storeMapper.selectById(storeId);
        String original = this.getFileName(store.getImg());
        if (!ObjectUtils.isEmpty(original)) {
            this.imageService.storeDeleteFile(store.getSid(), original);
            store.setImg("DELETE");
            this.storeMapper.update(store);
        }
    }

    public void deleteNewImage(String storeId) {
        Store store = this.storeMapper.selectById(storeId);
        String original = this.getFileName(store.getImg());
        if (!ObjectUtils.isEmpty(original)) {
            this.imageService.storeDeleteFile(store.getSid(), original);

            store.setImg("DELETE");
            this.storeMapper.update(store);//기존처럼 null처리해준다.

            Map<String, Object> param = new HashMap<>();
            param.put("storeId", storeId);
            param.put("storeImageType", "STORE_IMAGE_TYPE_SIGNATURE");

            this.storeImageMapper.deleteStoreImage(param);
        }
    }

    public void deleteBannerImage(String storeId) {
        Store store = this.storeMapper.selectById(storeId);
        String original = this.getFileName(store.getShippingImg());
        if (!ObjectUtils.isEmpty(original)) {
            this.imageService.storeDeleteFile(store.getSid(), original);
            store.setShippingImg("DELETE");
            this.storeMapper.updateShippingStoreInfo(store);
        }
    }

    public void deleteImageThumbnail(String storeId) {
        Store store = this.storeMapper.selectById(storeId);
        String original = this.getFileName(store.getImageThumbnail());
        if (!ObjectUtils.isEmpty(original)) {
            this.imageService.storeDeleteFileByThumbnail(store.getSid(), original);
            store.setImageThumbnail("DELETE");
            this.storeMapper.update(store);
        }
    }

    private String getFileName(String imageUrl) {
        if (StringUtils.isEmpty(imageUrl)) {
            return null;
        }

        Pattern p = Pattern.compile("(\\w|-)+\\.(png|jpg|gif)");
        Matcher m = p.matcher(imageUrl);

        if (m.find()) {
            return m.group();
        }
        return null;
    }

    /**
     * 업종 카테고리 리스트 조회 및 등록
     */
    public void insertStoreMainCategory(String sid, List<Integer> MainCategoryList, String createUid) {
        if (ObjectUtils.isEmpty(MainCategoryList)) {
            throw new CommonException(Errors.MainCategory_Empty);
        }
        StoreMainCategoryRelation storeMainCategoryRelationVo = new StoreMainCategoryRelation();
        storeMainCategoryRelationVo.setSid(sid);
        storeMainCategoryRelationVo.setCreateUid(createUid);
        for (Integer mainCategory : MainCategoryList) {
            storeMainCategoryRelationVo.setMainCategoryIdx(mainCategory);
            List<StoreMainCategoryRelation> storeMainCategoryRelation
                    = this.storeMainCategoryRelationPersist.readStoreMainCategoryRelationList(storeMainCategoryRelationVo);
            if (ObjectUtils.isEmpty(storeMainCategoryRelation)) {
                this.storeMainCategoryRelationPersist.insertStoreMainCategoryRelation(storeMainCategoryRelationVo);
            }
        }
    }

    /**
     * 업종 카테고리 리스트 수정
     */
    public void updateStoreMainCategory(String sid, List<Integer> MainCategoryList, String updateUid) {
        if (!ObjectUtils.isEmpty(MainCategoryList)) {
            StoreMainCategoryRelation storeMainCategoryRelationVo = new StoreMainCategoryRelation();
            storeMainCategoryRelationVo.setSid(sid);
            this.storeMainCategoryRelationPersist.deleteStoreMainCategoryRelation(storeMainCategoryRelationVo);

            storeMainCategoryRelationVo.setUpdateUid(updateUid);
            for (Integer mainCategory : MainCategoryList) {
                storeMainCategoryRelationVo.setMainCategoryIdx(mainCategory);
                this.storeMainCategoryRelationPersist.insertStoreMainCategoryRelation(storeMainCategoryRelationVo);
            }
        }
    }

    /**
     * store gps 조회
     */
    public Integer countStoreForGps(Store store) {
        return this.storeMapper.countStoreForGps(store);
    }


    /**
     * JS 계좌정보 리스트에 Setting
     */
    public List<Store> setStoreSettleAccountList(List<Store> storeList) {
        if (ObjectUtils.isEmpty(storeList)) {
            return new ArrayList<>();
        }

        try {
            return storeSettlementService.storeSettleAccountInfoList(storeList);
        } catch (VlocallyException ve) {
            log.error("storeService.setStoreSettleAccountList() ERROR : " + ve.getMessage());
            return storeList;
        }
    }

    /**
     * 제휴점 JS 계좌정보 및 사업자정보 리스트 조회
     */
    public StoreAccountBizRespones storeSettleAccountBizNumberList(StoreTO.ListQuery query) {
        try {
            return storeSettlementService.storeSettleAccountBizNumberList(query);
        } catch (VlocallyException ve) {
            log.error("storeService.storeSettleAccountBizNumberList() ERROR : " + ve.getMessage());
            return new StoreAccountBizRespones();
        }
    }

    public List<OriginType> getFixedOriginType() {
        return Arrays.stream(OriginType.values()).filter(type -> type.getType().equals("fixed")).collect(Collectors.toList());
    }

    public List<OriginType> getDisposableOriginType() {
        return Arrays.stream(OriginType.values()).filter(type -> type.getType().equals("disposable"))
                .collect(Collectors.toList());
    }

    public List<Company>  getStoreInquiryCompany(StoreTO.ListQuery query){
        Long total  = storeMapper.getStoreInquiryCompanyCount(query);
        query.setTotalCount(total);
        return storeMapper.selectStoreInquiryCompany(query);
    }


    /*
     * 제휴사 간이영수증 변경시 공지사항 등록
     * */
    public int insertTempTaxNoticeAdd(AdminUser info, StoreDTO.UpdateStoreBizInfoBody BizInfo) throws VlocallyException{
        try {
            NoticeDto.NoticeCompanyRequest noticeCompanyRequest = new NoticeDto.NoticeCompanyRequest();
            Calendar calendar = Calendar.getInstance();
            List<String> comList = storeMapper.getStoreCompanyExcept(BizInfo);
            Store storeInfo = storeMapper.selectById(BizInfo.getStoreId());
            noticeCompanyRequest.setComIdList(comList);
            noticeCompanyRequest.setTitle(String.format("[식권대장]'%s'이용종료 공지 안내", storeInfo.getName()));
            String content = String.format(
                            "<p>안녕하세요.</br> "
                            + "모바일식권 식권대장입니다.</br> "
                            + "아래 제휴점은 제휴점 사정으로 %s월 %s일 %s(요일)부터 식권대장 이용이 불가합니다.</br> "
                            + "<ul><li>제휴점명 : %s</li></ul>"
                            + "식사 이용에 참고 부탁드립니다.</p> "
                    ,calendar.get(Calendar.MONTH)+1,calendar.get(Calendar.DATE),getDayOfWeek(calendar.get(Calendar.DAY_OF_WEEK)), storeInfo.getName());
            noticeCompanyRequest.setContentshtml(content);
            noticeCompanyRequest.setContentType("NOTICE");
            noticeCompanyRequest.setOpentype(NoticeOpentype.p) ; //선택한 고객사
            noticeCompanyRequest.setStatus(true);

            return boardService.insertNotice(noticeCompanyRequest, info.getAdminId());
        }catch (Exception e){
            log.error("insertTempTaxNoticeAdd -> insertNotice : FAIL {}",e);
        }
        return 0;
    }

    private String getDayOfWeek(int dayOfWeek){
        String stWeek = "";
        switch(dayOfWeek){
            case 1 : stWeek = "일"; break;
            case 2 : stWeek = "월"; break;
            case 3 : stWeek = "화"; break;
            case 4 : stWeek = "수"; break;
            case 5 : stWeek = "목"; break;
            case 6 : stWeek = "금"; break;
            case 7 : stWeek = "토"; break;
            default: return stWeek;
        }
        return stWeek;
    }
}
