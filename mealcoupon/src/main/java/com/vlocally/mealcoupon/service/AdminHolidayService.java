package com.vlocally.mealcoupon.service;

import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import com.vlocally.mealcoupon.controller.admin.entity.HolidayDto;
import com.vlocally.mealcoupon.controller.admin.entity.HolidayDto.HolidayInfo;
import com.vlocally.mealcoupon.exception.VlocallyException;
import com.vlocally.mealcoupon.persist.AdminHolidayPersist;
import com.vlocally.mealcoupon.vo.AdminUser;
import com.vlocally.mealcoupon.vo.Company;
import com.vlocally.mealcoupon.vo.HolidayCompanyInfo;
import com.vlocally.mealcoupon.vo.HolidayCountryInfo;

@Service
@Transactional
public class AdminHolidayService {

    @Autowired private AdminModifyLogService adminModifyLogService;
    @Autowired private AdminHolidayPersist adminHolidayPersist;
    @Autowired private CompanyService companyService;

    public List<HolidayCountryInfo> readHolidayCountryInfoList(String year) throws VlocallyException {
        return this.adminHolidayPersist.readHolidayCountryInfoList(year);
    }

    /**
     * 연도정보 로드 (기준년도 + 2년까지 노출)
     * */
    public List<String> readYear() {
        return this.adminHolidayPersist.readYear();
    }

    /**
     * 공휴일 정보 업데이트
     * */
    public void updateHoliday(HolidayDto.HolidayRequestDto holidayRequestDto, AdminUser info) throws VlocallyException {
        for (HolidayInfo holidayInfo : holidayRequestDto.getHolidayInfoList()) {
            if (ObjectUtils.isEmpty(holidayInfo.getProcess())) {
                continue;
            }
            HolidayCountryInfo beforeData = (holidayInfo.getProcess().equalsIgnoreCase("insert"))
                    ? null : this.adminHolidayPersist.readHolidayCountryInfo(holidayInfo.getIdx());
            Long idx = this.updateHolidayCountryInfo(holidayRequestDto.getYear(), holidayInfo, info.getAdminId()); // 국가공휴일 반영
            holidayInfo.setIdx(idx);
            this.updateHolidayCompanyInfo(holidayRequestDto.getYear(), holidayInfo, info.getAdminId()); // 고객사 공휴일에도 같이 반영

            // TODO. HolidayCountryInfo만 작업 (HolidayCompanyInfo은 많은 row 등록예상으로 검토가 필요.)
            this.adminModifyLogService.setAdminModifyLog(
                    info,
                    null,
                    String.valueOf(holidayInfo.getIdx()),
                    "HolidayCountryInfo",
                    beforeData,
                    this.adminHolidayPersist.readHolidayCountryInfo(holidayInfo.getIdx())
            );
        }
    }

    /**
     * 백오피스 공휴일 정보 업데이트
     * */
    public Long updateHolidayCountryInfo(String year, HolidayInfo holidayInfo, String adminId) {
        HolidayCountryInfo holidayCountryInfo = new HolidayCountryInfo();
        if (!ObjectUtils.isEmpty(holidayInfo.getIdx())) {
            holidayCountryInfo.setIdx(holidayInfo.getIdx());
        }
        holidayCountryInfo.setYear(year);
        holidayCountryInfo.setStartDate(holidayInfo.getStartDate());
        holidayCountryInfo.setEndDate(holidayInfo.getEndDate());
        holidayCountryInfo.setDuration(holidayInfo.getDuration());
        holidayCountryInfo.setName(holidayInfo.getName());
        if (!ObjectUtils.isEmpty(holidayInfo.getDescription())) {
            holidayCountryInfo.setDescription(holidayInfo.getDescription());
        }
        holidayCountryInfo.setIsDelete((holidayInfo.getProcess().equalsIgnoreCase("delete")) ? true : false);
        holidayCountryInfo.setUpdateId(adminId);
        this.adminHolidayPersist.updateHolidayCountryInfo(holidayCountryInfo);
        return holidayCountryInfo.getIdx();
    }

    /**
     * 회사 공휴일 정보 업데이트
     * */
    public void updateHolidayCompanyInfo(String year, HolidayInfo holidayInfo, String adminId) {
        if (holidayInfo.getProcess().equalsIgnoreCase("insert")) {
            HolidayCompanyInfo holidayCompanyInfo
                    = this.settingHolidayCompanyInfo(adminId, year, holidayInfo.getIdx(), holidayInfo);
            holidayCompanyInfo.setComIdList(this.comIdList()); // 전체 회사 comId 리스트 뽑음.
            this.adminHolidayPersist.insertHolidayCompanyInfo(holidayCompanyInfo);
        } else if (holidayInfo.getProcess().equalsIgnoreCase("update")) {
            HolidayCompanyInfo holidayCompanyInfo
                    = this.settingHolidayCompanyInfo(adminId, year, holidayInfo.getIdx(), holidayInfo);
            this.adminHolidayPersist.updateHolidayCompanyInfo(holidayCompanyInfo);
        } else if (holidayInfo.getProcess().equalsIgnoreCase("delete")) {
            HolidayCompanyInfo holidayCompanyInfo = new HolidayCompanyInfo();
            holidayCompanyInfo.setHolidayCountryIdx(holidayInfo.getIdx());
            holidayCompanyInfo.setIsDelete(true);
            this.adminHolidayPersist.updateHolidayCompanyInfo(holidayCompanyInfo);
        }
    }

    /**
     * HolidayCompanyInfo 업데이트를 위한 파라미터 셋팅
     * */
    private HolidayCompanyInfo settingHolidayCompanyInfo(String adminId, String year, Long idx, HolidayInfo holidayInfo) {
        HolidayCompanyInfo holidayCompanyInfo = new HolidayCompanyInfo();
        holidayCompanyInfo.setYear(year);
        holidayCompanyInfo.setHolidayCountryIdx(idx);
        holidayCompanyInfo.setStartDate(holidayInfo.getStartDate());
        holidayCompanyInfo.setEndDate(holidayInfo.getEndDate());
        holidayCompanyInfo.setDuration(holidayInfo.getDuration());
        holidayCompanyInfo.setName(holidayInfo.getName());
        if (!ObjectUtils.isEmpty(holidayInfo.getDescription())) {
            holidayCompanyInfo.setDescription(holidayInfo.getDescription());
        }
        holidayCompanyInfo.setUpdateId(adminId);
        return holidayCompanyInfo;
    }

    /**
     * 회사 comIdList 추출
     * */
    private List<String> comIdList() {
        List<Company> companyList = this.companyService.getCompanyList();
        List<String> comIdList = new ArrayList();
        for (Company company : companyList) {
            comIdList.add(company.getComid());
        }
        return comIdList;
    }

    /**
     * 회사 공휴일 테이블에 백오피스 공휴일 정보 등록
     * */
    public void insertHolidayCompanyInfo(String comId) throws VlocallyException {
        List<HolidayCompanyInfo> holidayCompanyInfoList = this.adminHolidayPersist.readHolidayCompanyInfoList(comId);
        if (ObjectUtils.isEmpty(holidayCompanyInfoList)) { //회사공휴일 정보가 존재하지 않는 경우
            this.adminHolidayPersist.insertHolidayCompanyInfoList(comId);
        }
    }
}
