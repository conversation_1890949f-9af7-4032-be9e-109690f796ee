package com.vlocally.mealcoupon.service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import com.vlocally.mealcoupon.constant.FileStoragePath;
import com.vlocally.mealcoupon.util.GUIDGenerator;
import com.vlocally.mealcoupon.util.UcloudStorageUtil;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.vlocally.mealcoupon.constant.Errors;
import com.vlocally.mealcoupon.constant.InitProperties;
import com.vlocally.mealcoupon.exception.VlocallyException;
import com.vlocally.mealcoupon.util.FileUtil;

import javax.annotation.PostConstruct;

/**
 * 이미지 파일 입출력 관리 jpg, jpeg, png
 * <AUTHOR>
 *
 */
@Service
public class FileService {

	@Autowired private InitProperties initProp;

//    private UcloudStorageUtil fileServer = new UcloudStorageUtil(initProp.getUcloudFilebox(), "<EMAIL>", "********************************");
    private UcloudStorageUtil fileServer;

	private Logger logger = Logger.getLogger("filemanage");

    @PostConstruct
    private void init(){
        fileServer = new UcloudStorageUtil(initProp.getFilebox(), "<EMAIL>", "********************************");
    }

    public byte[] getFile(String path, String filename) throws VlocallyException, IOException {
        return fileServer.getFile(path, filename);
    }

    private String getExt(String name){
        String f_ext = name.substring(name.lastIndexOf(".") + 1);
        return f_ext;

    }

    /**
     * 회사 로고 사진 업로드용
     * @param file
     * @return
     * @throws VlocallyException
     * @throws IOException
     */
    public String saveFileForCompanies(MultipartFile file) throws VlocallyException {
        String f_ext = getExt(file.getOriginalFilename());
        if(!f_ext.equals("jpg")&&!f_ext.equals("jpeg")&&!f_ext.equals("png")){
            logger.error("사진 업로드 실패 - unknown file\n"+"\n"+file.getOriginalFilename());
            throw new VlocallyException(Errors.File_UnknownEXT);
        }

        String name = FileUtil.getNewFileName(file);

        try {
            fileServer.putFile(FileStoragePath.SikdaeImageComanies.path, name, file, f_ext, null);
        } catch (IOException ioException) {
            logger.error("FileService.saveFileForCompanies() : {}", ioException);
            throw new VlocallyException(Errors.File_IOError);
        }

        return name;
    }

    /**
     * 회사 로고 사진 삭제
     * @param name
     */
    public void deleteFileForCompany(String name) {
        try {
            fileServer.deleteFile(FileStoragePath.SikdaeImageComanies.path, name);
            logger.error("파일삭제\n" + FileStoragePath.SikdaeImageComanies.path + "/" + name);
        } catch (Exception e) {
            logger.error("파일삭제 실패\n"+FileStoragePath.SikdaeImageComanies.path+"/"+name);
        }
    }

    /**
     * 공지 게시판 로고 사진 업로드
     * @param file
     * @return
     * @throws VlocallyException
     * @throws IOException
     */
    public String saveFileForBoard(MultipartFile file) throws VlocallyException, IOException {
        String f_ext = getExt(file.getOriginalFilename());
        if(!f_ext.equals("jpg")&&!f_ext.equals("jpeg")&&!f_ext.equals("png")){
            logger.error("사진 업로드 실패 - unknown file\n"+"\n"+file.getOriginalFilename());
            throw new VlocallyException(Errors.File_UnknownEXT);
        }

        String name = FileUtil.getNewFileName(file);

        fileServer.putFile(FileStoragePath.SikdaeNotice.path, name, file, f_ext, null);

        return name;
    }

    /**
     * 공지 게시판 로고 사진 삭제
     * @param name
     */
    public void deleteFileForBorad(String name) {
        try {
            fileServer.deleteFile(FileStoragePath.SikdaeNotice.path, name);
            logger.error("파일삭제\n" + FileStoragePath.SikdaeNotice.path + "/" + name);
        } catch (Exception e) {
            logger.error("파일삭제 실패\n"+FileStoragePath.SikdaeNotice.path+"/"+name);
        }
    }

    /**
     * 상점 사진 업로드
     * @param file
     * @return
     * @throws VlocallyException
     * @throws IOException
     */
    public String saveFileForStore(MultipartFile file) throws VlocallyException, IOException {
        String f_ext = getExt(file.getOriginalFilename());
        if(!f_ext.equals("jpg")&&!f_ext.equals("jpeg")&&!f_ext.equals("png")){
            logger.error("사진 업로드 실패 - unknown file\n"+"\n"+file.getOriginalFilename());
            throw new VlocallyException(Errors.File_UnknownEXT);
        }

        String name = FileUtil.getNewFileName(file);

        fileServer.putFile(FileStoragePath.SikdaeImageStores.path, name, file, f_ext, null);

        return name;
    }

    /**
     * 상점 사진 삭제
     * @param name
     */
    public void deleteFileForStore(String name){
        try {
            fileServer.deleteFile(FileStoragePath.SikdaeImageStores.path, name);
            logger.error("파일삭제\n" + FileStoragePath.SikdaeImageStores.path + "/" + name);
        } catch (Exception e) {
            logger.error("파일삭제 실패\n"+FileStoragePath.SikdaeImageStores.path+"/"+name);
        }
    }

    /**
     * 매점 브랜드 사진 업로드
     * @param file
     * @return
     * @throws VlocallyException
     * @throws IOException
     */
    public String saveFileForSnackBrand(MultipartFile file) throws VlocallyException, IOException {
        String f_ext = getExt(file.getOriginalFilename());
        if(!f_ext.equals("jpg")&&!f_ext.equals("jpeg")&&!f_ext.equals("png")){
            logger.error("사진 업로드 실패 - unknown file\n"+"\n"+file.getOriginalFilename());
            throw new VlocallyException(Errors.File_UnknownEXT);
        }

        String name = FileUtil.getNewFileName(file);

        fileServer.putFile(FileStoragePath.SikdaeImageSnackBrand.path, name, file, f_ext, null);

        return name;
    }

    /**
     * 매점 브랜드 사진 삭제
     * @param name
     * @return
     */
    public void deleteFileForSnackBrand(String name){
        try {
            fileServer.deleteFile(FileStoragePath.SikdaeImageSnackBrand.path, name);
            logger.error("파일삭제\n" + FileStoragePath.SikdaeImageSnackBrand.path + "/" + name);
        } catch (Exception e) {
            logger.error("파일삭제 실패\n" + FileStoragePath.SikdaeImageSnackBrand.path + "/" + name);
        }
    }

    public String saveFileForSnackCategory(MultipartFile file) throws VlocallyException, IOException {
        String f_ext = getExt(file.getOriginalFilename());
        if(!f_ext.equals("jpg")&&!f_ext.equals("jpeg")&&!f_ext.equals("png")){
            logger.error("사진 업로드 실패 - unknown file\n"+"\n"+file.getOriginalFilename());
            throw new VlocallyException(Errors.File_UnknownEXT);
        }

        String name = FileUtil.getNewFileName(file);

        fileServer.putFile(FileStoragePath.SikdaeImageSnackCategory.path, name, file, f_ext, null);

        return name;
    }

    public void deleteFilesForSnackCategory(String name) {
        try {
            fileServer.deleteFile(FileStoragePath.SikdaeImageSnackCategory.path, name);
            logger.error("파일삭제\n" + FileStoragePath.SikdaeImageSnackCategory.path + "/" + name);
        } catch (Exception e) {
            logger.error("파일삭제 실패\n" + FileStoragePath.SikdaeImageSnackCategory.path + "/" + name);
        }
    }

    public String saveFileForSnackThema(MultipartFile file) throws VlocallyException, IOException {
        String f_ext = getExt(file.getOriginalFilename());
        if(!f_ext.equals("jpg")&&!f_ext.equals("jpeg")&&!f_ext.equals("png")){
            logger.error("사진 업로드 실패 - unknown file\n"+"\n"+file.getOriginalFilename());
            throw new VlocallyException(Errors.File_UnknownEXT);
        }

        String name = FileUtil.getNewFileName(file);

        fileServer.putFile(FileStoragePath.SikdaeImageSnackThema.path, name, file, f_ext, null);

        return name;
    }

    public void deleteFilesForSnackThema(String name) {
        try {
            fileServer.deleteFile(FileStoragePath.SikdaeImageSnackThema.path, name);
            logger.error("파일삭제\n" + FileStoragePath.SikdaeImageSnackThema.path + "/" + name);
        } catch (Exception e) {
            logger.error("파일삭제 실패\n" + FileStoragePath.SikdaeImageSnackThema.path + "/" + name);
        }
    }

    public String saveFileForSnackProduct(MultipartFile file) throws VlocallyException, IOException {
        String f_ext = getExt(file.getOriginalFilename());
        if(!f_ext.equals("jpg")&&!f_ext.equals("jpeg")&&!f_ext.equals("png")){
            logger.error("사진 업로드 실패 - unknown file\n"+"\n"+file.getOriginalFilename());
            throw new VlocallyException(Errors.File_UnknownEXT);
        }

        String name = FileUtil.getNewFileName(file);

        fileServer.putFile(FileStoragePath.SikdaeImageSnackProduct.path, name, file, f_ext, null);

        return name;
    }

    public void deleteFilesForSnackProduct(String name) {
        try {
            fileServer.deleteFile(FileStoragePath.SikdaeImageSnackProduct.path, name);
            logger.error("파일삭제\n" + FileStoragePath.SikdaeImageSnackProduct.path + "/" + name);
        } catch (Exception e) {
            logger.error("파일삭제 실패\n" + FileStoragePath.SikdaeImageSnackProduct.path + "/" + name);
        }
    }

    public String saveFileForQPcon(String name, byte[] file) throws VlocallyException, IOException {

        fileServer.putFile(FileStoragePath.SikdaeImageQPcons.path, name, file, "png", null);

        return name;
    }

    public void deleteFileForQPcon(String name) {
        try {
            fileServer.deleteFile(FileStoragePath.SikdaeImageQPcons.path, name);
            logger.error("파일삭제\n" + FileStoragePath.SikdaeImageQPcons.path + "/" + name);
        } catch (Exception e) {
            logger.error("파일삭제 실패\n" + FileStoragePath.SikdaeImageQPcons.path + "/" + name);
        }
    }

    public String saveFileForExcel(String name, File file) throws VlocallyException, IOException {

        fileServer.putFile(FileStoragePath.SikdaeExcel.path, name, file, "xls", null);

        return name;
    }

    public void deleteFileForExcel(String name) {
        try {
            fileServer.deleteFile(FileStoragePath.SikdaeExcel.path, name);
            logger.error("파일삭제\n" + FileStoragePath.SikdaeExcel.path + "/" + name);
        } catch (Exception e) {
            logger.error("파일삭제 실패\n" + FileStoragePath.SikdaeExcel.path + "/" + name);
        }
    }

//	/**
//	 * 식권 회사 정보등의 이미지 파일 삭제하기
//	 * @param filename
//	 * @throws VlocallyException
//	 */
//	public void deleteFiles(String filename) {
//        deleteFiles(filename, initProp.getFileStorePath());
//
//	}

//	/**
//	 * 매점용 파일 입력
//	 * @param file
//	 * @return
//	 * @throws VlocallyException
//	 * @throws IOException
//	 */
//	public String saveFileForSnack(MultipartFile file) throws VlocallyException, IOException{
//		return saveFile(file, initProp.getSnackImgPath(), "s");
//	}

//	/**
//	 * 매점용 파일 삭제
//	 * @param filename
//	 */
//	public void deleteFilesForSnack(String filename){
//		deleteFiles(filename, initProp.getSnackImgPath());
//	}

//	/**
//	 * 상점 사진 파일 추가
//	 * @param file
//	 * @return
//	 * @throws VlocallyException
//	 * @throws IOException
//	 */
//	public String saveFileForStore(MultipartFile file) throws VlocallyException, IOException {
//		return saveFile(file, initProp.getFileStorePath(), "store");
//	}



//	/**
//	 * 게시판 사진 파일 추가
//	 * @param file
//	 * @return
//	 * @throws VlocallyException
//	 * @throws IOException
//	 */
//	public String saveFileForBoard(MultipartFile file) throws VlocallyException, IOException {
//		return saveFile(file, initProp.getBoardImgPath(), "board");
//	}

//	/**
//	 * 상점 사진 삭제
//	 * @param filename
//	 */
//	public void deleteFilesForStore(String filename){
//		deleteFiles(filename, initProp.getFileStorePath());
//	}
	
	/**
	 * 이미지 파일 입력 jpg, jpeg, png 만 가능
	 * @param file
	 * @param path
	 * @param prefix
	 * @return
	 * @throws VlocallyException
	 * @throws IOException
	 */
	private String saveFile(MultipartFile file, String path, String prefix) throws VlocallyException, IOException{
		String orgin_name = file.getOriginalFilename().toLowerCase();
		String f_ext = orgin_name.substring(orgin_name.lastIndexOf(".") + 1);
		if(!f_ext.equals("jpg")&&!f_ext.equals("jpeg")&&!f_ext.equals("png")){
			logger.error("사진 업로드 실패 - unknown file\n"+"\n"+orgin_name);
			throw new VlocallyException(Errors.File_UnknownEXT);
		}
		
		String filename = null;
		try {
			filename = FileUtil.saveFile(file, path, prefix);
			
			logger.info("사진 업로드 "+orgin_name+"\n"+filename);
			return filename;
		} catch (IOException e) {
			logger.error("사진 업로드 실패 "+orgin_name+"\n"+filename, e);
			throw e;
		}
	}


	/**
	 * 이미지 파일 삭제
	 * @param filename
	 * @param path
	 */
	private void deleteFiles(String filename, String path){
		if(filename==null||filename.equals("")){
			return;
		}
		FileUtil.removeFile(path+"/"+filename);
		logger.info("사진 삭제 "+path+"/"+filename);
	}


}
