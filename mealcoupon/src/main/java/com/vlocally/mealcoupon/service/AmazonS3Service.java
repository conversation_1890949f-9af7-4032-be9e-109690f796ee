package com.vlocally.mealcoupon.service;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.vlocally.mealcoupon.constant.Errors;
import com.vlocally.mealcoupon.exception.VlocallyException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;

@Slf4j(topic = "adminio")
@Service
public class AmazonS3Service {

    @Autowired private AmazonS3 amazonS3;

    public byte[] getFile(String path, String fileName) throws VlocallyException {


        try (S3Object s3object = this.amazonS3.getObject(new GetObjectRequest(path, fileName));
             InputStream is = s3object.getObjectContent();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {

            int len;
            byte[] buffer = new byte[4096];
            while ((len = is.read(buffer, 0, buffer.length)) != -1) {
                baos.write(buffer, 0, len);
            }

            return baos.toByteArray();

        } catch (FileNotFoundException e) {
            throw new VlocallyException(Errors.File_isEmpty, e);
        } catch (IOException e) {
            throw new VlocallyException(Errors.File_IOError, e);
        }
    }

    public void putFile(String path, String fileName, byte[] fileBytes, int size) {

        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(size);

        InputStream targetStream = new ByteArrayInputStream(fileBytes);

        this.amazonS3.putObject(new PutObjectRequest(path, fileName, targetStream, metadata)
                .withCannedAcl(CannedAccessControlList.PublicRead));
    }

    public void putFileWrite(HttpServletResponse res, String ContentType, String fileName, byte[] file) throws VlocallyException {

        try {

            res.setContentType(ContentType);
            //res.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            res.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\"");

            OutputStream out = res.getOutputStream();
            out.write(file, 0, file.length);
            out.close();

        } catch (FileNotFoundException e) {
            throw new VlocallyException(Errors.File_isEmpty, e);
        } catch (IOException e) {
            throw new VlocallyException(Errors.File_IOError, e);
        }
    }

    public void putFileWriteUTF8(HttpServletResponse res, String ContentType, String fileName, byte[] file) throws VlocallyException {

        try {

            res.setContentType(ContentType);
            res.setHeader("Content-Disposition", "attachment;fileName=\"" + URLEncoder.encode(fileName,"UTF-8").replace("+","%20")+ "\";");

            OutputStream out = res.getOutputStream();
            out.write(file, 0, file.length);
            out.close();

        } catch (FileNotFoundException e) {
            throw new VlocallyException(Errors.File_isEmpty, e);
        } catch (IOException e) {
            throw new VlocallyException(Errors.File_IOError, e);
        }
    }

    public void deleteFile(String bucketPath, String fileName) {
        this.amazonS3.deleteObject(new DeleteObjectRequest(bucketPath, fileName));
    }
}
