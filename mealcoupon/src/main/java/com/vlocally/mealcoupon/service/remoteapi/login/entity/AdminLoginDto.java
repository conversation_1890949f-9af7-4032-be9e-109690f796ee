package com.vlocally.mealcoupon.service.remoteapi.login.entity;

import java.util.Date;
import java.util.List;

import com.vlocally.mealcoupon.annotaion.CIField;
import com.vlocally.mealcoupon.util.CiUtil.CIFieldType;
import lombok.Data;

public class AdminLoginDto {

    @Data
    public static class LoginRemoteResponse {

        private Paging paging;
        private List<Content> contents;

        @Data
        public static class Paging {
            private int page = 0;
            private int pageRow = 0;
            private int totalCount = 0;
        }

        @Data
        public static class Content {
            private Service service;
            private String serviceId;
            private String serviceName;
            private String guid;
            @CIField(type = CIFieldType.Account)
            private String signId;
            @CIField(type = CIFieldType.Ip)
            private String ip;
            private String userAgent;
            private Date created;
        }
    }

    public enum Service {
        SIKDAE		("SIKDAE", "식권대장"),
        COMPANY		("COMPANY", "회사관리자"),
        STORE		("STORE", "식당대장"),
        BACKOFFICE	("BACKOFFIC<PERSON>", "백오피스"),
        CAFETERIA	("CAFETERIA", "구내식당");

        private String code;
        private String msg;

        public String getCode(){
            return code;
        }

        public String getMsg(){
            return msg;
        }

        Service (String code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }
}
