package com.vlocally.mealcoupon.service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

import jxl.common.Logger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.vlocally.mealcoupon.constant.Errors;
import com.vlocally.mealcoupon.constant.db.UserLevel;
import com.vlocally.mealcoupon.dao.UserGroupDAO;
import com.vlocally.mealcoupon.exception.VlocallyException;
import com.vlocally.mealcoupon.util.GUIDGenerator;
import com.vlocally.mealcoupon.util.LogUtil;
import com.vlocally.mealcoupon.vo.User;
import com.vlocally.mealcoupon.vo.Usergroup;
import com.vlocally.mealcoupon.vo.Usergroupmember;
import com.vlocally.mealcoupon.vo.UsergroupmemberKey;

@Service
public class UserGroupService {

	@Autowired private UserService userService;
	@Autowired private UserGroupDAO groupDAO;
	
	public static final int MAX_GROUP_USER = 20;
	
	private Logger log = Logger.getLogger(getClass());
	
	public void setGroup(Usergroup group){
		group.setGroupid(GUIDGenerator.getRandomGUIDString());
		group.setRegdate(new Date());
		groupDAO.insertGroup(group);
		
		log.info("사용자 그룹 추가 "+LogUtil.writeBean(group));
		
	}
	
	public int updateGroupForUser(Usergroup group){
		group.setRegdate(null);
		group.setMember(null);
		int result = groupDAO.updateGroupForUser(group);
		if(result==1){
			log.info("사용자 그룹 수정"+LogUtil.writeBean(group));
		}
		return result;
	}
	
	public void setGroupMember(Usergroupmember member) throws VlocallyException{
		Usergroup group = groupDAO.selectGroup(member.getGroupid());
		if(group==null){
			throw new VlocallyException(Errors.UserGroup_UnknownGroup);
		}
		if(group.getMember()>=MAX_GROUP_USER){
			throw new VlocallyException(Errors.UserGroup_UserTooMany);
		}
		User owner = userService.getUser(group.getOwnerid());
		if(owner==null||owner.getLevel()<=UserLevel.Inactive.level){
			throw new VlocallyException(Errors.Account_UnknownUser);
		}
		String comid = owner.getComid();
		User memberUser = userService.getUser(member.getUid());
		if(memberUser==null||memberUser.getLevel()<=UserLevel.Inactive.level){
			throw new VlocallyException(Errors.Account_UnknownUser);
		}
		if(memberUser.getUid().equals(owner.getUid())){
			throw new VlocallyException(Errors.UserGroup_CantAddSelf);
		}
		if(!comid.equals(memberUser.getComid())){
			throw new VlocallyException(Errors.Account_UnknownUser);
		}
		member.setRegdate(new Date());
		groupDAO.insertGroupMember(member);
		groupDAO.updateGroupIncrease(group.getGroupid());
		log.info("(관리자권한)사용자 그룹 멤버 추가 "+LogUtil.writeBean(member));
	}
	
	public void setGroupMemberForUser(Usergroupmember member, String ownerid) throws VlocallyException{
		Usergroup group = groupDAO.selectGroup(member.getGroupid());
		if(group==null){
			throw new VlocallyException(Errors.UserGroup_UnknownGroup);
		}
		if(group.getMember()>=MAX_GROUP_USER){
			throw new VlocallyException(Errors.UserGroup_UserTooMany);
		}
		User owner = userService.getUser(group.getOwnerid());
		if(owner==null||owner.getLevel()<=UserLevel.Inactive.level){
			throw new VlocallyException(Errors.Account_UnknownUser);
		}
		if(!owner.getUid().equals(ownerid)){
			throw new VlocallyException(Errors.UserGroup_UnknownGroup);
		}
		String comid = owner.getComid();
		User memberUser = userService.getUser(member.getUid());
		if(memberUser==null||memberUser.getLevel()<=UserLevel.Inactive.level){
			throw new VlocallyException(Errors.Account_UnknownUser);
		}
		if(memberUser.getUid().equals(owner.getUid())){
			throw new VlocallyException(Errors.UserGroup_CantAddSelf);
		}
		if(!comid.equals(memberUser.getComid())){
			throw new VlocallyException(Errors.Account_UnknownUser);
		}
		member.setRegdate(new Date());
		groupDAO.insertGroupMember(member);
		groupDAO.updateGroupIncrease(group.getGroupid());
		log.info("사용자 그룹 멤버 추가 "+LogUtil.writeBean(member));
	}
	
	public void setGroupMemberForUser(String groupid, String ownerid, String[] addusers, String[] removeusers) throws VlocallyException{
		Date now = new Date();
		Usergroup group = groupDAO.selectGroup(groupid);
		if(group==null){
			throw new VlocallyException(Errors.UserGroup_UnknownGroup);
		}
		if(addusers==null){addusers = new String[0];}
		if(removeusers==null){removeusers = new String[0];}
		if(group.getMember()+addusers.length-removeusers.length>=MAX_GROUP_USER){
			throw new VlocallyException(Errors.UserGroup_UserTooMany);
		}
		User owner = userService.getUser(group.getOwnerid());
		if(owner==null||owner.getLevel()<=UserLevel.Inactive.level){
			throw new VlocallyException(Errors.Account_UnknownUser);
		}
		if(!owner.getUid().equals(ownerid)){
			throw new VlocallyException(Errors.UserGroup_UnknownGroup);
		}
		// 목록에 같은 사용자가 있는지 처리하기
		for(String au : addusers){
			for(String ru : removeusers){
				if(au.equals(ru)){
					throw new VlocallyException(Errors.UserGroup_WrongUserInfo);
				}
			}
		}
		StringBuilder sb = new StringBuilder();
		sb.append("사용자 그룹 수정 groupid: "+groupid+"\nadd users("+addusers.length+")");
		int membernum = group.getMember();
		String comid = owner.getComid();
		Usergroupmember member = new Usergroupmember();
		member.setGroupid(groupid);
		member.setRegdate(now);
		for(String userid : addusers){
			User memberUser = userService.getUser(userid);
			if(memberUser==null||memberUser.getLevel()<=UserLevel.Inactive.level){
				throw new VlocallyException(Errors.Account_UnknownUser);
			}
			if(memberUser.getUid().equals(owner.getUid())){
				throw new VlocallyException(Errors.UserGroup_CantAddSelf);
			}
			if(!comid.equals(memberUser.getComid())){
				throw new VlocallyException(Errors.Account_UnknownUser);
			}
			member.setUid(userid);
			try{
				// 입력 실패시 오류 처리하기
				groupDAO.insertGroupMember(member);
				membernum++;
				sb.append("\n"+userid);
			} catch(Exception e){
//				throw new VlocallyException(Errors.UserGroup_DupplicateUser, e);
			}
		}
		
		sb.append("\nremove users("+removeusers.length+")");
		UsergroupmemberKey key = new UsergroupmemberKey();
		key.setGroupid(groupid);
		for(String userid : removeusers){
			key.setUid(userid);
			if(groupDAO.deleteGroupMember(key)==1){
				membernum--;
				sb.append("\n"+userid);
			} else {
				sb.append("\n"+userid+"(not apply)");
			}
		}
		group.setMember(membernum);
		groupDAO.updateGroup(group);
		log.info(sb.toString());
	}
	
	public int deleteGroup(String groupid){
		int result = groupDAO.deleteGroup(groupid);
		if(result==1){
			result += groupDAO.deleteGroupMemberByGroupid(groupid);
			log.info("(관리자권한)사용자 그룹 삭제 "+(result-1)+"멤버 "+groupid);
		}
		return result;
	}
	
	public int deleteGroupForUser(String groupid, String ownerid) {
		int result = groupDAO.deleteGroupForUser(groupid, ownerid);
		if(result==1){
			result += groupDAO.deleteGroupMemberByGroupid(groupid);
			log.info("사용자 그룹 삭제 "+(result-1)+"멤버 "+groupid);
		}
		return result;
	}
	
	public int deleteGroupMember(String groupid, String uid){
		UsergroupmemberKey key = new UsergroupmemberKey();
		key.setGroupid(groupid);
		key.setUid(uid);
		int result = groupDAO.deleteGroupMember(key);
		if(result==1){
			groupDAO.updateGroupDecrease(groupid);
			log.info("사용자 그룹 멤버 삭제 "+uid+" 사용자 in "+groupid);
		}
		return result;
	}
	
	public Usergroup getGroup(String groupid){
		return groupDAO.selectGroup(groupid);
	}
	
	public List<Usergroup> getGroupByOwner(String uid){
		return groupDAO.selectGroupByOwner(uid);
	}
	
	public Usergroupmember getGroupMember(UsergroupmemberKey key) {
		return groupDAO.selectGroupMember(key);
	}
	
	/**
	 * groupid, uid, name, regdate
	 * @param groupid
	 * @return
	 */
	public List<HashMap<String, Object>> getGroupMember(String groupid){
		return groupDAO.selectGroupMember(groupid);
	}

	/**
	 * groupid, uid, name, regdate
	 * @param groupid
	 * @param ownerid
	 * @return
	 * @throws VlocallyException
	 */
	public List<HashMap<String, Object>> getGroupMemberForUser(String groupid, String ownerid) throws VlocallyException{
		Usergroup group = groupDAO.selectGroup(groupid);
		if(group==null||!group.getOwnerid().equals(ownerid)){
			throw new VlocallyException(Errors.UserGroup_UnknownGroup);
		}
		return groupDAO.selectGroupMember(groupid);
	}
	
}
