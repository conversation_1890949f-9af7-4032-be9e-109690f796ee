package com.vlocally.mealcoupon.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import com.vlocally.mealcoupon.constant.Errors;
import com.vlocally.mealcoupon.constant.db.StoreSupplyType;
import com.vlocally.mealcoupon.exception.CommonException;
import com.vlocally.mealcoupon.persist.ThirdPartyCouponPersist;
import com.vlocally.mealcoupon.vo.Store;
import com.vlocally.mealcoupon.vo.ThirdPartyCouponVo;

@Service
@Transactional
public class ThirdPartyCouponService {

    @Autowired private ThirdPartyCouponPersist thirdPartyCouponPersist;
    @Autowired private StoreService storeService;

    /**
     * 외부쿠폰 사용자가 존재하는지 확인
     * */
    public void checkThirdPartyCouponUser(Byte supplytype, String storeId, Long officeIdx) {

        // supplytype이 안넘어오는 경우에 한해 Store 제휴점 조회
        if (ObjectUtils.isEmpty(supplytype)) {
            Store store = this.storeService.getStore(storeId);
            supplytype = store.getSupplytype();
        }
        if (supplytype == StoreSupplyType.COUPON.type) {
            ThirdPartyCouponVo thirdPartyCouponVo = new ThirdPartyCouponVo();
            thirdPartyCouponVo.setStoreId(storeId);
            if (!ObjectUtils.isEmpty(officeIdx)) {
                thirdPartyCouponVo.setOfficeIdx(officeIdx);
            }
            Integer count = this.thirdPartyCouponPersist.checkThirdPartyCouponUser(thirdPartyCouponVo);
            if (!ObjectUtils.isEmpty(count) && count > 0) {
                if (!ObjectUtils.isEmpty(officeIdx)) { // 제휴점용 메세지
                    throw new CommonException(
                            Errors.REMAIN_THIRDPARTY_COUPON_BY_STORE,
                            Errors.REMAIN_THIRDPARTY_COUPON_BY_STORE.msg.replace("{count}", String.valueOf(count)));
                } else { // 고객사용 메세지
                    throw new CommonException(
                            Errors.REMAIN_THIRDPARTY_COUPON_BY_COMPANY,
                            Errors.REMAIN_THIRDPARTY_COUPON_BY_COMPANY.msg.replace("{count}", String.valueOf(count)));
                }
            }
        }
    }
}
