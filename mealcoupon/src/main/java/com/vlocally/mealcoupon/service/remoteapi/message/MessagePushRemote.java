package com.vlocally.mealcoupon.service.remoteapi.message;

import java.net.URI;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import com.vlocally.mealcoupon.constant.InitProperties;
import com.vlocally.mealcoupon.controller.admin.entity.MessagePushDto.UserPushRequest;

@Service
public class MessagePushRemote {

    @Autowired private RestTemplate restTemplate;
    @Autowired private InitProperties initProperties;

    /**
     * Message Server Send
     */
    public void send(UserPushRequest request) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(request, httpHeaders);
        URI uri = UriComponentsBuilder.fromUriString(
                        this.initProperties.getVendysServerMessageHost() + this.initProperties.getVendysServerMessageUrlUserPushSend())
                .build().toUri();

        this.restTemplate.exchange(uri, HttpMethod.POST,httpEntity, String.class);
    }
}
