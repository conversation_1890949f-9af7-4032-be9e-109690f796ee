package com.vlocally.mealcoupon.service;

import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.imageio.ImageIO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import com.vlocally.mealcoupon.constant.Errors;
import com.vlocally.mealcoupon.controller.admin.entity.AdminModifyLogDto.UpdateHistoryRequest;
import com.vlocally.mealcoupon.controller.admin.entity.AdminModifyLogDto.UpdateHistoryResponse;
import com.vlocally.mealcoupon.controller.admin.entity.ShippingSpotVo.CompanyModifyStatus;
import com.vlocally.mealcoupon.controller.admin.entity.ShippingSpotVo.CompanyShippingSpotTemplate;
import com.vlocally.mealcoupon.controller.admin.entity.ShippingSpotVo.CompanyShippingSpotVo;
import com.vlocally.mealcoupon.controller.admin.entity.ShippingSpotVo.CompanyShippingSpotWithStore;
import com.vlocally.mealcoupon.controller.admin.entity.ShippingSpotVo.CompanyShippingSpotWithStore.ShippingSpotStore;
import com.vlocally.mealcoupon.controller.admin.entity.ShippingSpotVo.ShippingSpotResponseVo;
import com.vlocally.mealcoupon.dao.CompanyDAO;
import com.vlocally.mealcoupon.dao.UserDAO;
import com.vlocally.mealcoupon.domain.master.CompanyTO;
import com.vlocally.mealcoupon.domain.master.PageInfo;
import com.vlocally.mealcoupon.exception.CommonException;
import com.vlocally.mealcoupon.exception.VlocallyException;
import com.vlocally.mealcoupon.mapper.master.BookingTemplateShippingSpotMapper;
import com.vlocally.mealcoupon.mapper.master.CaptainPaymentCompanyInfoMapper;
import com.vlocally.mealcoupon.mapper.master.CompanyMapper;
import com.vlocally.mealcoupon.persist.CaptainPaymentServiceApplyPersist;
import com.vlocally.mealcoupon.service.entity.CaptainPaymentCompanyInfoDto.InsertHistoryOrUpdateInfo;
import com.vlocally.mealcoupon.service.entity.CaptainPaymentCompanyInfoDto.InsertOrUpdateInfo;
import com.vlocally.mealcoupon.service.entity.CaptainPaymentCompanyInfoDto.ServiceTypeSearchInfo;
import com.vlocally.mealcoupon.service.entity.CaptainPaymentServiceApplyDto.UpdateParam;
import com.vlocally.mealcoupon.service.entity.CompanyShippingSpotDto.BookingTemplateShippingSpotModifyDto;
import com.vlocally.mealcoupon.service.entity.CompanyShippingSpotDto.CompanyShippingSpotSaveDto;
import com.vlocally.mealcoupon.service.entity.ModifyLogDto.CompanyModifyLogDto;
import com.vlocally.mealcoupon.service.remoteapi.customer.CustomerService;
import com.vlocally.mealcoupon.service.remoteapi.customer.entity.CustomerDto;
import com.vlocally.mealcoupon.service.remoteapi.settlement.StoreSettlementService;
import com.vlocally.mealcoupon.service.remoteapi.settlement.entity.StoreDTO.StoreCompanyResponse;
import com.vlocally.mealcoupon.service.websocket.WebSocketRemoteService;
import com.vlocally.mealcoupon.util.LogUtil;
import com.vlocally.mealcoupon.util.PageUtil;
import com.vlocally.mealcoupon.vo.AdminModifyLog;
import com.vlocally.mealcoupon.vo.AdminUser;
import com.vlocally.mealcoupon.vo.CaptainPaymentCompanyInfo;
import com.vlocally.mealcoupon.vo.CaptainPaymentCompanyInfo.ServiceType;
import com.vlocally.mealcoupon.vo.CaptainPaymentCompanyInfo.Status;
import com.vlocally.mealcoupon.vo.CaptainPaymentServiceApply.ApplyStatus;
import com.vlocally.mealcoupon.vo.Company;
import com.vlocally.mealcoupon.vo.CompanyBalanceInfo;
import com.vlocally.mealcoupon.vo.CompanyCommand;
import com.vlocally.mealcoupon.vo.CompanyExample;
import com.vlocally.mealcoupon.vo.Companylimit;
import com.vlocally.mealcoupon.vo.Discountmenu;
import com.vlocally.mealcoupon.vo.Office;
import com.vlocally.mealcoupon.vo.OfficeHistory;
import org.apache.log4j.Logger;

/**
 * 회사 정보 조정
 * 회사 식대 제한 관련 정보 조정
 * <AUTHOR>
 *
 */
@Service
@Transactional
public class CompanyService {

	@Autowired private CompanyDAO comDAO;
	@Autowired private CompanyMapper comMapper;
	@Autowired private AdminModifyLogService adminModifyLogService;
	@Autowired private ImageService imageService;
	@Autowired private FileService fileService;
	@Autowired private UserDAO userDAO;
	@Autowired private CustomerService customerService;
	@Autowired private SettlementService settlementService;
	@Autowired private AdminHolidayService adminHolidayService;
	@Autowired private OfficeService officeService;
	@Autowired private StoreSettlementService storeSettlementService;
	@Autowired private WebSocketRemoteService webSocketRemoteService;
	@Autowired private CaptainPaymentCompanyInfoMapper captainPaymentCompanyInfoMapper;
	@Autowired private CaptainPaymentServiceApplyPersist captainPaymentServiceApplyPersist;
	@Autowired private BookingTemplateShippingSpotMapper bookingTemplateShippingSpotMapper;

	private Logger log = Logger.getLogger("modinfo");

	/**
	 * 회사 입력
	 * @param com
	 * @throws IOException
	 * @throws VlocallyException
	 */
	public void insert(AdminUser info, CompanyCommand com) throws VlocallyException {

		/**
		 * (#BO-158) 조직구조 다변화 회사 생성 추가 jangjungsu on 2018. 3. 13..
		 */
		CustomerDto.CompanyCreateRequest request = new CustomerDto.CompanyCreateRequest();
		request.setName(com.getName());
		request.setAddress(com.getAddress());
		request.setAddressRoad(com.getAddressRoad());
		request.setAddressJibun(com.getAddressJibun());
		request.setAddressDetail(com.getAddressDetail());
		request.setAddressSido(com.getAddressSido());
		request.setAddressSigugun(com.getAddressSigugun());
		request.setAddressDongmyun(com.getAddressDongmyun());
		request.setAddressZipCode(com.getAddressZipCode());
		request.setGpslat(com.getGpslat());
		request.setGpslon(com.getGpslon());
		request.setRegion(com.getRegion());
		request.setIncharge(com.getChargename());
		request.setPhone(com.getPhone().replace("-",""));
		request.setCalculate(com.getCalculatedate());
		request.setMemo(com.getIntro());
		request.setEmployee(com.getEmploynum());
		request.setActive(com.getStatus() != 0);
		request.setIsTest(com.getIsTest() != 0);
		request.setUserName(info.getName());
		request.setSikdaeEnable(com.getSikdaeEnable());
		request.setWelfareEnable(com.getWelfareEnable());
		request.setUseSikdaeInMarket(com.getUseSikdaeInMarket());
		request.setStoreConfirm(com.getStoreConfirm() != 0);

		CustomerDto.CompanyCreateResponse companyCreateResponse = this.customerService.setCompany(info.getAdminId(), request);
		LogUtil.writeBean(log, com, "회사 입력");

		if (!ObjectUtils.isEmpty(companyCreateResponse)) {
			// 회사 등록 후 CI 등록처리
			if (!ObjectUtils.isEmpty(com.getCompanyCiFile())) {
				String fileName = this.companyS3ImageUpload(com.getCompanyCiFile(), companyCreateResponse.getCompanyId(),
						Arrays.asList("jpg", "jpeg", "png"));
				Company company = new Company();
				company.setComid(companyCreateResponse.getCompanyId());
				company.setCompanyCi(fileName);
				comDAO.update(company);
			}
			// 회사 등록 후, 공휴일 정보도 등록처리함.
			this.adminHolidayService.insertHolidayCompanyInfo(companyCreateResponse.getCompanyId());
			LogUtil.writeBean(log, com, "[회사등록] 회사 공휴일 등록");
		}

		// 감사로그 기록
		Company readCompany = this.comDAO.selectCompany(companyCreateResponse.getCompanyId());
		this.adminModifyLogService.setAdminModifyLog(
				info,
				readCompany.getComid(),
				readCompany.getComid(),
				"Company",
				null,
				readCompany
		);
	}

	/**
	 * 회사 정보 수정
	 * @param com
	 * @return
	 * @throws IOException
	 * @throws VlocallyException
	 */
	public Company update(AdminUser info, CompanyCommand com) throws VlocallyException {
		Company pcom = null;
		String paramComId = com.getComid();
		Company old = this.comDAO.selectCompany(paramComId);

		// Compant Image ADD
		if (!ObjectUtils.isEmpty(com.getIconfile())) {
			pcom = this.comDAO.selectCompany(paramComId);
			String filename = this.fileService.saveFileForCompanies(com.getIconfile());
			if (!ObjectUtils.isEmpty(filename)) {
				com.setIcon(filename);
			}
		}
		if (!ObjectUtils.isEmpty(com.getCompanyCiFile())) {
			String filename = this.companyS3ImageUpload(com.getCompanyCiFile(), paramComId, Arrays.asList("jpg", "jpeg", "png"));
			if (!ObjectUtils.isEmpty(filename)) {
				com.setCompanyCi(filename);
			}
		}

		com.setRegdate(null);
		com.setCash(null);
		com.setPhone(com.getPhone().replace("-",""));
		// Limit Update
		this.updateCompanyLimit(com);

		Boolean sikdaeEnable = com.getSikdaeEnable();
		Boolean welfareEnable = com.getWelfareEnable();
		// 식권대장 활성화를 체크한 경우
		if (!ObjectUtils.isEmpty(sikdaeEnable) && sikdaeEnable) {
			this.setPaymentServiceTypeInfo(paramComId, info.getAdminId(), ServiceType.SIKDAE);
		}
		// 복지대장 활성화를 체크한 경우
		if (!ObjectUtils.isEmpty(welfareEnable) && welfareEnable) {
			this.setPaymentServiceTypeInfo(paramComId, info.getAdminId(), ServiceType.WELFARE);
			this.updateMealcUsemypointCondition(paramComId);
		}
/*
		if (!ObjectUtils.isEmpty(sikdaeEnable) && sikdaeEnable) { // 식권대장 활성화를 체크한 경우
			String comId = com.getComid();
			String userId = info.getAdminId();

			ServiceTypeSearchInfo searchInfo = new ServiceTypeSearchInfo();
			searchInfo.setComId(comId);
			searchInfo.setServiceType(ServiceType.SIKDAE);

			CaptainPaymentCompanyInfo captainPaymentCompanyInfo = this.captainPaymentCompanyInfoMapper
					.selectByComIdAndServiceType(searchInfo);
			Long companyInfoKey = null;
			Date now = new Date();
			Date createDate = null;
			String createUser = null;
			if (!ObjectUtils.isEmpty(captainPaymentCompanyInfo)) { // 이미 식대 사용여부 정보가 있으면
				InsertOrUpdateInfo insertOrUpdateInfo = new InsertOrUpdateInfo();
				insertOrUpdateInfo.setComId(comId);
				insertOrUpdateInfo.setStatus(Status.ACTIVE.name());
				insertOrUpdateInfo.setUpdateUser(userId);
				insertOrUpdateInfo.setUpdateDate(now);
				insertOrUpdateInfo.setServiceType(ServiceType.SIKDAE);
				this.captainPaymentCompanyInfoMapper.update(insertOrUpdateInfo);

				// 이미 있으면 기존 정보에서 가져오고 (history)
				createDate = captainPaymentCompanyInfo.getCreateDate();
				createUser = captainPaymentCompanyInfo.getCreateUser();
				companyInfoKey = captainPaymentCompanyInfo.getIdx();

			} else { // 기존정보가 없을경우 대비
				InsertOrUpdateInfo insertOrUpdateInfo = new InsertOrUpdateInfo();
				insertOrUpdateInfo.setComId(comId);
				insertOrUpdateInfo.setUpdateUser(userId);
				insertOrUpdateInfo.setUpdateDate(now);
				insertOrUpdateInfo.setCreateUser(userId);
				insertOrUpdateInfo.setCreateDate(now);
				insertOrUpdateInfo.setStatus(Status.ACTIVE.name());
				insertOrUpdateInfo.setServiceType(ServiceType.SIKDAE);
				this.captainPaymentCompanyInfoMapper.insert(insertOrUpdateInfo);

				// 없을경우 insert 한 내용 확인 (history 용)
				createDate = insertOrUpdateInfo.getCreateDate();
				createUser = insertOrUpdateInfo.getCreateUser();
				companyInfoKey = insertOrUpdateInfo.getInsertIdx();
			}

			UpdateParam updateParam = new UpdateParam();
			updateParam.setUpdateUser(userId);
			updateParam.setUpdateDate(now);
			updateParam.setComId(comId);
			updateParam.setStatus(ApplyStatus.COMPLETE);
			this.captainPaymentServiceApplyPersist.update(updateParam);

			InsertHistoryOrUpdateInfo historyInfo = new InsertHistoryOrUpdateInfo();
			historyInfo.setCompanyInfoIdx(companyInfoKey);
			historyInfo.setComId(comId);
			historyInfo.setUpdateUser(userId);
			historyInfo.setUpdateDate(now);
			historyInfo.setCreateUser(createUser);
			historyInfo.setCreateDate(createDate);
			historyInfo.setServiceType(ServiceType.SIKDAE);
			historyInfo.setStatus(Status.ACTIVE.name());
			historyInfo.setStatus(Status.ACTIVE.name());
			historyInfo.setHistoryDate(now);
			this.captainPaymentCompanyInfoMapper.insertHistory(historyInfo);
		}
*/

		int result = this.comDAO.update(com);
		if (result != 1) {
			throw new VlocallyException(Errors.Company_UnknownCompany);
		}

		// Compant Image Delete
		if (!ObjectUtils.isEmpty(com.getIcon())
				&& !ObjectUtils.isEmpty(pcom)
				&& !ObjectUtils.isEmpty(pcom.getIcon())
				&& !com.getIcon().equals(pcom.getIcon())) {
			this.fileService.deleteFileForCompany(pcom.getIcon());
		}

		if (!ObjectUtils.isEmpty(com.getName())) {
			this.userDAO.updateCompanyNameByCompanyid(com.getComid(), com.getName());
		}

		LogUtil.writeBean(log, com, "회사 변경");
		// 회사 등록 후, 공휴일 정보도 등록처리함.
		if (result == 1) {
			this.adminHolidayService.insertHolidayCompanyInfo(com.getComid());
			LogUtil.writeBean(log, com, "[회사수정] 회사 공휴일 등록");
		}

		Company updatedCompany = this.comDAO.selectCompany(com.getComid());

		// 사업장 히스토리 업데이트 - 해당 테이블 데이터로 예약식사명 보는중
		this.updateOfficeHistoryInfo(com);

		this.adminModifyLogService.setAdminModifyLog(
				info,
				com.getComid(),
				com.getComid(),
				"Company",
				old,
				updatedCompany
		);
		this.settlementService.updateCompanyBasicInfo(info.getAdminId(), old, updatedCompany);
		return updatedCompany;
	}

	private void updateCompanyLimit(CompanyCommand company) {
		Companylimit companylimit = this.comDAO.selectLimit(company.getComid());
		if (!ObjectUtils.isEmpty(companylimit)) {
			companylimit.setUseSikdaeInMarket(company.getUseSikdaeInMarket());
			this.comDAO.updateCompanyLimit(companylimit);
		}
	}

	private void updateMealcUsemypointCondition(String comId) {
		Companylimit companylimit = this.comDAO.selectLimit(comId);
		if (!ObjectUtils.isEmpty(companylimit)) {
			companylimit.setMealcUsemypoint(true);
			this.comDAO.updateCompanyLimit(companylimit);
		}
	}

	public Company updateCompany(Company company) throws VlocallyException {
		int result = this.comDAO.update(company);
		if (result != 1) {
			throw new VlocallyException(Errors.Company_UnknownCompany);
		}
		return this.comDAO.selectCompany(company.getComid());
	}

	/**
	 * 대장페이먼츠 서비스정보 변경
	 * @param comId
	 * @param userId
	 * @param serviceType
	 */
	private void setPaymentServiceTypeInfo(String comId, String userId, ServiceType serviceType) {
		ServiceTypeSearchInfo searchInfo = new ServiceTypeSearchInfo();
		searchInfo.setComId(comId);
		searchInfo.setServiceType(serviceType);

		CaptainPaymentCompanyInfo captainPaymentCompanyInfo = this.captainPaymentCompanyInfoMapper
				.selectByComIdAndServiceType(searchInfo);
		Long companyInfoKey = null;
		Date now = new Date();
		Date createDate = null;
		String createUser = null;
		if (!ObjectUtils.isEmpty(captainPaymentCompanyInfo)) { // 이미 식대 사용여부 정보가 있으면
			InsertOrUpdateInfo insertOrUpdateInfo = new InsertOrUpdateInfo();
			insertOrUpdateInfo.setComId(comId);
			insertOrUpdateInfo.setStatus(Status.ACTIVE.name());
			insertOrUpdateInfo.setUpdateUser(userId);
			insertOrUpdateInfo.setUpdateDate(now);
			insertOrUpdateInfo.setServiceType(serviceType);
			this.captainPaymentCompanyInfoMapper.update(insertOrUpdateInfo);

			// 이미 있으면 기존 정보에서 가져오고 (history)
			createDate = captainPaymentCompanyInfo.getCreateDate();
			createUser = captainPaymentCompanyInfo.getCreateUser();
			companyInfoKey = captainPaymentCompanyInfo.getIdx();

		} else { // 기존정보가 없을경우 대비
			InsertOrUpdateInfo insertOrUpdateInfo = new InsertOrUpdateInfo();
			insertOrUpdateInfo.setComId(comId);
			insertOrUpdateInfo.setUpdateUser(userId);
			insertOrUpdateInfo.setUpdateDate(now);
			insertOrUpdateInfo.setCreateUser(userId);
			insertOrUpdateInfo.setCreateDate(now);
			insertOrUpdateInfo.setStatus(Status.ACTIVE.name());
			insertOrUpdateInfo.setServiceType(serviceType);
			this.captainPaymentCompanyInfoMapper.insert(insertOrUpdateInfo);

			// 없을경우 insert 한 내용 확인 (history 용)
			createDate = insertOrUpdateInfo.getCreateDate();
			createUser = insertOrUpdateInfo.getCreateUser();
			companyInfoKey = insertOrUpdateInfo.getInsertIdx();
		}

		UpdateParam updateParam = new UpdateParam();
		updateParam.setUpdateUser(userId);
		updateParam.setUpdateDate(now);
		updateParam.setComId(comId);
		updateParam.setStatus(ApplyStatus.COMPLETE);
		this.captainPaymentServiceApplyPersist.update(updateParam);

		InsertHistoryOrUpdateInfo historyInfo = new InsertHistoryOrUpdateInfo();
		historyInfo.setCompanyInfoIdx(companyInfoKey);
		historyInfo.setComId(comId);
		historyInfo.setUpdateUser(userId);
		historyInfo.setUpdateDate(now);
		historyInfo.setCreateUser(createUser);
		historyInfo.setCreateDate(createDate);
		historyInfo.setServiceType(serviceType);
		historyInfo.setStatus(Status.ACTIVE.name());
		historyInfo.setHistoryDate(now);
		this.captainPaymentCompanyInfoMapper.insertHistory(historyInfo);
	}

	/**
	 * officeHistory 정보 업데이트
	 * 	TODO. 추후 Customer-service 에서 처리하는 방식으로 변경 필요함.
	 * */
	public void updateOfficeHistoryInfo(Company com) throws VlocallyException {
		//1. officeIdx 조회
		List<Office> officeList = this.officeService.readOfficeList(com.getComid());
		if (ObjectUtils.isEmpty(officeList)) {
			throw new VlocallyException(Errors.NOTFOUND_OFFICE);
		}

		// 맨 처음것 가지고옴 (사업장 구조는 1:N 이지만, 1:1로 관리되고 있음.)
		Long officeIdx = officeList.get(0).getOfficeIdx();

		//2. officeIdx를 기준으로 OfficeHistory 정보 조회하여 모두 비활성화
		OfficeHistory officeHistoryParam = new OfficeHistory();
		officeHistoryParam.setOfficeIdx(officeIdx);
		officeHistoryParam.setIsActive(false);
		officeHistoryParam.setInactiveDate(new Date());
		this.officeService.updateOfficeHistory(officeHistoryParam);

		//4. OfficeHistory 에 officeIdx 를 새로 추가 (isActive : true, inactiveDate : null)
		Office office = new Office();

		if (!ObjectUtils.isEmpty(com.getName())) {
			officeHistoryParam.setName(com.getName());
			officeHistoryParam.setOfficialName(com.getName());
		}
		if (!ObjectUtils.isEmpty(com.getGpslat())) {
			officeHistoryParam.setGpslat(com.getGpslat());
			office.setGpslat(com.getGpslat());
		}
		if (!ObjectUtils.isEmpty(com.getGpslon())) {
			officeHistoryParam.setGpslon(com.getGpslon());
			office.setGpslon(com.getGpslon());
		}
		if (!ObjectUtils.isEmpty(com.getRegion())) {
			officeHistoryParam.setRegion(com.getRegion());
		}
		officeHistoryParam.setIsActive(true);
		officeHistoryParam.setInactiveDate(null);

		// officeHistory insert
		this.officeService.insertOfficeHistory(officeHistoryParam);

		// office gps정보 업데이트
		office.setOfficeIdx(officeIdx);
		this.officeService.updateOffice(office);
	}

	/**
	 * 회사 정보수정 - 회사 관리자용
	 * @param company
	 * @throws IOException
	 * @throws VlocallyException
	 */
	public Company updateForComSelf(AdminUser info, CompanyCommand company) throws VlocallyException, IOException {
		company.setName(null);
		company.setBizname(null);
		company.setBizserial(null);
		company.setRegion(null);
		company.setIntro(null);
		company.setDuesmonth(null);
		company.setTrustcash(null);
		company.setStatus(null);
		company.setCalculatedate(null);
		return update(info, company);
	}

	/**
	 * 회사 식대제한 수정
	 * mealtom = Boolean[3] 으로 각각의 종료시간이 익일인지 아닌지 표시
	 * @param limit
	 * @param mealtom
	 * @return
	 * @throws VlocallyException
	 */
	public int updateLimit(AdminUser info, Companylimit limit, boolean[] mealtom) throws VlocallyException {
		Companylimit old = this.comDAO.selectLimit(limit.getComid());
		this.conditionCompanyLimitSetting(limit);// 조건별 셋팅
		int result = this.comDAO.updateLimit(limit, mealtom);
		Companylimit updateCompayLimit = this.comDAO.selectLimit(limit.getComid());
		if (result > 0) {
			this.adminModifyLogService.setAdminModifyLog(
					info,
					limit.getComid(),
					limit.getComid(),
					"CompanyLimit",
					old,
					updateCompayLimit);

			//변경되었을 경우에만 웹소켓 전송
			if (old.getMealcUsemypoint() != updateCompayLimit.getMealcUsemypoint()) {
				this.webSocketRemoteService.sendIsCaptainPointWebSocketToUsers(
						limit.getComid(),
						updateCompayLimit.getMealcUsemypoint());
			}

		}
		return result;
	}

	/**
	 * CompanyLimit 조건별 Setting
	 */
	public void conditionCompanyLimitSetting(Companylimit companylimit) {
		if (!ObjectUtils.isEmpty(companylimit.getMealcMultipay()) && companylimit.getMealcMultipay()) {
			companylimit.setMealmultimenu(true);
		}
	}

	/**
	 * 회사 식대제한 정보 조회
	 * @param comid
	 * @return
	 */
	public Companylimit getLimit(String comid) {
		return comDAO.selectLimit(comid);
	}

	/**
	 * 회사 정보 자세히 조회
	 * @param comid
	 * @return
	 */
	public Company getCompany(String comid){
		return comDAO.selectCompany(comid);
	}

	/**
	 * 회사 정보 목록 조회
	 * @param ex
	 * @param listinfo
	 * @return
	 */
	public List<Company> getCompany(CompanyExample ex, HashMap<String, Integer> listinfo){
		int total = comDAO.count(ex);
		return comDAO.selectCompany(ex,PageUtil.setPageValue(listinfo, total));
	}

	public List<Company> getCompany(CompanyTO.ListQuery query){
		Long total  = comMapper.count(query);
		query.setTotalCount(total);
		return comMapper.select(query);
	}

	public List<Company> getCompanyList(){
		return comMapper.selectAll();
	}

	/**
	 *
	 * 특정 제휴점이 판매할 수 있는 회사 조회
	 * @param sid
	 * @param listinfo
	 * @return
	 */
	public List<Company> getCompanyOfStore(String sid, HashMap<String, Integer> listinfo, boolean admin) {

		int total = comDAO.countCompanyOfStore(sid, admin);
		return comDAO.selectCompanyOfStore(sid, PageUtil.setPageValue(listinfo, total), admin);
	}

	/**
	 * 특정 제휴점이 판매할 수 있는 회사 조회
	 * @param query
	 * @return
	 */
	public List<HashMap<String, Object>> getCompanyOfStore(CompanyTO.PrivilegeQuery query) throws VlocallyException {
		List<HashMap<String, Object>> mealcouponListResponse = comMapper.selectOnPrivStore(query);

		HashMap<String, StoreCompanyResponse> settlementResponse = storeSettlementService.getStoreCompanyList(query.getStoreId());

		for(HashMap<String, Object> mealcouponHashMap : mealcouponListResponse) {
			String key = (String) mealcouponHashMap.get("companyStoreKey");

			mealcouponHashMap.put("settlementInfo", settlementResponse.get(key));
		}

		return mealcouponListResponse;
	}

	public List<HashMap<String, Object>> getCompanyWithPrivilege(CompanyTO.PrivilegeQuery query){
		long total = comMapper.countWithPrivilege(query);
		query.setTotalCount(total);
		return comMapper.selectWithPrivilege(query);
	}

	/**
	 * 특정 메뉴를 구매할 수 있는 회사 조회
	 * @param mid
	 * @param listinfo
	 * @param admin
	 * @return
	 */
	public List<Company> getCompanyOfMenu(String mid, HashMap<String, Integer> listinfo, boolean admin){
		int total = comDAO.countCompanyOfMenu(mid, admin);
		return comDAO.selectCompanyOfMenu(mid, PageUtil.setPageValue(listinfo, total), admin);
	}

	public List<HashMap<String, Object>> getCompanyOfMenuWithPrivilege(String storeId, String menuId){
		HashMap<String, Object> param = new HashMap<String, Object>();
		param.put("storeId", storeId);
		param.put("menuId", menuId);
		return comMapper.selectCompanyOfMenu(param);
	}

	public List<HashMap<String, Object>> getOfficeOfMenu(String storeId, String menuId){
		HashMap<String, Object> param = new HashMap<String, Object>();
		param.put("storeId", storeId);
		param.put("menuId", menuId);
		return comMapper.selectOfficeOfMenu(param);
	}

	/**
	 * 특정 메뉴의 구매권한을 얻지 못한 회사 검색
	 * @param mid
	 * @param listinfo
	 * @return
	 */
	public List<Company> getCompanyAbleOfMenu(String mid, String comname, HashMap<String, Integer> listinfo){
		int total = comDAO.countCompanyAbleOfMenu(mid, comname);
		return comDAO.selectCompanyAbleOfMenu(mid, comname, PageUtil.setPageValue(listinfo, total));
	}

	/**
	 * 특정 할인쿠폰이 사용될수 있는 회사 조회
	 * @param did
	 * @param listinfo
	 * @param admin
	 * @return
	 */
	public List<Discountmenu> getCompanyOfDiscountMenu(String did, HashMap<String, Integer> listinfo, boolean admin){
		int total = comDAO.countCompanyOfDiscountMenu(did, admin);
		return comDAO.selectCompanyOfDiscountMenu(did, PageUtil.setPageValue(listinfo, total), admin);
	}

	public List<CompanyBalanceInfo> getCompanyByVirtualBankAccountNum(String accountNumber) {
		return comMapper.selectCompanyByVirtualBankAccountNum(accountNumber);
	}

	public List<Map<String,Object>> getCompanyForNotice() {
		return comMapper.selectCompanyForNotice();
	}

	public String getCompanyVersion(String companyId){
		return comMapper.selectCompanyVersion(companyId);
	}

	public List<Company> getCompanyForOffice(String queryWord, PageInfo pageinfo) {
		HashMap<String, Object> param = new HashMap<>();
		param.put("queryWord", queryWord);
		param.put("offset", pageinfo.getOffset());
		param.put("limit", pageinfo.getLimit());
		pageinfo.setTotalCount(comMapper.countCompanyForOffice(param));
		return comMapper.selectCompanyForOffice(param);
	}

	public Company getCompanyByUser(String uid) {
		return comMapper.selectCompanyByUser(uid);
	}

	public ShippingSpotResponseVo findCompanyShippingSpots(String companyId) {
		CompanyShippingSpotVo searchVo = new CompanyShippingSpotVo();
		searchVo.setCompanyId(companyId);
		List<CompanyShippingSpotVo> companyShippingSpots = this.comMapper.findCompanyShippingSpots(searchVo);
		ShippingSpotResponseVo shippingSpotResponseVo = new ShippingSpotResponseVo();
		shippingSpotResponseVo.setContents(companyShippingSpots);
		return shippingSpotResponseVo;
	}

	public CompanyShippingSpotWithStore findCompanyShippingSpotDetail(String spotKey) {
		CompanyShippingSpotVo companyShippingSpot =  this.comMapper.findCompanyShippingSpot(spotKey);
		CompanyShippingSpotWithStore withStore = new CompanyShippingSpotWithStore();
		BeanUtils.copyProperties(companyShippingSpot, withStore);

		List<ShippingSpotStore> storeList = this.bookingTemplateShippingSpotMapper.findCompanyShippingSpotStore(spotKey);
		withStore.setShippingSpotStores(storeList);

		int totalTemplateCount = storeList.stream().mapToInt(ShippingSpotStore::getTemplateCount).sum();
		withStore.setCountBookingTemplate(totalTemplateCount);
		return withStore;
	}

	public CompanyModifyStatus findShippingSpotModifyStatus(String spotKey) {
		List<CompanyShippingSpotTemplate> companyShippingSpotConnectCount = this.bookingTemplateShippingSpotMapper
				.findCompanyShippingSpotConnectCount(spotKey);
		CompanyModifyStatus modifyStatus = new CompanyModifyStatus();
		boolean canModify = companyShippingSpotConnectCount // 단독 연결 템플릿 수 확인
				.stream()
				.noneMatch(companyShippingSpotTemplate -> companyShippingSpotTemplate.getSpotCount() == 1);
		modifyStatus.setConnectTemplateCount(companyShippingSpotConnectCount.size());
		modifyStatus.setCanDelete(canModify);
		modifyStatus.setCanDisable(canModify);
		return modifyStatus;
	}

	public boolean updateCompanyShippingSpot(String companyId, String spotKey, CompanyShippingSpotVo companyShippingSpotVo,
			AdminUser adminUser) {

		boolean isInactive = !companyShippingSpotVo.getIsActive();
		Date updateDate = new Date();
		if (isInactive) {
			this.checkLimitActiveCompanyShipping(companyId, spotKey);
			this.validModifyStatus(spotKey);
		}
		CompanyShippingSpotVo beforeData = this.comMapper.findCompanyShippingSpot(spotKey);
		CompanyShippingSpotSaveDto companyShippingSpotDto = CompanyShippingSpotSaveDto.builder()
				.spotKey(spotKey)
				.companyId(companyId)
				.spotName(companyShippingSpotVo.getSpotName())
				.zonecode(companyShippingSpotVo.getZonecode())
				.addressType(companyShippingSpotVo.getAddressType())
				.roadAddress(companyShippingSpotVo.getRoadAddress())
				.jibunAddress(companyShippingSpotVo.getJibunAddress())
				.addressDetail(companyShippingSpotVo.getAddressDetail())
				.lat(companyShippingSpotVo.getLat())
				.lng(companyShippingSpotVo.getLng())
				.isActive(companyShippingSpotVo.getIsActive())
				.updateUser(adminUser.getAdminId())
				.updateDate(updateDate)
				.build();
		int updateCnt = this.comDAO.updateCompanyShippingSpot(companyShippingSpotDto);
		if (beforeData.getIsActive() && isInactive) { // 활성 -> 비활성화 요청에만, 해당 로직을 사용한다.
			BookingTemplateShippingSpotModifyDto modifyDto = new BookingTemplateShippingSpotModifyDto();
			modifyDto.setSpotKey(spotKey);
			modifyDto.setAdminUid(adminUser.getAdminId());
			modifyDto.setUpdateDate(updateDate);
			this.bookingTemplateShippingSpotMapper.disableBookingTemplateShippingSpot(modifyDto);
		}
		if (updateCnt > 0) {
			this.adminModifyLogService.setAdminModifyLog(
					adminUser,
					companyId,
					spotKey,
					"CompanyShippingSpot",
					beforeData,
					this.comMapper.findCompanyShippingSpot(spotKey));
			return true;
		}
		return false;
	}

	public void insertCompanyShippingSpot(String companyId, CompanyShippingSpotVo companyShippingSpotVo, AdminUser adminUser) {
		String spotKey = UUID.randomUUID().toString().toUpperCase();
		CompanyShippingSpotSaveDto companyShippingSpotDto = CompanyShippingSpotSaveDto.builder()
				.spotKey(spotKey)
				.companyId(companyId)
				.spotName(companyShippingSpotVo.getSpotName())
				.zonecode(companyShippingSpotVo.getZonecode())
				.addressType(companyShippingSpotVo.getAddressType())
				.roadAddress(companyShippingSpotVo.getRoadAddress())
				.jibunAddress(companyShippingSpotVo.getJibunAddress())
				.addressDetail(companyShippingSpotVo.getAddressDetail())
				.lat(companyShippingSpotVo.getLat())
				.lng(companyShippingSpotVo.getLng())
				.isActive(companyShippingSpotVo.getIsActive())
				.createUser(adminUser.getAdminId())
				.createDate(new Date())
				.build();
		this.comDAO.insertCompanyShippingSpot(companyShippingSpotDto);
		CompanyShippingSpotVo afterData = this.comMapper.findCompanyShippingSpot(spotKey);
		this.bookingTemplateShippingSpotMapper.insertBookingTemplateShippingSpot(spotKey);

		this.adminModifyLogService.setAdminModifyLog(
				adminUser,
				companyId,
				spotKey,
				"CompanyShippingSpot",
				null,
				afterData);
	}

	public CompanyModifyLogDto getModifyLog(String comId) {
		UpdateHistoryRequest updateHistoryRequest = new UpdateHistoryRequest();
		updateHistoryRequest.setTableIdx(comId);
		updateHistoryRequest.setTableName("Company");
		UpdateHistoryResponse updateHistoryResponse = this.adminModifyLogService.readAdminModifyLog(updateHistoryRequest);
		List<AdminModifyLog> list = updateHistoryResponse.getList();
		if (ObjectUtils.isEmpty(list)) {
			return new CompanyModifyLogDto();
		}

		AdminModifyLog createLog = list.get(list.size() - 1);
		AdminModifyLog lastModifyLog = list.get(0);

		CompanyModifyLogDto modifyLogDto = new CompanyModifyLogDto();
		modifyLogDto.setCreateUserName(createLog.getUserName());
		modifyLogDto.setCreateDate(createLog.getRegDate().getTime());
		modifyLogDto.setLastModifyUserName(lastModifyLog.getUserName());
		modifyLogDto.setLastModifyDate(lastModifyLog.getRegDate().getTime());
		return modifyLogDto;

	}

	public boolean deleteCompanyShippingSpot(String companyId, String spotKey, AdminUser adminUser) {

		this.checkLimitActiveCompanyShipping(companyId, spotKey);
		this.validDeleteAvailable(spotKey);

		Date updateDate = new Date();
		CompanyShippingSpotSaveDto companyShippingSpotDto = CompanyShippingSpotSaveDto.builder()
				.spotKey(spotKey)
				.updateDate(updateDate)
				.updateUser(adminUser.getAdminId())
				.build();
		CompanyShippingSpotVo beforeData = this.comMapper.findCompanyShippingSpot(spotKey);
		int count = this.comDAO.deleteCompanyShippingSpot(companyShippingSpotDto);
		CompanyShippingSpotVo afterData = this.comMapper.findCompanyShippingSpot(spotKey);

		BookingTemplateShippingSpotModifyDto modifyDto = new BookingTemplateShippingSpotModifyDto();
		modifyDto.setSpotKey(spotKey);
		modifyDto.setUpdateDate(updateDate);
		modifyDto.setAdminUid(adminUser.getAdminId());
		this.bookingTemplateShippingSpotMapper.deleteBookingTemplateShippingSpot(modifyDto);

		this.adminModifyLogService.setAdminModifyLog(
				adminUser,
				companyId,
				spotKey,
				"CompanyShippingSpot",
				beforeData,
				afterData);
		return count > 0;
	}

	private void checkLimitActiveCompanyShipping(String companyId, String spotKey) {
		CompanyShippingSpotVo searchVo = new CompanyShippingSpotVo();
		searchVo.setSpotKey(spotKey);
		searchVo.setCompanyId(companyId);
		searchVo.setIsActive(true);
		List<CompanyShippingSpotVo> spots = this.comMapper.findIgnoreCompanyShippingSpots(searchVo);
		if (ObjectUtils.isEmpty(spots)) {
			throw new CommonException(Errors.BOOKING_SPOT_MINIMUM_SIZE_ERROR);
		}
	}

	private void validDeleteAvailable(String spotKey) {
		CompanyModifyStatus shippingSpotModifyStatus = this.findShippingSpotModifyStatus(spotKey);
		if (!shippingSpotModifyStatus.getCanDelete()) {
			throw new CommonException(Errors.BOOKING_TEMPLATE_MINIMUM_SIZE_ERROR);
		}
	}

	private void validModifyStatus(String spotKey) {
		CompanyModifyStatus shippingSpotModifyStatus = this.findShippingSpotModifyStatus(spotKey);
		if (!shippingSpotModifyStatus.getCanDisable()) {
			throw new CommonException(Errors.BOOKING_TEMPLATE_MINIMUM_SIZE_ERROR);
		}
	}

	private String companyS3ImageUpload(MultipartFile multipartFile, String comId, List<String> ArrPossibleExtenstion)
			throws VlocallyException {
		if (ObjectUtils.isEmpty(comId)) {
			return null;
		}

		BufferedImage original = null;
		String contentType = multipartFile.getContentType();
		String extension = this.imageService.getMenuExtension(contentType);
		String originName = comId + "." + extension;

		try {
			original = ImageIO.read(multipartFile.getInputStream());
		} catch (IOException ioException) {
			log.error("CompanyService.companyS3ImageUpload Exception : {}", ioException);
			throw new VlocallyException(Errors.File_isEmpty);
		}
		if (!ArrPossibleExtenstion.contains(extension)) {
			throw new VlocallyException(Errors.File_UnknownEXT);
		}

		byte[] originBuff = this.imageService.toByteArray(original, extension);
		return this.imageService.companyUploadFile(originName, contentType, originBuff);
	}

	public String companyImageDelete(String comId) {
		String msg = "고객사 고유번호가 존재하지 않습니다.";
		if (ObjectUtils.isEmpty(comId))
			return msg;

		Company company = comDAO.selectCompany(comId);
		if (!ObjectUtils.isEmpty(company.getCompanyCi())) {
			String companyCi = company.getCompanyCi();
			String originImageName = companyCi.substring(companyCi.lastIndexOf("ci/") + 3, companyCi.length());

			try {
				// Image Delete
				this.imageService.companyDeleteFile(originImageName);

				// Image Update
				Company param = new Company();
				param.setComid(comId);
				param.setCompanyCi("");
				int cnt = comDAO.update(param);
				msg = cnt > 0 ? "삭제 완료" : "삭제 실패";
			} catch (Exception e) {
				msg = e.getMessage();
				log.error("CompanyService.companyImageDelete() Exception : {}", e);
			}
		} else {
			msg = "고객사 CI 이미지가 존재하지 않습니다.";
		}

		return msg;
	}
}
