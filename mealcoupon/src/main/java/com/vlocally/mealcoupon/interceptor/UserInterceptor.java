package com.vlocally.mealcoupon.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

@Component
public class UserInterceptor extends HandlerInterceptorAdapter {

	Logger log = Logger.getLogger(getClass());
	
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception{
//		log.info("user intercepted!!");
		
//		String url = request.getRequestURI();
//		url = url.replace(request.getContextPath(), "");
//		
//		HttpSession session = request.getSession(false);
//		if(session!=null){
//			log.info("로그인 성공 후 세션 확인");
//			LoginUserInfo userinfo = (LoginUserInfo) session.getAttribute("loginUserInfo");
//			if(userinfo!=null&&userinfo.getLevel()<10){
//				log.info("레벨 확인");
//				return super.preHandle(request, response, handler);
//			}
//		}
//		
//		// 세션 없을때 보낼 페이지
//		response.sendRedirect(request.getContextPath() + "/m/loginform.pop");
//		
		return super.preHandle(request, response, handler);
	}
	
//	@Override
//	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView){
//		
//	}
//	
//	@Override
//	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex){
//		
//	}
	
}