package com.vlocally.mealcoupon.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import com.vlocally.mealcoupon.constant.Errors;
import com.vlocally.mealcoupon.exception.VlocallyException;
import com.vlocally.mealcoupon.vo.LoginUserInfo;

@Component
public class AppInterceptor extends HandlerInterceptorAdapter {

	Logger log = Logger.getLogger(getClass());
	
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception{
		
		String url = request.getRequestURI();
		url = url.replace(request.getContextPath()+"/app/", "");
//		url = url.substring(0, url.lastIndexOf("/"));
//		log.info("admin intercepted!! "+url);
		
		
		// 로그인 검사
		HttpSession session = request.getSession(false);
		if(session==null){
//			System.out.println("세션이 없네");
			throw new VlocallyException(Errors.General_SessionOut,"로그인이 필요합니다");
		}
		LoginUserInfo loginInfo = (LoginUserInfo) session.getAttribute("loginUserInfo");
		if(loginInfo == null){
//			System.out.println("로그인 정보가 없네");
			throw new VlocallyException(Errors.General_SessionOut,"로그인이 필요합니다");
		}
//		int level = loginInfo.getLevel();
//		if(level<90){
//			// TODO 404페이지 띄우기
//			System.out.println("레벨이 낮네");
//			response.sendRedirect(request.getContextPath()+"/index.vm");
//			return false;
//		}
		// 메뉴 권한 검사
		
//		Integer t_privilege = pri.getPrivilege(url);
//		
//		if(t_privilege == null){
//			throw new PhilpopException(Errors.General_Permission_Error,"권한이 설정되지 않았습니다 "+url);
//		} else if(t_privilege>level){ 
//			throw new PhilpopException(Errors.General_Permission_Error);
//		}
		
//		log.info("privilege pass");
		return super.preHandle(request, response, handler);
	}
	
//	@Override
//	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView){
//		
//	}
//	
//	@Override
//	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex){
//		
//	}
	
}