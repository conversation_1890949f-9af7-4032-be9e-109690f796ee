package com.vlocally.mealcoupon.domain.master;

import java.util.List;

import com.vlocally.mealcoupon.vo.Store;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by ttobii on 2015. 11. 25..
 */
@Data
public class StoreTO {

    @Data
    public static class ListQuery extends PageInfo{
        private String searchWord;
        private String region;
        private String categoryid;
        private String supplyType;
        private String marketCategoryType;
        private String storeSort;

        private String storeId;
        private Boolean status;
        private Boolean isTest;
    }

    @Getter @Setter
    public static class StoreAccountBizRespones{
        private List<Store> storeAccountList;
        private long totalCount;
    }

}
