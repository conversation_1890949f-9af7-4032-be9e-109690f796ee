package com.vlocally.mealcoupon.domain.master;

import com.vlocally.mealcoupon.annotaion.CIField;
import com.vlocally.mealcoupon.constant.db.CaptainPaymentGrade;
import com.vlocally.mealcoupon.constant.db.UserGradeAll;
import com.vlocally.mealcoupon.constant.db.UserStatus;
import com.vlocally.mealcoupon.constant.db.UserStatusV2;
import com.vlocally.mealcoupon.util.CiUtil;
import com.vlocally.mealcoupon.vo.User.Grade;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Created by ttobii on 2015. 12. 16..
 */
@Data
public class UserTO {

    @Data
    public static class User {

        private String uid;
        @CIField(type = CiUtil.CIFieldType.Account)
        private String signId;
        @CIField(type = CiUtil.CIFieldType.Name)
        private String name;
        private String comid;
        private String comname;
        private Byte level;
        private String position;
        private String division;
        @CIField(type = CiUtil.CIFieldType.PhoneNo)
        private String cellphone;
        private String password;
        private String comidnum;
        private Boolean sex;
        private Date birthday;
        private Date joindate;
        private Byte ostype;
        private String pushtoken;
        private String accesstoken;
        private Integer mealpoint;
        private Date mpdepositdate;
        private Integer mypoint;
        private Integer welfarePoint;
        @CIField(type = CiUtil.CIFieldType.Email)
        private String email;
        private String appversion;
        private String rankposition;
        private Integer compointright;
        private String passwordreset;
        private Date longdepositdate;
        private Long groupIdx;
        private Long welfareGroupIdx;
        private String icon;
        private String version;
        private Integer pointDay;
        private Integer pointLongTerm;
        private Integer pointInfinite;

        private UserStatus status;

        private Boolean emailIsConfirm;
        private Boolean phoneIsConfirm;
        private Grade   grade;
        private CaptainPaymentGrade captainPaymentGrade;
        private int     isDormant;
        private Date    dormantDate;
        private Boolean isCertification;
        private Date certificationDate;

        private Boolean isRestore;
        private Date withdrawDate;
    }

    @Data
    public static class ListSearchQuery extends PageInfo{
        private String searchWord;
        private String comid;
        private Boolean excludeDisable;
        private UserGradeAll grade;
        private UserStatusV2 status;
        private List<String> versions;
        private Boolean notInGroup;
        private Boolean notWelfareInGroup;
        private Long groupidx;
        private Long welfareGroupidx;
        private Boolean extraInfo;
        private Boolean dormantAble;
        private Boolean withdrawAble;
        private CaptainPaymentGrade captainPaymentGrade;
    }
}
