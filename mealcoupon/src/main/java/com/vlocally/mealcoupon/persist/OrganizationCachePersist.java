package com.vlocally.mealcoupon.persist;

import com.vlocally.mealcoupon.mapper.master.OrganizationCacheMapper;
import com.vlocally.mealcoupon.vo.OrganizationCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created By huidragon 2019-07-01
 */
@Service
public class OrganizationCachePersist {

    @Autowired
    OrganizationCacheMapper organizationCacheMapper;

    public OrganizationCache getDivision(String orgCode) {
        return organizationCacheMapper.getDivision(orgCode);
    }

    public List<OrganizationCache> getAllParentDivision(String rootCode, String orgCode) {
        OrganizationCache param = new OrganizationCache();
        param.setRootCode(rootCode);
        param.setOrgCode(orgCode);
        return organizationCacheMapper.getAllParentDivision(param);
    }
}
