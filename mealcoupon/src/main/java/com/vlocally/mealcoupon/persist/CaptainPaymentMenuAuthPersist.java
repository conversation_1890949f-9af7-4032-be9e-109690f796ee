package com.vlocally.mealcoupon.persist;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.vlocally.mealcoupon.mapper.master.CaptainPaymentMenuMapper;
import com.vlocally.mealcoupon.service.entity.CaptainPaymentMenuAuthDto.DefaultSearchParam;
import com.vlocally.mealcoupon.vo.CaptainPaymentMenuInfo;

@Repository
public class CaptainPaymentMenuAuthPersist {

    @Autowired private CaptainPaymentMenuMapper menuMapper;

    public List<CaptainPaymentMenuInfo> getMenuAuthList() {
        return this.menuMapper.selectAllConfigurableMenu();
    }

    public List<Long> selectDefaultAuthList(DefaultSearchParam defaultSearchParam) {
        return this.menuMapper.selectDefaultAuthList(defaultSearchParam);
    }
}
