package com.vlocally.mealcoupon.constant.db;

public enum PaymentMethod {

	/**
	 * 신용카드
	 */
	CreditCard(1,"신용카드", PaymentMethodLGD.CreditCard),
	
	/**
	 * 계좌이체
	 */
	Transfer(2,"계좌이체", null),
	//Transfer(2,"계좌이체", PaymentMethodLGD.Transfer),
	
	/**
	 * 무통장 입금
	 */
	VirtualDeposit(3,"무통장 입금", null),
	//VirtualDeposit(3,"무통장 입금", PaymentMethodLGD.VirtualDeposit),
	
	/**
	 * 핸드폰 소액결제
	 */
	Cellphone(4,"핸드폰 소액결제", PaymentMethodLGD.Cellphone),
	
	/**
	 * 유선전화결제
	 */
	LinePhone(5,"유선전화결제", null),
	
	/**
	 * OK 캐쉬백
	 */
	OKCashPoint(6,"OK캐쉬백", null),
	
	/**
	 * 문화상품권
	 */
	CultureGiftcard(7,"문화상품권", null),
	
	/**
	 * 게임문화상품권
	 */
	GameGiftcard(8,"게임문화상품권", null), 
	
	/**
	 * 
	 */
	OnlyPoint(33,"내 포인트",null),
	
	/**
	 * 알 수 없음
	 */
	Unknown(44,"불명", null);
	
	public int method;
	public String name;
	public PaymentMethodLGD lgd;
	
	public int getMethod(){
		return method;
	}
	
	public String getName(){
		return name;
	}
	
	PaymentMethod(int method, String name, PaymentMethodLGD lgd){
		this.method = method;
		this.name = name;
		this.lgd = lgd;
	}
}
