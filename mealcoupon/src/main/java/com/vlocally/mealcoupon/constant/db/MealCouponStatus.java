package com.vlocally.mealcoupon.constant.db;

public enum MealCouponStatus {
	/**
	 * 일반 사용자의 식권 사용 처리
	 */
	Normal(0,"일반사용"),
	
	/**
	 * 기프티콘 사용 완료
	 * 기프티콘 발급 취소
	 */
	GifticonUse(1,"콘 사용완료"),
	GifticonCancel(-1,"콘 발급취소"),
	
	
	/**
	 * 회사 관리자의 추가
	 * 회사 관리자의 사용취소
	 */
	ComAdminUse(6,"회사 관리자 추가"),
	ComAdminCancel(-6,"회사 관리자 사용취소"),

	/**
	 * 회사 관리자의 추가
	 * 회사 관리자의 사용취소
	 */
	//StoreAdminUse(8,"상점 관리자 추가"),
	StoreAdminCancel(-8,"상점 관리자 사용취소"),

	/**
	 * 전체 관리자의 추가
	 * 전체 관리자의 사용취소
	 */
	MealcAdminUse(9,"전체 관리자 추가"),
	MealcAdminCancel(-9,"전체 관리자 사용취소"),
	PART_CANCEL_USER(81, "부분 취소(사용자)"),
	PART_CANCEL_STORE(82, "부분 취소(제휴점)"),
	PART_CANCEL_ADMIN(83, "부분 취소(관리자)"),
	CANCEL_USER(91, "전체 취소(사용자)"),
	CANCEL_STORE(92, "전체 취소(제휴점)"),
	CANCEL_ADMIN(93, "전체 취소(관리자)"),
	NET_CANCEL(94, "망 취소")
	;
	
	public int status;
	public String name;
	
	public int getStatus(){
		return status;
	}
	
	public String getName(){
		return name;
	}
	
//	public String toJavascriptMap(CouponUseType[] list){
//		String output = "";
//		for(CouponUseType type : list){
//			output += ","+type.type+":\"" + type.name+ "\"";
//		}
//		output = "var couponusetypemap = {"+output.substring(1)+"};";
//		return output;
//	}
//	
//	public String toJavascriptArray(CouponUseType[] list){
//		String output = "";
//		for(CouponUseType type : list){
//			output += ",{type:"+type.type+",name:\"" + type.name+ "\"}";
//		}
//		output = "var couponusetype = ["+output.substring(1)+"];";
//		return output;
//	}
	
	MealCouponStatus(int status, String name){
		this.status = status;
		this.name = name;
	}
}
