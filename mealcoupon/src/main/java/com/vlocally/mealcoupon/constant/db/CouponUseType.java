package com.vlocally.mealcoupon.constant.db;

public enum CouponUseType {

//	안쓰는것 삭제할것 - 구버전 Coupon Table 용
	
	/**
	 * 일반 사용자의 식권 사용 처리
	 */
	Normal(0,"일반 사용"),
	
	/**
	 * 기프티콘 사용 완료
	 * 기프티콘 발급 취소
	 */
	GifticonUse(1,"콘 사용완료"),
	GifticonCancel(-1,"콘 발급취소"),
	
	
	/**
	 * 회사 관리자의 추가
	 * 회사 관리자의 사용취소
	 */
	ComAdminUse(6,"회사 관리자 추가"),
	ComAdminCancel(-6,"회사 관리자 사용취소"),
	
	/**
	 * 전체 관리자의 추가
	 * 전체 관리자의 사용취소
	 */
	MealcAdminUse(9,"전체 관리자 추가"),
	MealcAdminCancel(-9,"전체 관리자 사용취소");
	
	public int type;
	public String name;
	
	public int getType(){
		return type;
	}
	
	public String getName(){
		return name;
	}
	
//	public String toJavascriptMap(CouponUseType[] list){
//		String output = "";
//		for(CouponUseType type : list){
//			output += ","+type.type+":\"" + type.name+ "\"";
//		}
//		output = "var couponusetypemap = {"+output.substring(1)+"};";
//		return output;
//	}
//	
//	public String toJavascriptArray(CouponUseType[] list){
//		String output = "";
//		for(CouponUseType type : list){
//			output += ",{type:"+type.type+",name:\"" + type.name+ "\"}";
//		}
//		output = "var couponusetype = ["+output.substring(1)+"];";
//		return output;
//	}
	
	CouponUseType(int type, String name){
		this.type = type;
		this.name = name;
	}
}
