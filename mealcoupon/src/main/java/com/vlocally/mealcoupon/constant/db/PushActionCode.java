package com.vlocally.mealcoupon.constant.db;

public enum PushActionCode {
	DefaultPush,

	// 결제
	GenPayRoom,
	MealCouponUser,
	MealCouponCancel,

	// 식대
	DemandApprovalUser,
	<PERSON>mand<PERSON>serCharge,
	DemandUserReject,
	PresentReceived,

	// 공지
	CompanyAnnounce,
	CompanyNotice,

	// 예약
	BookingFinish,
	BookingDelivery,

	// 외부 쿠폰
	ThirdPartyCoupon,
	ThirdPartyCouponFail,

	// 제휴식당 목록 이동(필터링)
	ShowStoreList,

	// 대장쿠폰 함 이동
	CaptainCouponBox,

	// 소멸예정 포인트 이동
	CaptainPointExpire;
}
