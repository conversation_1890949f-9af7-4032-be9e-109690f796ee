package com.vlocally.mealcoupon.vo;

import com.vlocally.mealcoupon.annotaion.CIField;
import com.vlocally.mealcoupon.util.CiUtil;

import java.util.Date;

public class Pointlog {
    private String pid;

    private String uid;

    private String comid;
    @CIField(type = CiUtil.CIFieldType.Name)
    private String username;

    private Date changedate;

    private Integer changepoint;

    private Integer outpoint;

    private Date extinctdate;

    private Byte status;

    private Integer usedpoint;

    private Byte causetype;

    private String causetypeString;

    private String cause;

    private String causelink;

    private String paymethod;

    private String captainPointManageTypeId;

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getComid() {
        return comid;
    }

    public void setComid(String comid) {
        this.comid = comid;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Date getChangedate() {
        return changedate;
    }

    public void setChangedate(Date changedate) {
        this.changedate = changedate;
    }

    public Integer getChangepoint() {
        return changepoint;
    }

    public void setChangepoint(Integer changepoint) {
        this.changepoint = changepoint;
    }

    public Integer getOutpoint() {
        return outpoint;
    }

    public void setOutpoint(Integer outpoint) {
        this.outpoint = outpoint;
    }

    public Date getExtinctdate() {
        return extinctdate;
    }

    public void setExtinctdate(Date extinctdate) {
        this.extinctdate = extinctdate;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Integer getUsedpoint() {
        return usedpoint;
    }

    public void setUsedpoint(Integer usedpoint) {
        this.usedpoint = usedpoint;
    }

    public Byte getCausetype() {
        return causetype;
    }

    public void setCausetype(Byte causetype) {
        this.causetype = causetype;
    }

    public String getCausetypeString() {
        return causetypeString;
    }

    public void setCausetypeString(String causetypeString) {
        this.causetypeString = causetypeString;
    }

    public String getCause() {
        return cause;
    }

    public void setCause(String cause) {
        this.cause = cause;
    }

    public String getCauselink() {
        return causelink;
    }

    public void setCauselink(String causelink) {
        this.causelink = causelink;
    }

    public String getPaymethod() {
        return paymethod;
    }

    public void setPaymethod(String paymethod) {
        this.paymethod = paymethod;
    }

    public String getCaptainPointManageTypeId() {
        return captainPointManageTypeId;
    }

    public void setCaptainPointManageTypeId(String captainPointManageTypeId) {
        this.captainPointManageTypeId = captainPointManageTypeId;
    }
}