package com.vlocally.mealcoupon.vo;

import java.util.Date;

public class Userentrust extends UserentrustKey {
    private Date regdate;

    private Boolean trust;

    private Integer daylimit;

    private Integer oncelimit;

    private Integer uselimit;

    private Date uselimitdate;

    public Date getRegdate() {
        return regdate;
    }

    public void setRegdate(Date regdate) {
        this.regdate = regdate;
    }

    public Boolean getTrust() {
        return trust;
    }

    public void setTrust(Boolean trust) {
        this.trust = trust;
    }

    public Integer getDaylimit() {
        return daylimit;
    }

    public void setDaylimit(Integer daylimit) {
        this.daylimit = daylimit;
    }

    public Integer getOncelimit() {
        return oncelimit;
    }

    public void setOncelimit(Integer oncelimit) {
        this.oncelimit = oncelimit;
    }

    public Integer getUselimit() {
        return uselimit;
    }

    public void setUselimit(Integer uselimit) {
        this.uselimit = uselimit;
    }

    public Date getUselimitdate() {
        return uselimitdate;
    }

    public void setUselimitdate(Date uselimitdate) {
        this.uselimitdate = uselimitdate;
    }
}