package com.vlocally.mealcoupon.vo;

import java.util.Date;

import com.vlocally.mealcoupon.annotaion.CIField;
import com.vlocally.mealcoupon.util.CiUtil;
import lombok.Data;

@Data
public class Mealpointlog {
    private Long pointLogIdx;

    private Long groupIdx;

    private Long policyIdx;

    private String comid;

    private String comName;

    @CIField(type = CiUtil.CIFieldType.Account)
    private String userId;

    @CIField(type = CiUtil.CIFieldType.Name)
    private String userName;

    private String type;

    private Integer amount;

    private Integer balance;

    private Boolean isActive;

    private Date expireDate;

    private Date regDate;

    private String policyName;

    private String policyType;

    private String policyday;

    private Date startdate;

    private Date enddate;

    private Date starttime;

    private Date endtime;

    private String cause;

    private CauseType causeType;

    private String causeLink;

    private String extra;

    public static enum CauseType {

        USE_MEALCOUPON("식권사용"),
        GROUP_CHANGE("사용자 그룹이동"),
        POLICY_ACTIONS("자동배치작업"),
        USER_WITHDRAW("사용자 탈퇴"),
        ADMIN_MODIFY("회사관리자입력"),
        SUPER_MODIFY("슈퍼관리자입력"),
        POLICY_RESET("정책초기화"),
        REFUND("환불"),
        PRESENT("선물하기"),
        EXPIRE_COUNT("결제횟수제한 소멸"),
        EXPIRE_DATE("유효기간 만료"),
        ETC("기타");

        private String description;

        CauseType(String description){
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}