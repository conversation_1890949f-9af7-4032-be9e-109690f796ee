package com.vlocally.mealcoupon.vo;

import java.util.Date;

public class Discountmenu {
    private String did;

    private String menuname;

    private String sid;

    private String storename;

    private String intro;

    private Integer due;

    private Integer countlimit;

    private Double gpslat;

    private Double gpslon;

    private Date regdate;

    private Byte status;

    public String getDid() {
        return did;
    }

    public void setDid(String did) {
        this.did = did;
    }

    public String getMenuname() {
        return menuname;
    }

    public void setMenuname(String menuname) {
        this.menuname = menuname;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public String getStorename() {
        return storename;
    }

    public void setStorename(String storename) {
        this.storename = storename;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public Integer getDue() {
        return due;
    }

    public void setDue(Integer due) {
        this.due = due;
    }

    public Integer getCountlimit() {
        return countlimit;
    }

    public void setCountlimit(Integer countlimit) {
        this.countlimit = countlimit;
    }

    public Double getGpslat() {
        return gpslat;
    }

    public void setGpslat(Double gpslat) {
        this.gpslat = gpslat;
    }

    public Double getGpslon() {
        return gpslon;
    }

    public void setGpslon(Double gpslon) {
        this.gpslon = gpslon;
    }

    public Date getRegdate() {
        return regdate;
    }

    public void setRegdate(Date regdate) {
        this.regdate = regdate;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }
}