package com.vlocally.mealcoupon.vo;

import java.util.Date;

public class Snackproduct {
    private String snackid;

    private String productid;

    private String storename;

    private String prodname;

    private Integer brandid;

    private String brandname;

    private String tag;

    private Integer supplyprice;

    private Integer sellprice;

    private Integer price;

    private Integer discountprice;

    private Byte discountratio;

    private Boolean discountflag;

    private String introurl;

    private Integer prodseq;

    private String prodtype;

    private String vendertype;

    private Integer cateid;

    private String catename;

    private Integer cateseq;

    private Byte status;

    private Date regdate;

    private String sendtype;

    private String image;

    private String introtext;

    public String getSnackid() {
        return snackid;
    }

    public void setSnackid(String snackid) {
        this.snackid = snackid;
    }

    public String getProductid() {
        return productid;
    }

    public void setProductid(String productid) {
        this.productid = productid;
    }

    public String getStorename() {
        return storename;
    }

    public void setStorename(String storename) {
        this.storename = storename;
    }

    public String getProdname() {
        return prodname;
    }

    public void setProdname(String prodname) {
        this.prodname = prodname;
    }

    public Integer getBrandid() {
        return brandid;
    }

    public void setBrandid(Integer brandid) {
        this.brandid = brandid;
    }

    public String getBrandname() {
        return brandname;
    }

    public void setBrandname(String brandname) {
        this.brandname = brandname;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public Integer getSupplyprice() {
        return supplyprice;
    }

    public void setSupplyprice(Integer supplyprice) {
        this.supplyprice = supplyprice;
    }

    public Integer getSellprice() {
        return sellprice;
    }

    public void setSellprice(Integer sellprice) {
        this.sellprice = sellprice;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public Integer getDiscountprice() {
        return discountprice;
    }

    public void setDiscountprice(Integer discountprice) {
        this.discountprice = discountprice;
    }

    public Byte getDiscountratio() {
        return discountratio;
    }

    public void setDiscountratio(Byte discountratio) {
        this.discountratio = discountratio;
    }

    public Boolean getDiscountflag() {
        return discountflag;
    }

    public void setDiscountflag(Boolean discountflag) {
        this.discountflag = discountflag;
    }

    public String getIntrourl() {
        return introurl;
    }

    public void setIntrourl(String introurl) {
        this.introurl = introurl;
    }

    public Integer getProdseq() {
        return prodseq;
    }

    public void setProdseq(Integer prodseq) {
        this.prodseq = prodseq;
    }

    public String getProdtype() {
        return prodtype;
    }

    public void setProdtype(String prodtype) {
        this.prodtype = prodtype;
    }

    public String getVendertype() {
        return vendertype;
    }

    public void setVendertype(String vendertype) {
        this.vendertype = vendertype;
    }

    public Integer getCateid() {
        return cateid;
    }

    public void setCateid(Integer cateid) {
        this.cateid = cateid;
    }

    public String getCatename() {
        return catename;
    }

    public void setCatename(String catename) {
        this.catename = catename;
    }

    public Integer getCateseq() {
        return cateseq;
    }

    public void setCateseq(Integer cateseq) {
        this.cateseq = cateseq;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getRegdate() {
        return regdate;
    }

    public void setRegdate(Date regdate) {
        this.regdate = regdate;
    }

    public String getSendtype() {
        return sendtype;
    }

    public void setSendtype(String sendtype) {
        this.sendtype = sendtype;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getIntrotext() {
        return introtext;
    }

    public void setIntrotext(String introtext) {
        this.introtext = introtext;
    }
}