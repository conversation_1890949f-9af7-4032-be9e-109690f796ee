package com.vlocally.mealcoupon.vo;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import lombok.Data;

@Data
public class HolidayCountryInfo {
    private Long idx;
    private String year; //년도
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date startDate; //시작일
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date endDate; //종료일
    private Integer duration; //기간
    private String name; //공휴일명
    private String description; //공휴일 상세
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
    private String updateId; //수정자 uid
    private Boolean isDelete; //삭제여부
}
