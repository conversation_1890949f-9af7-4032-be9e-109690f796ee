package com.vlocally.mealcoupon.vo;

import java.util.ArrayList;
import java.util.List;

public class MealpointlogdetailExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public MealpointlogdetailExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andLogdetailidxIsNull() {
            addCriterion("logDetailIdx is null");
            return (Criteria) this;
        }

        public Criteria andLogdetailidxIsNotNull() {
            addCriterion("logDetailIdx is not null");
            return (Criteria) this;
        }

        public Criteria andLogdetailidxEqualTo(Long value) {
            addCriterion("logDetailIdx =", value, "logdetailidx");
            return (Criteria) this;
        }

        public Criteria andLogdetailidxNotEqualTo(Long value) {
            addCriterion("logDetailIdx <>", value, "logdetailidx");
            return (Criteria) this;
        }

        public Criteria andLogdetailidxGreaterThan(Long value) {
            addCriterion("logDetailIdx >", value, "logdetailidx");
            return (Criteria) this;
        }

        public Criteria andLogdetailidxGreaterThanOrEqualTo(Long value) {
            addCriterion("logDetailIdx >=", value, "logdetailidx");
            return (Criteria) this;
        }

        public Criteria andLogdetailidxLessThan(Long value) {
            addCriterion("logDetailIdx <", value, "logdetailidx");
            return (Criteria) this;
        }

        public Criteria andLogdetailidxLessThanOrEqualTo(Long value) {
            addCriterion("logDetailIdx <=", value, "logdetailidx");
            return (Criteria) this;
        }

        public Criteria andLogdetailidxIn(List<Long> values) {
            addCriterion("logDetailIdx in", values, "logdetailidx");
            return (Criteria) this;
        }

        public Criteria andLogdetailidxNotIn(List<Long> values) {
            addCriterion("logDetailIdx not in", values, "logdetailidx");
            return (Criteria) this;
        }

        public Criteria andLogdetailidxBetween(Long value1, Long value2) {
            addCriterion("logDetailIdx between", value1, value2, "logdetailidx");
            return (Criteria) this;
        }

        public Criteria andLogdetailidxNotBetween(Long value1, Long value2) {
            addCriterion("logDetailIdx not between", value1, value2, "logdetailidx");
            return (Criteria) this;
        }

        public Criteria andOutputlogidxIsNull() {
            addCriterion("outputLogIdx is null");
            return (Criteria) this;
        }

        public Criteria andOutputlogidxIsNotNull() {
            addCriterion("outputLogIdx is not null");
            return (Criteria) this;
        }

        public Criteria andOutputlogidxEqualTo(Long value) {
            addCriterion("outputLogIdx =", value, "outputlogidx");
            return (Criteria) this;
        }

        public Criteria andOutputlogidxNotEqualTo(Long value) {
            addCriterion("outputLogIdx <>", value, "outputlogidx");
            return (Criteria) this;
        }

        public Criteria andOutputlogidxGreaterThan(Long value) {
            addCriterion("outputLogIdx >", value, "outputlogidx");
            return (Criteria) this;
        }

        public Criteria andOutputlogidxGreaterThanOrEqualTo(Long value) {
            addCriterion("outputLogIdx >=", value, "outputlogidx");
            return (Criteria) this;
        }

        public Criteria andOutputlogidxLessThan(Long value) {
            addCriterion("outputLogIdx <", value, "outputlogidx");
            return (Criteria) this;
        }

        public Criteria andOutputlogidxLessThanOrEqualTo(Long value) {
            addCriterion("outputLogIdx <=", value, "outputlogidx");
            return (Criteria) this;
        }

        public Criteria andOutputlogidxIn(List<Long> values) {
            addCriterion("outputLogIdx in", values, "outputlogidx");
            return (Criteria) this;
        }

        public Criteria andOutputlogidxNotIn(List<Long> values) {
            addCriterion("outputLogIdx not in", values, "outputlogidx");
            return (Criteria) this;
        }

        public Criteria andOutputlogidxBetween(Long value1, Long value2) {
            addCriterion("outputLogIdx between", value1, value2, "outputlogidx");
            return (Criteria) this;
        }

        public Criteria andOutputlogidxNotBetween(Long value1, Long value2) {
            addCriterion("outputLogIdx not between", value1, value2, "outputlogidx");
            return (Criteria) this;
        }

        public Criteria andInputlogidxIsNull() {
            addCriterion("inputLogIdx is null");
            return (Criteria) this;
        }

        public Criteria andInputlogidxIsNotNull() {
            addCriterion("inputLogIdx is not null");
            return (Criteria) this;
        }

        public Criteria andInputlogidxEqualTo(Long value) {
            addCriterion("inputLogIdx =", value, "inputlogidx");
            return (Criteria) this;
        }

        public Criteria andInputlogidxNotEqualTo(Long value) {
            addCriterion("inputLogIdx <>", value, "inputlogidx");
            return (Criteria) this;
        }

        public Criteria andInputlogidxGreaterThan(Long value) {
            addCriterion("inputLogIdx >", value, "inputlogidx");
            return (Criteria) this;
        }

        public Criteria andInputlogidxGreaterThanOrEqualTo(Long value) {
            addCriterion("inputLogIdx >=", value, "inputlogidx");
            return (Criteria) this;
        }

        public Criteria andInputlogidxLessThan(Long value) {
            addCriterion("inputLogIdx <", value, "inputlogidx");
            return (Criteria) this;
        }

        public Criteria andInputlogidxLessThanOrEqualTo(Long value) {
            addCriterion("inputLogIdx <=", value, "inputlogidx");
            return (Criteria) this;
        }

        public Criteria andInputlogidxIn(List<Long> values) {
            addCriterion("inputLogIdx in", values, "inputlogidx");
            return (Criteria) this;
        }

        public Criteria andInputlogidxNotIn(List<Long> values) {
            addCriterion("inputLogIdx not in", values, "inputlogidx");
            return (Criteria) this;
        }

        public Criteria andInputlogidxBetween(Long value1, Long value2) {
            addCriterion("inputLogIdx between", value1, value2, "inputlogidx");
            return (Criteria) this;
        }

        public Criteria andInputlogidxNotBetween(Long value1, Long value2) {
            addCriterion("inputLogIdx not between", value1, value2, "inputlogidx");
            return (Criteria) this;
        }

        public Criteria andAmountIsNull() {
            addCriterion("amount is null");
            return (Criteria) this;
        }

        public Criteria andAmountIsNotNull() {
            addCriterion("amount is not null");
            return (Criteria) this;
        }

        public Criteria andAmountEqualTo(Integer value) {
            addCriterion("amount =", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotEqualTo(Integer value) {
            addCriterion("amount <>", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThan(Integer value) {
            addCriterion("amount >", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThanOrEqualTo(Integer value) {
            addCriterion("amount >=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThan(Integer value) {
            addCriterion("amount <", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThanOrEqualTo(Integer value) {
            addCriterion("amount <=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountIn(List<Integer> values) {
            addCriterion("amount in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotIn(List<Integer> values) {
            addCriterion("amount not in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountBetween(Integer value1, Integer value2) {
            addCriterion("amount between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotBetween(Integer value1, Integer value2) {
            addCriterion("amount not between", value1, value2, "amount");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}