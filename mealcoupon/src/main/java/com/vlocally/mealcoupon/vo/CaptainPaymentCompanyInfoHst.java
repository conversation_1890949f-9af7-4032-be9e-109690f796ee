package com.vlocally.mealcoupon.vo;

import java.util.Date;

import com.vlocally.mealcoupon.vo.CaptainPaymentCompanyInfo.ServiceType;
import com.vlocally.mealcoupon.vo.CaptainPaymentCompanyInfo.Status;
import lombok.Data;

@Data
public class CaptainPaymentCompanyInfoHst {

    // Entity
    private Long idx;
    private String comId;
    private ServiceType serviceType;
    private Status status;
    private String updateUser;
    private Date updateDate;
    private String createUser;
    private Date createDate;
    private Date HistoryDate;

}
