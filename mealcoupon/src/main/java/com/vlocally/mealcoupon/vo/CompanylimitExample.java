package com.vlocally.mealcoupon.vo;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class CompanylimitExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public CompanylimitExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCTime(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Time(value.getTime()), property);
        }

        protected void addCriterionForJDBCTime(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Time> timeList = new ArrayList<java.sql.Time>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                timeList.add(new java.sql.Time(iter.next().getTime()));
            }
            addCriterion(condition, timeList, property);
        }

        protected void addCriterionForJDBCTime(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Time(value1.getTime()), new java.sql.Time(value2.getTime()), property);
        }

        public Criteria andComidIsNull() {
            addCriterion("comid is null");
            return (Criteria) this;
        }

        public Criteria andComidIsNotNull() {
            addCriterion("comid is not null");
            return (Criteria) this;
        }

        public Criteria andComidEqualTo(String value) {
            addCriterion("comid =", value, "comid");
            return (Criteria) this;
        }

        public Criteria andComidNotEqualTo(String value) {
            addCriterion("comid <>", value, "comid");
            return (Criteria) this;
        }

        public Criteria andComidGreaterThan(String value) {
            addCriterion("comid >", value, "comid");
            return (Criteria) this;
        }

        public Criteria andComidGreaterThanOrEqualTo(String value) {
            addCriterion("comid >=", value, "comid");
            return (Criteria) this;
        }

        public Criteria andComidLessThan(String value) {
            addCriterion("comid <", value, "comid");
            return (Criteria) this;
        }

        public Criteria andComidLessThanOrEqualTo(String value) {
            addCriterion("comid <=", value, "comid");
            return (Criteria) this;
        }

        public Criteria andComidLike(String value) {
            addCriterion("comid like", value, "comid");
            return (Criteria) this;
        }

        public Criteria andComidNotLike(String value) {
            addCriterion("comid not like", value, "comid");
            return (Criteria) this;
        }

        public Criteria andComidIn(List<String> values) {
            addCriterion("comid in", values, "comid");
            return (Criteria) this;
        }

        public Criteria andComidNotIn(List<String> values) {
            addCriterion("comid not in", values, "comid");
            return (Criteria) this;
        }

        public Criteria andComidBetween(String value1, String value2) {
            addCriterion("comid between", value1, value2, "comid");
            return (Criteria) this;
        }

        public Criteria andComidNotBetween(String value1, String value2) {
            addCriterion("comid not between", value1, value2, "comid");
            return (Criteria) this;
        }

        public Criteria andMeal1IsNull() {
            addCriterion("meal1 is null");
            return (Criteria) this;
        }

        public Criteria andMeal1IsNotNull() {
            addCriterion("meal1 is not null");
            return (Criteria) this;
        }

        public Criteria andMeal1EqualTo(Boolean value) {
            addCriterion("meal1 =", value, "meal1");
            return (Criteria) this;
        }

        public Criteria andMeal1NotEqualTo(Boolean value) {
            addCriterion("meal1 <>", value, "meal1");
            return (Criteria) this;
        }

        public Criteria andMeal1GreaterThan(Boolean value) {
            addCriterion("meal1 >", value, "meal1");
            return (Criteria) this;
        }

        public Criteria andMeal1GreaterThanOrEqualTo(Boolean value) {
            addCriterion("meal1 >=", value, "meal1");
            return (Criteria) this;
        }

        public Criteria andMeal1LessThan(Boolean value) {
            addCriterion("meal1 <", value, "meal1");
            return (Criteria) this;
        }

        public Criteria andMeal1LessThanOrEqualTo(Boolean value) {
            addCriterion("meal1 <=", value, "meal1");
            return (Criteria) this;
        }

        public Criteria andMeal1In(List<Boolean> values) {
            addCriterion("meal1 in", values, "meal1");
            return (Criteria) this;
        }

        public Criteria andMeal1NotIn(List<Boolean> values) {
            addCriterion("meal1 not in", values, "meal1");
            return (Criteria) this;
        }

        public Criteria andMeal1Between(Boolean value1, Boolean value2) {
            addCriterion("meal1 between", value1, value2, "meal1");
            return (Criteria) this;
        }

        public Criteria andMeal1NotBetween(Boolean value1, Boolean value2) {
            addCriterion("meal1 not between", value1, value2, "meal1");
            return (Criteria) this;
        }

        public Criteria andMealname1IsNull() {
            addCriterion("mealname1 is null");
            return (Criteria) this;
        }

        public Criteria andMealname1IsNotNull() {
            addCriterion("mealname1 is not null");
            return (Criteria) this;
        }

        public Criteria andMealname1EqualTo(String value) {
            addCriterion("mealname1 =", value, "mealname1");
            return (Criteria) this;
        }

        public Criteria andMealname1NotEqualTo(String value) {
            addCriterion("mealname1 <>", value, "mealname1");
            return (Criteria) this;
        }

        public Criteria andMealname1GreaterThan(String value) {
            addCriterion("mealname1 >", value, "mealname1");
            return (Criteria) this;
        }

        public Criteria andMealname1GreaterThanOrEqualTo(String value) {
            addCriterion("mealname1 >=", value, "mealname1");
            return (Criteria) this;
        }

        public Criteria andMealname1LessThan(String value) {
            addCriterion("mealname1 <", value, "mealname1");
            return (Criteria) this;
        }

        public Criteria andMealname1LessThanOrEqualTo(String value) {
            addCriterion("mealname1 <=", value, "mealname1");
            return (Criteria) this;
        }

        public Criteria andMealname1Like(String value) {
            addCriterion("mealname1 like", value, "mealname1");
            return (Criteria) this;
        }

        public Criteria andMealname1NotLike(String value) {
            addCriterion("mealname1 not like", value, "mealname1");
            return (Criteria) this;
        }

        public Criteria andMealname1In(List<String> values) {
            addCriterion("mealname1 in", values, "mealname1");
            return (Criteria) this;
        }

        public Criteria andMealname1NotIn(List<String> values) {
            addCriterion("mealname1 not in", values, "mealname1");
            return (Criteria) this;
        }

        public Criteria andMealname1Between(String value1, String value2) {
            addCriterion("mealname1 between", value1, value2, "mealname1");
            return (Criteria) this;
        }

        public Criteria andMealname1NotBetween(String value1, String value2) {
            addCriterion("mealname1 not between", value1, value2, "mealname1");
            return (Criteria) this;
        }

        public Criteria andMeallimit1IsNull() {
            addCriterion("meallimit1 is null");
            return (Criteria) this;
        }

        public Criteria andMeallimit1IsNotNull() {
            addCriterion("meallimit1 is not null");
            return (Criteria) this;
        }

        public Criteria andMeallimit1EqualTo(Integer value) {
            addCriterion("meallimit1 =", value, "meallimit1");
            return (Criteria) this;
        }

        public Criteria andMeallimit1NotEqualTo(Integer value) {
            addCriterion("meallimit1 <>", value, "meallimit1");
            return (Criteria) this;
        }

        public Criteria andMeallimit1GreaterThan(Integer value) {
            addCriterion("meallimit1 >", value, "meallimit1");
            return (Criteria) this;
        }

        public Criteria andMeallimit1GreaterThanOrEqualTo(Integer value) {
            addCriterion("meallimit1 >=", value, "meallimit1");
            return (Criteria) this;
        }

        public Criteria andMeallimit1LessThan(Integer value) {
            addCriterion("meallimit1 <", value, "meallimit1");
            return (Criteria) this;
        }

        public Criteria andMeallimit1LessThanOrEqualTo(Integer value) {
            addCriterion("meallimit1 <=", value, "meallimit1");
            return (Criteria) this;
        }

        public Criteria andMeallimit1In(List<Integer> values) {
            addCriterion("meallimit1 in", values, "meallimit1");
            return (Criteria) this;
        }

        public Criteria andMeallimit1NotIn(List<Integer> values) {
            addCriterion("meallimit1 not in", values, "meallimit1");
            return (Criteria) this;
        }

        public Criteria andMeallimit1Between(Integer value1, Integer value2) {
            addCriterion("meallimit1 between", value1, value2, "meallimit1");
            return (Criteria) this;
        }

        public Criteria andMeallimit1NotBetween(Integer value1, Integer value2) {
            addCriterion("meallimit1 not between", value1, value2, "meallimit1");
            return (Criteria) this;
        }

        public Criteria andDay1IsNull() {
            addCriterion("day1 is null");
            return (Criteria) this;
        }

        public Criteria andDay1IsNotNull() {
            addCriterion("day1 is not null");
            return (Criteria) this;
        }

        public Criteria andDay1EqualTo(String value) {
            addCriterion("day1 =", value, "day1");
            return (Criteria) this;
        }

        public Criteria andDay1NotEqualTo(String value) {
            addCriterion("day1 <>", value, "day1");
            return (Criteria) this;
        }

        public Criteria andDay1GreaterThan(String value) {
            addCriterion("day1 >", value, "day1");
            return (Criteria) this;
        }

        public Criteria andDay1GreaterThanOrEqualTo(String value) {
            addCriterion("day1 >=", value, "day1");
            return (Criteria) this;
        }

        public Criteria andDay1LessThan(String value) {
            addCriterion("day1 <", value, "day1");
            return (Criteria) this;
        }

        public Criteria andDay1LessThanOrEqualTo(String value) {
            addCriterion("day1 <=", value, "day1");
            return (Criteria) this;
        }

        public Criteria andDay1Like(String value) {
            addCriterion("day1 like", value, "day1");
            return (Criteria) this;
        }

        public Criteria andDay1NotLike(String value) {
            addCriterion("day1 not like", value, "day1");
            return (Criteria) this;
        }

        public Criteria andDay1In(List<String> values) {
            addCriterion("day1 in", values, "day1");
            return (Criteria) this;
        }

        public Criteria andDay1NotIn(List<String> values) {
            addCriterion("day1 not in", values, "day1");
            return (Criteria) this;
        }

        public Criteria andDay1Between(String value1, String value2) {
            addCriterion("day1 between", value1, value2, "day1");
            return (Criteria) this;
        }

        public Criteria andDay1NotBetween(String value1, String value2) {
            addCriterion("day1 not between", value1, value2, "day1");
            return (Criteria) this;
        }

        public Criteria andStart1IsNull() {
            addCriterion("start1 is null");
            return (Criteria) this;
        }

        public Criteria andStart1IsNotNull() {
            addCriterion("start1 is not null");
            return (Criteria) this;
        }

        public Criteria andStart1EqualTo(Date value) {
            addCriterionForJDBCTime("start1 =", value, "start1");
            return (Criteria) this;
        }

        public Criteria andStart1NotEqualTo(Date value) {
            addCriterionForJDBCTime("start1 <>", value, "start1");
            return (Criteria) this;
        }

        public Criteria andStart1GreaterThan(Date value) {
            addCriterionForJDBCTime("start1 >", value, "start1");
            return (Criteria) this;
        }

        public Criteria andStart1GreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("start1 >=", value, "start1");
            return (Criteria) this;
        }

        public Criteria andStart1LessThan(Date value) {
            addCriterionForJDBCTime("start1 <", value, "start1");
            return (Criteria) this;
        }

        public Criteria andStart1LessThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("start1 <=", value, "start1");
            return (Criteria) this;
        }

        public Criteria andStart1In(List<Date> values) {
            addCriterionForJDBCTime("start1 in", values, "start1");
            return (Criteria) this;
        }

        public Criteria andStart1NotIn(List<Date> values) {
            addCriterionForJDBCTime("start1 not in", values, "start1");
            return (Criteria) this;
        }

        public Criteria andStart1Between(Date value1, Date value2) {
            addCriterionForJDBCTime("start1 between", value1, value2, "start1");
            return (Criteria) this;
        }

        public Criteria andStart1NotBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("start1 not between", value1, value2, "start1");
            return (Criteria) this;
        }

        public Criteria andEnd1IsNull() {
            addCriterion("end1 is null");
            return (Criteria) this;
        }

        public Criteria andEnd1IsNotNull() {
            addCriterion("end1 is not null");
            return (Criteria) this;
        }

        public Criteria andEnd1EqualTo(Date value) {
            addCriterionForJDBCTime("end1 =", value, "end1");
            return (Criteria) this;
        }

        public Criteria andEnd1NotEqualTo(Date value) {
            addCriterionForJDBCTime("end1 <>", value, "end1");
            return (Criteria) this;
        }

        public Criteria andEnd1GreaterThan(Date value) {
            addCriterionForJDBCTime("end1 >", value, "end1");
            return (Criteria) this;
        }

        public Criteria andEnd1GreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("end1 >=", value, "end1");
            return (Criteria) this;
        }

        public Criteria andEnd1LessThan(Date value) {
            addCriterionForJDBCTime("end1 <", value, "end1");
            return (Criteria) this;
        }

        public Criteria andEnd1LessThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("end1 <=", value, "end1");
            return (Criteria) this;
        }

        public Criteria andEnd1In(List<Date> values) {
            addCriterionForJDBCTime("end1 in", values, "end1");
            return (Criteria) this;
        }

        public Criteria andEnd1NotIn(List<Date> values) {
            addCriterionForJDBCTime("end1 not in", values, "end1");
            return (Criteria) this;
        }

        public Criteria andEnd1Between(Date value1, Date value2) {
            addCriterionForJDBCTime("end1 between", value1, value2, "end1");
            return (Criteria) this;
        }

        public Criteria andEnd1NotBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("end1 not between", value1, value2, "end1");
            return (Criteria) this;
        }

        public Criteria andMeal2IsNull() {
            addCriterion("meal2 is null");
            return (Criteria) this;
        }

        public Criteria andMeal2IsNotNull() {
            addCriterion("meal2 is not null");
            return (Criteria) this;
        }

        public Criteria andMeal2EqualTo(Boolean value) {
            addCriterion("meal2 =", value, "meal2");
            return (Criteria) this;
        }

        public Criteria andMeal2NotEqualTo(Boolean value) {
            addCriterion("meal2 <>", value, "meal2");
            return (Criteria) this;
        }

        public Criteria andMeal2GreaterThan(Boolean value) {
            addCriterion("meal2 >", value, "meal2");
            return (Criteria) this;
        }

        public Criteria andMeal2GreaterThanOrEqualTo(Boolean value) {
            addCriterion("meal2 >=", value, "meal2");
            return (Criteria) this;
        }

        public Criteria andMeal2LessThan(Boolean value) {
            addCriterion("meal2 <", value, "meal2");
            return (Criteria) this;
        }

        public Criteria andMeal2LessThanOrEqualTo(Boolean value) {
            addCriterion("meal2 <=", value, "meal2");
            return (Criteria) this;
        }

        public Criteria andMeal2In(List<Boolean> values) {
            addCriterion("meal2 in", values, "meal2");
            return (Criteria) this;
        }

        public Criteria andMeal2NotIn(List<Boolean> values) {
            addCriterion("meal2 not in", values, "meal2");
            return (Criteria) this;
        }

        public Criteria andMeal2Between(Boolean value1, Boolean value2) {
            addCriterion("meal2 between", value1, value2, "meal2");
            return (Criteria) this;
        }

        public Criteria andMeal2NotBetween(Boolean value1, Boolean value2) {
            addCriterion("meal2 not between", value1, value2, "meal2");
            return (Criteria) this;
        }

        public Criteria andMealname2IsNull() {
            addCriterion("mealname2 is null");
            return (Criteria) this;
        }

        public Criteria andMealname2IsNotNull() {
            addCriterion("mealname2 is not null");
            return (Criteria) this;
        }

        public Criteria andMealname2EqualTo(String value) {
            addCriterion("mealname2 =", value, "mealname2");
            return (Criteria) this;
        }

        public Criteria andMealname2NotEqualTo(String value) {
            addCriterion("mealname2 <>", value, "mealname2");
            return (Criteria) this;
        }

        public Criteria andMealname2GreaterThan(String value) {
            addCriterion("mealname2 >", value, "mealname2");
            return (Criteria) this;
        }

        public Criteria andMealname2GreaterThanOrEqualTo(String value) {
            addCriterion("mealname2 >=", value, "mealname2");
            return (Criteria) this;
        }

        public Criteria andMealname2LessThan(String value) {
            addCriterion("mealname2 <", value, "mealname2");
            return (Criteria) this;
        }

        public Criteria andMealname2LessThanOrEqualTo(String value) {
            addCriterion("mealname2 <=", value, "mealname2");
            return (Criteria) this;
        }

        public Criteria andMealname2Like(String value) {
            addCriterion("mealname2 like", value, "mealname2");
            return (Criteria) this;
        }

        public Criteria andMealname2NotLike(String value) {
            addCriterion("mealname2 not like", value, "mealname2");
            return (Criteria) this;
        }

        public Criteria andMealname2In(List<String> values) {
            addCriterion("mealname2 in", values, "mealname2");
            return (Criteria) this;
        }

        public Criteria andMealname2NotIn(List<String> values) {
            addCriterion("mealname2 not in", values, "mealname2");
            return (Criteria) this;
        }

        public Criteria andMealname2Between(String value1, String value2) {
            addCriterion("mealname2 between", value1, value2, "mealname2");
            return (Criteria) this;
        }

        public Criteria andMealname2NotBetween(String value1, String value2) {
            addCriterion("mealname2 not between", value1, value2, "mealname2");
            return (Criteria) this;
        }

        public Criteria andMeallimit2IsNull() {
            addCriterion("meallimit2 is null");
            return (Criteria) this;
        }

        public Criteria andMeallimit2IsNotNull() {
            addCriterion("meallimit2 is not null");
            return (Criteria) this;
        }

        public Criteria andMeallimit2EqualTo(Integer value) {
            addCriterion("meallimit2 =", value, "meallimit2");
            return (Criteria) this;
        }

        public Criteria andMeallimit2NotEqualTo(Integer value) {
            addCriterion("meallimit2 <>", value, "meallimit2");
            return (Criteria) this;
        }

        public Criteria andMeallimit2GreaterThan(Integer value) {
            addCriterion("meallimit2 >", value, "meallimit2");
            return (Criteria) this;
        }

        public Criteria andMeallimit2GreaterThanOrEqualTo(Integer value) {
            addCriterion("meallimit2 >=", value, "meallimit2");
            return (Criteria) this;
        }

        public Criteria andMeallimit2LessThan(Integer value) {
            addCriterion("meallimit2 <", value, "meallimit2");
            return (Criteria) this;
        }

        public Criteria andMeallimit2LessThanOrEqualTo(Integer value) {
            addCriterion("meallimit2 <=", value, "meallimit2");
            return (Criteria) this;
        }

        public Criteria andMeallimit2In(List<Integer> values) {
            addCriterion("meallimit2 in", values, "meallimit2");
            return (Criteria) this;
        }

        public Criteria andMeallimit2NotIn(List<Integer> values) {
            addCriterion("meallimit2 not in", values, "meallimit2");
            return (Criteria) this;
        }

        public Criteria andMeallimit2Between(Integer value1, Integer value2) {
            addCriterion("meallimit2 between", value1, value2, "meallimit2");
            return (Criteria) this;
        }

        public Criteria andMeallimit2NotBetween(Integer value1, Integer value2) {
            addCriterion("meallimit2 not between", value1, value2, "meallimit2");
            return (Criteria) this;
        }

        public Criteria andDay2IsNull() {
            addCriterion("day2 is null");
            return (Criteria) this;
        }

        public Criteria andDay2IsNotNull() {
            addCriterion("day2 is not null");
            return (Criteria) this;
        }

        public Criteria andDay2EqualTo(String value) {
            addCriterion("day2 =", value, "day2");
            return (Criteria) this;
        }

        public Criteria andDay2NotEqualTo(String value) {
            addCriterion("day2 <>", value, "day2");
            return (Criteria) this;
        }

        public Criteria andDay2GreaterThan(String value) {
            addCriterion("day2 >", value, "day2");
            return (Criteria) this;
        }

        public Criteria andDay2GreaterThanOrEqualTo(String value) {
            addCriterion("day2 >=", value, "day2");
            return (Criteria) this;
        }

        public Criteria andDay2LessThan(String value) {
            addCriterion("day2 <", value, "day2");
            return (Criteria) this;
        }

        public Criteria andDay2LessThanOrEqualTo(String value) {
            addCriterion("day2 <=", value, "day2");
            return (Criteria) this;
        }

        public Criteria andDay2Like(String value) {
            addCriterion("day2 like", value, "day2");
            return (Criteria) this;
        }

        public Criteria andDay2NotLike(String value) {
            addCriterion("day2 not like", value, "day2");
            return (Criteria) this;
        }

        public Criteria andDay2In(List<String> values) {
            addCriterion("day2 in", values, "day2");
            return (Criteria) this;
        }

        public Criteria andDay2NotIn(List<String> values) {
            addCriterion("day2 not in", values, "day2");
            return (Criteria) this;
        }

        public Criteria andDay2Between(String value1, String value2) {
            addCriterion("day2 between", value1, value2, "day2");
            return (Criteria) this;
        }

        public Criteria andDay2NotBetween(String value1, String value2) {
            addCriterion("day2 not between", value1, value2, "day2");
            return (Criteria) this;
        }

        public Criteria andStart2IsNull() {
            addCriterion("start2 is null");
            return (Criteria) this;
        }

        public Criteria andStart2IsNotNull() {
            addCriterion("start2 is not null");
            return (Criteria) this;
        }

        public Criteria andStart2EqualTo(Date value) {
            addCriterionForJDBCTime("start2 =", value, "start2");
            return (Criteria) this;
        }

        public Criteria andStart2NotEqualTo(Date value) {
            addCriterionForJDBCTime("start2 <>", value, "start2");
            return (Criteria) this;
        }

        public Criteria andStart2GreaterThan(Date value) {
            addCriterionForJDBCTime("start2 >", value, "start2");
            return (Criteria) this;
        }

        public Criteria andStart2GreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("start2 >=", value, "start2");
            return (Criteria) this;
        }

        public Criteria andStart2LessThan(Date value) {
            addCriterionForJDBCTime("start2 <", value, "start2");
            return (Criteria) this;
        }

        public Criteria andStart2LessThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("start2 <=", value, "start2");
            return (Criteria) this;
        }

        public Criteria andStart2In(List<Date> values) {
            addCriterionForJDBCTime("start2 in", values, "start2");
            return (Criteria) this;
        }

        public Criteria andStart2NotIn(List<Date> values) {
            addCriterionForJDBCTime("start2 not in", values, "start2");
            return (Criteria) this;
        }

        public Criteria andStart2Between(Date value1, Date value2) {
            addCriterionForJDBCTime("start2 between", value1, value2, "start2");
            return (Criteria) this;
        }

        public Criteria andStart2NotBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("start2 not between", value1, value2, "start2");
            return (Criteria) this;
        }

        public Criteria andEnd2IsNull() {
            addCriterion("end2 is null");
            return (Criteria) this;
        }

        public Criteria andEnd2IsNotNull() {
            addCriterion("end2 is not null");
            return (Criteria) this;
        }

        public Criteria andEnd2EqualTo(Date value) {
            addCriterionForJDBCTime("end2 =", value, "end2");
            return (Criteria) this;
        }

        public Criteria andEnd2NotEqualTo(Date value) {
            addCriterionForJDBCTime("end2 <>", value, "end2");
            return (Criteria) this;
        }

        public Criteria andEnd2GreaterThan(Date value) {
            addCriterionForJDBCTime("end2 >", value, "end2");
            return (Criteria) this;
        }

        public Criteria andEnd2GreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("end2 >=", value, "end2");
            return (Criteria) this;
        }

        public Criteria andEnd2LessThan(Date value) {
            addCriterionForJDBCTime("end2 <", value, "end2");
            return (Criteria) this;
        }

        public Criteria andEnd2LessThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("end2 <=", value, "end2");
            return (Criteria) this;
        }

        public Criteria andEnd2In(List<Date> values) {
            addCriterionForJDBCTime("end2 in", values, "end2");
            return (Criteria) this;
        }

        public Criteria andEnd2NotIn(List<Date> values) {
            addCriterionForJDBCTime("end2 not in", values, "end2");
            return (Criteria) this;
        }

        public Criteria andEnd2Between(Date value1, Date value2) {
            addCriterionForJDBCTime("end2 between", value1, value2, "end2");
            return (Criteria) this;
        }

        public Criteria andEnd2NotBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("end2 not between", value1, value2, "end2");
            return (Criteria) this;
        }

        public Criteria andMeal3IsNull() {
            addCriterion("meal3 is null");
            return (Criteria) this;
        }

        public Criteria andMeal3IsNotNull() {
            addCriterion("meal3 is not null");
            return (Criteria) this;
        }

        public Criteria andMeal3EqualTo(Boolean value) {
            addCriterion("meal3 =", value, "meal3");
            return (Criteria) this;
        }

        public Criteria andMeal3NotEqualTo(Boolean value) {
            addCriterion("meal3 <>", value, "meal3");
            return (Criteria) this;
        }

        public Criteria andMeal3GreaterThan(Boolean value) {
            addCriterion("meal3 >", value, "meal3");
            return (Criteria) this;
        }

        public Criteria andMeal3GreaterThanOrEqualTo(Boolean value) {
            addCriterion("meal3 >=", value, "meal3");
            return (Criteria) this;
        }

        public Criteria andMeal3LessThan(Boolean value) {
            addCriterion("meal3 <", value, "meal3");
            return (Criteria) this;
        }

        public Criteria andMeal3LessThanOrEqualTo(Boolean value) {
            addCriterion("meal3 <=", value, "meal3");
            return (Criteria) this;
        }

        public Criteria andMeal3In(List<Boolean> values) {
            addCriterion("meal3 in", values, "meal3");
            return (Criteria) this;
        }

        public Criteria andMeal3NotIn(List<Boolean> values) {
            addCriterion("meal3 not in", values, "meal3");
            return (Criteria) this;
        }

        public Criteria andMeal3Between(Boolean value1, Boolean value2) {
            addCriterion("meal3 between", value1, value2, "meal3");
            return (Criteria) this;
        }

        public Criteria andMeal3NotBetween(Boolean value1, Boolean value2) {
            addCriterion("meal3 not between", value1, value2, "meal3");
            return (Criteria) this;
        }

        public Criteria andMealname3IsNull() {
            addCriterion("mealname3 is null");
            return (Criteria) this;
        }

        public Criteria andMealname3IsNotNull() {
            addCriterion("mealname3 is not null");
            return (Criteria) this;
        }

        public Criteria andMealname3EqualTo(String value) {
            addCriterion("mealname3 =", value, "mealname3");
            return (Criteria) this;
        }

        public Criteria andMealname3NotEqualTo(String value) {
            addCriterion("mealname3 <>", value, "mealname3");
            return (Criteria) this;
        }

        public Criteria andMealname3GreaterThan(String value) {
            addCriterion("mealname3 >", value, "mealname3");
            return (Criteria) this;
        }

        public Criteria andMealname3GreaterThanOrEqualTo(String value) {
            addCriterion("mealname3 >=", value, "mealname3");
            return (Criteria) this;
        }

        public Criteria andMealname3LessThan(String value) {
            addCriterion("mealname3 <", value, "mealname3");
            return (Criteria) this;
        }

        public Criteria andMealname3LessThanOrEqualTo(String value) {
            addCriterion("mealname3 <=", value, "mealname3");
            return (Criteria) this;
        }

        public Criteria andMealname3Like(String value) {
            addCriterion("mealname3 like", value, "mealname3");
            return (Criteria) this;
        }

        public Criteria andMealname3NotLike(String value) {
            addCriterion("mealname3 not like", value, "mealname3");
            return (Criteria) this;
        }

        public Criteria andMealname3In(List<String> values) {
            addCriterion("mealname3 in", values, "mealname3");
            return (Criteria) this;
        }

        public Criteria andMealname3NotIn(List<String> values) {
            addCriterion("mealname3 not in", values, "mealname3");
            return (Criteria) this;
        }

        public Criteria andMealname3Between(String value1, String value2) {
            addCriterion("mealname3 between", value1, value2, "mealname3");
            return (Criteria) this;
        }

        public Criteria andMealname3NotBetween(String value1, String value2) {
            addCriterion("mealname3 not between", value1, value2, "mealname3");
            return (Criteria) this;
        }

        public Criteria andMeallimit3IsNull() {
            addCriterion("meallimit3 is null");
            return (Criteria) this;
        }

        public Criteria andMeallimit3IsNotNull() {
            addCriterion("meallimit3 is not null");
            return (Criteria) this;
        }

        public Criteria andMeallimit3EqualTo(Integer value) {
            addCriterion("meallimit3 =", value, "meallimit3");
            return (Criteria) this;
        }

        public Criteria andMeallimit3NotEqualTo(Integer value) {
            addCriterion("meallimit3 <>", value, "meallimit3");
            return (Criteria) this;
        }

        public Criteria andMeallimit3GreaterThan(Integer value) {
            addCriterion("meallimit3 >", value, "meallimit3");
            return (Criteria) this;
        }

        public Criteria andMeallimit3GreaterThanOrEqualTo(Integer value) {
            addCriterion("meallimit3 >=", value, "meallimit3");
            return (Criteria) this;
        }

        public Criteria andMeallimit3LessThan(Integer value) {
            addCriterion("meallimit3 <", value, "meallimit3");
            return (Criteria) this;
        }

        public Criteria andMeallimit3LessThanOrEqualTo(Integer value) {
            addCriterion("meallimit3 <=", value, "meallimit3");
            return (Criteria) this;
        }

        public Criteria andMeallimit3In(List<Integer> values) {
            addCriterion("meallimit3 in", values, "meallimit3");
            return (Criteria) this;
        }

        public Criteria andMeallimit3NotIn(List<Integer> values) {
            addCriterion("meallimit3 not in", values, "meallimit3");
            return (Criteria) this;
        }

        public Criteria andMeallimit3Between(Integer value1, Integer value2) {
            addCriterion("meallimit3 between", value1, value2, "meallimit3");
            return (Criteria) this;
        }

        public Criteria andMeallimit3NotBetween(Integer value1, Integer value2) {
            addCriterion("meallimit3 not between", value1, value2, "meallimit3");
            return (Criteria) this;
        }

        public Criteria andDay3IsNull() {
            addCriterion("day3 is null");
            return (Criteria) this;
        }

        public Criteria andDay3IsNotNull() {
            addCriterion("day3 is not null");
            return (Criteria) this;
        }

        public Criteria andDay3EqualTo(String value) {
            addCriterion("day3 =", value, "day3");
            return (Criteria) this;
        }

        public Criteria andDay3NotEqualTo(String value) {
            addCriterion("day3 <>", value, "day3");
            return (Criteria) this;
        }

        public Criteria andDay3GreaterThan(String value) {
            addCriterion("day3 >", value, "day3");
            return (Criteria) this;
        }

        public Criteria andDay3GreaterThanOrEqualTo(String value) {
            addCriterion("day3 >=", value, "day3");
            return (Criteria) this;
        }

        public Criteria andDay3LessThan(String value) {
            addCriterion("day3 <", value, "day3");
            return (Criteria) this;
        }

        public Criteria andDay3LessThanOrEqualTo(String value) {
            addCriterion("day3 <=", value, "day3");
            return (Criteria) this;
        }

        public Criteria andDay3Like(String value) {
            addCriterion("day3 like", value, "day3");
            return (Criteria) this;
        }

        public Criteria andDay3NotLike(String value) {
            addCriterion("day3 not like", value, "day3");
            return (Criteria) this;
        }

        public Criteria andDay3In(List<String> values) {
            addCriterion("day3 in", values, "day3");
            return (Criteria) this;
        }

        public Criteria andDay3NotIn(List<String> values) {
            addCriterion("day3 not in", values, "day3");
            return (Criteria) this;
        }

        public Criteria andDay3Between(String value1, String value2) {
            addCriterion("day3 between", value1, value2, "day3");
            return (Criteria) this;
        }

        public Criteria andDay3NotBetween(String value1, String value2) {
            addCriterion("day3 not between", value1, value2, "day3");
            return (Criteria) this;
        }

        public Criteria andStart3IsNull() {
            addCriterion("start3 is null");
            return (Criteria) this;
        }

        public Criteria andStart3IsNotNull() {
            addCriterion("start3 is not null");
            return (Criteria) this;
        }

        public Criteria andStart3EqualTo(Date value) {
            addCriterionForJDBCTime("start3 =", value, "start3");
            return (Criteria) this;
        }

        public Criteria andStart3NotEqualTo(Date value) {
            addCriterionForJDBCTime("start3 <>", value, "start3");
            return (Criteria) this;
        }

        public Criteria andStart3GreaterThan(Date value) {
            addCriterionForJDBCTime("start3 >", value, "start3");
            return (Criteria) this;
        }

        public Criteria andStart3GreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("start3 >=", value, "start3");
            return (Criteria) this;
        }

        public Criteria andStart3LessThan(Date value) {
            addCriterionForJDBCTime("start3 <", value, "start3");
            return (Criteria) this;
        }

        public Criteria andStart3LessThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("start3 <=", value, "start3");
            return (Criteria) this;
        }

        public Criteria andStart3In(List<Date> values) {
            addCriterionForJDBCTime("start3 in", values, "start3");
            return (Criteria) this;
        }

        public Criteria andStart3NotIn(List<Date> values) {
            addCriterionForJDBCTime("start3 not in", values, "start3");
            return (Criteria) this;
        }

        public Criteria andStart3Between(Date value1, Date value2) {
            addCriterionForJDBCTime("start3 between", value1, value2, "start3");
            return (Criteria) this;
        }

        public Criteria andStart3NotBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("start3 not between", value1, value2, "start3");
            return (Criteria) this;
        }

        public Criteria andEnd3IsNull() {
            addCriterion("end3 is null");
            return (Criteria) this;
        }

        public Criteria andEnd3IsNotNull() {
            addCriterion("end3 is not null");
            return (Criteria) this;
        }

        public Criteria andEnd3EqualTo(Date value) {
            addCriterionForJDBCTime("end3 =", value, "end3");
            return (Criteria) this;
        }

        public Criteria andEnd3NotEqualTo(Date value) {
            addCriterionForJDBCTime("end3 <>", value, "end3");
            return (Criteria) this;
        }

        public Criteria andEnd3GreaterThan(Date value) {
            addCriterionForJDBCTime("end3 >", value, "end3");
            return (Criteria) this;
        }

        public Criteria andEnd3GreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("end3 >=", value, "end3");
            return (Criteria) this;
        }

        public Criteria andEnd3LessThan(Date value) {
            addCriterionForJDBCTime("end3 <", value, "end3");
            return (Criteria) this;
        }

        public Criteria andEnd3LessThanOrEqualTo(Date value) {
            addCriterionForJDBCTime("end3 <=", value, "end3");
            return (Criteria) this;
        }

        public Criteria andEnd3In(List<Date> values) {
            addCriterionForJDBCTime("end3 in", values, "end3");
            return (Criteria) this;
        }

        public Criteria andEnd3NotIn(List<Date> values) {
            addCriterionForJDBCTime("end3 not in", values, "end3");
            return (Criteria) this;
        }

        public Criteria andEnd3Between(Date value1, Date value2) {
            addCriterionForJDBCTime("end3 between", value1, value2, "end3");
            return (Criteria) this;
        }

        public Criteria andEnd3NotBetween(Date value1, Date value2) {
            addCriterionForJDBCTime("end3 not between", value1, value2, "end3");
            return (Criteria) this;
        }

        public Criteria andCommonPointtransIsNull() {
            addCriterion("common_pointtrans is null");
            return (Criteria) this;
        }

        public Criteria andCommonPointtransIsNotNull() {
            addCriterion("common_pointtrans is not null");
            return (Criteria) this;
        }

        public Criteria andCommonPointtransEqualTo(Boolean value) {
            addCriterion("common_pointtrans =", value, "commonPointtrans");
            return (Criteria) this;
        }

        public Criteria andCommonPointtransNotEqualTo(Boolean value) {
            addCriterion("common_pointtrans <>", value, "commonPointtrans");
            return (Criteria) this;
        }

        public Criteria andCommonPointtransGreaterThan(Boolean value) {
            addCriterion("common_pointtrans >", value, "commonPointtrans");
            return (Criteria) this;
        }

        public Criteria andCommonPointtransGreaterThanOrEqualTo(Boolean value) {
            addCriterion("common_pointtrans >=", value, "commonPointtrans");
            return (Criteria) this;
        }

        public Criteria andCommonPointtransLessThan(Boolean value) {
            addCriterion("common_pointtrans <", value, "commonPointtrans");
            return (Criteria) this;
        }

        public Criteria andCommonPointtransLessThanOrEqualTo(Boolean value) {
            addCriterion("common_pointtrans <=", value, "commonPointtrans");
            return (Criteria) this;
        }

        public Criteria andCommonPointtransIn(List<Boolean> values) {
            addCriterion("common_pointtrans in", values, "commonPointtrans");
            return (Criteria) this;
        }

        public Criteria andCommonPointtransNotIn(List<Boolean> values) {
            addCriterion("common_pointtrans not in", values, "commonPointtrans");
            return (Criteria) this;
        }

        public Criteria andCommonPointtransBetween(Boolean value1, Boolean value2) {
            addCriterion("common_pointtrans between", value1, value2, "commonPointtrans");
            return (Criteria) this;
        }

        public Criteria andCommonPointtransNotBetween(Boolean value1, Boolean value2) {
            addCriterion("common_pointtrans not between", value1, value2, "commonPointtrans");
            return (Criteria) this;
        }

        public Criteria andMealcServiceIsNull() {
            addCriterion("mealc_service is null");
            return (Criteria) this;
        }

        public Criteria andMealcServiceIsNotNull() {
            addCriterion("mealc_service is not null");
            return (Criteria) this;
        }

        public Criteria andMealcServiceEqualTo(Boolean value) {
            addCriterion("mealc_service =", value, "mealcService");
            return (Criteria) this;
        }

        public Criteria andMealcServiceNotEqualTo(Boolean value) {
            addCriterion("mealc_service <>", value, "mealcService");
            return (Criteria) this;
        }

        public Criteria andMealcServiceGreaterThan(Boolean value) {
            addCriterion("mealc_service >", value, "mealcService");
            return (Criteria) this;
        }

        public Criteria andMealcServiceGreaterThanOrEqualTo(Boolean value) {
            addCriterion("mealc_service >=", value, "mealcService");
            return (Criteria) this;
        }

        public Criteria andMealcServiceLessThan(Boolean value) {
            addCriterion("mealc_service <", value, "mealcService");
            return (Criteria) this;
        }

        public Criteria andMealcServiceLessThanOrEqualTo(Boolean value) {
            addCriterion("mealc_service <=", value, "mealcService");
            return (Criteria) this;
        }

        public Criteria andMealcServiceIn(List<Boolean> values) {
            addCriterion("mealc_service in", values, "mealcService");
            return (Criteria) this;
        }

        public Criteria andMealcServiceNotIn(List<Boolean> values) {
            addCriterion("mealc_service not in", values, "mealcService");
            return (Criteria) this;
        }

        public Criteria andMealcServiceBetween(Boolean value1, Boolean value2) {
            addCriterion("mealc_service between", value1, value2, "mealcService");
            return (Criteria) this;
        }

        public Criteria andMealcServiceNotBetween(Boolean value1, Boolean value2) {
            addCriterion("mealc_service not between", value1, value2, "mealcService");
            return (Criteria) this;
        }

        public Criteria andMealcUsemypointIsNull() {
            addCriterion("mealc_usemypoint is null");
            return (Criteria) this;
        }

        public Criteria andMealcUsemypointIsNotNull() {
            addCriterion("mealc_usemypoint is not null");
            return (Criteria) this;
        }

        public Criteria andMealcUsemypointEqualTo(Boolean value) {
            addCriterion("mealc_usemypoint =", value, "mealcUsemypoint");
            return (Criteria) this;
        }

        public Criteria andMealcUsemypointNotEqualTo(Boolean value) {
            addCriterion("mealc_usemypoint <>", value, "mealcUsemypoint");
            return (Criteria) this;
        }

        public Criteria andMealcUsemypointGreaterThan(Boolean value) {
            addCriterion("mealc_usemypoint >", value, "mealcUsemypoint");
            return (Criteria) this;
        }

        public Criteria andMealcUsemypointGreaterThanOrEqualTo(Boolean value) {
            addCriterion("mealc_usemypoint >=", value, "mealcUsemypoint");
            return (Criteria) this;
        }

        public Criteria andMealcUsemypointLessThan(Boolean value) {
            addCriterion("mealc_usemypoint <", value, "mealcUsemypoint");
            return (Criteria) this;
        }

        public Criteria andMealcUsemypointLessThanOrEqualTo(Boolean value) {
            addCriterion("mealc_usemypoint <=", value, "mealcUsemypoint");
            return (Criteria) this;
        }

        public Criteria andMealcUsemypointIn(List<Boolean> values) {
            addCriterion("mealc_usemypoint in", values, "mealcUsemypoint");
            return (Criteria) this;
        }

        public Criteria andMealcUsemypointNotIn(List<Boolean> values) {
            addCriterion("mealc_usemypoint not in", values, "mealcUsemypoint");
            return (Criteria) this;
        }

        public Criteria andMealcUsemypointBetween(Boolean value1, Boolean value2) {
            addCriterion("mealc_usemypoint between", value1, value2, "mealcUsemypoint");
            return (Criteria) this;
        }

        public Criteria andMealcUsemypointNotBetween(Boolean value1, Boolean value2) {
            addCriterion("mealc_usemypoint not between", value1, value2, "mealcUsemypoint");
            return (Criteria) this;
        }

        public Criteria andMealcMultipayIsNull() {
            addCriterion("mealc_multipay is null");
            return (Criteria) this;
        }

        public Criteria andMealcMultipayIsNotNull() {
            addCriterion("mealc_multipay is not null");
            return (Criteria) this;
        }

        public Criteria andMealcMultipayEqualTo(Boolean value) {
            addCriterion("mealc_multipay =", value, "mealcMultipay");
            return (Criteria) this;
        }

        public Criteria andMealcMultipayNotEqualTo(Boolean value) {
            addCriterion("mealc_multipay <>", value, "mealcMultipay");
            return (Criteria) this;
        }

        public Criteria andMealcMultipayGreaterThan(Boolean value) {
            addCriterion("mealc_multipay >", value, "mealcMultipay");
            return (Criteria) this;
        }

        public Criteria andMealcMultipayGreaterThanOrEqualTo(Boolean value) {
            addCriterion("mealc_multipay >=", value, "mealcMultipay");
            return (Criteria) this;
        }

        public Criteria andMealcMultipayLessThan(Boolean value) {
            addCriterion("mealc_multipay <", value, "mealcMultipay");
            return (Criteria) this;
        }

        public Criteria andMealcMultipayLessThanOrEqualTo(Boolean value) {
            addCriterion("mealc_multipay <=", value, "mealcMultipay");
            return (Criteria) this;
        }

        public Criteria andMealcMultipayIn(List<Boolean> values) {
            addCriterion("mealc_multipay in", values, "mealcMultipay");
            return (Criteria) this;
        }

        public Criteria andMealcMultipayNotIn(List<Boolean> values) {
            addCriterion("mealc_multipay not in", values, "mealcMultipay");
            return (Criteria) this;
        }

        public Criteria andMealcMultipayBetween(Boolean value1, Boolean value2) {
            addCriterion("mealc_multipay between", value1, value2, "mealcMultipay");
            return (Criteria) this;
        }

        public Criteria andMealcMultipayNotBetween(Boolean value1, Boolean value2) {
            addCriterion("mealc_multipay not between", value1, value2, "mealcMultipay");
            return (Criteria) this;
        }

        public Criteria andMealcMultientrustIsNull() {
            addCriterion("mealc_multientrust is null");
            return (Criteria) this;
        }

        public Criteria andMealcMultientrustIsNotNull() {
            addCriterion("mealc_multientrust is not null");
            return (Criteria) this;
        }

        public Criteria andMealcMultientrustEqualTo(Boolean value) {
            addCriterion("mealc_multientrust =", value, "mealcMultientrust");
            return (Criteria) this;
        }

        public Criteria andMealcMultientrustNotEqualTo(Boolean value) {
            addCriterion("mealc_multientrust <>", value, "mealcMultientrust");
            return (Criteria) this;
        }

        public Criteria andMealcMultientrustGreaterThan(Boolean value) {
            addCriterion("mealc_multientrust >", value, "mealcMultientrust");
            return (Criteria) this;
        }

        public Criteria andMealcMultientrustGreaterThanOrEqualTo(Boolean value) {
            addCriterion("mealc_multientrust >=", value, "mealcMultientrust");
            return (Criteria) this;
        }

        public Criteria andMealcMultientrustLessThan(Boolean value) {
            addCriterion("mealc_multientrust <", value, "mealcMultientrust");
            return (Criteria) this;
        }

        public Criteria andMealcMultientrustLessThanOrEqualTo(Boolean value) {
            addCriterion("mealc_multientrust <=", value, "mealcMultientrust");
            return (Criteria) this;
        }

        public Criteria andMealcMultientrustIn(List<Boolean> values) {
            addCriterion("mealc_multientrust in", values, "mealcMultientrust");
            return (Criteria) this;
        }

        public Criteria andMealcMultientrustNotIn(List<Boolean> values) {
            addCriterion("mealc_multientrust not in", values, "mealcMultientrust");
            return (Criteria) this;
        }

        public Criteria andMealcMultientrustBetween(Boolean value1, Boolean value2) {
            addCriterion("mealc_multientrust between", value1, value2, "mealcMultientrust");
            return (Criteria) this;
        }

        public Criteria andMealcMultientrustNotBetween(Boolean value1, Boolean value2) {
            addCriterion("mealc_multientrust not between", value1, value2, "mealcMultientrust");
            return (Criteria) this;
        }

        public Criteria andDiscountServiceIsNull() {
            addCriterion("discount_service is null");
            return (Criteria) this;
        }

        public Criteria andDiscountServiceIsNotNull() {
            addCriterion("discount_service is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountServiceEqualTo(Boolean value) {
            addCriterion("discount_service =", value, "discountService");
            return (Criteria) this;
        }

        public Criteria andDiscountServiceNotEqualTo(Boolean value) {
            addCriterion("discount_service <>", value, "discountService");
            return (Criteria) this;
        }

        public Criteria andDiscountServiceGreaterThan(Boolean value) {
            addCriterion("discount_service >", value, "discountService");
            return (Criteria) this;
        }

        public Criteria andDiscountServiceGreaterThanOrEqualTo(Boolean value) {
            addCriterion("discount_service >=", value, "discountService");
            return (Criteria) this;
        }

        public Criteria andDiscountServiceLessThan(Boolean value) {
            addCriterion("discount_service <", value, "discountService");
            return (Criteria) this;
        }

        public Criteria andDiscountServiceLessThanOrEqualTo(Boolean value) {
            addCriterion("discount_service <=", value, "discountService");
            return (Criteria) this;
        }

        public Criteria andDiscountServiceIn(List<Boolean> values) {
            addCriterion("discount_service in", values, "discountService");
            return (Criteria) this;
        }

        public Criteria andDiscountServiceNotIn(List<Boolean> values) {
            addCriterion("discount_service not in", values, "discountService");
            return (Criteria) this;
        }

        public Criteria andDiscountServiceBetween(Boolean value1, Boolean value2) {
            addCriterion("discount_service between", value1, value2, "discountService");
            return (Criteria) this;
        }

        public Criteria andDiscountServiceNotBetween(Boolean value1, Boolean value2) {
            addCriterion("discount_service not between", value1, value2, "discountService");
            return (Criteria) this;
        }

        public Criteria andDiscountDaymanlimitIsNull() {
            addCriterion("discount_daymanlimit is null");
            return (Criteria) this;
        }

        public Criteria andDiscountDaymanlimitIsNotNull() {
            addCriterion("discount_daymanlimit is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountDaymanlimitEqualTo(Boolean value) {
            addCriterion("discount_daymanlimit =", value, "discountDaymanlimit");
            return (Criteria) this;
        }

        public Criteria andDiscountDaymanlimitNotEqualTo(Boolean value) {
            addCriterion("discount_daymanlimit <>", value, "discountDaymanlimit");
            return (Criteria) this;
        }

        public Criteria andDiscountDaymanlimitGreaterThan(Boolean value) {
            addCriterion("discount_daymanlimit >", value, "discountDaymanlimit");
            return (Criteria) this;
        }

        public Criteria andDiscountDaymanlimitGreaterThanOrEqualTo(Boolean value) {
            addCriterion("discount_daymanlimit >=", value, "discountDaymanlimit");
            return (Criteria) this;
        }

        public Criteria andDiscountDaymanlimitLessThan(Boolean value) {
            addCriterion("discount_daymanlimit <", value, "discountDaymanlimit");
            return (Criteria) this;
        }

        public Criteria andDiscountDaymanlimitLessThanOrEqualTo(Boolean value) {
            addCriterion("discount_daymanlimit <=", value, "discountDaymanlimit");
            return (Criteria) this;
        }

        public Criteria andDiscountDaymanlimitIn(List<Boolean> values) {
            addCriterion("discount_daymanlimit in", values, "discountDaymanlimit");
            return (Criteria) this;
        }

        public Criteria andDiscountDaymanlimitNotIn(List<Boolean> values) {
            addCriterion("discount_daymanlimit not in", values, "discountDaymanlimit");
            return (Criteria) this;
        }

        public Criteria andDiscountDaymanlimitBetween(Boolean value1, Boolean value2) {
            addCriterion("discount_daymanlimit between", value1, value2, "discountDaymanlimit");
            return (Criteria) this;
        }

        public Criteria andDiscountDaymanlimitNotBetween(Boolean value1, Boolean value2) {
            addCriterion("discount_daymanlimit not between", value1, value2, "discountDaymanlimit");
            return (Criteria) this;
        }

        public Criteria andSnackServiceIsNull() {
            addCriterion("snack_service is null");
            return (Criteria) this;
        }

        public Criteria andSnackServiceIsNotNull() {
            addCriterion("snack_service is not null");
            return (Criteria) this;
        }

        public Criteria andSnackServiceEqualTo(Boolean value) {
            addCriterion("snack_service =", value, "snackService");
            return (Criteria) this;
        }

        public Criteria andSnackServiceNotEqualTo(Boolean value) {
            addCriterion("snack_service <>", value, "snackService");
            return (Criteria) this;
        }

        public Criteria andSnackServiceGreaterThan(Boolean value) {
            addCriterion("snack_service >", value, "snackService");
            return (Criteria) this;
        }

        public Criteria andSnackServiceGreaterThanOrEqualTo(Boolean value) {
            addCriterion("snack_service >=", value, "snackService");
            return (Criteria) this;
        }

        public Criteria andSnackServiceLessThan(Boolean value) {
            addCriterion("snack_service <", value, "snackService");
            return (Criteria) this;
        }

        public Criteria andSnackServiceLessThanOrEqualTo(Boolean value) {
            addCriterion("snack_service <=", value, "snackService");
            return (Criteria) this;
        }

        public Criteria andSnackServiceIn(List<Boolean> values) {
            addCriterion("snack_service in", values, "snackService");
            return (Criteria) this;
        }

        public Criteria andSnackServiceNotIn(List<Boolean> values) {
            addCriterion("snack_service not in", values, "snackService");
            return (Criteria) this;
        }

        public Criteria andSnackServiceBetween(Boolean value1, Boolean value2) {
            addCriterion("snack_service between", value1, value2, "snackService");
            return (Criteria) this;
        }

        public Criteria andSnackServiceNotBetween(Boolean value1, Boolean value2) {
            addCriterion("snack_service not between", value1, value2, "snackService");
            return (Criteria) this;
        }

        public Criteria andSnackPresentIsNull() {
            addCriterion("snack_present is null");
            return (Criteria) this;
        }

        public Criteria andSnackPresentIsNotNull() {
            addCriterion("snack_present is not null");
            return (Criteria) this;
        }

        public Criteria andSnackPresentEqualTo(Boolean value) {
            addCriterion("snack_present =", value, "snackPresent");
            return (Criteria) this;
        }

        public Criteria andSnackPresentNotEqualTo(Boolean value) {
            addCriterion("snack_present <>", value, "snackPresent");
            return (Criteria) this;
        }

        public Criteria andSnackPresentGreaterThan(Boolean value) {
            addCriterion("snack_present >", value, "snackPresent");
            return (Criteria) this;
        }

        public Criteria andSnackPresentGreaterThanOrEqualTo(Boolean value) {
            addCriterion("snack_present >=", value, "snackPresent");
            return (Criteria) this;
        }

        public Criteria andSnackPresentLessThan(Boolean value) {
            addCriterion("snack_present <", value, "snackPresent");
            return (Criteria) this;
        }

        public Criteria andSnackPresentLessThanOrEqualTo(Boolean value) {
            addCriterion("snack_present <=", value, "snackPresent");
            return (Criteria) this;
        }

        public Criteria andSnackPresentIn(List<Boolean> values) {
            addCriterion("snack_present in", values, "snackPresent");
            return (Criteria) this;
        }

        public Criteria andSnackPresentNotIn(List<Boolean> values) {
            addCriterion("snack_present not in", values, "snackPresent");
            return (Criteria) this;
        }

        public Criteria andSnackPresentBetween(Boolean value1, Boolean value2) {
            addCriterion("snack_present between", value1, value2, "snackPresent");
            return (Criteria) this;
        }

        public Criteria andSnackPresentNotBetween(Boolean value1, Boolean value2) {
            addCriterion("snack_present not between", value1, value2, "snackPresent");
            return (Criteria) this;
        }

        public Criteria andCommerceServiceIsNull() {
            addCriterion("commerce_service is null");
            return (Criteria) this;
        }

        public Criteria andCommerceServiceIsNotNull() {
            addCriterion("commerce_service is not null");
            return (Criteria) this;
        }

        public Criteria andCommerceServiceEqualTo(Boolean value) {
            addCriterion("commerce_service =", value, "commerceService");
            return (Criteria) this;
        }

        public Criteria andCommerceServiceNotEqualTo(Boolean value) {
            addCriterion("commerce_service <>", value, "commerceService");
            return (Criteria) this;
        }

        public Criteria andCommerceServiceGreaterThan(Boolean value) {
            addCriterion("commerce_service >", value, "commerceService");
            return (Criteria) this;
        }

        public Criteria andCommerceServiceGreaterThanOrEqualTo(Boolean value) {
            addCriterion("commerce_service >=", value, "commerceService");
            return (Criteria) this;
        }

        public Criteria andCommerceServiceLessThan(Boolean value) {
            addCriterion("commerce_service <", value, "commerceService");
            return (Criteria) this;
        }

        public Criteria andCommerceServiceLessThanOrEqualTo(Boolean value) {
            addCriterion("commerce_service <=", value, "commerceService");
            return (Criteria) this;
        }

        public Criteria andCommerceServiceIn(List<Boolean> values) {
            addCriterion("commerce_service in", values, "commerceService");
            return (Criteria) this;
        }

        public Criteria andCommerceServiceNotIn(List<Boolean> values) {
            addCriterion("commerce_service not in", values, "commerceService");
            return (Criteria) this;
        }

        public Criteria andCommerceServiceBetween(Boolean value1, Boolean value2) {
            addCriterion("commerce_service between", value1, value2, "commerceService");
            return (Criteria) this;
        }

        public Criteria andCommerceServiceNotBetween(Boolean value1, Boolean value2) {
            addCriterion("commerce_service not between", value1, value2, "commerceService");
            return (Criteria) this;
        }

        public Criteria andPversionIsNull() {
            addCriterion("pversion is null");
            return (Criteria) this;
        }

        public Criteria andPversionIsNotNull() {
            addCriterion("pversion is not null");
            return (Criteria) this;
        }

        public Criteria andPversionEqualTo(Integer value) {
            addCriterion("pversion =", value, "pversion");
            return (Criteria) this;
        }

        public Criteria andPversionNotEqualTo(Integer value) {
            addCriterion("pversion <>", value, "pversion");
            return (Criteria) this;
        }

        public Criteria andPversionGreaterThan(Integer value) {
            addCriterion("pversion >", value, "pversion");
            return (Criteria) this;
        }

        public Criteria andPversionGreaterThanOrEqualTo(Integer value) {
            addCriterion("pversion >=", value, "pversion");
            return (Criteria) this;
        }

        public Criteria andPversionLessThan(Integer value) {
            addCriterion("pversion <", value, "pversion");
            return (Criteria) this;
        }

        public Criteria andPversionLessThanOrEqualTo(Integer value) {
            addCriterion("pversion <=", value, "pversion");
            return (Criteria) this;
        }

        public Criteria andPversionIn(List<Integer> values) {
            addCriterion("pversion in", values, "pversion");
            return (Criteria) this;
        }

        public Criteria andPversionNotIn(List<Integer> values) {
            addCriterion("pversion not in", values, "pversion");
            return (Criteria) this;
        }

        public Criteria andPversionBetween(Integer value1, Integer value2) {
            addCriterion("pversion between", value1, value2, "pversion");
            return (Criteria) this;
        }

        public Criteria andPversionNotBetween(Integer value1, Integer value2) {
            addCriterion("pversion not between", value1, value2, "pversion");
            return (Criteria) this;
        }

        public Criteria andMealmonthamountIsNull() {
            addCriterion("mealmonthamount is null");
            return (Criteria) this;
        }

        public Criteria andMealmonthamountIsNotNull() {
            addCriterion("mealmonthamount is not null");
            return (Criteria) this;
        }

        public Criteria andMealmonthamountEqualTo(Integer value) {
            addCriterion("mealmonthamount =", value, "mealmonthamount");
            return (Criteria) this;
        }

        public Criteria andMealmonthamountNotEqualTo(Integer value) {
            addCriterion("mealmonthamount <>", value, "mealmonthamount");
            return (Criteria) this;
        }

        public Criteria andMealmonthamountGreaterThan(Integer value) {
            addCriterion("mealmonthamount >", value, "mealmonthamount");
            return (Criteria) this;
        }

        public Criteria andMealmonthamountGreaterThanOrEqualTo(Integer value) {
            addCriterion("mealmonthamount >=", value, "mealmonthamount");
            return (Criteria) this;
        }

        public Criteria andMealmonthamountLessThan(Integer value) {
            addCriterion("mealmonthamount <", value, "mealmonthamount");
            return (Criteria) this;
        }

        public Criteria andMealmonthamountLessThanOrEqualTo(Integer value) {
            addCriterion("mealmonthamount <=", value, "mealmonthamount");
            return (Criteria) this;
        }

        public Criteria andMealmonthamountIn(List<Integer> values) {
            addCriterion("mealmonthamount in", values, "mealmonthamount");
            return (Criteria) this;
        }

        public Criteria andMealmonthamountNotIn(List<Integer> values) {
            addCriterion("mealmonthamount not in", values, "mealmonthamount");
            return (Criteria) this;
        }

        public Criteria andMealmonthamountBetween(Integer value1, Integer value2) {
            addCriterion("mealmonthamount between", value1, value2, "mealmonthamount");
            return (Criteria) this;
        }

        public Criteria andMealmonthamountNotBetween(Integer value1, Integer value2) {
            addCriterion("mealmonthamount not between", value1, value2, "mealmonthamount");
            return (Criteria) this;
        }

        public Criteria andMealmonthforwardIsNull() {
            addCriterion("mealmonthforward is null");
            return (Criteria) this;
        }

        public Criteria andMealmonthforwardIsNotNull() {
            addCriterion("mealmonthforward is not null");
            return (Criteria) this;
        }

        public Criteria andMealmonthforwardEqualTo(Byte value) {
            addCriterion("mealmonthforward =", value, "mealmonthforward");
            return (Criteria) this;
        }

        public Criteria andMealmonthforwardNotEqualTo(Byte value) {
            addCriterion("mealmonthforward <>", value, "mealmonthforward");
            return (Criteria) this;
        }

        public Criteria andMealmonthforwardGreaterThan(Byte value) {
            addCriterion("mealmonthforward >", value, "mealmonthforward");
            return (Criteria) this;
        }

        public Criteria andMealmonthforwardGreaterThanOrEqualTo(Byte value) {
            addCriterion("mealmonthforward >=", value, "mealmonthforward");
            return (Criteria) this;
        }

        public Criteria andMealmonthforwardLessThan(Byte value) {
            addCriterion("mealmonthforward <", value, "mealmonthforward");
            return (Criteria) this;
        }

        public Criteria andMealmonthforwardLessThanOrEqualTo(Byte value) {
            addCriterion("mealmonthforward <=", value, "mealmonthforward");
            return (Criteria) this;
        }

        public Criteria andMealmonthforwardIn(List<Byte> values) {
            addCriterion("mealmonthforward in", values, "mealmonthforward");
            return (Criteria) this;
        }

        public Criteria andMealmonthforwardNotIn(List<Byte> values) {
            addCriterion("mealmonthforward not in", values, "mealmonthforward");
            return (Criteria) this;
        }

        public Criteria andMealmonthforwardBetween(Byte value1, Byte value2) {
            addCriterion("mealmonthforward between", value1, value2, "mealmonthforward");
            return (Criteria) this;
        }

        public Criteria andMealmonthforwardNotBetween(Byte value1, Byte value2) {
            addCriterion("mealmonthforward not between", value1, value2, "mealmonthforward");
            return (Criteria) this;
        }

        public Criteria andMealmonthchargedayIsNull() {
            addCriterion("mealmonthchargeday is null");
            return (Criteria) this;
        }

        public Criteria andMealmonthchargedayIsNotNull() {
            addCriterion("mealmonthchargeday is not null");
            return (Criteria) this;
        }

        public Criteria andMealmonthchargedayEqualTo(Byte value) {
            addCriterion("mealmonthchargeday =", value, "mealmonthchargeday");
            return (Criteria) this;
        }

        public Criteria andMealmonthchargedayNotEqualTo(Byte value) {
            addCriterion("mealmonthchargeday <>", value, "mealmonthchargeday");
            return (Criteria) this;
        }

        public Criteria andMealmonthchargedayGreaterThan(Byte value) {
            addCriterion("mealmonthchargeday >", value, "mealmonthchargeday");
            return (Criteria) this;
        }

        public Criteria andMealmonthchargedayGreaterThanOrEqualTo(Byte value) {
            addCriterion("mealmonthchargeday >=", value, "mealmonthchargeday");
            return (Criteria) this;
        }

        public Criteria andMealmonthchargedayLessThan(Byte value) {
            addCriterion("mealmonthchargeday <", value, "mealmonthchargeday");
            return (Criteria) this;
        }

        public Criteria andMealmonthchargedayLessThanOrEqualTo(Byte value) {
            addCriterion("mealmonthchargeday <=", value, "mealmonthchargeday");
            return (Criteria) this;
        }

        public Criteria andMealmonthchargedayIn(List<Byte> values) {
            addCriterion("mealmonthchargeday in", values, "mealmonthchargeday");
            return (Criteria) this;
        }

        public Criteria andMealmonthchargedayNotIn(List<Byte> values) {
            addCriterion("mealmonthchargeday not in", values, "mealmonthchargeday");
            return (Criteria) this;
        }

        public Criteria andMealmonthchargedayBetween(Byte value1, Byte value2) {
            addCriterion("mealmonthchargeday between", value1, value2, "mealmonthchargeday");
            return (Criteria) this;
        }

        public Criteria andMealmonthchargedayNotBetween(Byte value1, Byte value2) {
            addCriterion("mealmonthchargeday not between", value1, value2, "mealmonthchargeday");
            return (Criteria) this;
        }

        public Criteria andMealpolicyIsNull() {
            addCriterion("mealpolicy is null");
            return (Criteria) this;
        }

        public Criteria andMealpolicyIsNotNull() {
            addCriterion("mealpolicy is not null");
            return (Criteria) this;
        }

        public Criteria andMealpolicyEqualTo(Byte value) {
            addCriterion("mealpolicy =", value, "mealpolicy");
            return (Criteria) this;
        }

        public Criteria andMealpolicyNotEqualTo(Byte value) {
            addCriterion("mealpolicy <>", value, "mealpolicy");
            return (Criteria) this;
        }

        public Criteria andMealpolicyGreaterThan(Byte value) {
            addCriterion("mealpolicy >", value, "mealpolicy");
            return (Criteria) this;
        }

        public Criteria andMealpolicyGreaterThanOrEqualTo(Byte value) {
            addCriterion("mealpolicy >=", value, "mealpolicy");
            return (Criteria) this;
        }

        public Criteria andMealpolicyLessThan(Byte value) {
            addCriterion("mealpolicy <", value, "mealpolicy");
            return (Criteria) this;
        }

        public Criteria andMealpolicyLessThanOrEqualTo(Byte value) {
            addCriterion("mealpolicy <=", value, "mealpolicy");
            return (Criteria) this;
        }

        public Criteria andMealpolicyIn(List<Byte> values) {
            addCriterion("mealpolicy in", values, "mealpolicy");
            return (Criteria) this;
        }

        public Criteria andMealpolicyNotIn(List<Byte> values) {
            addCriterion("mealpolicy not in", values, "mealpolicy");
            return (Criteria) this;
        }

        public Criteria andMealpolicyBetween(Byte value1, Byte value2) {
            addCriterion("mealpolicy between", value1, value2, "mealpolicy");
            return (Criteria) this;
        }

        public Criteria andMealpolicyNotBetween(Byte value1, Byte value2) {
            addCriterion("mealpolicy not between", value1, value2, "mealpolicy");
            return (Criteria) this;
        }

        public Criteria andMealcommentIsNull() {
            addCriterion("mealcomment is null");
            return (Criteria) this;
        }

        public Criteria andMealcommentIsNotNull() {
            addCriterion("mealcomment is not null");
            return (Criteria) this;
        }

        public Criteria andMealcommentEqualTo(String value) {
            addCriterion("mealcomment =", value, "mealcomment");
            return (Criteria) this;
        }

        public Criteria andMealcommentNotEqualTo(String value) {
            addCriterion("mealcomment <>", value, "mealcomment");
            return (Criteria) this;
        }

        public Criteria andMealcommentGreaterThan(String value) {
            addCriterion("mealcomment >", value, "mealcomment");
            return (Criteria) this;
        }

        public Criteria andMealcommentGreaterThanOrEqualTo(String value) {
            addCriterion("mealcomment >=", value, "mealcomment");
            return (Criteria) this;
        }

        public Criteria andMealcommentLessThan(String value) {
            addCriterion("mealcomment <", value, "mealcomment");
            return (Criteria) this;
        }

        public Criteria andMealcommentLessThanOrEqualTo(String value) {
            addCriterion("mealcomment <=", value, "mealcomment");
            return (Criteria) this;
        }

        public Criteria andMealcommentLike(String value) {
            addCriterion("mealcomment like", value, "mealcomment");
            return (Criteria) this;
        }

        public Criteria andMealcommentNotLike(String value) {
            addCriterion("mealcomment not like", value, "mealcomment");
            return (Criteria) this;
        }

        public Criteria andMealcommentIn(List<String> values) {
            addCriterion("mealcomment in", values, "mealcomment");
            return (Criteria) this;
        }

        public Criteria andMealcommentNotIn(List<String> values) {
            addCriterion("mealcomment not in", values, "mealcomment");
            return (Criteria) this;
        }

        public Criteria andMealcommentBetween(String value1, String value2) {
            addCriterion("mealcomment between", value1, value2, "mealcomment");
            return (Criteria) this;
        }

        public Criteria andMealcommentNotBetween(String value1, String value2) {
            addCriterion("mealcomment not between", value1, value2, "mealcomment");
            return (Criteria) this;
        }

        public Criteria andMealusershareIsNull() {
            addCriterion("mealusershare is null");
            return (Criteria) this;
        }

        public Criteria andMealusershareIsNotNull() {
            addCriterion("mealusershare is not null");
            return (Criteria) this;
        }

        public Criteria andMealusershareEqualTo(Byte value) {
            addCriterion("mealusershare =", value, "mealusershare");
            return (Criteria) this;
        }

        public Criteria andMealusershareNotEqualTo(Byte value) {
            addCriterion("mealusershare <>", value, "mealusershare");
            return (Criteria) this;
        }

        public Criteria andMealusershareGreaterThan(Byte value) {
            addCriterion("mealusershare >", value, "mealusershare");
            return (Criteria) this;
        }

        public Criteria andMealusershareGreaterThanOrEqualTo(Byte value) {
            addCriterion("mealusershare >=", value, "mealusershare");
            return (Criteria) this;
        }

        public Criteria andMealusershareLessThan(Byte value) {
            addCriterion("mealusershare <", value, "mealusershare");
            return (Criteria) this;
        }

        public Criteria andMealusershareLessThanOrEqualTo(Byte value) {
            addCriterion("mealusershare <=", value, "mealusershare");
            return (Criteria) this;
        }

        public Criteria andMealusershareIn(List<Byte> values) {
            addCriterion("mealusershare in", values, "mealusershare");
            return (Criteria) this;
        }

        public Criteria andMealusershareNotIn(List<Byte> values) {
            addCriterion("mealusershare not in", values, "mealusershare");
            return (Criteria) this;
        }

        public Criteria andMealusershareBetween(Byte value1, Byte value2) {
            addCriterion("mealusershare between", value1, value2, "mealusershare");
            return (Criteria) this;
        }

        public Criteria andMealusershareNotBetween(Byte value1, Byte value2) {
            addCriterion("mealusershare not between", value1, value2, "mealusershare");
            return (Criteria) this;
        }

        public Criteria andMealmultimenuIsNull() {
            addCriterion("mealmultimenu is null");
            return (Criteria) this;
        }

        public Criteria andMealmultimenuIsNotNull() {
            addCriterion("mealmultimenu is not null");
            return (Criteria) this;
        }

        public Criteria andMealmultimenuEqualTo(Boolean value) {
            addCriterion("mealmultimenu =", value, "mealmultimenu");
            return (Criteria) this;
        }

        public Criteria andMealmultimenuNotEqualTo(Boolean value) {
            addCriterion("mealmultimenu <>", value, "mealmultimenu");
            return (Criteria) this;
        }

        public Criteria andMealmultimenuGreaterThan(Boolean value) {
            addCriterion("mealmultimenu >", value, "mealmultimenu");
            return (Criteria) this;
        }

        public Criteria andMealmultimenuGreaterThanOrEqualTo(Boolean value) {
            addCriterion("mealmultimenu >=", value, "mealmultimenu");
            return (Criteria) this;
        }

        public Criteria andMealmultimenuLessThan(Boolean value) {
            addCriterion("mealmultimenu <", value, "mealmultimenu");
            return (Criteria) this;
        }

        public Criteria andMealmultimenuLessThanOrEqualTo(Boolean value) {
            addCriterion("mealmultimenu <=", value, "mealmultimenu");
            return (Criteria) this;
        }

        public Criteria andMealmultimenuIn(List<Boolean> values) {
            addCriterion("mealmultimenu in", values, "mealmultimenu");
            return (Criteria) this;
        }

        public Criteria andMealmultimenuNotIn(List<Boolean> values) {
            addCriterion("mealmultimenu not in", values, "mealmultimenu");
            return (Criteria) this;
        }

        public Criteria andMealmultimenuBetween(Boolean value1, Boolean value2) {
            addCriterion("mealmultimenu between", value1, value2, "mealmultimenu");
            return (Criteria) this;
        }

        public Criteria andMealmultimenuNotBetween(Boolean value1, Boolean value2) {
            addCriterion("mealmultimenu not between", value1, value2, "mealmultimenu");
            return (Criteria) this;
        }

        public Criteria andMealdayIsNull() {
            addCriterion("mealday is null");
            return (Criteria) this;
        }

        public Criteria andMealdayIsNotNull() {
            addCriterion("mealday is not null");
            return (Criteria) this;
        }

        public Criteria andMealdayEqualTo(Boolean value) {
            addCriterion("mealday =", value, "mealday");
            return (Criteria) this;
        }

        public Criteria andMealdayNotEqualTo(Boolean value) {
            addCriterion("mealday <>", value, "mealday");
            return (Criteria) this;
        }

        public Criteria andMealdayGreaterThan(Boolean value) {
            addCriterion("mealday >", value, "mealday");
            return (Criteria) this;
        }

        public Criteria andMealdayGreaterThanOrEqualTo(Boolean value) {
            addCriterion("mealday >=", value, "mealday");
            return (Criteria) this;
        }

        public Criteria andMealdayLessThan(Boolean value) {
            addCriterion("mealday <", value, "mealday");
            return (Criteria) this;
        }

        public Criteria andMealdayLessThanOrEqualTo(Boolean value) {
            addCriterion("mealday <=", value, "mealday");
            return (Criteria) this;
        }

        public Criteria andMealdayIn(List<Boolean> values) {
            addCriterion("mealday in", values, "mealday");
            return (Criteria) this;
        }

        public Criteria andMealdayNotIn(List<Boolean> values) {
            addCriterion("mealday not in", values, "mealday");
            return (Criteria) this;
        }

        public Criteria andMealdayBetween(Boolean value1, Boolean value2) {
            addCriterion("mealday between", value1, value2, "mealday");
            return (Criteria) this;
        }

        public Criteria andMealdayNotBetween(Boolean value1, Boolean value2) {
            addCriterion("mealday not between", value1, value2, "mealday");
            return (Criteria) this;
        }

        public Criteria andMeallongIsNull() {
            addCriterion("meallong is null");
            return (Criteria) this;
        }

        public Criteria andMeallongIsNotNull() {
            addCriterion("meallong is not null");
            return (Criteria) this;
        }

        public Criteria andMeallongEqualTo(Boolean value) {
            addCriterion("meallong =", value, "meallong");
            return (Criteria) this;
        }

        public Criteria andMeallongNotEqualTo(Boolean value) {
            addCriterion("meallong <>", value, "meallong");
            return (Criteria) this;
        }

        public Criteria andMeallongGreaterThan(Boolean value) {
            addCriterion("meallong >", value, "meallong");
            return (Criteria) this;
        }

        public Criteria andMeallongGreaterThanOrEqualTo(Boolean value) {
            addCriterion("meallong >=", value, "meallong");
            return (Criteria) this;
        }

        public Criteria andMeallongLessThan(Boolean value) {
            addCriterion("meallong <", value, "meallong");
            return (Criteria) this;
        }

        public Criteria andMeallongLessThanOrEqualTo(Boolean value) {
            addCriterion("meallong <=", value, "meallong");
            return (Criteria) this;
        }

        public Criteria andMeallongIn(List<Boolean> values) {
            addCriterion("meallong in", values, "meallong");
            return (Criteria) this;
        }

        public Criteria andMeallongNotIn(List<Boolean> values) {
            addCriterion("meallong not in", values, "meallong");
            return (Criteria) this;
        }

        public Criteria andMeallongBetween(Boolean value1, Boolean value2) {
            addCriterion("meallong between", value1, value2, "meallong");
            return (Criteria) this;
        }

        public Criteria andMeallongNotBetween(Boolean value1, Boolean value2) {
            addCriterion("meallong not between", value1, value2, "meallong");
            return (Criteria) this;
        }

        public Criteria andMealunlimitIsNull() {
            addCriterion("mealunlimit is null");
            return (Criteria) this;
        }

        public Criteria andMealunlimitIsNotNull() {
            addCriterion("mealunlimit is not null");
            return (Criteria) this;
        }

        public Criteria andMealunlimitEqualTo(Boolean value) {
            addCriterion("mealunlimit =", value, "mealunlimit");
            return (Criteria) this;
        }

        public Criteria andMealunlimitNotEqualTo(Boolean value) {
            addCriterion("mealunlimit <>", value, "mealunlimit");
            return (Criteria) this;
        }

        public Criteria andMealunlimitGreaterThan(Boolean value) {
            addCriterion("mealunlimit >", value, "mealunlimit");
            return (Criteria) this;
        }

        public Criteria andMealunlimitGreaterThanOrEqualTo(Boolean value) {
            addCriterion("mealunlimit >=", value, "mealunlimit");
            return (Criteria) this;
        }

        public Criteria andMealunlimitLessThan(Boolean value) {
            addCriterion("mealunlimit <", value, "mealunlimit");
            return (Criteria) this;
        }

        public Criteria andMealunlimitLessThanOrEqualTo(Boolean value) {
            addCriterion("mealunlimit <=", value, "mealunlimit");
            return (Criteria) this;
        }

        public Criteria andMealunlimitIn(List<Boolean> values) {
            addCriterion("mealunlimit in", values, "mealunlimit");
            return (Criteria) this;
        }

        public Criteria andMealunlimitNotIn(List<Boolean> values) {
            addCriterion("mealunlimit not in", values, "mealunlimit");
            return (Criteria) this;
        }

        public Criteria andMealunlimitBetween(Boolean value1, Boolean value2) {
            addCriterion("mealunlimit between", value1, value2, "mealunlimit");
            return (Criteria) this;
        }

        public Criteria andMealunlimitNotBetween(Boolean value1, Boolean value2) {
            addCriterion("mealunlimit not between", value1, value2, "mealunlimit");
            return (Criteria) this;
        }

        public Criteria andMealautomonthIsNull() {
            addCriterion("mealautomonth is null");
            return (Criteria) this;
        }

        public Criteria andMealautomonthIsNotNull() {
            addCriterion("mealautomonth is not null");
            return (Criteria) this;
        }

        public Criteria andMealautomonthEqualTo(Boolean value) {
            addCriterion("mealautomonth =", value, "mealautomonth");
            return (Criteria) this;
        }

        public Criteria andMealautomonthNotEqualTo(Boolean value) {
            addCriterion("mealautomonth <>", value, "mealautomonth");
            return (Criteria) this;
        }

        public Criteria andMealautomonthGreaterThan(Boolean value) {
            addCriterion("mealautomonth >", value, "mealautomonth");
            return (Criteria) this;
        }

        public Criteria andMealautomonthGreaterThanOrEqualTo(Boolean value) {
            addCriterion("mealautomonth >=", value, "mealautomonth");
            return (Criteria) this;
        }

        public Criteria andMealautomonthLessThan(Boolean value) {
            addCriterion("mealautomonth <", value, "mealautomonth");
            return (Criteria) this;
        }

        public Criteria andMealautomonthLessThanOrEqualTo(Boolean value) {
            addCriterion("mealautomonth <=", value, "mealautomonth");
            return (Criteria) this;
        }

        public Criteria andMealautomonthIn(List<Boolean> values) {
            addCriterion("mealautomonth in", values, "mealautomonth");
            return (Criteria) this;
        }

        public Criteria andMealautomonthNotIn(List<Boolean> values) {
            addCriterion("mealautomonth not in", values, "mealautomonth");
            return (Criteria) this;
        }

        public Criteria andMealautomonthBetween(Boolean value1, Boolean value2) {
            addCriterion("mealautomonth between", value1, value2, "mealautomonth");
            return (Criteria) this;
        }

        public Criteria andMealautomonthNotBetween(Boolean value1, Boolean value2) {
            addCriterion("mealautomonth not between", value1, value2, "mealautomonth");
            return (Criteria) this;
        }

        public Criteria andCafeteriaIsNull() {
            addCriterion("cafeteria is null");
            return (Criteria) this;
        }

        public Criteria andCafeteriaIsNotNull() {
            addCriterion("cafeteria is not null");
            return (Criteria) this;
        }

        public Criteria andCafeteriaEqualTo(Boolean value) {
            addCriterion("cafeteria =", value, "cafeteria");
            return (Criteria) this;
        }

        public Criteria andCafeteriaNotEqualTo(Boolean value) {
            addCriterion("cafeteria <>", value, "cafeteria");
            return (Criteria) this;
        }

        public Criteria andCafeteriaGreaterThan(Boolean value) {
            addCriterion("cafeteria >", value, "cafeteria");
            return (Criteria) this;
        }

        public Criteria andCafeteriaGreaterThanOrEqualTo(Boolean value) {
            addCriterion("cafeteria >=", value, "cafeteria");
            return (Criteria) this;
        }

        public Criteria andCafeteriaLessThan(Boolean value) {
            addCriterion("cafeteria <", value, "cafeteria");
            return (Criteria) this;
        }

        public Criteria andCafeteriaLessThanOrEqualTo(Boolean value) {
            addCriterion("cafeteria <=", value, "cafeteria");
            return (Criteria) this;
        }

        public Criteria andCafeteriaIn(List<Boolean> values) {
            addCriterion("cafeteria in", values, "cafeteria");
            return (Criteria) this;
        }

        public Criteria andCafeteriaNotIn(List<Boolean> values) {
            addCriterion("cafeteria not in", values, "cafeteria");
            return (Criteria) this;
        }

        public Criteria andCafeteriaBetween(Boolean value1, Boolean value2) {
            addCriterion("cafeteria between", value1, value2, "cafeteria");
            return (Criteria) this;
        }

        public Criteria andCafeteriaNotBetween(Boolean value1, Boolean value2) {
            addCriterion("cafeteria not between", value1, value2, "cafeteria");
            return (Criteria) this;
        }

        public Criteria andCompanyNoticeIsNull() {
            addCriterion("company_notice is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNoticeIsNotNull() {
            addCriterion("company_notice is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNoticeEqualTo(Boolean value) {
            addCriterion("company_notice =", value, "companyNotice");
            return (Criteria) this;
        }

        public Criteria andCompanyNoticeNotEqualTo(Boolean value) {
            addCriterion("company_notice <>", value, "companyNotice");
            return (Criteria) this;
        }

        public Criteria andCompanyNoticeGreaterThan(Boolean value) {
            addCriterion("company_notice >", value, "companyNotice");
            return (Criteria) this;
        }

        public Criteria andCompanyNoticeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("company_notice >=", value, "companyNotice");
            return (Criteria) this;
        }

        public Criteria andCompanyNoticeLessThan(Boolean value) {
            addCriterion("company_notice <", value, "companyNotice");
            return (Criteria) this;
        }

        public Criteria andCompanyNoticeLessThanOrEqualTo(Boolean value) {
            addCriterion("company_notice <=", value, "companyNotice");
            return (Criteria) this;
        }

        public Criteria andCompanyNoticeIn(List<Boolean> values) {
            addCriterion("company_notice in", values, "companyNotice");
            return (Criteria) this;
        }

        public Criteria andCompanyNoticeNotIn(List<Boolean> values) {
            addCriterion("company_notice not in", values, "companyNotice");
            return (Criteria) this;
        }

        public Criteria andCompanyNoticeBetween(Boolean value1, Boolean value2) {
            addCriterion("company_notice between", value1, value2, "companyNotice");
            return (Criteria) this;
        }

        public Criteria andCompanyNoticeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("company_notice not between", value1, value2, "companyNotice");
            return (Criteria) this;
        }

        public Criteria andIsmealgroupIsNull() {
            addCriterion("isMealGroup is null");
            return (Criteria) this;
        }

        public Criteria andIsmealgroupIsNotNull() {
            addCriterion("isMealGroup is not null");
            return (Criteria) this;
        }

        public Criteria andIsmealgroupEqualTo(Boolean value) {
            addCriterion("isMealGroup =", value, "ismealgroup");
            return (Criteria) this;
        }

        public Criteria andIsmealgroupNotEqualTo(Boolean value) {
            addCriterion("isMealGroup <>", value, "ismealgroup");
            return (Criteria) this;
        }

        public Criteria andIsmealgroupGreaterThan(Boolean value) {
            addCriterion("isMealGroup >", value, "ismealgroup");
            return (Criteria) this;
        }

        public Criteria andIsmealgroupGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isMealGroup >=", value, "ismealgroup");
            return (Criteria) this;
        }

        public Criteria andIsmealgroupLessThan(Boolean value) {
            addCriterion("isMealGroup <", value, "ismealgroup");
            return (Criteria) this;
        }

        public Criteria andIsmealgroupLessThanOrEqualTo(Boolean value) {
            addCriterion("isMealGroup <=", value, "ismealgroup");
            return (Criteria) this;
        }

        public Criteria andIsmealgroupIn(List<Boolean> values) {
            addCriterion("isMealGroup in", values, "ismealgroup");
            return (Criteria) this;
        }

        public Criteria andIsmealgroupNotIn(List<Boolean> values) {
            addCriterion("isMealGroup not in", values, "ismealgroup");
            return (Criteria) this;
        }

        public Criteria andIsmealgroupBetween(Boolean value1, Boolean value2) {
            addCriterion("isMealGroup between", value1, value2, "ismealgroup");
            return (Criteria) this;
        }

        public Criteria andIsmealgroupNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isMealGroup not between", value1, value2, "ismealgroup");
            return (Criteria) this;
        }

        public Criteria andIsticketformatIsNull() {
            addCriterion("isTicketFormat is null");
            return (Criteria) this;
        }

        public Criteria andIsticketformatIsNotNull() {
            addCriterion("isTicketFormat is not null");
            return (Criteria) this;
        }

        public Criteria andIsticketformatEqualTo(Boolean value) {
            addCriterion("isTicketFormat =", value, "isticketformat");
            return (Criteria) this;
        }

        public Criteria andIsticketformatNotEqualTo(Boolean value) {
            addCriterion("isTicketFormat <>", value, "isticketformat");
            return (Criteria) this;
        }

        public Criteria andIsticketformatGreaterThan(Boolean value) {
            addCriterion("isTicketFormat >", value, "isticketformat");
            return (Criteria) this;
        }

        public Criteria andIsticketformatGreaterThanOrEqualTo(Boolean value) {
            addCriterion("isTicketFormat >=", value, "isticketformat");
            return (Criteria) this;
        }

        public Criteria andIsticketformatLessThan(Boolean value) {
            addCriterion("isTicketFormat <", value, "isticketformat");
            return (Criteria) this;
        }

        public Criteria andIsticketformatLessThanOrEqualTo(Boolean value) {
            addCriterion("isTicketFormat <=", value, "isticketformat");
            return (Criteria) this;
        }

        public Criteria andIsticketformatIn(List<Boolean> values) {
            addCriterion("isTicketFormat in", values, "isticketformat");
            return (Criteria) this;
        }

        public Criteria andIsticketformatNotIn(List<Boolean> values) {
            addCriterion("isTicketFormat not in", values, "isticketformat");
            return (Criteria) this;
        }

        public Criteria andIsticketformatBetween(Boolean value1, Boolean value2) {
            addCriterion("isTicketFormat between", value1, value2, "isticketformat");
            return (Criteria) this;
        }

        public Criteria andIsticketformatNotBetween(Boolean value1, Boolean value2) {
            addCriterion("isTicketFormat not between", value1, value2, "isticketformat");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}