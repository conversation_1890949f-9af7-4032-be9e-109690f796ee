package com.vlocally.mealcoupon.vo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CoupongroupExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public CoupongroupExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andCidIsNull() {
            addCriterion("cid is null");
            return (Criteria) this;
        }

        public Criteria andCidIsNotNull() {
            addCriterion("cid is not null");
            return (Criteria) this;
        }

        public Criteria andCidEqualTo(String value) {
            addCriterion("cid =", value, "cid");
            return (Criteria) this;
        }

        public Criteria andCidNotEqualTo(String value) {
            addCriterion("cid <>", value, "cid");
            return (Criteria) this;
        }

        public Criteria andCidGreaterThan(String value) {
            addCriterion("cid >", value, "cid");
            return (Criteria) this;
        }

        public Criteria andCidGreaterThanOrEqualTo(String value) {
            addCriterion("cid >=", value, "cid");
            return (Criteria) this;
        }

        public Criteria andCidLessThan(String value) {
            addCriterion("cid <", value, "cid");
            return (Criteria) this;
        }

        public Criteria andCidLessThanOrEqualTo(String value) {
            addCriterion("cid <=", value, "cid");
            return (Criteria) this;
        }

        public Criteria andCidLike(String value) {
            addCriterion("cid like", value, "cid");
            return (Criteria) this;
        }

        public Criteria andCidNotLike(String value) {
            addCriterion("cid not like", value, "cid");
            return (Criteria) this;
        }

        public Criteria andCidIn(List<String> values) {
            addCriterion("cid in", values, "cid");
            return (Criteria) this;
        }

        public Criteria andCidNotIn(List<String> values) {
            addCriterion("cid not in", values, "cid");
            return (Criteria) this;
        }

        public Criteria andCidBetween(String value1, String value2) {
            addCriterion("cid between", value1, value2, "cid");
            return (Criteria) this;
        }

        public Criteria andCidNotBetween(String value1, String value2) {
            addCriterion("cid not between", value1, value2, "cid");
            return (Criteria) this;
        }

        public Criteria andComidIsNull() {
            addCriterion("comid is null");
            return (Criteria) this;
        }

        public Criteria andComidIsNotNull() {
            addCriterion("comid is not null");
            return (Criteria) this;
        }

        public Criteria andComidEqualTo(String value) {
            addCriterion("comid =", value, "comid");
            return (Criteria) this;
        }

        public Criteria andComidNotEqualTo(String value) {
            addCriterion("comid <>", value, "comid");
            return (Criteria) this;
        }

        public Criteria andComidGreaterThan(String value) {
            addCriterion("comid >", value, "comid");
            return (Criteria) this;
        }

        public Criteria andComidGreaterThanOrEqualTo(String value) {
            addCriterion("comid >=", value, "comid");
            return (Criteria) this;
        }

        public Criteria andComidLessThan(String value) {
            addCriterion("comid <", value, "comid");
            return (Criteria) this;
        }

        public Criteria andComidLessThanOrEqualTo(String value) {
            addCriterion("comid <=", value, "comid");
            return (Criteria) this;
        }

        public Criteria andComidLike(String value) {
            addCriterion("comid like", value, "comid");
            return (Criteria) this;
        }

        public Criteria andComidNotLike(String value) {
            addCriterion("comid not like", value, "comid");
            return (Criteria) this;
        }

        public Criteria andComidIn(List<String> values) {
            addCriterion("comid in", values, "comid");
            return (Criteria) this;
        }

        public Criteria andComidNotIn(List<String> values) {
            addCriterion("comid not in", values, "comid");
            return (Criteria) this;
        }

        public Criteria andComidBetween(String value1, String value2) {
            addCriterion("comid between", value1, value2, "comid");
            return (Criteria) this;
        }

        public Criteria andComidNotBetween(String value1, String value2) {
            addCriterion("comid not between", value1, value2, "comid");
            return (Criteria) this;
        }

        public Criteria andComnameIsNull() {
            addCriterion("comname is null");
            return (Criteria) this;
        }

        public Criteria andComnameIsNotNull() {
            addCriterion("comname is not null");
            return (Criteria) this;
        }

        public Criteria andComnameEqualTo(String value) {
            addCriterion("comname =", value, "comname");
            return (Criteria) this;
        }

        public Criteria andComnameNotEqualTo(String value) {
            addCriterion("comname <>", value, "comname");
            return (Criteria) this;
        }

        public Criteria andComnameGreaterThan(String value) {
            addCriterion("comname >", value, "comname");
            return (Criteria) this;
        }

        public Criteria andComnameGreaterThanOrEqualTo(String value) {
            addCriterion("comname >=", value, "comname");
            return (Criteria) this;
        }

        public Criteria andComnameLessThan(String value) {
            addCriterion("comname <", value, "comname");
            return (Criteria) this;
        }

        public Criteria andComnameLessThanOrEqualTo(String value) {
            addCriterion("comname <=", value, "comname");
            return (Criteria) this;
        }

        public Criteria andComnameLike(String value) {
            addCriterion("comname like", value, "comname");
            return (Criteria) this;
        }

        public Criteria andComnameNotLike(String value) {
            addCriterion("comname not like", value, "comname");
            return (Criteria) this;
        }

        public Criteria andComnameIn(List<String> values) {
            addCriterion("comname in", values, "comname");
            return (Criteria) this;
        }

        public Criteria andComnameNotIn(List<String> values) {
            addCriterion("comname not in", values, "comname");
            return (Criteria) this;
        }

        public Criteria andComnameBetween(String value1, String value2) {
            addCriterion("comname between", value1, value2, "comname");
            return (Criteria) this;
        }

        public Criteria andComnameNotBetween(String value1, String value2) {
            addCriterion("comname not between", value1, value2, "comname");
            return (Criteria) this;
        }

        public Criteria andLeaderidIsNull() {
            addCriterion("leaderid is null");
            return (Criteria) this;
        }

        public Criteria andLeaderidIsNotNull() {
            addCriterion("leaderid is not null");
            return (Criteria) this;
        }

        public Criteria andLeaderidEqualTo(String value) {
            addCriterion("leaderid =", value, "leaderid");
            return (Criteria) this;
        }

        public Criteria andLeaderidNotEqualTo(String value) {
            addCriterion("leaderid <>", value, "leaderid");
            return (Criteria) this;
        }

        public Criteria andLeaderidGreaterThan(String value) {
            addCriterion("leaderid >", value, "leaderid");
            return (Criteria) this;
        }

        public Criteria andLeaderidGreaterThanOrEqualTo(String value) {
            addCriterion("leaderid >=", value, "leaderid");
            return (Criteria) this;
        }

        public Criteria andLeaderidLessThan(String value) {
            addCriterion("leaderid <", value, "leaderid");
            return (Criteria) this;
        }

        public Criteria andLeaderidLessThanOrEqualTo(String value) {
            addCriterion("leaderid <=", value, "leaderid");
            return (Criteria) this;
        }

        public Criteria andLeaderidLike(String value) {
            addCriterion("leaderid like", value, "leaderid");
            return (Criteria) this;
        }

        public Criteria andLeaderidNotLike(String value) {
            addCriterion("leaderid not like", value, "leaderid");
            return (Criteria) this;
        }

        public Criteria andLeaderidIn(List<String> values) {
            addCriterion("leaderid in", values, "leaderid");
            return (Criteria) this;
        }

        public Criteria andLeaderidNotIn(List<String> values) {
            addCriterion("leaderid not in", values, "leaderid");
            return (Criteria) this;
        }

        public Criteria andLeaderidBetween(String value1, String value2) {
            addCriterion("leaderid between", value1, value2, "leaderid");
            return (Criteria) this;
        }

        public Criteria andLeaderidNotBetween(String value1, String value2) {
            addCriterion("leaderid not between", value1, value2, "leaderid");
            return (Criteria) this;
        }

        public Criteria andLeadernameIsNull() {
            addCriterion("leadername is null");
            return (Criteria) this;
        }

        public Criteria andLeadernameIsNotNull() {
            addCriterion("leadername is not null");
            return (Criteria) this;
        }

        public Criteria andLeadernameEqualTo(String value) {
            addCriterion("leadername =", value, "leadername");
            return (Criteria) this;
        }

        public Criteria andLeadernameNotEqualTo(String value) {
            addCriterion("leadername <>", value, "leadername");
            return (Criteria) this;
        }

        public Criteria andLeadernameGreaterThan(String value) {
            addCriterion("leadername >", value, "leadername");
            return (Criteria) this;
        }

        public Criteria andLeadernameGreaterThanOrEqualTo(String value) {
            addCriterion("leadername >=", value, "leadername");
            return (Criteria) this;
        }

        public Criteria andLeadernameLessThan(String value) {
            addCriterion("leadername <", value, "leadername");
            return (Criteria) this;
        }

        public Criteria andLeadernameLessThanOrEqualTo(String value) {
            addCriterion("leadername <=", value, "leadername");
            return (Criteria) this;
        }

        public Criteria andLeadernameLike(String value) {
            addCriterion("leadername like", value, "leadername");
            return (Criteria) this;
        }

        public Criteria andLeadernameNotLike(String value) {
            addCriterion("leadername not like", value, "leadername");
            return (Criteria) this;
        }

        public Criteria andLeadernameIn(List<String> values) {
            addCriterion("leadername in", values, "leadername");
            return (Criteria) this;
        }

        public Criteria andLeadernameNotIn(List<String> values) {
            addCriterion("leadername not in", values, "leadername");
            return (Criteria) this;
        }

        public Criteria andLeadernameBetween(String value1, String value2) {
            addCriterion("leadername between", value1, value2, "leadername");
            return (Criteria) this;
        }

        public Criteria andLeadernameNotBetween(String value1, String value2) {
            addCriterion("leadername not between", value1, value2, "leadername");
            return (Criteria) this;
        }

        public Criteria andSidIsNull() {
            addCriterion("sid is null");
            return (Criteria) this;
        }

        public Criteria andSidIsNotNull() {
            addCriterion("sid is not null");
            return (Criteria) this;
        }

        public Criteria andSidEqualTo(String value) {
            addCriterion("sid =", value, "sid");
            return (Criteria) this;
        }

        public Criteria andSidNotEqualTo(String value) {
            addCriterion("sid <>", value, "sid");
            return (Criteria) this;
        }

        public Criteria andSidGreaterThan(String value) {
            addCriterion("sid >", value, "sid");
            return (Criteria) this;
        }

        public Criteria andSidGreaterThanOrEqualTo(String value) {
            addCriterion("sid >=", value, "sid");
            return (Criteria) this;
        }

        public Criteria andSidLessThan(String value) {
            addCriterion("sid <", value, "sid");
            return (Criteria) this;
        }

        public Criteria andSidLessThanOrEqualTo(String value) {
            addCriterion("sid <=", value, "sid");
            return (Criteria) this;
        }

        public Criteria andSidLike(String value) {
            addCriterion("sid like", value, "sid");
            return (Criteria) this;
        }

        public Criteria andSidNotLike(String value) {
            addCriterion("sid not like", value, "sid");
            return (Criteria) this;
        }

        public Criteria andSidIn(List<String> values) {
            addCriterion("sid in", values, "sid");
            return (Criteria) this;
        }

        public Criteria andSidNotIn(List<String> values) {
            addCriterion("sid not in", values, "sid");
            return (Criteria) this;
        }

        public Criteria andSidBetween(String value1, String value2) {
            addCriterion("sid between", value1, value2, "sid");
            return (Criteria) this;
        }

        public Criteria andSidNotBetween(String value1, String value2) {
            addCriterion("sid not between", value1, value2, "sid");
            return (Criteria) this;
        }

        public Criteria andStorenameIsNull() {
            addCriterion("storename is null");
            return (Criteria) this;
        }

        public Criteria andStorenameIsNotNull() {
            addCriterion("storename is not null");
            return (Criteria) this;
        }

        public Criteria andStorenameEqualTo(String value) {
            addCriterion("storename =", value, "storename");
            return (Criteria) this;
        }

        public Criteria andStorenameNotEqualTo(String value) {
            addCriterion("storename <>", value, "storename");
            return (Criteria) this;
        }

        public Criteria andStorenameGreaterThan(String value) {
            addCriterion("storename >", value, "storename");
            return (Criteria) this;
        }

        public Criteria andStorenameGreaterThanOrEqualTo(String value) {
            addCriterion("storename >=", value, "storename");
            return (Criteria) this;
        }

        public Criteria andStorenameLessThan(String value) {
            addCriterion("storename <", value, "storename");
            return (Criteria) this;
        }

        public Criteria andStorenameLessThanOrEqualTo(String value) {
            addCriterion("storename <=", value, "storename");
            return (Criteria) this;
        }

        public Criteria andStorenameLike(String value) {
            addCriterion("storename like", value, "storename");
            return (Criteria) this;
        }

        public Criteria andStorenameNotLike(String value) {
            addCriterion("storename not like", value, "storename");
            return (Criteria) this;
        }

        public Criteria andStorenameIn(List<String> values) {
            addCriterion("storename in", values, "storename");
            return (Criteria) this;
        }

        public Criteria andStorenameNotIn(List<String> values) {
            addCriterion("storename not in", values, "storename");
            return (Criteria) this;
        }

        public Criteria andStorenameBetween(String value1, String value2) {
            addCriterion("storename between", value1, value2, "storename");
            return (Criteria) this;
        }

        public Criteria andStorenameNotBetween(String value1, String value2) {
            addCriterion("storename not between", value1, value2, "storename");
            return (Criteria) this;
        }

        public Criteria andMenunameIsNull() {
            addCriterion("menuname is null");
            return (Criteria) this;
        }

        public Criteria andMenunameIsNotNull() {
            addCriterion("menuname is not null");
            return (Criteria) this;
        }

        public Criteria andMenunameEqualTo(String value) {
            addCriterion("menuname =", value, "menuname");
            return (Criteria) this;
        }

        public Criteria andMenunameNotEqualTo(String value) {
            addCriterion("menuname <>", value, "menuname");
            return (Criteria) this;
        }

        public Criteria andMenunameGreaterThan(String value) {
            addCriterion("menuname >", value, "menuname");
            return (Criteria) this;
        }

        public Criteria andMenunameGreaterThanOrEqualTo(String value) {
            addCriterion("menuname >=", value, "menuname");
            return (Criteria) this;
        }

        public Criteria andMenunameLessThan(String value) {
            addCriterion("menuname <", value, "menuname");
            return (Criteria) this;
        }

        public Criteria andMenunameLessThanOrEqualTo(String value) {
            addCriterion("menuname <=", value, "menuname");
            return (Criteria) this;
        }

        public Criteria andMenunameLike(String value) {
            addCriterion("menuname like", value, "menuname");
            return (Criteria) this;
        }

        public Criteria andMenunameNotLike(String value) {
            addCriterion("menuname not like", value, "menuname");
            return (Criteria) this;
        }

        public Criteria andMenunameIn(List<String> values) {
            addCriterion("menuname in", values, "menuname");
            return (Criteria) this;
        }

        public Criteria andMenunameNotIn(List<String> values) {
            addCriterion("menuname not in", values, "menuname");
            return (Criteria) this;
        }

        public Criteria andMenunameBetween(String value1, String value2) {
            addCriterion("menuname between", value1, value2, "menuname");
            return (Criteria) this;
        }

        public Criteria andMenunameNotBetween(String value1, String value2) {
            addCriterion("menuname not between", value1, value2, "menuname");
            return (Criteria) this;
        }

        public Criteria andTpriceIsNull() {
            addCriterion("tprice is null");
            return (Criteria) this;
        }

        public Criteria andTpriceIsNotNull() {
            addCriterion("tprice is not null");
            return (Criteria) this;
        }

        public Criteria andTpriceEqualTo(Integer value) {
            addCriterion("tprice =", value, "tprice");
            return (Criteria) this;
        }

        public Criteria andTpriceNotEqualTo(Integer value) {
            addCriterion("tprice <>", value, "tprice");
            return (Criteria) this;
        }

        public Criteria andTpriceGreaterThan(Integer value) {
            addCriterion("tprice >", value, "tprice");
            return (Criteria) this;
        }

        public Criteria andTpriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("tprice >=", value, "tprice");
            return (Criteria) this;
        }

        public Criteria andTpriceLessThan(Integer value) {
            addCriterion("tprice <", value, "tprice");
            return (Criteria) this;
        }

        public Criteria andTpriceLessThanOrEqualTo(Integer value) {
            addCriterion("tprice <=", value, "tprice");
            return (Criteria) this;
        }

        public Criteria andTpriceIn(List<Integer> values) {
            addCriterion("tprice in", values, "tprice");
            return (Criteria) this;
        }

        public Criteria andTpriceNotIn(List<Integer> values) {
            addCriterion("tprice not in", values, "tprice");
            return (Criteria) this;
        }

        public Criteria andTpriceBetween(Integer value1, Integer value2) {
            addCriterion("tprice between", value1, value2, "tprice");
            return (Criteria) this;
        }

        public Criteria andTpriceNotBetween(Integer value1, Integer value2) {
            addCriterion("tprice not between", value1, value2, "tprice");
            return (Criteria) this;
        }

        public Criteria andTsalespriceIsNull() {
            addCriterion("tsalesprice is null");
            return (Criteria) this;
        }

        public Criteria andTsalespriceIsNotNull() {
            addCriterion("tsalesprice is not null");
            return (Criteria) this;
        }

        public Criteria andTsalespriceEqualTo(Integer value) {
            addCriterion("tsalesprice =", value, "tsalesprice");
            return (Criteria) this;
        }

        public Criteria andTsalespriceNotEqualTo(Integer value) {
            addCriterion("tsalesprice <>", value, "tsalesprice");
            return (Criteria) this;
        }

        public Criteria andTsalespriceGreaterThan(Integer value) {
            addCriterion("tsalesprice >", value, "tsalesprice");
            return (Criteria) this;
        }

        public Criteria andTsalespriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("tsalesprice >=", value, "tsalesprice");
            return (Criteria) this;
        }

        public Criteria andTsalespriceLessThan(Integer value) {
            addCriterion("tsalesprice <", value, "tsalesprice");
            return (Criteria) this;
        }

        public Criteria andTsalespriceLessThanOrEqualTo(Integer value) {
            addCriterion("tsalesprice <=", value, "tsalesprice");
            return (Criteria) this;
        }

        public Criteria andTsalespriceIn(List<Integer> values) {
            addCriterion("tsalesprice in", values, "tsalesprice");
            return (Criteria) this;
        }

        public Criteria andTsalespriceNotIn(List<Integer> values) {
            addCriterion("tsalesprice not in", values, "tsalesprice");
            return (Criteria) this;
        }

        public Criteria andTsalespriceBetween(Integer value1, Integer value2) {
            addCriterion("tsalesprice between", value1, value2, "tsalesprice");
            return (Criteria) this;
        }

        public Criteria andTsalespriceNotBetween(Integer value1, Integer value2) {
            addCriterion("tsalesprice not between", value1, value2, "tsalesprice");
            return (Criteria) this;
        }

        public Criteria andTpersonalpayIsNull() {
            addCriterion("tpersonalpay is null");
            return (Criteria) this;
        }

        public Criteria andTpersonalpayIsNotNull() {
            addCriterion("tpersonalpay is not null");
            return (Criteria) this;
        }

        public Criteria andTpersonalpayEqualTo(Integer value) {
            addCriterion("tpersonalpay =", value, "tpersonalpay");
            return (Criteria) this;
        }

        public Criteria andTpersonalpayNotEqualTo(Integer value) {
            addCriterion("tpersonalpay <>", value, "tpersonalpay");
            return (Criteria) this;
        }

        public Criteria andTpersonalpayGreaterThan(Integer value) {
            addCriterion("tpersonalpay >", value, "tpersonalpay");
            return (Criteria) this;
        }

        public Criteria andTpersonalpayGreaterThanOrEqualTo(Integer value) {
            addCriterion("tpersonalpay >=", value, "tpersonalpay");
            return (Criteria) this;
        }

        public Criteria andTpersonalpayLessThan(Integer value) {
            addCriterion("tpersonalpay <", value, "tpersonalpay");
            return (Criteria) this;
        }

        public Criteria andTpersonalpayLessThanOrEqualTo(Integer value) {
            addCriterion("tpersonalpay <=", value, "tpersonalpay");
            return (Criteria) this;
        }

        public Criteria andTpersonalpayIn(List<Integer> values) {
            addCriterion("tpersonalpay in", values, "tpersonalpay");
            return (Criteria) this;
        }

        public Criteria andTpersonalpayNotIn(List<Integer> values) {
            addCriterion("tpersonalpay not in", values, "tpersonalpay");
            return (Criteria) this;
        }

        public Criteria andTpersonalpayBetween(Integer value1, Integer value2) {
            addCriterion("tpersonalpay between", value1, value2, "tpersonalpay");
            return (Criteria) this;
        }

        public Criteria andTpersonalpayNotBetween(Integer value1, Integer value2) {
            addCriterion("tpersonalpay not between", value1, value2, "tpersonalpay");
            return (Criteria) this;
        }

        public Criteria andTsupplypriceIsNull() {
            addCriterion("tsupplyprice is null");
            return (Criteria) this;
        }

        public Criteria andTsupplypriceIsNotNull() {
            addCriterion("tsupplyprice is not null");
            return (Criteria) this;
        }

        public Criteria andTsupplypriceEqualTo(Integer value) {
            addCriterion("tsupplyprice =", value, "tsupplyprice");
            return (Criteria) this;
        }

        public Criteria andTsupplypriceNotEqualTo(Integer value) {
            addCriterion("tsupplyprice <>", value, "tsupplyprice");
            return (Criteria) this;
        }

        public Criteria andTsupplypriceGreaterThan(Integer value) {
            addCriterion("tsupplyprice >", value, "tsupplyprice");
            return (Criteria) this;
        }

        public Criteria andTsupplypriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("tsupplyprice >=", value, "tsupplyprice");
            return (Criteria) this;
        }

        public Criteria andTsupplypriceLessThan(Integer value) {
            addCriterion("tsupplyprice <", value, "tsupplyprice");
            return (Criteria) this;
        }

        public Criteria andTsupplypriceLessThanOrEqualTo(Integer value) {
            addCriterion("tsupplyprice <=", value, "tsupplyprice");
            return (Criteria) this;
        }

        public Criteria andTsupplypriceIn(List<Integer> values) {
            addCriterion("tsupplyprice in", values, "tsupplyprice");
            return (Criteria) this;
        }

        public Criteria andTsupplypriceNotIn(List<Integer> values) {
            addCriterion("tsupplyprice not in", values, "tsupplyprice");
            return (Criteria) this;
        }

        public Criteria andTsupplypriceBetween(Integer value1, Integer value2) {
            addCriterion("tsupplyprice between", value1, value2, "tsupplyprice");
            return (Criteria) this;
        }

        public Criteria andTsupplypriceNotBetween(Integer value1, Integer value2) {
            addCriterion("tsupplyprice not between", value1, value2, "tsupplyprice");
            return (Criteria) this;
        }

        public Criteria andMemberIsNull() {
            addCriterion("member is null");
            return (Criteria) this;
        }

        public Criteria andMemberIsNotNull() {
            addCriterion("member is not null");
            return (Criteria) this;
        }

        public Criteria andMemberEqualTo(Integer value) {
            addCriterion("member =", value, "member");
            return (Criteria) this;
        }

        public Criteria andMemberNotEqualTo(Integer value) {
            addCriterion("member <>", value, "member");
            return (Criteria) this;
        }

        public Criteria andMemberGreaterThan(Integer value) {
            addCriterion("member >", value, "member");
            return (Criteria) this;
        }

        public Criteria andMemberGreaterThanOrEqualTo(Integer value) {
            addCriterion("member >=", value, "member");
            return (Criteria) this;
        }

        public Criteria andMemberLessThan(Integer value) {
            addCriterion("member <", value, "member");
            return (Criteria) this;
        }

        public Criteria andMemberLessThanOrEqualTo(Integer value) {
            addCriterion("member <=", value, "member");
            return (Criteria) this;
        }

        public Criteria andMemberIn(List<Integer> values) {
            addCriterion("member in", values, "member");
            return (Criteria) this;
        }

        public Criteria andMemberNotIn(List<Integer> values) {
            addCriterion("member not in", values, "member");
            return (Criteria) this;
        }

        public Criteria andMemberBetween(Integer value1, Integer value2) {
            addCriterion("member between", value1, value2, "member");
            return (Criteria) this;
        }

        public Criteria andMemberNotBetween(Integer value1, Integer value2) {
            addCriterion("member not between", value1, value2, "member");
            return (Criteria) this;
        }

        public Criteria andMenunumIsNull() {
            addCriterion("menunum is null");
            return (Criteria) this;
        }

        public Criteria andMenunumIsNotNull() {
            addCriterion("menunum is not null");
            return (Criteria) this;
        }

        public Criteria andMenunumEqualTo(Integer value) {
            addCriterion("menunum =", value, "menunum");
            return (Criteria) this;
        }

        public Criteria andMenunumNotEqualTo(Integer value) {
            addCriterion("menunum <>", value, "menunum");
            return (Criteria) this;
        }

        public Criteria andMenunumGreaterThan(Integer value) {
            addCriterion("menunum >", value, "menunum");
            return (Criteria) this;
        }

        public Criteria andMenunumGreaterThanOrEqualTo(Integer value) {
            addCriterion("menunum >=", value, "menunum");
            return (Criteria) this;
        }

        public Criteria andMenunumLessThan(Integer value) {
            addCriterion("menunum <", value, "menunum");
            return (Criteria) this;
        }

        public Criteria andMenunumLessThanOrEqualTo(Integer value) {
            addCriterion("menunum <=", value, "menunum");
            return (Criteria) this;
        }

        public Criteria andMenunumIn(List<Integer> values) {
            addCriterion("menunum in", values, "menunum");
            return (Criteria) this;
        }

        public Criteria andMenunumNotIn(List<Integer> values) {
            addCriterion("menunum not in", values, "menunum");
            return (Criteria) this;
        }

        public Criteria andMenunumBetween(Integer value1, Integer value2) {
            addCriterion("menunum between", value1, value2, "menunum");
            return (Criteria) this;
        }

        public Criteria andMenunumNotBetween(Integer value1, Integer value2) {
            addCriterion("menunum not between", value1, value2, "menunum");
            return (Criteria) this;
        }

        public Criteria andCoupontypeIsNull() {
            addCriterion("coupontype is null");
            return (Criteria) this;
        }

        public Criteria andCoupontypeIsNotNull() {
            addCriterion("coupontype is not null");
            return (Criteria) this;
        }

        public Criteria andCoupontypeEqualTo(String value) {
            addCriterion("coupontype =", value, "coupontype");
            return (Criteria) this;
        }

        public Criteria andCoupontypeNotEqualTo(String value) {
            addCriterion("coupontype <>", value, "coupontype");
            return (Criteria) this;
        }

        public Criteria andCoupontypeGreaterThan(String value) {
            addCriterion("coupontype >", value, "coupontype");
            return (Criteria) this;
        }

        public Criteria andCoupontypeGreaterThanOrEqualTo(String value) {
            addCriterion("coupontype >=", value, "coupontype");
            return (Criteria) this;
        }

        public Criteria andCoupontypeLessThan(String value) {
            addCriterion("coupontype <", value, "coupontype");
            return (Criteria) this;
        }

        public Criteria andCoupontypeLessThanOrEqualTo(String value) {
            addCriterion("coupontype <=", value, "coupontype");
            return (Criteria) this;
        }

        public Criteria andCoupontypeLike(String value) {
            addCriterion("coupontype like", value, "coupontype");
            return (Criteria) this;
        }

        public Criteria andCoupontypeNotLike(String value) {
            addCriterion("coupontype not like", value, "coupontype");
            return (Criteria) this;
        }

        public Criteria andCoupontypeIn(List<String> values) {
            addCriterion("coupontype in", values, "coupontype");
            return (Criteria) this;
        }

        public Criteria andCoupontypeNotIn(List<String> values) {
            addCriterion("coupontype not in", values, "coupontype");
            return (Criteria) this;
        }

        public Criteria andCoupontypeBetween(String value1, String value2) {
            addCriterion("coupontype between", value1, value2, "coupontype");
            return (Criteria) this;
        }

        public Criteria andCoupontypeNotBetween(String value1, String value2) {
            addCriterion("coupontype not between", value1, value2, "coupontype");
            return (Criteria) this;
        }

        public Criteria andVendertypeIsNull() {
            addCriterion("vendertype is null");
            return (Criteria) this;
        }

        public Criteria andVendertypeIsNotNull() {
            addCriterion("vendertype is not null");
            return (Criteria) this;
        }

        public Criteria andVendertypeEqualTo(String value) {
            addCriterion("vendertype =", value, "vendertype");
            return (Criteria) this;
        }

        public Criteria andVendertypeNotEqualTo(String value) {
            addCriterion("vendertype <>", value, "vendertype");
            return (Criteria) this;
        }

        public Criteria andVendertypeGreaterThan(String value) {
            addCriterion("vendertype >", value, "vendertype");
            return (Criteria) this;
        }

        public Criteria andVendertypeGreaterThanOrEqualTo(String value) {
            addCriterion("vendertype >=", value, "vendertype");
            return (Criteria) this;
        }

        public Criteria andVendertypeLessThan(String value) {
            addCriterion("vendertype <", value, "vendertype");
            return (Criteria) this;
        }

        public Criteria andVendertypeLessThanOrEqualTo(String value) {
            addCriterion("vendertype <=", value, "vendertype");
            return (Criteria) this;
        }

        public Criteria andVendertypeLike(String value) {
            addCriterion("vendertype like", value, "vendertype");
            return (Criteria) this;
        }

        public Criteria andVendertypeNotLike(String value) {
            addCriterion("vendertype not like", value, "vendertype");
            return (Criteria) this;
        }

        public Criteria andVendertypeIn(List<String> values) {
            addCriterion("vendertype in", values, "vendertype");
            return (Criteria) this;
        }

        public Criteria andVendertypeNotIn(List<String> values) {
            addCriterion("vendertype not in", values, "vendertype");
            return (Criteria) this;
        }

        public Criteria andVendertypeBetween(String value1, String value2) {
            addCriterion("vendertype between", value1, value2, "vendertype");
            return (Criteria) this;
        }

        public Criteria andVendertypeNotBetween(String value1, String value2) {
            addCriterion("vendertype not between", value1, value2, "vendertype");
            return (Criteria) this;
        }

        public Criteria andUsedateIsNull() {
            addCriterion("usedate is null");
            return (Criteria) this;
        }

        public Criteria andUsedateIsNotNull() {
            addCriterion("usedate is not null");
            return (Criteria) this;
        }

        public Criteria andUsedateEqualTo(Date value) {
            addCriterion("usedate =", value, "usedate");
            return (Criteria) this;
        }

        public Criteria andUsedateNotEqualTo(Date value) {
            addCriterion("usedate <>", value, "usedate");
            return (Criteria) this;
        }

        public Criteria andUsedateGreaterThan(Date value) {
            addCriterion("usedate >", value, "usedate");
            return (Criteria) this;
        }

        public Criteria andUsedateGreaterThanOrEqualTo(Date value) {
            addCriterion("usedate >=", value, "usedate");
            return (Criteria) this;
        }

        public Criteria andUsedateLessThan(Date value) {
            addCriterion("usedate <", value, "usedate");
            return (Criteria) this;
        }

        public Criteria andUsedateLessThanOrEqualTo(Date value) {
            addCriterion("usedate <=", value, "usedate");
            return (Criteria) this;
        }

        public Criteria andUsedateIn(List<Date> values) {
            addCriterion("usedate in", values, "usedate");
            return (Criteria) this;
        }

        public Criteria andUsedateNotIn(List<Date> values) {
            addCriterion("usedate not in", values, "usedate");
            return (Criteria) this;
        }

        public Criteria andUsedateBetween(Date value1, Date value2) {
            addCriterion("usedate between", value1, value2, "usedate");
            return (Criteria) this;
        }

        public Criteria andUsedateNotBetween(Date value1, Date value2) {
            addCriterion("usedate not between", value1, value2, "usedate");
            return (Criteria) this;
        }

        public Criteria andRegdateIsNull() {
            addCriterion("regdate is null");
            return (Criteria) this;
        }

        public Criteria andRegdateIsNotNull() {
            addCriterion("regdate is not null");
            return (Criteria) this;
        }

        public Criteria andRegdateEqualTo(Date value) {
            addCriterion("regdate =", value, "regdate");
            return (Criteria) this;
        }

        public Criteria andRegdateNotEqualTo(Date value) {
            addCriterion("regdate <>", value, "regdate");
            return (Criteria) this;
        }

        public Criteria andRegdateGreaterThan(Date value) {
            addCriterion("regdate >", value, "regdate");
            return (Criteria) this;
        }

        public Criteria andRegdateGreaterThanOrEqualTo(Date value) {
            addCriterion("regdate >=", value, "regdate");
            return (Criteria) this;
        }

        public Criteria andRegdateLessThan(Date value) {
            addCriterion("regdate <", value, "regdate");
            return (Criteria) this;
        }

        public Criteria andRegdateLessThanOrEqualTo(Date value) {
            addCriterion("regdate <=", value, "regdate");
            return (Criteria) this;
        }

        public Criteria andRegdateIn(List<Date> values) {
            addCriterion("regdate in", values, "regdate");
            return (Criteria) this;
        }

        public Criteria andRegdateNotIn(List<Date> values) {
            addCriterion("regdate not in", values, "regdate");
            return (Criteria) this;
        }

        public Criteria andRegdateBetween(Date value1, Date value2) {
            addCriterion("regdate between", value1, value2, "regdate");
            return (Criteria) this;
        }

        public Criteria andRegdateNotBetween(Date value1, Date value2) {
            addCriterion("regdate not between", value1, value2, "regdate");
            return (Criteria) this;
        }

        public Criteria andCalcidIsNull() {
            addCriterion("calcid is null");
            return (Criteria) this;
        }

        public Criteria andCalcidIsNotNull() {
            addCriterion("calcid is not null");
            return (Criteria) this;
        }

        public Criteria andCalcidEqualTo(String value) {
            addCriterion("calcid =", value, "calcid");
            return (Criteria) this;
        }

        public Criteria andCalcidNotEqualTo(String value) {
            addCriterion("calcid <>", value, "calcid");
            return (Criteria) this;
        }

        public Criteria andCalcidGreaterThan(String value) {
            addCriterion("calcid >", value, "calcid");
            return (Criteria) this;
        }

        public Criteria andCalcidGreaterThanOrEqualTo(String value) {
            addCriterion("calcid >=", value, "calcid");
            return (Criteria) this;
        }

        public Criteria andCalcidLessThan(String value) {
            addCriterion("calcid <", value, "calcid");
            return (Criteria) this;
        }

        public Criteria andCalcidLessThanOrEqualTo(String value) {
            addCriterion("calcid <=", value, "calcid");
            return (Criteria) this;
        }

        public Criteria andCalcidLike(String value) {
            addCriterion("calcid like", value, "calcid");
            return (Criteria) this;
        }

        public Criteria andCalcidNotLike(String value) {
            addCriterion("calcid not like", value, "calcid");
            return (Criteria) this;
        }

        public Criteria andCalcidIn(List<String> values) {
            addCriterion("calcid in", values, "calcid");
            return (Criteria) this;
        }

        public Criteria andCalcidNotIn(List<String> values) {
            addCriterion("calcid not in", values, "calcid");
            return (Criteria) this;
        }

        public Criteria andCalcidBetween(String value1, String value2) {
            addCriterion("calcid between", value1, value2, "calcid");
            return (Criteria) this;
        }

        public Criteria andCalcidNotBetween(String value1, String value2) {
            addCriterion("calcid not between", value1, value2, "calcid");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCancelcauseIsNull() {
            addCriterion("cancelcause is null");
            return (Criteria) this;
        }

        public Criteria andCancelcauseIsNotNull() {
            addCriterion("cancelcause is not null");
            return (Criteria) this;
        }

        public Criteria andCancelcauseEqualTo(String value) {
            addCriterion("cancelcause =", value, "cancelcause");
            return (Criteria) this;
        }

        public Criteria andCancelcauseNotEqualTo(String value) {
            addCriterion("cancelcause <>", value, "cancelcause");
            return (Criteria) this;
        }

        public Criteria andCancelcauseGreaterThan(String value) {
            addCriterion("cancelcause >", value, "cancelcause");
            return (Criteria) this;
        }

        public Criteria andCancelcauseGreaterThanOrEqualTo(String value) {
            addCriterion("cancelcause >=", value, "cancelcause");
            return (Criteria) this;
        }

        public Criteria andCancelcauseLessThan(String value) {
            addCriterion("cancelcause <", value, "cancelcause");
            return (Criteria) this;
        }

        public Criteria andCancelcauseLessThanOrEqualTo(String value) {
            addCriterion("cancelcause <=", value, "cancelcause");
            return (Criteria) this;
        }

        public Criteria andCancelcauseLike(String value) {
            addCriterion("cancelcause like", value, "cancelcause");
            return (Criteria) this;
        }

        public Criteria andCancelcauseNotLike(String value) {
            addCriterion("cancelcause not like", value, "cancelcause");
            return (Criteria) this;
        }

        public Criteria andCancelcauseIn(List<String> values) {
            addCriterion("cancelcause in", values, "cancelcause");
            return (Criteria) this;
        }

        public Criteria andCancelcauseNotIn(List<String> values) {
            addCriterion("cancelcause not in", values, "cancelcause");
            return (Criteria) this;
        }

        public Criteria andCancelcauseBetween(String value1, String value2) {
            addCriterion("cancelcause between", value1, value2, "cancelcause");
            return (Criteria) this;
        }

        public Criteria andCancelcauseNotBetween(String value1, String value2) {
            addCriterion("cancelcause not between", value1, value2, "cancelcause");
            return (Criteria) this;
        }

        public Criteria andCanceldateIsNull() {
            addCriterion("canceldate is null");
            return (Criteria) this;
        }

        public Criteria andCanceldateIsNotNull() {
            addCriterion("canceldate is not null");
            return (Criteria) this;
        }

        public Criteria andCanceldateEqualTo(Date value) {
            addCriterion("canceldate =", value, "canceldate");
            return (Criteria) this;
        }

        public Criteria andCanceldateNotEqualTo(Date value) {
            addCriterion("canceldate <>", value, "canceldate");
            return (Criteria) this;
        }

        public Criteria andCanceldateGreaterThan(Date value) {
            addCriterion("canceldate >", value, "canceldate");
            return (Criteria) this;
        }

        public Criteria andCanceldateGreaterThanOrEqualTo(Date value) {
            addCriterion("canceldate >=", value, "canceldate");
            return (Criteria) this;
        }

        public Criteria andCanceldateLessThan(Date value) {
            addCriterion("canceldate <", value, "canceldate");
            return (Criteria) this;
        }

        public Criteria andCanceldateLessThanOrEqualTo(Date value) {
            addCriterion("canceldate <=", value, "canceldate");
            return (Criteria) this;
        }

        public Criteria andCanceldateIn(List<Date> values) {
            addCriterion("canceldate in", values, "canceldate");
            return (Criteria) this;
        }

        public Criteria andCanceldateNotIn(List<Date> values) {
            addCriterion("canceldate not in", values, "canceldate");
            return (Criteria) this;
        }

        public Criteria andCanceldateBetween(Date value1, Date value2) {
            addCriterion("canceldate between", value1, value2, "canceldate");
            return (Criteria) this;
        }

        public Criteria andCanceldateNotBetween(Date value1, Date value2) {
            addCriterion("canceldate not between", value1, value2, "canceldate");
            return (Criteria) this;
        }

        public Criteria andCapturedateIsNull() {
            addCriterion("capturedate is null");
            return (Criteria) this;
        }

        public Criteria andCapturedateIsNotNull() {
            addCriterion("capturedate is not null");
            return (Criteria) this;
        }

        public Criteria andCapturedateEqualTo(Date value) {
            addCriterion("capturedate =", value, "capturedate");
            return (Criteria) this;
        }

        public Criteria andCapturedateNotEqualTo(Date value) {
            addCriterion("capturedate <>", value, "capturedate");
            return (Criteria) this;
        }

        public Criteria andCapturedateGreaterThan(Date value) {
            addCriterion("capturedate >", value, "capturedate");
            return (Criteria) this;
        }

        public Criteria andCapturedateGreaterThanOrEqualTo(Date value) {
            addCriterion("capturedate >=", value, "capturedate");
            return (Criteria) this;
        }

        public Criteria andCapturedateLessThan(Date value) {
            addCriterion("capturedate <", value, "capturedate");
            return (Criteria) this;
        }

        public Criteria andCapturedateLessThanOrEqualTo(Date value) {
            addCriterion("capturedate <=", value, "capturedate");
            return (Criteria) this;
        }

        public Criteria andCapturedateIn(List<Date> values) {
            addCriterion("capturedate in", values, "capturedate");
            return (Criteria) this;
        }

        public Criteria andCapturedateNotIn(List<Date> values) {
            addCriterion("capturedate not in", values, "capturedate");
            return (Criteria) this;
        }

        public Criteria andCapturedateBetween(Date value1, Date value2) {
            addCriterion("capturedate between", value1, value2, "capturedate");
            return (Criteria) this;
        }

        public Criteria andCapturedateNotBetween(Date value1, Date value2) {
            addCriterion("capturedate not between", value1, value2, "capturedate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}