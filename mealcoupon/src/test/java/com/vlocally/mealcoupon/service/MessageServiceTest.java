package com.vlocally.mealcoupon.service;


import java.io.IOException;
import java.net.URI;
import java.util.Objects;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.vlocally.mealcoupon.constant.InitProperties;
import com.vlocally.mealcoupon.service.remoteapi.common.entity.RemoteHeaderDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vlocally.mealcoupon.vo.MessageDto;
import com.vlocally.mealcoupon.vo.MessageDto.SmsResponse;
import org.json.simple.JSONObject;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MessageServiceTest {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final String localUrl = "http://127.0.0.1:10080/sms";
    private final String devUrl = "https://dev-message.mealc.co.kr";
    private final String smsPath = "/sms";


    @Test
    public void sendSmsApiCall() {
        InitProperties initProp = new InitProperties();
        String url = devUrl + smsPath;
        MessageDto.SmsResponse response = null;

        //### Header & Body Setting
        MessageDto.SmsRequest smsRequest = sampleData();
        HttpHeaders httpHeaders = getMessageHeaders();
        HttpEntity<Object> httpEntity = new HttpEntity<>(smsRequest, httpHeaders);
        ResponseEntity<MessageDto.SmsResponse> responseEntity = null;


        URI uri = UriComponentsBuilder.fromUriString(url).build().toUri();
        //#### TimeOut & ConnectTimeOut Setting
        HttpComponentsClientHttpRequestFactory httpFactory = new HttpComponentsClientHttpRequestFactory();
        httpFactory.setReadTimeout(5000);
        httpFactory.setConnectTimeout(5000);

        try {
            // ReponseEntity Type  responseEntity = new RestTemplate(httpFactory).exchange(uri, HttpMethod.POST, httpEntity, MessageDto.SmsResponse.class);
            response = new RestTemplate(httpFactory).postForObject(uri, httpEntity, SmsResponse.class);
        } catch (HttpServerErrorException | HttpClientErrorException e) {
            logger.error("문자 메시지 전송 오류 : {}", e.getMessage(), e);
        }

        logger.info("#########################################");
        logger.info(response.toString());
        logger.info("#########################################");
    }


    /**
     * Header 정보 셋팅
     * @return
     */
    public HttpHeaders getMessageHeaders() {
        JSONObject header = new JSONObject();
        HttpHeaders httpHeaders = new HttpHeaders();

        header.put("client", "Settlement");
        header.put("userId", "backoffice-service");
        header.put("version", "1.0");
        httpHeaders.set("Content-type", "application/json");

        try {
            httpHeaders.set("X-Message", Objects.toString(new ObjectMapper().writeValueAsString(header), ""));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        return httpHeaders;
    }


    /**
     * 샘플 데이터
     * @return
     */
    public MessageDto.SmsRequest sampleData() {
        MessageDto.SmsRequest rtnDto = new MessageDto.SmsRequest();
        rtnDto.setTitle("TEST 문자 전송");
        rtnDto.setContent("Test Message 입니다.");
        rtnDto.setReceiver("01084143320");
        rtnDto.setType("SMS");
        rtnDto.setUserId("backoffice-service");
        rtnDto.setUseType("AS");
        rtnDto.setUsecause("정산 식당 대행발행 동의");

        return rtnDto;
    }

}
