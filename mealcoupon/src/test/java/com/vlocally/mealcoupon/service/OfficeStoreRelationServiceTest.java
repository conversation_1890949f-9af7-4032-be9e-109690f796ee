package com.vlocally.mealcoupon.service;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import static junit.framework.TestCase.assertTrue;

import com.test.ApplicationContextTest;
import com.vlocally.mealcoupon.constant.db.UserGrade;
import com.vlocally.mealcoupon.constant.db.UserStatus;
import com.vlocally.mealcoupon.mapper.master.OfficeStoreRelataionMapper;
import com.vlocally.mealcoupon.vo.AdminUser;
import com.vlocally.mealcoupon.vo.OfficeStoreRelation;
import org.junit.Test;

public class OfficeStoreRelationServiceTest extends ApplicationContextTest {

    @Autowired
    private OfficeStoreRelationService officeStoreRelationService;

    @Autowired
    private OfficeStoreRelataionMapper officeStoreRelataionMapper;


    @Test
    @Transactional(value = "transactionManagerMaster")
    @Rollback(value = true)
    public void updateExposureStateTest() {
        // given
        AdminUser testAdminUser = testAdminUser();
        // 개발 DB 데이터 -> 개발 DB 데이터의 의존이 높음
        String storeId = "BC1B178F-A71A-E41B-B36D-566526D3D97D";

        // when
        officeStoreRelationService.updateExposureState(testAdminUser, storeId, true);
        List<OfficeStoreRelation> officeStoreRelations = officeStoreRelataionMapper.selectAllOfficeStoreRelationByStoreId(
                storeId);

        // then
        assertTrue(officeStoreRelations.stream().anyMatch(item -> item.getIsCoupon().equals(true)));
    }

    private AdminUser testAdminUser() {
        AdminUser adminUser = new AdminUser();
        adminUser.setAdminId("9C3DDB90-41D9-7824-2475-EB16B6268BA6");
        adminUser.setSignId("테스트");
        adminUser.setName("테스터");
        adminUser.setPhone("01000000000");
        adminUser.setEmail("<EMAIL>");
        adminUser.setDivision("제품본부");
        adminUser.setIsPrivacy(0);
        adminUser.setIsExternal(0);
        adminUser.setStatus(UserStatus.ACTIVE);
        adminUser.setGrade(UserGrade.ADMIN);
        return adminUser;

    }
}