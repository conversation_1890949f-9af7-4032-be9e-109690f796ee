package com.test;

import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertThat;

import com.vlocally.mealcoupon.constant.Errors;
import com.vlocally.mealcoupon.exception.VlocallyException;

public class CommonTool {
	
	public static void checkException(VlocallyException e, Errors ee){
		assertThat(e.getErrorNum(), is(ee.errorNum));
		assertThat(e.getMessage(), is(ee.msg));
	}
	
	public static void checkException(VlocallyException e, int errorNum, String message){
		assertThat(e.getErrorNum(), is(errorNum));
		assertThat(e.getMessage(), is(message));
	}

	public static void checkExceptionOnlyNum(VlocallyException e,Errors ee) {
		assertThat(e.getErrorNum(), is(ee.errorNum));
	}
}
