package com.test;

import java.util.GregorianCalendar;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import com.vlocally.mealcoupon.service.ExcelCustomService;
import org.junit.Test;

public class TestAnything extends ApplicationContextTest{

	@Autowired private ExcelCustomService excelService;

	@Test
	@Transactional
    @Rollback
	public void test() throws Exception {
	    /**
         * 2021 02 26 ~ 2021 03 25 정산
         * >> target: 2021, 1, 26
         * >> end: 2021, 2, 26
         * */
	    //       일진
        String comid = "74F37CCF-22B4-E7A3-49B5-8B4299C963EE";
        GregorianCalendar target = new GregorianCalendar(2021, 7, 26);
        GregorianCalendar end = new GregorianCalendar(2021, 8, 26);

        this.excelService.getExcelforIljinNew(target.getTime(), end.getTime(), comid);
    }
}
