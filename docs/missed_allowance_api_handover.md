### **일일식대 미지급 수동 지급 API 인수인계 문서**

**작성일:** 2025-06-26
**API Endpoint:** `POST /account/v1/policy/missed-allowance`

### 1. 개요 (Overview)

본 API는 시스템 장애나 데이터 누락 등의 이유로 **일일식대가 자동으로 지급되지 않은 사용자**에 대해, 수동으로 식대를 즉시 지급하기 위해 사용됩니다.

주요 특징은 다음과 같습니다.
*   로그인한 사용자를 대상으로 본인의 미지급 식대를 직접 요청하여 지급받습니다.
*   Redis 기반의 **분산락(Distributed Lock)**을 적용하여 아래와 같은 동시성 문제를 방지합니다.
    *   자동 일일식대 지급 스케줄러(`runDayTask`)와의 충돌 방지
    *   동일 사용자에 대한 중복 API 요청 방지

### 2. 관련 핵심 컴포넌트

| 클래스명                     | 역할                                                                                             |
| ---------------------------- | ------------------------------------------------------------------------------------------------ |
| `PolicyController`           | API 요청을 수신하고 `PolicyPointService`에 처리를 위임하는 컨트롤러.                               |
| `PolicyPointService`         | **핵심 비즈니스 로직**을 담당. 사용자/정책 유효성 검증, 분산락 제어, 포인트 지급 프로세스 실행.    |
| `MissedMealPointProcessor`   | 실제 포인트 지급 계산 및 DB 반영을 처리. **독립적인 트랜잭션**으로 동작하여 데이터 정합성을 보장. |
| `DistributedLockService`     | `RedisLockRegistry`를 사용하여 분산 환경에서 Lock을 획득하고 해제하는 서비스.                      |
| `PolicyService`              | `runDayTask` 메서드를 통해 **자동 식대 지급 로직**을 수행.                                         |
| `MainScheduler`              | `runDayTask`를 주기적으로 실행시키는 스케줄러.                                                     |

### 3. 처리 흐름 (Process Flow)

#### 가. 컴포넌트 상호작용
```mermaid
graph TD
    subgraph "Client"
        A[사용자]
    end

    subgraph "Payment Service"
        B[PolicyController]
        C[PolicyPointService]
        D[DistributedLockService]
        E[MissedMealPointProcessor]
        F[DB]
        G[Redis]
    end

    A -- 1. 식대 지급 요청 --> B
    B -- 2. 지급 처리 위임 --> C
    C -- 3. 사용자/정책 검증 --> F
    C -- 4. 분산락 획득 요청 --> D
    D -- 5. Lock 획득 (SETNX) --> G
    C -- 6. 포인트 지급 처리 --> E
    E -- 7. 포인트 지급 (REQUIRES_NEW TX) --> F
    D -- 8. Lock 해제 (DEL) --> G
    B -- 9. 처리 결과 응답 --> A
```

#### 나. 시퀀스 다이어그램 (Sequence Diagram)
```mermaid
sequenceDiagram
    actor User
    participant PC as PolicyController
    participant PPS as PolicyPointService
    participant DLS as DistributedLockService
    participant Redis
    participant MMP as MissedMealPointProcessor
    participant DB

    User->>+PC: POST /account/v1/policy/missed-allowance
    PC->>+PPS: provideMissedDailyMealAllowance(userId)
    PPS->>PPS: 1. validateUserAndGetAvailablePolicies(userId)
    PPS->>DB: 사용자, 그룹, 정책 정보 조회
    DB-->>PPS: 정보 반환

    loop 각 일일 식대 정책(MealPolicy)에 대해
        PPS->>+DLS: executeWithLock(lockKey, task)
        DLS->>+Redis: lock.tryLock()
        Redis-->>-DLS: Lock 획득 성공
        
        Note right of DLS: 락 획득 후, 지급 로직 실행
        DLS->>+PPS: (Run Task) 포인트 지급 로직 호출
        PPS->>DB: 오늘 이미 지급되었는지 확인
        alt 미지급 상태인 경우
            PPS->>+MMP: processPoints(task)
            Note right of MMP: @Transactional(REQUIRES_NEW)
            MMP->>+DB: 포인트 계산 및 지급 처리
            DB-->>-MMP: 처리 완료
            MMP-->>-PPS: 성공
        else 이미 지급된 경우
             PPS-->>-DLS: 처리 종료
        end
        
        DLS->>+Redis: lock.unlock()
        Redis-->>-DLS: Lock 해제 성공
        DLS-->>-PPS: 락 해제 및 작업 종료
    end
    
    PPS-->>-PC: 모든 처리 완료
    PC-->>-User: 200 OK
```

### 4. 핵심 로직 상세

#### 가. 분산락 (Distributed Lock)

*   **목적**: `runDayTask` 스케줄러와 수동 지급 API 간의 데이터 경합(Race Condition)을 방지합니다.
*   **구현**: `DistributedLockService`를 통해 구현되며, 내부적으로 `RedisLockRegistry`를 사용합니다.
*   **Lock Key (예시)**: `USER-DAILY-ALLOWANCE:{userId}:{policyId}` 와 같이 사용자 ID와 정책 ID를 조합한 고유 키를 사용하여 특정 사용자의 특정 정책에 대한 작업을 격리합니다.
*   **동작**:
    1.  작업 실행 전 `tryLock()`으로 10초 타임아웃을 두고 락 획득을 시도합니다.
    2.  락 획득에 실패하면 `GENERAL_LOCK_ACQUISITION_FAILED` 예외를 발생시켜 중복 실행을 막습니다.
    3.  **[중요] Redis 연결 실패 시**: Redis 장애 상황에서도 서비스 가용성을 확보하기 위해, 락을 획득하지 않고 작업을 그대로 실행하는 Fallback 로직이 포함되어 있습니다. 이 경우 드물게 데이터 불일치가 발생할 수 있습니다.

#### 나. 데이터베이스 트랜잭션 (DB Transaction)

*   `PolicyPointService`의 주 메서드는 대부분 조회(`@Transactional(readOnly = true)`)로 동작합니다.
*   실제 DB에 데이터를 쓰는 `MissedMealPointProcessor.processPoints` 메서드는 **`@Transactional(propagation = Propagation.REQUIRES_NEW)`** 로 선언되어 있습니다.
    *   **이유**: 포인트 지급이라는 핵심 작업을 호출부의 트랜잭션과 완전히 분리하기 위함입니다. 이를 통해 `PolicyPointService`에서 다른 정책 처리 중 예외가 발생하여 롤백되더라도, 이미 성공한 `processPoints`의 트랜잭션은 커밋된 상태로 유지되어 데이터 정합성을 보장합니다.
