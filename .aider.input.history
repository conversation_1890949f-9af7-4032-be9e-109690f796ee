
# 2025-05-13 13:31:21.409789
+n

# 2025-05-13 13:31:54.306322
+/companion start

# 2025-05-13 13:31:56.854905
+Ezwel 이라는 연동사로부터 매시간마다 전체 베스트 상품과 배너정보를 받고있어.
+항상 전체데이터가 전달되며 전달된 데이터에 유니크값은 없기에 기존 등록된 데이터를 전부 삭제하고 새롭게 등록하고 있어.
+EzwelProductHistory 에 매 시간마다 전체삭제 기록과 새롭게 전체등록 기록을 적재하니 로그가 너무 많이 누적되고 통계 및 추적이 힘들어.
+EzwelProduct 는 현재 로직을 그대로 유지하고,
+기존과 모든값이 동일하게 전달된 데이터는 History를 추가적으로 적재하지 않게 수정해줘.

# 2025-05-13 13:33:34.003711
+n

# 2025-05-13 13:34:15.115224
+/companion start

# 2025-05-13 13:37:25.015387
+Ezwel 이라는 연동사로부터 매시간마다 전체 베스트 상품과 배너정보를 받고있어.
+항상 전체데이터가 전달되며 전달된 데이터에 유니크값은 없기에 기존 등록된 데이터를 전부 삭제하고 새롭게 등록하고 있어.
+EzwelProductHistory 에 매 시간마다 전체삭제 기록과 새롭게 전체등록 기록을 적재하니 로그가 너무 많이 누적되고 통계 및 추적이 힘들어.
+EzwelProduct 는 현재 로직을 그대로 유지하고,
+기존과 sortNo(order)를 제외하고 모든값이 동일하게 전달된 데이터는 History를 추가적으로 적재하지 않게 수정해줘.

# 2025-05-13 13:41:27.164875
+n

# 2025-05-13 13:43:05.417540
+code 값은 유니크가 아니야, 전달되는 데이터에 유니크값이 없기에 sortNo(order)를 제외하고 EzwelProduct 에 필드값을 모두 비교해야 해.

# 2025-05-13 13:56:33.274276
+/map-refresh

# 2025-05-13 13:57:26.830556
+EzwelProductServiceTest 를 변경된 로직에 맞게 수정해줘.

# 2025-05-13 14:01:19.514227
+n

# 2025-05-13 14:08:27.965274
+/companion start

# 2025-05-13 14:08:55.264164
+EzwelProductServiceTest 에 불필요 주석 제거 및 주석 한글로 재작성 해줘

# 2025-05-13 14:11:43.000044
+n

# 2025-05-13 14:12:11.693585
+/commit

# 2025-05-14 13:21:58.394899
+n

# 2025-05-14 13:25:32.125933
+/companion start

# 2025-05-14 13:35:02.627213
+조회할 필요없이 그냥 JPQL, where 조건으로 바로 삭제처리 하면 안되는거야?

# 2025-05-14 13:43:01.328745
+/commit
