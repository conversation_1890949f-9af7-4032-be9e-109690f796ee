
# 2025-05-12 15:57:43.051475
+n

# 2025-05-12 15:59:24.922924
+/map

# 2025-05-12 16:02:38.165531
+/companion start

# 2025-05-12 16:03:20.697822
+퀵배송 주문을 동기화하는 API를 지금 작업중인 코드에서 호출할거야.
+스케쥴러를 통해 아래 API를 30분마다 호출해줘.
+
+ /captain-payment/quick/v2/sync-orders API
+
+Request Body
+    @Getter
+    @Builder
+    @NoArgsConstructor
+    @AllArgsConstructor
+    public static class SyncOrders {
+        @NotNull(message = "updateTimeThreshold는 필수입니다.")
+        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
+        private LocalDateTime updateTimeThreshold;
+        
+        @Builder.Default
+        @Min(value = 1, message = "batchSize는 최소 1 이상이어야 합니다.")
+        @Max(value = 1000, message = "batchSize는 최대 100까지 가능합니다.")
+        private Integer batchSize = 100;
+        
+        @Builder.Default
+        private Integer page = 0;
+    }
+
+Response Body
+    @Getter
+    @Builder
+    public static class SyncOrdersResult {
+        private int ordersChecked;
+        private int ordersUpdated;
+        private int ordersFailedToSync;
+        private List<String> syncErrors;
+        private Integer currentPage;
+        private Integer totalPages;
+        private Long totalOrders;
+        private Boolean hasNextPage;
+    }

# 2025-05-12 16:07:41.455283
+n

# 2025-05-12 16:22:26.638854
+/map-refresh

# 2025-05-12 16:23:24.584835
+quickManagerSyncKakao 스케쥴러를 fixedDelay 가 아닌 cron 으로 처리해줘.

# 2025-05-13 11:14:40.504592
+n

# 2025-05-13 11:16:07.459991
+/commit

# 2025-05-13 11:16:19.623914
+/companion start

# 2025-05-13 11:16:25.181045
+syncQuickOrders 에서 json으로 발송하게 수정해줘

# 2025-05-13 11:34:19.677863
+/commit

# 2025-05-14 13:51:42.231673
+n

# 2025-05-14 14:58:20.471395
+/companion start

# 2025-05-14 14:58:24.567445
+OrganizationUserRemote 에 Delete /ezwel/v1/product-history 를 호출하는 메서드 추가해줘. 쿼리 파라미터로 batchSize를 보내고, 응답값으로 삭제된 갯수를 Integer로 받아야 해.
+LogDataCleanupScheduler 에 기존 코드를 참고하여 동일하게 호출 및 추가해줘.

# 2025-05-20 09:44:11.425624
+n

# 2025-05-20 09:45:31.707003
+/companion start

# 2025-05-20 09:45:36.777494
+/map

# 2025-06-02 17:54:00.701740
+n

# 2025-06-02 17:54:05.401857
+/commit

# 2025-06-18 09:36:06.366537
+n

# 2025-06-18 09:37:25.303801
+/map-refresh

# 2025-06-18 09:39:01.411115
+/companion start

# 2025-06-18 09:39:03.951873
+/map

# 2025-06-24 14:29:53.010251
+n

# 2025-06-24 14:30:38.665965
+/commit

# 2025-06-25 11:03:03.925812
+n

# 2025-06-25 11:03:19.747091
+/companion start

# 2025-06-25 11:05:07.713422
+"[엑셀]파일명에 기입되는 특수문자 제거" 라는 요구사항이 있어, createFileName 메서드에서 특수문자를 " " 공백문자로 치환하여 리턴하게 수정해줘.

# 2025-06-25 11:35:14.553672
+/commit

# 2025-06-25 13:04:16.801945
+n
