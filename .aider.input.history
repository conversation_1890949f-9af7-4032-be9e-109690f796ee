
# 2025-05-13 09:16:31.741140
+n

# 2025-05-13 09:17:47.436608
+/companion start

# 2025-05-13 09:17:51.523445
+내부 다른 서버인 BatchServer를 통해서 /captain-payment/quick/v2/sync-orders 가 호출될거야.
+아래 PaymentServer와 CustomerServer에 내부 서버간 인증 코드를 참고하여 x-corp 헤더를 통해 인증처리 되게 수정해줘.
+
+public class XPayment {
+    private String client;
+    private String userId;
+    private String version;
+}
+
+public class PaymentInterceptor extends HandlerInterceptorAdapter {
+
+    @Value(value = "${vendys.applicationName}")
+    private String vendysApplicationName;
+
+    @Autowired
+    private XPaymentHeader paymentHeader;
+
+    @Autowired
+    private VendysIamHeader vendysIamHeader;
+
+    @Override
+    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
+
+        if (HttpMethod.OPTIONS.name().equalsIgnoreCase(request.getMethod())) {
+            return true;
+        }
+
+        // VendysIamAuth 어노테이션 존재 여부를 확인 한다.
+        HandlerMethod handlerMethod = (HandlerMethod) handler;
+        VendysIamAuth vendysIamAuth = handlerMethod.getMethodAnnotation(VendysIamAuth.class);
+
+        if (ObjectUtils.isEmpty(vendysIamAuth)) {
+            // VendysIamAuth 어노테이션이 존재 하지 않으면 기존의 xpayment 헤더를 통해 인증 처리를 한다.
+            this.paymentHeader.getHeader(request);
+        } else {
+            // VendysIamAuth 어노테이션이 존재 하면 VendysIam에 설정 된 접근 제어 정보로 인증 처리를 한다.
+            VendysIam vendysIam = this.vendysIamHeader.getVendysIamHeader(request);
+            String currentRequestUri = request.getRequestURI();
+            String currentRequestMethod = request.getMethod();
+
+            if (ObjectUtils.isEmpty(vendysIam)) {
+                throw new CommonException(Errors.GENERAL_INVALID_IAM_HEADER);
+            }
+
+            if (ObjectUtils.isEmpty(vendysIam.getClientType()) || ObjectUtils.isEmpty(vendysIam.getClientId())) {
+                throw new CommonException(Errors.GENERAL_INVALID_IAM_HEADER);
+            }
+
+            VendysIam.Service requestService = vendysIam.getServiceList().stream()
+                    .filter(service -> vendysApplicationName.equals(service.getServiceName())).findFirst().orElse(null);
+
+            if (ObjectUtils.isEmpty(requestService)) {
+                throw new AuthException();
+            }
+
+            VendysIam.Service.ServiceFunction requestServiceFunction = requestService.getServiceFunctionList().stream()
+                    .filter(serviceFunction -> {
+                        return currentRequestMethod.equals(serviceFunction.getFunctionMethod()) && (new AntPathMatcher()
+                                .match(serviceFunction.getFunctionName(), currentRequestUri));
+                    }).findFirst().orElse(null);
+
+            if (ObjectUtils.isEmpty(requestServiceFunction)) {
+                throw new AuthException();
+            }
+
+        }
+
+        return super.preHandle(request, response, handler);
+    }
+}
+
+---
+
+public class XCustomer {
+    private Client client;
+    private String userId;
+    private String version;
+}
+
+public class CustomerInterceptor extends HandlerInterceptorAdapter {
+
+    @Value(value = "${vendys.applicationName}")
+    private String vendysApplicationName;
+
+    @Autowired
+    private VendysIamHeader vendysIamHeader;
+
+    @Autowired
+    private XCustomerHeader customerHeader;
+
+    @Override
+    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
+        if (HttpMethod.OPTIONS.name().equalsIgnoreCase(request.getMethod())) {
+            return true;
+        }
+
+        // VendysIamAuth 어노테이션 존재 여부를 확인 한다.
+        HandlerMethod handlerMethod = (HandlerMethod) handler;
+        VendysIamAuth vendysIamAuth = handlerMethod.getMethodAnnotation(VendysIamAuth.class);
+
+        if (ObjectUtils.isEmpty(vendysIamAuth)) {
+            // VendysIamAuth 어노테이션이 존재 하지 않으면 기존의 XCustomer 헤더를 통해 인증 처리를 한다.
+            XCustomer customerHeader = this.customerHeader.getHeader(request);
+            if (customerHeader.getClient() == null || customerHeader.getUserId() == null || customerHeader.getVersion() == null) {
+                throw new CommonException(Errors.GENERAL_INVALID_HEADER);
+            }
+        } else {
+            // VendysIamAuth 어노테이션이 존재 하면 VendysIam에 설정 된 접근 제어 정보로 인증 처리를 한다.
+            VendysIam vendysIam = this.vendysIamHeader.getVendysIamHeader(request);
+            String currentRequestUri = request.getRequestURI();
+            String currentRequestMethod = request.getMethod();
+
+            if (ObjectUtils.isEmpty(vendysIam)) {
+                throw new CommonException(Errors.GENERAL_INVALID_IAM_HEADER);
+            }
+
+            if (ObjectUtils.isEmpty(vendysIam.getClientType()) || ObjectUtils.isEmpty(vendysIam.getClientId())) {
+                throw new CommonException(Errors.GENERAL_INVALID_IAM_HEADER);
+            }
+
+            VendysIam.Service requestService = vendysIam.getServiceList().stream()
+                    .filter(service -> vendysApplicationName.equals(service.getServiceName())).findFirst().orElse(null);
+
+            if (ObjectUtils.isEmpty(requestService)) {
+                throw new Auth401Exception();
+            }
+
+            VendysIam.Service.ServiceFunction requestServiceFunction = requestService.getServiceFunctionList().stream()
+                    .filter(serviceFunction -> {
+                        return currentRequestMethod.equals(serviceFunction.getFunctionMethod()) && (new AntPathMatcher()
+                                .match(serviceFunction.getFunctionName(), currentRequestUri));
+                    }).findFirst().orElse(null);
+
+            if (ObjectUtils.isEmpty(requestServiceFunction)) {
+                throw new Auth401Exception();
+            }
+        }
+        return super.preHandle(request, response, handler);
+    }
+}

# 2025-05-13 09:30:42.930219
+/clear

# 2025-05-13 09:30:48.521165
+내부 다른 서버인 BatchServer를 통해서 /captain-payment/quick/v2/sync-orders 가 호출될거야.
+아래 PaymentServer와 CustomerServer에 내부 서버간 인증 코드를 참고하여 x-corp 헤더를 통해 인증처리 되게 수정해줘.
+
+public class XPayment {
+    private String client;
+    private String userId;
+    private String version;
+}
+
+public class PaymentInterceptor extends HandlerInterceptorAdapter {
+
+    @Value(value = "${vendys.applicationName}")
+    private String vendysApplicationName;
+
+    @Autowired
+    private XPaymentHeader paymentHeader;
+
+    @Autowired
+    private VendysIamHeader vendysIamHeader;
+
+    @Override
+    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
+
+        if (HttpMethod.OPTIONS.name().equalsIgnoreCase(request.getMethod())) {
+            return true;
+        }
+
+        // VendysIamAuth 어노테이션 존재 여부를 확인 한다.
+        HandlerMethod handlerMethod = (HandlerMethod) handler;
+        VendysIamAuth vendysIamAuth = handlerMethod.getMethodAnnotation(VendysIamAuth.class);
+
+        if (ObjectUtils.isEmpty(vendysIamAuth)) {
+            // VendysIamAuth 어노테이션이 존재 하지 않으면 기존의 xpayment 헤더를 통해 인증 처리를 한다.
+            this.paymentHeader.getHeader(request);
+        } else {
+            // VendysIamAuth 어노테이션이 존재 하면 VendysIam에 설정 된 접근 제어 정보로 인증 처리를 한다.
+            VendysIam vendysIam = this.vendysIamHeader.getVendysIamHeader(request);
+            String currentRequestUri = request.getRequestURI();
+            String currentRequestMethod = request.getMethod();
+
+            if (ObjectUtils.isEmpty(vendysIam)) {
+                throw new CommonException(Errors.GENERAL_INVALID_IAM_HEADER);
+            }
+
+            if (ObjectUtils.isEmpty(vendysIam.getClientType()) || ObjectUtils.isEmpty(vendysIam.getClientId())) {
+                throw new CommonException(Errors.GENERAL_INVALID_IAM_HEADER);
+            }
+
+            VendysIam.Service requestService = vendysIam.getServiceList().stream()
+                    .filter(service -> vendysApplicationName.equals(service.getServiceName())).findFirst().orElse(null);
+
+            if (ObjectUtils.isEmpty(requestService)) {
+                throw new AuthException();
+            }
+
+            VendysIam.Service.ServiceFunction requestServiceFunction = requestService.getServiceFunctionList().stream()
+                    .filter(serviceFunction -> {
+                        return currentRequestMethod.equals(serviceFunction.getFunctionMethod()) && (new AntPathMatcher()
+                                .match(serviceFunction.getFunctionName(), currentRequestUri));
+                    }).findFirst().orElse(null);
+
+            if (ObjectUtils.isEmpty(requestServiceFunction)) {
+                throw new AuthException();
+            }
+
+        }
+
+        return super.preHandle(request, response, handler);
+    }
+}
+
+---
+
+public class XCustomer {
+    private Client client;
+    private String userId;
+    private String version;
+}
+
+public class CustomerInterceptor extends HandlerInterceptorAdapter {
+
+    @Value(value = "${vendys.applicationName}")
+    private String vendysApplicationName;
+
+    @Autowired
+    private VendysIamHeader vendysIamHeader;
+
+    @Autowired
+    private XCustomerHeader customerHeader;
+
+    @Override
+    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
+        if (HttpMethod.OPTIONS.name().equalsIgnoreCase(request.getMethod())) {
+            return true;
+        }
+
+        // VendysIamAuth 어노테이션 존재 여부를 확인 한다.
+        HandlerMethod handlerMethod = (HandlerMethod) handler;
+        VendysIamAuth vendysIamAuth = handlerMethod.getMethodAnnotation(VendysIamAuth.class);
+
+        if (ObjectUtils.isEmpty(vendysIamAuth)) {
+            // VendysIamAuth 어노테이션이 존재 하지 않으면 기존의 XCustomer 헤더를 통해 인증 처리를 한다.
+            XCustomer customerHeader = this.customerHeader.getHeader(request);
+            if (customerHeader.getClient() == null || customerHeader.getUserId() == null || customerHeader.getVersion() == null) {
+                throw new CommonException(Errors.GENERAL_INVALID_HEADER);
+            }
+        } else {
+            // VendysIamAuth 어노테이션이 존재 하면 VendysIam에 설정 된 접근 제어 정보로 인증 처리를 한다.
+            VendysIam vendysIam = this.vendysIamHeader.getVendysIamHeader(request);
+            String currentRequestUri = request.getRequestURI();
+            String currentRequestMethod = request.getMethod();
+
+            if (ObjectUtils.isEmpty(vendysIam)) {
+                throw new CommonException(Errors.GENERAL_INVALID_IAM_HEADER);
+            }
+
+            if (ObjectUtils.isEmpty(vendysIam.getClientType()) || ObjectUtils.isEmpty(vendysIam.getClientId())) {
+                throw new CommonException(Errors.GENERAL_INVALID_IAM_HEADER);
+            }
+
+            VendysIam.Service requestService = vendysIam.getServiceList().stream()
+                    .filter(service -> vendysApplicationName.equals(service.getServiceName())).findFirst().orElse(null);
+
+            if (ObjectUtils.isEmpty(requestService)) {
+                throw new Auth401Exception();
+            }
+
+            VendysIam.Service.ServiceFunction requestServiceFunction = requestService.getServiceFunctionList().stream()
+                    .filter(serviceFunction -> {
+                        return currentRequestMethod.equals(serviceFunction.getFunctionMethod()) && (new AntPathMatcher()
+                                .match(serviceFunction.getFunctionName(), currentRequestUri));
+                    }).findFirst().orElse(null);
+
+            if (ObjectUtils.isEmpty(requestServiceFunction)) {
+                throw new Auth401Exception();
+            }
+        }
+        return super.preHandle(request, response, handler);
+    }
+}

# 2025-05-13 09:52:58.991140
+n

# 2025-05-13 09:55:10.449012
+/companion start

# 2025-05-13 09:55:16.057420
+내부 다른 서버인 BatchServer를 통해서 /captain-payment/quick/v2/sync-orders 가 호출될거야.
+아래 PaymentServer와 CustomerServer에 내부 서버간 인증 코드를 참고하여 x-corp 헤더를 통해 인증처리 되게 수정해줘.
+
+public class XPayment {
+    private String client;
+    private String userId;
+    private String version;
+}
+
+public class PaymentInterceptor extends HandlerInterceptorAdapter {
+
+    @Value(value = "${vendys.applicationName}")
+    private String vendysApplicationName;
+
+    @Autowired
+    private XPaymentHeader paymentHeader;
+
+    @Autowired
+    private VendysIamHeader vendysIamHeader;
+
+    @Override
+    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
+
+        if (HttpMethod.OPTIONS.name().equalsIgnoreCase(request.getMethod())) {
+            return true;
+        }
+
+        // VendysIamAuth 어노테이션 존재 여부를 확인 한다.
+        HandlerMethod handlerMethod = (HandlerMethod) handler;
+        VendysIamAuth vendysIamAuth = handlerMethod.getMethodAnnotation(VendysIamAuth.class);
+
+        if (ObjectUtils.isEmpty(vendysIamAuth)) {
+            // VendysIamAuth 어노테이션이 존재 하지 않으면 기존의 xpayment 헤더를 통해 인증 처리를 한다.
+            this.paymentHeader.getHeader(request);
+        } else {
+            // VendysIamAuth 어노테이션이 존재 하면 VendysIam에 설정 된 접근 제어 정보로 인증 처리를 한다.
+            VendysIam vendysIam = this.vendysIamHeader.getVendysIamHeader(request);
+            String currentRequestUri = request.getRequestURI();
+            String currentRequestMethod = request.getMethod();
+
+            if (ObjectUtils.isEmpty(vendysIam)) {
+                throw new CommonException(Errors.GENERAL_INVALID_IAM_HEADER);
+            }
+
+            if (ObjectUtils.isEmpty(vendysIam.getClientType()) || ObjectUtils.isEmpty(vendysIam.getClientId())) {
+                throw new CommonException(Errors.GENERAL_INVALID_IAM_HEADER);
+            }
+
+            VendysIam.Service requestService = vendysIam.getServiceList().stream()
+                    .filter(service -> vendysApplicationName.equals(service.getServiceName())).findFirst().orElse(null);
+
+            if (ObjectUtils.isEmpty(requestService)) {
+                throw new AuthException();
+            }
+
+            VendysIam.Service.ServiceFunction requestServiceFunction = requestService.getServiceFunctionList().stream()
+                    .filter(serviceFunction -> {
+                        return currentRequestMethod.equals(serviceFunction.getFunctionMethod()) && (new AntPathMatcher()
+                                .match(serviceFunction.getFunctionName(), currentRequestUri));
+                    }).findFirst().orElse(null);
+
+            if (ObjectUtils.isEmpty(requestServiceFunction)) {
+                throw new AuthException();
+            }
+
+        }
+
+        return super.preHandle(request, response, handler);
+    }
+}
+
+---
+
+public class XCustomer {
+    private Client client;
+    private String userId;
+    private String version;
+}
+
+public class CustomerInterceptor extends HandlerInterceptorAdapter {
+
+    @Value(value = "${vendys.applicationName}")
+    private String vendysApplicationName;
+
+    @Autowired
+    private VendysIamHeader vendysIamHeader;
+
+    @Autowired
+    private XCustomerHeader customerHeader;
+
+    @Override
+    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
+        if (HttpMethod.OPTIONS.name().equalsIgnoreCase(request.getMethod())) {
+            return true;
+        }
+
+        // VendysIamAuth 어노테이션 존재 여부를 확인 한다.
+        HandlerMethod handlerMethod = (HandlerMethod) handler;
+        VendysIamAuth vendysIamAuth = handlerMethod.getMethodAnnotation(VendysIamAuth.class);
+
+        if (ObjectUtils.isEmpty(vendysIamAuth)) {
+            // VendysIamAuth 어노테이션이 존재 하지 않으면 기존의 XCustomer 헤더를 통해 인증 처리를 한다.
+            XCustomer customerHeader = this.customerHeader.getHeader(request);
+            if (customerHeader.getClient() == null || customerHeader.getUserId() == null || customerHeader.getVersion() == null) {
+                throw new CommonException(Errors.GENERAL_INVALID_HEADER);
+            }
+        } else {
+            // VendysIamAuth 어노테이션이 존재 하면 VendysIam에 설정 된 접근 제어 정보로 인증 처리를 한다.
+            VendysIam vendysIam = this.vendysIamHeader.getVendysIamHeader(request);
+            String currentRequestUri = request.getRequestURI();
+            String currentRequestMethod = request.getMethod();
+
+            if (ObjectUtils.isEmpty(vendysIam)) {
+                throw new CommonException(Errors.GENERAL_INVALID_IAM_HEADER);
+            }
+
+            if (ObjectUtils.isEmpty(vendysIam.getClientType()) || ObjectUtils.isEmpty(vendysIam.getClientId())) {
+                throw new CommonException(Errors.GENERAL_INVALID_IAM_HEADER);
+            }
+
+            VendysIam.Service requestService = vendysIam.getServiceList().stream()
+                    .filter(service -> vendysApplicationName.equals(service.getServiceName())).findFirst().orElse(null);
+
+            if (ObjectUtils.isEmpty(requestService)) {
+                throw new Auth401Exception();
+            }
+
+            VendysIam.Service.ServiceFunction requestServiceFunction = requestService.getServiceFunctionList().stream()
+                    .filter(serviceFunction -> {
+                        return currentRequestMethod.equals(serviceFunction.getFunctionMethod()) && (new AntPathMatcher()
+                                .match(serviceFunction.getFunctionName(), currentRequestUri));
+                    }).findFirst().orElse(null);
+
+            if (ObjectUtils.isEmpty(requestServiceFunction)) {
+                throw new Auth401Exception();
+            }
+        }
+        return super.preHandle(request, response, handler);
+    }
+}

# 2025-05-13 09:57:21.925790
+n

# 2025-05-13 10:04:55.267777
+커스텀 어노테이션을 만들거나 사용하지 말고, 그냥 CaptainPaymentOAuthInterceptor 를 사용하는 모든 API를 그냥 인증 및 통과시킬거야

# 2025-05-13 10:09:20.890598
+/clear

# 2025-05-13 10:09:30.935966
+/drop

# 2025-05-13 10:10:15.906809
+내부서버간 인증처리를 위한 키값을 생성하고 싶어. AnyTest 클래스에 키값 생성용 코드 작성해줘

# 2025-05-13 10:31:00.323248
+/map-refresh

# 2025-05-13 10:31:20.742316
+Batch 서버를 통해서 /captain-payment/quick/v2/sync-orders API가 호출될거야.
+내부서버간 인증을 위해서 internalKey를 사용해줘, x-corp 헤더를 통해서 받을거야.
+그리고 아래 다른 서버인 Payment서버에 XPayment 를 통한 인증 샘플코드를 참고하여 XCorp Dto 및 로직 작성해줘.
+
+
+public class XPayment {
+    private String client;
+    private String userId;
+    private String version;
+}
+
+@Component
+public class XPaymentHeader {
+
+    @Autowired
+    private ConverterUtil converterUtil;
+
+    public XPayment getHeader(HttpServletRequest request) {
+        String header = request.getHeader("x-payment");
+        if (header == null) {
+            throw new AuthException();
+        }
+
+        XPayment paymentHeader = this.converterUtil.toObject(header, XPayment.class);
+        if (paymentHeader == null) {
+            throw new AuthException();
+        }
+
+        return paymentHeader;
+    }
+}
+
+@Component
+public class PaymentInterceptor extends HandlerInterceptorAdapter {
+
+    @Value(value = "${vendys.applicationName}")
+    private String vendysApplicationName;
+
+    @Autowired
+    private XPaymentHeader paymentHeader;
+
+    @Autowired
+    private VendysIamHeader vendysIamHeader;
+
+    @Override
+    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
+
+        if (HttpMethod.OPTIONS.name().equalsIgnoreCase(request.getMethod())) {
+            return true;
+        }
+
+        // VendysIamAuth 어노테이션 존재 여부를 확인 한다.
+        HandlerMethod handlerMethod = (HandlerMethod) handler;
+        VendysIamAuth vendysIamAuth = handlerMethod.getMethodAnnotation(VendysIamAuth.class);
+
+        if (ObjectUtils.isEmpty(vendysIamAuth)) {
+            // VendysIamAuth 어노테이션이 존재 하지 않으면 기존의 xpayment 헤더를 통해 인증 처리를 한다.
+            this.paymentHeader.getHeader(request);
+        } else {
+            // VendysIamAuth 어노테이션이 존재 하면 VendysIam에 설정 된 접근 제어 정보로 인증 처리를 한다.
+            VendysIam vendysIam = this.vendysIamHeader.getVendysIamHeader(request);
+            String currentRequestUri = request.getRequestURI();
+            String currentRequestMethod = request.getMethod();
+
+            if (ObjectUtils.isEmpty(vendysIam)) {
+                throw new CommonException(Errors.GENERAL_INVALID_IAM_HEADER);
+            }
+
+            if (ObjectUtils.isEmpty(vendysIam.getClientType()) || ObjectUtils.isEmpty(vendysIam.getClientId())) {
+                throw new CommonException(Errors.GENERAL_INVALID_IAM_HEADER);
+            }
+
+            VendysIam.Service requestService = vendysIam.getServiceList().stream()
+                    .filter(service -> vendysApplicationName.equals(service.getServiceName())).findFirst().orElse(null);
+
+            if (ObjectUtils.isEmpty(requestService)) {
+                throw new AuthException();
+            }
+
+            VendysIam.Service.ServiceFunction requestServiceFunction = requestService.getServiceFunctionList().stream()
+                    .filter(serviceFunction -> {
+                        return currentRequestMethod.equals(serviceFunction.getFunctionMethod()) && (new AntPathMatcher()
+                                .match(serviceFunction.getFunctionName(), currentRequestUri));
+                    }).findFirst().orElse(null);
+
+            if (ObjectUtils.isEmpty(requestServiceFunction)) {
+                throw new AuthException();
+            }
+
+        }
+
+        return super.preHandle(request, response, handler);
+    }
+}

# 2025-05-13 10:42:08.899914
+/map-refresh

# 2025-05-13 10:45:39.155493
+InternalKey 를 통한 인증시 userId 값을 uid, name 으로 설정하여 userInfo 를 설정해줘

# 2025-05-13 10:57:30.207906
+n

# 2025-05-13 11:01:46.400429
+/commit

# 2025-05-13 11:21:57.217547
+n

# 2025-05-13 11:22:00.564370
+/commit

# 2025-05-13 17:15:36.395175
+n

# 2025-05-13 17:15:45.442333
+/commit

# 2025-05-14 10:54:52.398326
+n

# 2025-05-14 10:55:53.588029
+/commit

# 2025-05-14 14:34:27.115225
+n

# 2025-05-14 14:34:30.471136
+/commit

# 2025-05-14 15:13:14.841830
+n

# 2025-05-14 15:15:13.087168
+/companion start

# 2025-05-14 15:16:29.022054
+아래와 같이 에러 응답시 응답값에 message가 깨져서 보여.
+Kakao T API Error: Status=400, RawBody={"requestId":"d140e678512c15ec","code":-70003,"status":400,"message":"ìëª»ë ìì²­ìëë¤.","data":{"pickup.wishTime":"ìê°ì´ ëë¬´ ì´ë¦ëë¤. ìµì 60 ë¶ í ë¶í° ê°ë¥í©ëë¤."}}

# 2025-05-14 16:23:59.884500
+/commit

# 2025-05-14 16:25:51.802415
+/map-refresh

# 2025-05-14 16:29:26.394541
+createorder 에서 ordertype이 QUICK_ECONOMY 인 경우 appointmentDate에 최소시간은 현재시간 + 60분 이후여야 해.

# 2025-05-15 09:49:26.053093
+n

# 2025-05-15 09:59:33.791302
+/companion start

# 2025-05-15 09:59:38.243737
+Order Dto 및 populateFromQuickManager 메서드에서 amount, vatAmount, totalAmount 에 수정이 필요해.
+gogoX에서는 공급가액을 전달 하기에 QuickManager.amount 에 공급가액이 저장되고 응답시 현재 로직 및 공식대로 계산하고 있어.
+하지만 kakaoT 에서는 amount에 합계금액이 저장되기에 응답 Dto에 amount, vatAmount 를 역산하여 설정하여 주어야 해.

# 2025-05-15 10:02:33.065085
+/undo

# 2025-05-15 10:02:48.513922
+n

# 2025-05-15 10:11:56.450735
+/companion start

# 2025-05-15 10:12:22.139972
+Order Dto 및 populateFromQuickManager 메서드에서 amount, vatAmount, totalAmount 에 수정이 필요해.
+gogoX에서는 공급가액을 전달 하기에 QuickManager.amount 에 공급가액이 저장되고 응답시 현재 로직 및 공식대로 계산하고 있어.
+하지만 kakaoT 에서는 QuickManager.amount에 합계금액이 저장되기에 응답 Dto에 amount, vatAmount 를 역산하여 설정하여 주어야 해.

# 2025-05-15 10:17:19.801395
+/commit

# 2025-05-15 11:02:55.575894
+/clear

# 2025-05-15 11:13:12.254859
+/map-refresh

# 2025-05-15 11:14:01.810964
+/commit

# 2025-05-15 14:19:09.814283
+n

# 2025-05-15 14:24:26.057593
+/map-refresh

# 2025-05-15 14:30:40.726017
+/companion start

# 2025-05-15 14:30:42.687116
+getOrderList 조회에서 검색조건을 QuickStatus 가 아닌 DisplayQuickStatus 를 받아서 조회하게 수정해줘.

# 2025-05-15 14:37:37.115772
+/commit

# 2025-05-16 11:19:51.364484
+n

# 2025-05-16 11:21:32.086881
+/companion start

# 2025-05-16 11:24:09.690236
+현재 KakaoT에 API 요청 실패인 경우 아래와 같이 로그에 저장하고 있어.
+
+apiLog.updateResponse(e.getResponseBodyAsString(), e.getRawStatusCode());
+
+DB상에 getResponseBodyAsString 에 한글문자가 깨져서 보여, UTF_8로 처리하여 저장하여야 해.

# 2025-05-16 12:06:25.247436
+n

# 2025-05-16 12:08:01.244873
+/commit

# 2025-05-16 13:54:51.271662
+/companion start

# 2025-05-16 13:55:21.261153
+/copy-context

# 2025-05-16 13:55:50.747616
+n

# 2025-05-16 13:55:54.370550
+/companion start

# 2025-05-16 13:55:59.097684
+/copy-context

# 2025-05-16 16:44:51.127632
+n

# 2025-05-16 16:44:53.986507
+/commit

# 2025-05-19 09:59:17.236763
+n

# 2025-05-19 10:08:36.652667
+/companion start

# 2025-05-19 10:08:43.265839
+/map-refresh

# 2025-05-19 10:36:45.779465
+주문 동기화(processSingleOrderForSync)에서 조회하여 응답받은 totalPrice가 최초 주문시점에 totalPrice와 다른경우, 차액만큼 추가운임비인 bonusAmount 에 설정해줘.

# 2025-05-19 11:06:50.223868
+/commit

# 2025-05-19 11:08:26.186088
+/map-refresh

# 2025-05-19 11:13:02.236524
+callback 메서드에 status가(newStatus) COMPLETED 로 변경되는 경우 processSingleOrderForSync 를 호출해줘.

# 2025-05-19 11:22:27.479199
+/commit

# 2025-05-19 12:15:39.316143
+n

# 2025-05-19 12:16:46.187284
+/map-refresh

# 2025-05-19 12:34:57.134535
+n

# 2025-05-19 12:35:52.619946
+/map-refresh

# 2025-05-19 12:50:55.283578
+n

# 2025-05-19 12:52:04.922815
+/map-refresh

# 2025-05-19 13:29:16.044061
+/commit

# 2025-05-19 14:42:55.131613
+n

# 2025-05-19 14:43:49.282664
+/companion start

# 2025-05-19 14:45:00.125308
+/map-refresh

# 2025-05-19 14:49:29.029092
+현재 findStatByXXX, quickStatByXXX, quickStatExcelByMonth 형식에 쿼리문이 사실상 동일해. 하나로 통합하여 재사용하게 수정해줘.

# 2025-05-19 14:51:08.757323
+n

# 2025-05-19 14:51:10.522219
+/clear

# 2025-05-19 14:53:19.508851
+/code 현재 findStatByXXX, quickStatByXXX, quickStatExcelByMonth 메서드가 전달되는 startDate, endDate 날짜만 다르고 사실상 동일해. 깔끔하게 리팩토링 및 정리해줘.

# 2025-05-19 14:56:51.933217
+/undo

# 2025-05-19 14:56:56.927787
+/clear

# 2025-05-19 14:57:06.630643
+findStatByXXX, quickStatByXXX, quickStatExcelByMonth 메서드가 전달되는 startDate, endDate 날짜만 다르고 사실상 동일해. 깔끔하게 리팩토링 및 정리해줘.

# 2025-05-19 15:03:47.379747
+n

# 2025-05-19 15:03:49.152309
+/clear

# 2025-05-19 15:03:53.385303
+/reset

# 2025-05-19 15:04:47.650682
+findStatByMonth 에서 startYear, endYear 를 LocalDateTime 으로 받게 수정해줘

# 2025-05-19 15:10:57.738309
+/commit

# 2025-05-19 15:19:25.348039
+/reset

# 2025-05-19 15:20:29.224476
+/map-refresh

# 2025-05-19 15:22:26.272546
+/paste

# 2025-05-19 15:29:21.564036
+/map-refresh

# 2025-05-19 15:30:33.973506
+QuickController에 stat 이라는 API 추가해줘.
+이미지로 제공한 통계페이지 API를 제공해야 해.
+createDate가 아닌 픽업희망시간인 appointmentDate를 기준으로 그룹화 해줘.
+취소수수료 = cancelFee, 운임총합 = totalPrice+bonusAmount

# 2025-05-19 15:30:56.174502
+n

# 2025-05-19 15:31:28.365128
+/companion start

# 2025-05-19 15:32:01.782708
+QuickController에 stat 이라는 API 추가해줘.
+이미지로 제공한 통계페이지 API를 제공해야 해.
+createDate가 아닌 픽업희망시간인 appointmentDate를 기준으로 그룹화 해줘.
+취소수수료 = cancelFee, 운임총합 = totalPrice+bonusAmount

# 2025-05-19 15:43:04.664929
+n

# 2025-05-19 15:43:58.789787
+/companion start

# 2025-05-19 15:44:38.594783
+/paste

# 2025-05-19 15:44:46.029135
+QuickController에 stat 이라는 API 추가해줘.
+이미지로 제공한 통계페이지 API를 제공해야 해.
+createDate가 아닌 픽업희망시간인 appointmentDate를 기준으로 그룹화 해줘.
+취소수수료 = cancelFee, 운임총합 = totalPrice+bonusAmount

# 2025-05-19 16:09:55.018455
+/map-refresh

# 2025-05-19 16:11:26.603363
+이용건수(TotalOrderCount)에 기준은 status가 COMPLETED, INCOMPLETED, RETURN 인 데이터야.
+취소건수(CanceledOrderCount)에 기준은 cancelFee 데이터가 null이 아니고 0 초과인 데이터야.
+QueryDsl 4.2.2 버전이야.

# 2025-05-19 16:15:31.809333
+QueryDsl 4.2.2 버전에는 CaseBuilder가 없어.

# 2025-05-19 16:21:31.202012
+C:\vendys\corp-api\src\main\java\kr\co\vendys\company\api\repository\mealcoupon\quick\QuickManagerRepositoryImpl.java:195:91
+java: incompatible types: com.querydsl.core.types.dsl.Coalesce<java.lang.Integer> cannot be converted to com.querydsl.core.types.dsl.NumberExpression<java.lang.Integer>
+C:\vendys\corp-api\src\main\java\kr\co\vendys\company\api\repository\mealcoupon\quick\QuickManagerRepositoryImpl.java:214:27
+java: incompatible types: com.querydsl.core.types.dsl.SimpleExpression<java.lang.Integer> cannot be converted to com.querydsl.core.types.dsl.NumberExpression<java.lang.Integer>
+C:\vendys\corp-api\src\main\java\kr\co\vendys\company\api\repository\mealcoupon\quick\QuickManagerRepositoryImpl.java:220:27
+java: incompatible types: com.querydsl.core.types.dsl.SimpleExpression<java.lang.Long> cannot be converted to com.querydsl.core.types.dsl.NumberExpression<java.lang.Long>
+C:\vendys\corp-api\src\main\java\kr\co\vendys\company\api\repository\mealcoupon\quick\QuickManagerRepositoryImpl.java:230:27
+java: incompatible types: com.querydsl.core.types.dsl.SimpleExpression<java.lang.Long> cannot be converted to com.querydsl.core.types.dsl.NumberExpression<java.lang.Long>
+

# 2025-05-19 16:33:46.060131
+운임가(판매가)는 각 건별로 vendor가 KAKAOT 인 경우엔 totalPrice+bonusAmount  이고,
+GOGOX인 경우엔 round(amount * 1.1)로 계산해야 해.

# 2025-05-19 16:37:27.633839
+/map-refresh

# 2025-05-19 16:37:35.879832
+/drop ..\..\Users\skawn\AppData\Local\Temp\tmpb9q45xgo\clipboard_image.png

# 2025-05-19 16:37:36.982088
+C:\vendys\corp-api\src\main\java\kr\co\vendys\company\api\repository\mealcoupon\quick\QuickManagerRepositoryImpl.java:200:88
+java: cannot find symbol
+  symbol:   method multiply(double)
+  location: class com.querydsl.core.types.dsl.Coalesce<java.lang.Integer>

# 2025-05-19 16:39:01.254448
+C:\vendys\corp-api\src\main\java\kr\co\vendys\company\api\repository\mealcoupon\quick\QuickManagerRepositoryImpl.java:200:119
+java: incompatible types: no instance(s) of type variable(s) N exist so that com.querydsl.core.types.dsl.NumberExpression<java.lang.Integer> conforms to com.querydsl.core.types.dsl.NumberExpression<java.lang.Double>

# 2025-05-19 16:44:01.297545
+/map-refresh

# 2025-05-19 16:44:39.073241
+QuickServiceTest 에 getQuickStats 테스트코드 추가해줘

# 2025-05-19 16:48:41.517761
+/map-refresh

# 2025-05-19 16:48:47.786423
+/tokens

# 2025-05-19 16:48:55.430140
+WARNING: An illegal reflective access operation has occurred
+WARNING: Illegal reflective access by org.springframework.cglib.core.ReflectUtils (file:/C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-core/5.1.19.RELEASE/642f492d69cb465479e8316dbc4fd41ce7910ead/spring-core-5.1.19.RELEASE.jar) to method java.lang.ClassLoader.defineClass(java.lang.String,byte[],int,int,java.security.ProtectionDomain)
+WARNING: Please consider reporting this to the maintainers of org.springframework.cglib.core.ReflectUtils
+WARNING: Use --illegal-access=warn to enable warnings of further illegal reflective access operations
+WARNING: All illegal access operations will be denied in a future release
+
+com.querydsl.core.types.ExpressionException: kr.co.vendys.core.schema.mealcoupon.entity.QuickManager$QuickStatProjection
+
+	at com.querydsl.core.types.QBean.newInstance(QBean.java:246)
+	at com.querydsl.core.types.FactoryExpressionUtils$FactoryExpressionAdapter.newInstance(FactoryExpressionUtils.java:62)
+	at com.querydsl.core.support.NumberConversions.newInstance(NumberConversions.java:86)
+	at com.querydsl.jpa.impl.AbstractJPAQuery.getResultList(AbstractJPAQuery.java:153)
+	at com.querydsl.jpa.impl.AbstractJPAQuery.fetch(AbstractJPAQuery.java:202)
+	at kr.co.vendys.company.api.repository.mealcoupon.quick.QuickManagerRepositoryImpl.findQuickStats(QuickManagerRepositoryImpl.java:259)
+	at kr.co.vendys.company.api.repository.mealcoupon.quick.QuickManagerRepositoryImpl$$FastClassBySpringCGLIB$$45b61fc5.invoke(<generated>)
+	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)

# 2025-05-19 16:51:24.868946
+n

# 2025-05-19 16:51:27.963044
+WARNING: An illegal reflective access operation has occurred
+WARNING: Illegal reflective access by org.springframework.cglib.core.ReflectUtils (file:/C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/spring-core/5.1.19.RELEASE/642f492d69cb465479e8316dbc4fd41ce7910ead/spring-core-5.1.19.RELEASE.jar) to method java.lang.ClassLoader.defineClass(java.lang.String,byte[],int,int,java.security.ProtectionDomain)
+WARNING: Please consider reporting this to the maintainers of org.springframework.cglib.core.ReflectUtils
+WARNING: Use --illegal-access=warn to enable warnings of further illegal reflective access operations
+WARNING: All illegal access operations will be denied in a future release
+
+com.querydsl.core.types.ExpressionException: kr.co.vendys.core.schema.mealcoupon.entity.QuickManager$QuickStatProjection
+
+	at com.querydsl.core.types.QBean.newInstance(QBean.java:246)
+	at com.querydsl.core.types.FactoryExpressionUtils$FactoryExpressionAdapter.newInstance(FactoryExpressionUtils.java:62)
+	at com.querydsl.core.support.NumberConversions.newInstance(NumberConversions.java:86)
+	at com.querydsl.jpa.impl.AbstractJPAQuery.getResultList(AbstractJPAQuery.java:153)
+	at com.querydsl.jpa.impl.AbstractJPAQuery.fetch(AbstractJPAQuery.java:202)
+	at kr.co.vendys.company.api.repository.mealcoupon.quick.QuickManagerRepositoryImpl.findQuickStats(QuickManagerRepositoryImpl.java:259)
+	at kr.co.vendys.company.api.repository.mealcoupon.quick.QuickManagerRepositoryImpl$$FastClassBySpringCGLIB$$45b61fc5.invoke(<generated>)
+	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)

# 2025-05-19 16:52:42.493960
+n

# 2025-05-19 17:02:20.159139
+/map-refresh

# 2025-05-19 17:06:12.837915
+이용건수(TotalOrderCount)에 기준은 status가 COMPLETED, INCOMPLETED, RETURN 인 데이터야.
+취소건수(CanceledOrderCount)에 기준은 cancelFee 데이터가 null이 아니고 0 초과인 데이터야.
+위 조건으로 필터링되어 데이터가 없는 날짜(orderCount, canceledOrderCount 가 각각 0, 0)도 List에 포함되어 응답되고 있어.
+where 조건에도 포함시켜서 데이터를 우선 필터링할수는 없을까?

# 2025-05-19 17:15:47.471630
+/commit

# 2025-05-19 17:18:19.827995
+ /stat API에 대한 Swagger Yaml 파일 작성해줘. 스키마등은 정의하지 말고 response에 모두 명시해줘

# 2025-05-22 09:36:12.548164
+n

# 2025-05-22 09:37:06.287648
+/map

# 2025-05-22 09:38:50.634867
+/commit

# 2025-05-22 09:42:12.544926
+/companion start

# 2025-05-22 09:43:54.455620
+/map-refresh

# 2025-05-22 09:45:32.843142
+주소록 조회인 /address-book API 및 getAddressBookList 메서드에 응답값인 QuickResponseDto.AddressBook 에 updateUserName을 설정하여 응답하게 수정해줘.
+updateUser는 수정자의 uid야.

# 2025-05-22 09:47:23.360138
+n

# 2025-05-22 09:48:17.892277
+/companion start

# 2025-05-22 09:48:21.308478
+주소록 조회인 /address-book API 및 getAddressBookList 메서드에 응답값인 QuickResponseDto.AddressBook 에 updateUserName을 설정하여 응답하게 수정해줘.
+updateUser는 수정자의 uid야.

# 2025-05-22 10:05:59.152830
+/commit

# 2025-05-22 13:54:31.235707
+n

# 2025-05-22 13:55:46.949570
+/map-refresh

# 2025-05-22 14:02:50.772679
+n

# 2025-05-22 14:02:56.978447
+/companion start

# 2025-05-22 14:05:23.867331
+/map-refresh

# 2025-05-22 14:11:50.704557
+throw new AuthorizedException(Errors.OAuth_TOKEN_EMPTY);
+위 코드로 발생하는 예외의 로그가, 존재하는 API 엔드포인트에 접근시에는 정상적으로 WARN 레벨로 표시 되지만,
+없는 API엔드포인트에 접근시에는 아래와 같이 ERROR로 표시되고 있어, WARN으로 표시되어야 해.
+
+
+2025-05-22 14:00:43 ERROR o.a.j.l.DirectJDKLog.log - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.sikdae.company.api.exception.AuthorizedException: 다시 로그인 해 주세요. (TOKEN EMPTY)] with root cause
+com.sikdae.company.api.exception.AuthorizedException: 다시 로그인 해 주세요. (TOKEN EMPTY)
+	at com.sikdae.company.api.interceptor.OAuth2Interceptor.preHandle(OAuth2Interceptor.java:74)
+	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:143)
+	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1035)
+	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
+	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
+	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
+	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
+	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
+	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at com.sikdae.company.api.interceptor.ReadableRequestResponseWrapperFilter.doFilter(ReadableRequestResponseWrapperFilter.java:26)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at com.sikdae.company.api.interceptor.CorsFilter.doFilterInternal(CorsFilter.java:48)

# 2025-05-22 14:39:48.875257
+/undo

# 2025-05-22 14:41:36.535309
+n

# 2025-05-22 15:58:52.114152
+/companion start

# 2025-05-22 16:00:59.938969
+/map-refresh

# 2025-05-22 16:10:11.044590
+존재하는 URL(API 엔드포인트)에 accessToken 없이 접근시 WARN 레벨로 로그가 남고.
+미존재하는 URL(API 엔드포인트)에 accessToken 없이 접근시 ERROR 레벨로 로그가 남고 있어.
+WARN 레벨로 로그가 남아야 해.
+
+
+2025-05-22 16:07:54 WARN  c.s.c.a.i.LoggingInterceptor.afterCompletion - com.sikdae.company.api.exception.AuthorizedException : {"status":-2304,"title":"에러","message":"다시 로그인 해 주세요. (TOKEN EMPTY)"}
+com.sikdae.company.api.exception.AuthorizedException: 다시 로그인 해 주세요. (TOKEN EMPTY)
+	at com.sikdae.company.api.interceptor.OAuth2Interceptor.preHandle(OAuth2Interceptor.java:74)
+
+...
+
+2025-05-22 16:08:00 ERROR o.a.j.l.DirectJDKLog.log - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.sikdae.company.api.exception.AuthorizedException: 다시 로그인 해 주세요. (TOKEN EMPTY)] with root cause
+com.sikdae.company.api.exception.AuthorizedException: 다시 로그인 해 주세요. (TOKEN EMPTY)
+	at com.sikdae.company.api.interceptor.OAuth2Interceptor.preHandle(OAuth2Interceptor.java:74)

# 2025-05-22 16:23:49.506588
+/map-refresh

# 2025-05-22 16:23:51.395334
+문제가 동일해, 아래 두개의 예외 로그중에 첫번째 WARN 로그처럼 처리되어야 해.
+
+2025-05-22 16:22:24 WARN  c.s.c.a.i.LoggingInterceptor.afterCompletion - com.sikdae.company.api.exception.AuthorizedException : {"status":-2304,"title":"에러","message":"다시 로그인 해 주세요. (TOKEN EMPTY)"}
+com.sikdae.company.api.exception.AuthorizedException: 다시 로그인 해 주세요. (TOKEN EMPTY)
+	at com.sikdae.company.api.interceptor.OAuth2Interceptor.preHandle(OAuth2Interceptor.java:74)
+	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:143)
+	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1035)
+	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
+	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
+	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
+	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
+	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
+	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at com.sikdae.company.api.interceptor.ReadableRequestResponseWrapperFilter.doFilter(ReadableRequestResponseWrapperFilter.java:26)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at com.sikdae.company.api.interceptor.CorsFilter.doFilterInternal(CorsFilter.java:48)
+	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
+	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
+	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:94)
+	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
+	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
+	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
+	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
+	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
+	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
+	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
+	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
+	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
+	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
+	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
+	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
+	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
+	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
+	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
+	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
+	at java.base/java.lang.Thread.run(Thread.java:829)
+2025-05-22 16:22:27 INFO  c.s.c.a.i.LoggingInterceptor.afterCompletion - GET : 200
+2025-05-22 16:22:27 ERROR o.a.j.l.DirectJDKLog.log - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is com.sikdae.company.api.exception.AuthorizedException: 다시 로그인 해 주세요. (TOKEN EMPTY)] with root cause
+com.sikdae.company.api.exception.AuthorizedException: 다시 로그인 해 주세요. (TOKEN EMPTY)
+	at com.sikdae.company.api.interceptor.OAuth2Interceptor.preHandle(OAuth2Interceptor.java:74)
+	at org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:143)
+	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1035)
+	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
+	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
+	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
+	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
+	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
+	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at com.sikdae.company.api.interceptor.ReadableRequestResponseWrapperFilter.doFilter(ReadableRequestResponseWrapperFilter.java:26)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at com.sikdae.company.api.interceptor.CorsFilter.doFilterInternal(CorsFilter.java:48)
+	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
+	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
+	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:94)
+	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
+	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
+	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
+	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
+	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
+	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
+	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
+	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
+	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
+	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
+	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
+	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
+	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
+	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
+	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
+	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
+	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
+	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
+	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
+	at java.base/java.lang.Thread.run(Thread.java:829)
+2025-05-22 16:22:27 INFO  c.s.c.a.i.LoggingInterceptor.afterCompletion - GET : 500

# 2025-05-22 16:34:40.393363
+SpringBoot 2.1.x 버전이야. 브레이크포인트를 걸어서 확인하니, 존재하는 API 엔드포인트에선 @ExceptionHandler(AuthorizedException.class) 가 작동하지만, 존재안하는 API 엔드포인트에 접근시에는 해당 예외 핸들러 없이 처리 및 응답되는거 같아. {"status":500,"title":"Internal Server Error","message":"Internal Server Error (/bill/v1/accounts)"}

# 2025-05-23 12:47:39.608813
+n

# 2025-05-23 12:50:38.704173
+/companion start

# 2025-05-23 12:54:20.743651
+/map-refresh

# 2025-05-23 12:55:09.469671
+handleNoHandlerFound 핸들러 메서드를 고쳐줘, 그리고 robots.txt 파일을 추가 및 작성해줘.

# 2025-05-23 12:59:13.400139
+robots.txt 에 접근 가능하게 해줘.

# 2025-05-23 13:02:32.398975
+add-mappings false는 유지할거야, 소스에서 명시적으로 robots.txt 만 리소스로 추가해줘

# 2025-05-23 13:18:19.450310
+n

# 2025-05-23 13:18:28.086279
+/commit

# 2025-05-27 15:27:25.407263
+n

# 2025-05-27 15:27:34.185825
+/commit

# 2025-05-29 10:52:22.075091
+n

# 2025-05-29 11:26:25.956838
+/companion start

# 2025-05-29 11:26:50.812402
+/map-refresh

# 2025-05-29 13:09:04.925575
+n

# 2025-05-29 13:13:03.580106
+/companion start

# 2025-05-29 13:13:12.032891
+QuickAddressBook 테이블에 gpslat, gpslon 컬럼이 추가되었어, 그리고 Daum지도 사용에서 Naver 지도 사용으로 변경되었어.
+QuickAddressBook 에 idx 689 이상부터 데이터 마이그레이션이 필요해.
+
+기존에 저장된 roadAddress or jibunAddress 로 NaverMapApi를 통해서 주소를 조회하고 이후 검색결과가 1개인 경우에만 응답된 roadAddress, jibunAddress, x, y 값을 idx, roadAddress, jibunAddress, x, y 형식으로 엑셀파일로 생성하는 코드 KakaoMigration 클래스에 작성해줘.
+검색결과가 2개 이상이거나 없는 등 실패한 데이터의 idx만 모아서 따로 출력해줘.

# 2025-05-29 13:42:10.299767
+로컬c 에 temp 폴더안에 파일을 생성하게 수정해줘

# 2025-05-29 13:46:52.564862
+/map-refresh

# 2025-05-29 13:47:11.913662
+QuickAddressBookRepository 를 직접 사용하여 모두 조회해줘.

# 2025-05-29 13:57:57.469311
+/commit

# 2025-06-02 16:32:44.870657
+n

# 2025-06-02 16:38:31.361986
+/companion start

# 2025-06-02 16:39:09.220492
+/map-refresh

# 2025-06-02 17:21:21.249506
+Batch 서버를 통해서 매일 새벽에 호출될 전날 완료된 주문을 동기화 기능을 추가해야 해.
+API는 /sync-orders 를 그대로 사용하며 request로 받는 SyncOrders DTO 에서 구분값을 추가하여 구분해줘.

# 2025-06-02 17:24:26.174198
+/undo

# 2025-06-02 17:24:47.438304
+n

# 2025-06-02 17:25:40.810728
+/map-refresh

# 2025-06-02 17:26:44.084668
+/companion start

# 2025-06-02 17:26:51.986913
+Batch 서버를 통해서 매일 새벽에 호출될 전날 완료된 주문을 동기화 기능을 추가해야 해.
+API는 /sync-orders 를 그대로 사용하며 request로 받는 SyncOrders DTO 에서 isComplete 조건을 추가하여 구분해줘.

# 2025-06-02 17:28:25.673329
+n

# 2025-06-02 17:29:10.160144
+/companion start

# 2025-06-02 17:29:13.392628
+Batch 서버를 통해서 매일 새벽에 호출될 전날 완료된 주문을 동기화 기능을 추가해야 해.
+API는 /sync-orders 를 그대로 사용하며 request로 받는 SyncOrders DTO 에서 isComplete 조건을 추가하여 구분해줘.

# 2025-06-02 17:37:48.498066
+/commit

# 2025-06-09 16:03:48.517275
+n

# 2025-06-09 16:05:34.913774
+/map-refresh

# 2025-06-10 09:06:37.384028
+n

# 2025-06-10 09:07:24.539938
+/companion start

# 2025-06-10 09:09:51.199174
+ /address-book 형식에 API 엔드포인트를 /booking/address-book 으로도 접근 가능하게 수정해줘.
+하위 호환성을 위해 /address-book 형식에 기존 엔드포인트도 그대로 유지할거야.

# 2025-06-10 09:26:53.233143
+/clear

# 2025-06-10 09:26:57.018411
+/map-refresh

# 2025-06-10 09:55:31.339660
+헤더에 x-userid, x-comid 가 없으면 accessToken 및 유저정보 조회를 통하여 얻어온 uid와 comid를 MDC에 ua-user-id, ua-com-id 로 설정하게 수정해줘.
+
+

# 2025-06-10 16:55:29.625292
+n

# 2025-06-10 17:25:13.284398
+/companion start

# 2025-06-10 17:26:50.362487
+/map-refresh

# 2025-06-10 17:36:56.498516
+ListRequest Dto에 검색조건 enum을 하나 추가해야 해.
+"신청일자로 검색", "지급/차감일자로 검색" 이야,
+신청일자로 검색인 경우는 현재로직 그대로 동작되면 되며,
+지급/차감일자로 검색인 경우 startdate, enddate 조건을 reserveDate 로 비교하며 reserveDate가 null인 경우엔 regDate 로 검색해야 해.
+정렬조건도 신청일자로 검색인 경우엔 현재 regDate를 그대로 유지하고, 지급/차감일자로 검색인 경우 reserveDate(null인 데이터는 regDate)로 정렬해야 해.

# 2025-06-10 17:47:28.297987
+/commit

# 2025-06-11 13:50:24.696046
+n

# 2025-06-11 13:52:11.371134
+/companion start

# 2025-06-11 13:53:23.435645
+DateSearchType 에 "지급/차감일자로 검색"시 executeDate 로 기간범위를 조회해야 해,
+RESERVE_DATE, RESERVE 라는 명칭이 부적합한거 같아, 적절하게 수정하고 쿼리문에 검색조건도 executeDate 를 사용하게 수정해줘.

# 2025-06-12 14:31:29.941781
+n

# 2025-06-12 14:32:39.277501
+/commit

# 2025-06-20 13:35:19.602952
+n

# 2025-06-20 13:40:14.108316
+/map

# 2025-06-20 13:48:52.941509
+n

# 2025-06-20 15:01:21.313287
+/companion start

# 2025-06-20 15:01:28.540159
+payRoomIdx | useDate | comName | orgIdx | username | menuName | companyPrice | instantPayPrice | price
+결제번호 | 결제시간 | 회사명 | 부서 | 결제자 | 메뉴명 | 식대금액 | 간편결제 | 총 결제금액
+123145 | 2025-06-02 11:44:41 | 삼성E&A | 03. 기흥/화성 (계산서 분리 발급) > (SE7004) NRD-K 초순수 > 공사 | 김대장 | 5000원 메뉴 | 4500| 500| 5,000
+123145 | 2025-06-02 11:44:41 | 삼성E&A | 03. 기흥/화성 (계산서 분리 발급) > (SE7004) NRD-K 초순수 > PM+관리 | 이대장 | 5000원 메뉴 | 4500| 500 | 5,000
+
+StoreStatController 에 /v1/store/detail API를 추가해야 해.
+request로 startDate, endDate, sid를 받아야 해.
+requestData.getComId() 의 회사와 전달받은 sid 간의 결제내역을 확인하여 위와 같은 양식으로 응답해야 해.
+
+CouponGroup에 status 0~9 인 데이터만 조회하면 되고, 1:N 으로 연결된 CouponMember 값을 기준으로 위 데이터를 추출해야 해.
+부서같은 경우 OrganizationDivisionRemote 를 통해 회사에 전체 부서를 조회하고 응답받은 값을 재사용 해줘.

# 2025-06-20 15:13:59.891127
+/map-refresh

# 2025-06-20 15:18:09.941390
+StoreStatServiceTest 클래스에 getStoreDetail 통합테스트 추가해줘

# 2025-06-20 15:21:25.397699
+getStoreDetail 메서드에서 ParamDto.RequestData 를 매개변수로 받지말고, StoreDetailRequest 에 comId까지 추가하여 받아줘

# 2025-06-20 15:30:06.870113
+/map-refresh

# 2025-06-20 15:30:27.785778
+StoreDetailRequest 에서 Date형이 아닌 LocalDate를 사용하게 변경해줘
